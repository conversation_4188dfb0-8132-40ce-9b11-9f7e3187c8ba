#服务器部署资料
##说明
    上海是不开内网的，所以需要使用上海老师提供的堡垒机跳转，目前我已经在堡垒机安装了xshell和navicat客户端。
    xshell的地址已经配置
    navicat访问的地址也已经配置
##服务器地址列表
###mysql数据库服务器：
####地址
    ip: *********
####ssh
    username:root
    password:Kaiya@123
####mysql用户
    username:root
    password:Kaiya@123

##操作步骤
    在进行这个操作前，先要将所有部署脚本的
        数据库地址、数据库名称、数据库账户名、数据库密码
    修改正确（就是修改为你要部署的环境地址）
###1、备份老的数据库
    将MySQL中的mnjx备份为了mnjx_v3.6
###2、查看目前的容器情况
    停止所有容器，并且容器和镜像都不要删除
###3、删除mnjx数据内部的表对象
    不要去删除了备份库哈
###4、拷贝数据库初始化文件（使用我们提供的文件）
    1、将db文件夹下的数据库结构文件(mnjx_structure.sql)拷贝到堡垒机，使用navicat执行就可以了
    2、
###5、拷贝部署脚本
    1、将当前文件夹下的除部署说明.md外的所有文件拷贝到/home/<USER>
    2、到/home下执行chmod -R 744 /deploy
    3、执行脚本（这个根据自己情况来）
