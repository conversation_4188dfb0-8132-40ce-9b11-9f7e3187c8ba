version: "3.0"
services:
  #mysql服务器
  mysql:
    # 容器名称
    container_name: mysql
    # 使用的镜像
    image: harbor.kaiya.com:30443/public/mysql:5.7
    # 网络策略使用共享主机网络策略
    network_mode:
      bridge
    # 暴露端口
    ports:
      - "3309:3306"
    # 一直重启
    restart: always
    # 设置环境变量
    environment:
      # 时区设置
      TZ: Asia/Shanghai
      # root用户的密码
      MYSQL_ROOT_PASSWORD: 3w@4C2yx2
      # 创建的数据库
      MYSQL_DATABASE: mnjx
      # 自定义用户名
      # MYSQL_USER: worm
      # 自定义用户的密码
      # MYSQL_PASSWORD: root
      #
      LANG: "C.UTF-8"
    # 数据库启动的一些配置
    command:
      # 设置数据库的编码方式
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_general_ci
      # 设置数据库的时间戳方式
      - --explicit_defaults_for_timestamp=true
      # 设置数据库表名大小不敏感 （1 不敏感；0 敏感）
      - --lower_case_table_names=1
      # 设置mysql的加密方式: 8.0及以后使用的是:caching_sha2_password,再这之前使用的mysql_native_password
      - --default-authentication-plugin=mysql_native_password
      # Sql_mode是一组mysql的语法校验规则，定义了mysql应该支持的sql语法、数据校验等。
      - --sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
      # 设置锁等待超时时间300
      - --innodb_lock_wait_timeout=300

    # 使用自己定义的卷标
    volumes:
      # 宿主机的本地路径或者自定义的卷标：容器中的路径
      - data:/var/lib/mysql

# 使用卷标的方式，简洁易于管理，但是数据实际存放的位置需要费点周折才能看到。
# 在容器第一次启动的时候，容器内映射目录的数据复制到volume,当卷标中存在数据时，指定这个有数据的卷标，容器启动多次都没问题
volumes:
  data: