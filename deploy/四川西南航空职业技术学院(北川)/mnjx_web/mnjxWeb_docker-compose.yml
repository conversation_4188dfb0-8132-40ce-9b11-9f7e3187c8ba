version: '3.0'
services:
  # =======================mnjx_web服务============================
  mnjx_web_bc:
    # 镜像地址(这个是开发环境的构建镜像)
    #build:
    #context: .
    #dockerfile: ./Dockerfile
    # 这个是正式的环境
    image: harbor.kaiya.com:30443/sts/mnjx_web:20230208115509
    # 容器名称
    container_name: mnjx_web_bc
    # 服务挂掉的情况下自动重启（目前先不启用，稳定后启用）
    restart: always
    # 网络策略使用共享主机网络策略
    network_mode:
      bridge
    # 暴露端口
    ports:
      - "8352:8351"
    # 配置环境变量，这些环境变量就是容器钟要使用的，目前就是配置数据库的地址
    environment:
      # 数据库驱动
      DATABASE_DRIVER_CLASS_NAME: com.mysql.cj.jdbc.Driver
      # 数据库的IP地址
      DATABASE_IP: *************
      # 端口
      DATABASE_PORT: 3306
      # 数据库名(模拟教学的数据库、模拟教学自动任务数据库)
      DATABASE_NAME_MNJX: mnjx_bc
      DATABASE_NAME_JOB: xxl_job_bc
      # 数据库用户名
      DATABASE_USER: root
      # 数据库root账户的密码
      MYSQL_ROOT_PASSWORD: GmhRf@3w@4C2]T&
      # 自动任务管理器的IP
      JOB_IP: *************
      # 自动任务管理器的端口（端口是做了映射需要改成新端口）
      JOB_PORT: 8381
      # 自动任务管理器的项目名 （这个是项目名称改部署脚本不要改这个名称）
      JOB_ADMIN: mnjx_job
    volumes:
      - data:/usr/local/mnjx_web/logs

volumes:
  data:
