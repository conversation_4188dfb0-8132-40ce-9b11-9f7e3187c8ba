# 输出防火墙的状态
sudo systemctl status firewalld
# 开启3309
sudo firewall-cmd --zone=public --add-port=3309/tcp --permanent
# 开启3306
sudo firewall-cmd --zone=public --add-port=3306/tcp --permanent
# 开启351
sudo firewall-cmd --zone=public --add-port=351/tcp --permanent
# 开启8380
sudo firewall-cmd --zone=public --add-port=8380/tcp --permanent
# 开启8351
sudo firewall-cmd --zone=public --add-port=8351/tcp --permanent
# 开启80
sudo firewall-cmd --zone=public --add-port=80/tcp --permanent
# 重启firewall
sudo firewall-cmd --reload
# 查看当前已经开放的端口
sudo firewall-cmd --list-ports
# 输出防火墙的状态
sudo systemctl status firewalld