## 项目发版
### 发版说明
    要保证项目发布版本的相对稳定，即要求数据库的脚本、部署脚本、代码之间要相对的是可运行、可部署、可发布。

### 现有镜像的版本号
    目前的镜像版本最新版的2种版本号格式：年月日时分秒/latest 
    latest 始终是最新版= 最后一个时间戳 的版本
    
### 发版要求
    所有的正式发版都要有确定的对应需求列表，在次需求列表就已经列出了具体的版本TAG,TAG我们建议用时间戳。以便和现有的版本号规整对应。

#### 代码打版

##### 后端代码
##### 前段代码
##### 测试代码

## 流程梳理
### 生产流程
    1、配置前需要在POM中配置SCM和distributionManagement。（已配置）
        SCM配置代码提交地址与TAG的名字
        distributionManagement配置deploy的地址。
            用于发布代码（此地址可以用于本地的deploy,如果版本包含：SNAPSHOT，则发布到SNAPSHOT，如果没有包含，则发布到release）
    2、当前的所有代码都是SNAPSHOT状态，开发修改都在当前的版本中。
    3、当有代码修改时，将代码合并到dev_netty分支,并且提交到代码库。
    4、使用maven-release-plugin插件执行prepare，完成版本更新与tag，并且提交到代码库
    5、使用maven-release-plugin插件执行perform，将代码发布到nexus的release中(此步骤非必须的)
    6、使用最后的一个tag获取代码，并且clean package,使用本地打包后的构建镜像。
    7、将本地构建的镜像推送到harbor服务器的sts_release
    8、在部署的时候使用sts_release里面的具体镜像（目前采用最后一个版本）
### 测试流程
    1、本地有代码修改，修改的是在snapshot代码上面，那就直接将修改的代码合并到dev_netty分支上面。
    2、使用maven的clean package打包代码。
    3、在jenkins所在的服务器根据项目中的脚本构建镜像。
    4、在jenkins的服务器调用部署脚本重新部署测试服务器，测试服务器上面就运行的是最新的snapshot代码。