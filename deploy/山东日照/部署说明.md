#服务器部署资料
##说明
##服务器地址列表
	ip: **************2
	username:root
     password:Scholar@4416
###mysql数据库服务器：
####地址
    ip: **************2
    username:root
    password:kaiya@123
        端口:3306

###镜像tag：
  eterm：20221205083953
    web：20221201185303
    job：20221201185212
  front：20221201184938

##操作步骤
    在进行这个操作前，先要将所有部署脚本的
        数据库地址、数据库名称、数据库账户名、数据库密码、最新镜像地址
    修改正确（就是修改为你要部署的环境地址）
###1、备份老的数据库
    
###2、查看目前的容器情况
    停止所有容器，并且容器和镜像都不要删除
###3、删除mnjx数据内部的表对象
    
###4、拷贝数据库初始化文件（使用我们提供的文件）
    
###5、拷贝部署脚本
    1、将当前文件夹下的除部署说明.md外的所有文件拷贝到/opt/deploy文件夹下
    2、到/opt下执行chmod -R 744 /opt/deploy
    3、执行脚本（这个根据自己情况来）

###部署成功，服务器访问
  自动任务：http://**************2:8380/mnjx_job/toLogin
  后台管理系统：http://**************2/sts/#/login?redirect=%2Fdashboard
  eterm: **************2 端口：351