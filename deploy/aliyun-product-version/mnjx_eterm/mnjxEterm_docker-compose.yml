version: '3.0'
services:
  # =======================mnjx_eterm服务============================
  mnjx_eterm:
    # 镜像地址(这个是开发环境的构建镜像)
    #build:
    #context: .
    #dockerfile: ./Dockerfile
    # 这个是正式的环境
    image: harbor.kaiya.com:30443/sts/mnjx_eterm:latest
    # 容器名称
    container_name: mnjx_eterm
    # 服务挂掉的情况下自动重启（目前先不启用，稳定后启用）
    restart: always
    # 网络策略使用共享主机网络策略
    network_mode:
      bridge
    # 暴露端口
    ports:
      - "350:351"
    #设置全连接的大小（这个大小会与程序中的设置一起取两者的最小值，我们都设置的1024）
    # https://docs.docker.com/compose/compose-file/#sysctls
    sysctls:
      net.core.somaxconn: 1024
    # 配置环境变量，这些环境变量就是容器钟要使用的，目前就是配置数据库的地址
    environment:
      # 数据库驱动
      DATABASE_DRIVER_CLASS_NAME: com.mysql.cj.jdbc.Driver
      # 数据库的IP地址
      DATABASE_IP: rm-f8zld8y563d53n50o.mysql.rds.aliyuncs.com
      # 端口
      DATABASE_PORT: 3306
      # 数据库名(模拟教学的数据库、模拟教学自动任务数据库)
      DATABASE_NAME_MNJX: mnjx
      DATABASE_NAME_JOB: xxl_job
      # 数据库用户名
      DATABASE_USER: root
      # 数据库root账户的密码
      MYSQL_ROOT_PASSWORD: kaiya@Mysql_Pass123
      # 启动模式（学校教学模式、考试模式）
      RUNNING_MODE: teach
    # 设置mnjx_eterm的数据映射
#    volumes:
#      - data:/usr/local/mnjx_eterm/logs

volumes:
  data:
