version: '3.0'
services:
  # =======================mnjx_deep_face服务============================
  mnjx_deep_face:
    # 镜像地址(这个是开发环境的构建镜像)
    #build:
    #context: .
    #dockerfile: ./Dockerfile
    # 这个是正式的环境
    image: harbor.kaiya.com:30443/sts/mnjx_deep_face:latest
    # 容器名称
    container_name: mnjx_deep_face
    # 服务挂掉的情况下自动重启（目前先不启用，稳定后启用）
    restart: always
    # 网络策略使用共享主机网络策略
    network_mode:
      bridge
    # 暴露端口
    ports:
      - "5000:5000"
    #设置全连接的大小（这个大小会与程序中的设置一起取两者的最小值，我们都设置的1024）
    # https://docs.docker.com/compose/compose-file/#sysctls
    sysctls:
      net.core.somaxconn: 1024
    # 设置mnjx_deep_face的数据映射
    volumes:
      - data:/usr/local/mnjx_deep_face/logs

volumes:
  data:
