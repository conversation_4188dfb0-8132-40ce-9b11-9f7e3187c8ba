echo "启动所有容器"
./mnjx_eterm/mnjxEterm_startContainer.sh
./mnjx_job/mnjxJob_startContainer.sh
./mnjx_web/mnjxWeb_startContainer.sh
./mnjx_web_front/mnjxWebFront_startContainer.sh
./mnjx_screen/mnjxScreen_startContainer.sh
./mnjx_screen_front/mnjxScreenFront_startContainer.sh
./mnjx_test/mnjxTest_startContainer.sh
./mnjx_security_check/mnjxSecurityCheck_startContainer.sh
./mnjx_security_check_front/mnjxSecurityCheckFront_startContainer.sh