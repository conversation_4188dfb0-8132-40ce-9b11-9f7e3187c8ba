version: '3.0'
services:
  # =======================mnjx_security_check_front服务============================
  mnjx_security_check_front:
    # 镜像地址(这个是开发环境的构建镜像)
    #build:
    #context: .
    #dockerfile: ./Dockerfile
    # 这个是正式的环境
    image: harbor.kaiya.com:30443/sts/mnjx_security_check_front:latest
    # 容器名称
    container_name: mnjx_security_check_front
    # 服务挂掉的情况下自动重启（目前先不启用，稳定后启用）
    restart: always
    # 网络策略使用共享主机网络策略
    network_mode:
      bridge
    # 暴露端口
    ports:
      - "30086:30086"
    # 配置环境变量，这些环境变量就是容器钟要使用的，目前就是配置数据库的地址
    environment:
      # 指定模板的存放位置
      NGINX_ENVSUBST_TEMPLATE_DIR: /etc/nginx/templates
      # 指定模板的后缀
      NGINX_ENVSUBST_TEMPLATE_SUFFIX: .template
      # 指定根据模板输出文件的目的位置
      NGINX_ENVSUBST_OUTPUT_DIR: /etc/nginx/conf.d
      # NGINX的http协议监听的端口
      NGINX_PORT: 30086
      # 需要访问的后台服务
      SERVICE_ADDRESS: "http://*************:8355"
      # 访问人脸识别的后台服务
      DEEP_FACE_SERVICE_ADDRESS: "http://*************:5000"
      # 配置访问域名
      SERVER_NAME: "check.ve.sw"
      # 配置访问域名
      SERVER_ADDRESS: "https://check.ve.sw"
      # 服务端口
      SERVER_PORT: "30086"
#    volumes:
#      - dist_data:/usr/share/nginx/html/sts/
#      - config_data:/etc/nginx/

volumes:
  dist_data:
  config_data:
