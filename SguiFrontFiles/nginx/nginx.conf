# main(全局配置)、server(主机配置)、upstream(负载均衡服务器设置)以及location(URL匹配特定位置的设置).
# 这四者的关系是：server继承main,location继承server,upstream既不会继承其它设置也不会被继承。
# 指定nginx运行的用户及用户组,默认为nobody
user  nginx;
# 开启的线程数，一般跟逻辑CPU核数一致
worker_processes  auto;
# 指定一个nginx进程打开的最多文件描述符数目，受系统进程的最大打开文件数量限制
worker_rlimit_nofile 65535;

# 定位全局错误日志文件，级别以notice显示，还有debug,info,warn,error,crit模式，debug输出最多，crir输出最少，根据实际环境而定
error_log  /var/log/nginx/error.log warn;
# 指定进程id的存储文件位置
pid        /var/run/nginx.pid;

events {
    #参考事件模型，use [ kqueue | rtsig | epoll | /dev/poll | select | poll ]; epoll模型
    #是Linux 2.6以上版本内核中的高性能网络I/O模型，linux建议epoll，如果跑在FreeBSD上面，就用kqueue模型。
    #补充说明：
    #与apache相类，nginx针对不同的操作系统，有不同的事件模型
    #A）标准事件模型
    #Select、poll属于标准事件模型，如果当前系统不存在更有效的方法，nginx会选择select或poll
    #B）高效事件模型
    #Kqueue：使用于FreeBSD 4.1+, OpenBSD 2.9+, NetBSD 2.0 和 MacOS X.使用双处理器的MacOS X系统使用kqueue可能会造成内核崩溃。
    #Epoll：使用于Linux内核2.6版本及以后的系统。
    #/dev/poll：使用于Solaris 7 11/99+，HP/UX 11.22+ (eventport)，IRIX 6.5.15+ 和 Tru64 UNIX 5.1A+。
    #Eventport：使用于Solaris 10。 为了防止出现内核崩溃的问题， 有必要安装安全补丁。
    use epoll;
    # 定义每个进程的最大连接数,受系统进程的最大打开文件数量限制。
    worker_connections  65535;
    multi_accept on;
}

# 定义tcp的请求的相关配置（这个配置目前不使用）
stream {
    log_format access_json '{"@timestamp":"$time_iso8601",'
            '"clientip":"$remote_addr",' #客户端ip
            '"host":"$server_addr",' #服务器ip
            '"protocol":"$protocol",' #使用的协议
            '"status":"$status",' #放回状态
            '"bytes_sent":"$bytes_sent",' #客户端到nginx的数据发送
            '"bytes_received":"$bytes_received",' #nginx返回客户端的数据量
            '"session_time":"$session_time",' #客户端到nginx的session时长
            '"connection":"$connection",' #tcp连接序号
            '"upstreamhost":"$upstream_addr",' #反向代理服务器的目标地址
            '"upstream_bytes_sent":"$upstream_bytes_sent",' #代理到应用服务器的数据两
            '"upstream_bytes_received":"$upstream_bytes_received",' #应用服务器代理的数据量
            '"upstream_connect_time":"$upstream_connect_time",' #代理到应用服务器的时长
            '}';

    # 定义nginx输出日志的文件以及格式
    access_log /var/log/nginx/stream-access.log access_json;

    limit_conn_zone $binary_remote_addr zone=ip_addr:10m;
}

#######Nginx的Http服务器配置,Gzip配置
http {
    # 主模块指令，实现对配置文件所包含的文件的设定，可以减少主配置文件的复杂度，DNS主配置文件中的zonerfc1912,acl基本上都是用include语句。
    include       /etc/nginx/mime.types;
    # 核心模块指令，智力默认设置为二进制流，也就是当文件类型未定义时使用这种方式
    default_type  application/octet-stream;

    # 下面代码为日志格式的设定，main为日志格式的名称，可自行设置，后面引用
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    # 引用日志main
    access_log  /var/log/nginx/access.log  main;

    #设置允许客户端请求的最大的单个文件字节数
    #client_max_body_size 20M;
    #指定来自客户端请求头的headebuffer大小
    #client_header_buffer_size  32k;
    #指定连接请求试图写入缓存文件的目录路径
    #client_body_temp_path /dev/shm/client_body_temp;
    #指定客户端请求中较大的消息头的缓存最大数量和大小，目前设置为4个32KB
    #large client_header_buffers 4 32k;

    #开启高效文件传输模式
    sendfile        on;
    #开启防止网络阻塞
    #tcp_nopush     on;
    #tcp_nodelay on;

    #设置客户端连接保存活动的超时时间
    keepalive_timeout  65;

    #设置客户端请求读取超时时间
    #client_header_timeout 10;
    #设置客户端请求主体读取超时时间
    #client_body_timeout 10;
    #用于设置相应客户端的超时时间
    #send_timeout

    ####HttpGZip模块配置
    #httpGzip modules
    #开启gzip压缩
    #gzip  on;
    #设置允许压缩的页面最小字节数
    #gzip_min_length 1k;
    #申请4个单位为16K的内存作为压缩结果流缓存
    #gzip_buffers 4 16k;
    #设置识别http协议的版本，默认为1.1
    #gzip_http_version 1.1;
    #指定gzip压缩比，1-9数字越小，压缩比越小，速度越快
    #gzip_comp_level 2;
    #指定压缩的类型
    #gzip_types text/plain application/x-javascript text/css application/xml;
    #让前端的缓存服务器进过gzip压缩的页面
    #gzip_vary on;

    #隐藏Nginx版本。
    server_tokens off;

    # 系统的配置文件
    include /etc/nginx/conf.d/*.conf;
}
