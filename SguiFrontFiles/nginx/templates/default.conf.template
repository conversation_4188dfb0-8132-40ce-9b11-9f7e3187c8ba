server {
    # 容器内部监听80端口
    listen       80;
    server_name  localhost;

    #设置访问的语言编码
    #charset koi8-r;

    #清除 Server 信息
    server_tokens off;
    add_header X-Frame-Options SAMEORIGIN always;
    add_header X-Content-Type-Options nosniff;

    #设置虚拟主机访问日志的存放路径及日志的格式为main
    #access_log  logs/host.access.log  main;

	#location ~ ^/sgui/(static|assets)/ {
	#	try_files $uri =404; # 关键：直接返回 404
	#}

    #设置虚拟主机的基本信息
    location / {
      #设置虚拟主机的网站根目录
      root   /usr/share/nginx/html;

      #设置虚拟主机默认访问的网页
      index  index.html;
      try_files $uri $uri/ /index.html;
    }

    error_page   500 502 503 504  /50x.html;

    # 反向代理配置
    location /sgui-bkc {
        proxy_pass http://${SERVICE_ADDRESS}/sgui-bkc;
    }

    location /sgui-et {
        proxy_pass http://${SERVICE_ADDRESS}/sgui-et;
    }

    location /sgui-tc {
        proxy_pass http://${SERVICE_ADDRESS}/sgui-tc;
    }

    location /sgui-sor {
        proxy_pass http://${SERVICE_ADDRESS}/sgui-sor;
    }

    location /sgui-rpt {
        proxy_pass http://${SERVICE_ADDRESS}/sgui-rpt;
    }
}