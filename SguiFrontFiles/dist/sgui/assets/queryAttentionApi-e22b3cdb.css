@charset "UTF-8";.portTabs[data-v-12f64a04]>.bkc-el-tabs__header{margin:0;border-bottom:none;background:var(--bkc-el-bg-color)}.portTabs[data-v-12f64a04]>.bkc-el-tabs__header .bkc-el-tabs__nav-wrap{width:100%;border-bottom:1px solid var(--bkc-color-gray-7)}.portTabs[data-v-12f64a04]>.bkc-el-tabs__header .bkc-el-tabs__nav{width:100%}.portTabs[data-v-12f64a04]>.bkc-el-tabs__header .bkc-el-tabs__nav .bkc-el-tabs__item{width:92px;background:var(--bkc-el-bg-color);text-align:center;border-left:none;border-right:none;color:var(--bkc-color-gray-4);font-size:12px}.portTabs[data-v-12f64a04]>.bkc-el-tabs__header .bkc-el-tabs__nav .bkc-el-tabs__item:first-child{width:unset;padding:0}.portTabs[data-v-12f64a04]>.bkc-el-tabs__header .bkc-el-tabs__nav .bkc-el-tabs__item.is-active{font-weight:700;color:var(--bkc-el-color-primary)}.portTabs[data-v-12f64a04] .bkc-el-tabs__content{padding:2px;overflow-y:auto}.portTabs[data-v-12f64a04] .bkc-el-tabs__content::-webkit-scrollbar{width:4px}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .main .content{height:22px;line-height:22px;margin:0 0 10px;width:120px;white-space:nowrap;overflow:hidden;word-break:break-all;text-overflow:ellipsis;font-size:12px;font-weight:400;cursor:pointer;color:var(--bkc-color-gray-2)}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .main .more .map .content{height:22px;line-height:22px;margin:0 0 8px;width:110px;white-space:nowrap;overflow:hidden;word-break:break-all;text-overflow:ellipsis;font-size:12px;font-weight:400;cursor:pointer;color:var(--bkc-color-gray-2)}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .scroll::-webkit-scrollbar{width:4px}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .scroll::-webkit-scrollbar-thumb{border-radius:8px;-webkit-box-shadow:inset 0 0 5px var(--bkc-color-gray-6);background:var(--bkc-color-gray-6)}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .scroll::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 5px var(--bkc-el-color-white);border-radius:0;background:var(--bkc-el-bg-color)}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .airport-info-box{display:flex;align-items:center}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .airport-info-box .airport-code-box{min-width:40px;text-align:right}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .airport-info-box .airport-code-box .airport-code-text{margin-right:4px;height:22px;text-overflow:ellipsis;white-space:nowrap;word-break:break-all;background-color:var(--bkc-tw-gray-7);padding-left:2px;padding-right:2px;font-size:.75rem;line-height:1rem;line-height:22px;color:var(--bkc-tw-gray-4)}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .airport-info-box .airport-name-text{overflow:hidden;text-overflow:ellipsis}.portTabs[data-v-12f64a04] .bkc-el-tabs__content ul li .content{height:22px;line-height:22px;margin:0 0 8px;width:110px;white-space:nowrap;overflow:hidden;word-break:break-all;text-overflow:ellipsis;font-size:12px;font-weight:400;cursor:pointer;color:var(--bkc-color-gray-2)}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .content.active{background-color:var(--bkc-auxiliary-3)!important;color:var(--bkc-el-color-primary)!important}.portTabs[data-v-12f64a04] .bkc-el-tabs__content .content.active .airport-info-box .airport-code-box .airport-code-text{background-color:var(--bkc-tw-brand-3);color:var(--bkc-tw-brand-1)}.memory .panel[data-v-70c79d4c]{padding:2px 4px}.memory .panel .list[data-v-70c79d4c]{min-width:0px;flex:1 1 0%}.memory .panel .list .content[data-v-70c79d4c]{height:22px;line-height:22px;margin:0;width:110px;white-space:nowrap;overflow:hidden;word-break:break-all;text-overflow:ellipsis;font-size:12px;font-weight:400;cursor:pointer;color:var(--bkc-color-gray-2)}.memory[data-v-70c79d4c]::-webkit-scrollbar{width:4px}.memory[data-v-70c79d4c]::-webkit-scrollbar-thumb{border-radius:8px;-webkit-box-shadow:inset 0 0 5px var(--bkc-color-gray-6);background:var(--bkc-color-gray-6)}.memory[data-v-70c79d4c]::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 5px var(--bkc-el-color-white);border-radius:0;background:var(--bkc-el-bg-color)}.agent-search[data-v-70c79d4c]{max-height:400px;width:348px;overflow-y:auto;border-radius:.125rem;border-width:1px;border-color:var(--bkc-tw-gray-6);background-color:var(--bkc-tw-gray-0);padding-top:.375rem;padding-bottom:.375rem;color:var(--bkc-tw-gray-6);--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.agent-search .code-text[data-v-70c79d4c]{margin-right:5px;width:90px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:break-all;font-size:.75rem;line-height:1rem;color:var(--bkc-tw-gray-2)}.agent-search .list[data-v-70c79d4c]{display:flex;min-width:0px;flex:1 1 0%;flex-direction:column}.en-agent-search[data-v-70c79d4c]{width:404px}.en-agent-search .code-text[data-v-70c79d4c]{width:120px}.panel[data-v-70c79d4c]{padding:2px 4px}.panel .type-text-box[data-v-70c79d4c]{margin-bottom:8px;display:flex;align-items:center}.panel .type-text-box .text-name[data-v-70c79d4c]{margin-right:10px;font-weight:700;color:var(--bkc-tw-gray-2)}.panel .type-text-box .name-line[data-v-70c79d4c]{height:1px;flex:1 1 0%;background-color:var(--bkc-tw-gray-6)}.panel .country-code-text[data-v-70c79d4c]{display:inline-block;height:18px;text-overflow:ellipsis;white-space:nowrap;word-break:break-all;background-color:var(--bkc-tw-gray-7);padding-left:3px;padding-right:3px;font-size:.75rem;line-height:1rem;line-height:18px;color:var(--bkc-tw-gray-2);margin-right:7px;min-width:22px;text-align:center}.panel .airport-info-box[data-v-70c79d4c]{margin-bottom:10px;display:flex}.panel .airport-info-box .airport-code-box[data-v-70c79d4c]{margin-right:5px;min-width:40px;text-align:right}.panel .airport-info-box .airport-code-box .airport-code-text[data-v-70c79d4c]{display:inline-block;height:18px;text-overflow:ellipsis;white-space:nowrap;word-break:break-all;background-color:var(--bkc-tw-gray-7);padding-left:3px;padding-right:3px;font-size:.75rem;line-height:1rem;line-height:18px;color:var(--bkc-tw-gray-2)}.panel .airport-info-box .airport-name-text[data-v-70c79d4c]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--bkc-tw-gray-2)}[data-v-4b22c934] .diTabs>.bkc-el-tabs__header{margin:0;border-bottom:none}[data-v-4b22c934] .diTabs>.bkc-el-tabs__header .bkc-el-tabs__nav{width:100%}[data-v-4b22c934] .diTabs>.bkc-el-tabs__header .bkc-el-tabs__nav .bkc-el-tabs__item{width:50%;text-align:center;background:var(--bkc-color-gray-7);font-weight:400;color:var(--bkc-color-gray-2)}[data-v-4b22c934] .diTabs>.bkc-el-tabs__header .bkc-el-tabs__nav .bkc-el-tabs__item.is-active{background:var(--bkc-el-color-primary);color:var(--bkc-el-color-white)}[data-v-4b22c934] .bkc-el-input__inner{padding-left:38px;position:relative}[data-v-4b22c934] .bkc-el-input__prefix{height:100%;display:inline-flex;align-items:center}[data-v-4b22c934] .bkc-el-input__prefix .svg-icon{font-size:21px;color:var(--bkc-el-color-primary);margin-left:5px}.top-err .middlePosition[data-v-4b22c934] .bkc-el-input__clear{height:50px;padding-top:1px}.middlePosition[data-v-4b22c934] .bkc-el-input__wrapper{position:relative;height:2rem;border-width:1px;border-style:solid;border-color:transparent}.middlePosition[data-v-4b22c934] .bkc-el-input__wrapper .bkc-el-input__suffix{position:absolute;right:6px;height:26px;width:1rem;background-color:var(--bkc-tw-gray-0)}.middlePosition[data-v-4b22c934] .bkc-el-input__wrapper .bkc-el-input__icon{margin-left:3px}.middlePosition[data-v-4b22c934] .bkc-el-form-item.is-error .bkc-el-input__wrapper.is-focus{--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.airportClass{width:auto!important;padding:0!important;border:0!important;box-shadow:none}.transit-terminal-airport-popover{padding:0!important}.row-base[data-v-2f9e66d0]{min-height:30px;padding:6px}.th[data-v-2f9e66d0]{color:var(--bkc-tw-gray-4)}.cell[data-v-2f9e66d0]{background-color:var(--bkc-tw-brand-7)}.cursor-default[data-v-66ec95ac] .bkc-el-divider{margin-top:10px;margin-bottom:10px}.input-focus-tip[data-v-6bab0367]{display:flex;align-items:center}:not(.is-error) .custom-focus-tip-input .input-focus .bkc-el-form-item__error{display:inline-block;min-width:20px;background-color:var(--bkc-tw-brand-4);color:var(--bkc-tw-brand-2)}.header-av-query-form[data-v-bd87614f]{display:flex;width:100%;flex-wrap:wrap}.header-av-query-form[data-v-bd87614f] .bkc-el-form-item{margin-bottom:10px}.header-av-query-form[data-v-bd87614f] .bkc-el-input__wrapper{border-radius:.125rem;padding-left:6px;padding-right:6px;font-size:12px}.header-av-query-form[data-v-bd87614f] .bkc-el-input__inner{height:2rem;padding-left:0}.header-av-query-form[data-v-bd87614f] .bkc-el-input.is-disabled .bkc-el-input__wrapper{background-color:var(--bkc-tw-gray-7);--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.header-av-query-form[data-v-bd87614f] .bkc-el-form-item__content{height:100%}.header-av-query-form[data-v-bd87614f] .bkc-el-form-item__content .bkc-el-form-item__error{z-index:10}.header-av-query-form .service-book-btn[data-v-bd87614f],.header-av-query-form .service-book-btn[data-v-bd87614f]:hover{outline:none}.header-av-query-form .service-book-btn .iconfont[data-v-bd87614f]{font-size:16px;margin-right:3px}@media (min-width: 1920px){.agent-airport-item[data-v-bd87614f]{min-width:135px}}@media (min-width: 1440px) and (max-width: 1919px){.agent-airport-item[data-v-bd87614f]{min-width:53px}}@media (max-width: 1439px){.agent-airport-item[data-v-bd87614f]{min-width:134px}}.agent-airport-item[data-v-bd87614f] .bkc-el-input__wrapper{border-radius:.125rem}@media (min-width: 1920px){.airport-item[data-v-bd87614f]{max-width:200px}}@media (min-width: 1440px) and (max-width: 1919px){.airport-item[data-v-bd87614f]{max-width:135px}}@media (max-width: 1439px){.airport-item[data-v-bd87614f]{max-width:134px}}.fast-query-airport-item[data-v-bd87614f]{width:187px}.sort-text[data-v-bd87614f]{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));color:var(--bkc-tw-brand-2)}.departure-date[data-v-bd87614f]{margin-left:10px;margin-right:10px}.departure-date[data-v-bd87614f] .bkc-el-form-item__content{width:180px}@media (max-width: 1439px){.departure-date[data-v-bd87614f] .bkc-el-form-item__content{width:180px}}.departure-date[data-v-bd87614f] .bkc-el-input__wrapper{position:relative;height:2rem;border-width:1px;border-style:solid;border-color:transparent}.departure-date[data-v-bd87614f] .bkc-el-input__wrapper .bkc-el-input__prefix{position:absolute;right:0;color:var(--bkc-tw-brand-2)}.departure-date[data-v-bd87614f] .bkc-el-input__wrapper .bkc-el-input__suffix{position:absolute;right:22px}.departure-date[data-v-bd87614f] .bkc-el-form-item.is-error .bkc-el-input__wrapper:hover{--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.round-trip-date[data-v-bd87614f] .bkc-el-form-item__content{width:210px}@media (max-width: 1439px){.round-trip-date[data-v-bd87614f] .bkc-el-form-item__content{width:210px}}.airlines-item-box[data-v-bd87614f]{display:flex;height:100%;align-items:center}.airlines-item-box .airlines-item[data-v-bd87614f]{height:34px!important;cursor:pointer!important;border-top-right-radius:0!important;border-bottom-right-radius:0!important;padding-top:0!important;padding-bottom:0!important}.airlines-input[data-v-bd87614f]{display:flex;width:44px;align-items:center}.airlines-input[data-v-bd87614f] .bkc-el-input__wrapper{width:44px;border-top-left-radius:0;border-bottom-left-radius:0}.small-width[data-v-bd87614f],.flightNumber-input[data-v-bd87614f] .bkc-el-input__wrapper{width:62px}.small-width[data-v-bd87614f]:first-child,.flightNumber-input[data-v-bd87614f] .bkc-el-input__wrapper:first-child{margin-right:10px}.airlines-input-item[data-v-bd87614f] .bkc-el-form-item__error{left:26px}.transit-terminal[data-v-bd87614f] .bkc-el-form-item__content{width:48px}.carrier-only-direct[data-v-bd87614f]{margin-left:16px;margin-right:16px;display:flex;flex-direction:column}.carrier-only-direct[data-v-bd87614f] .bkc-el-checkbox{margin-right:0;height:14px}.carrier-only-direct[data-v-bd87614f] .bkc-el-checkbox:first-child{margin-bottom:4px}.carrier-only-direct[data-v-bd87614f] .bkc-el-checkbox__label{padding-left:4px;font-size:12px}.lowest-price-box[data-v-bd87614f] .bkc-el-checkbox{margin-right:0;height:14px}.lowest-price-box[data-v-bd87614f] .bkc-el-checkbox:first-child{margin-bottom:4px}.lowest-price-box[data-v-bd87614f] .bkc-el-checkbox__label{padding-left:4px;font-size:12px}.radio-selected .airlines-item[data-v-bd87614f]{background-color:var(--bkc-tw-brand-7);--tw-shadow: inset 0 0 0 1px var(--bkc-tw-brand-2);--tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.radio-cancel .bkc-el-input__wrapper[data-v-bd87614f]:hover{--tw-shadow: inset 0 0 0 1px var(--bkc-tw-gray-6);--tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.query-btn[data-v-bd87614f]{height:2rem;width:3rem;padding-left:.625rem;padding-right:.625rem;padding-top:5px;padding-bottom:5px;font-size:.875rem;line-height:1.25rem}.av-history-popper.bkc-el-popover.bkc-el-popper{padding:.375rem .125rem .375rem .375rem}.av-history-popper .bkc-el-dropdown-menu__item{padding-left:6px;padding-right:20px}.date-icon[data-v-78242d0d] .bkc-el-date-editor{height:100%;width:16px}.date-icon[data-v-78242d0d] .bkc-el-date-editor .bkc-el-input__wrapper{background-color:transparent;padding:0;color:var(--bkc-tw-brand-2);--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.date-icon[data-v-78242d0d] .bkc-el-date-editor .bkc-el-input__wrapper .bkc-el-input__prefix{height:16px;width:16px;cursor:pointer}.date-icon[data-v-78242d0d] .bkc-el-date-editor .bkc-el-input__wrapper .bkc-el-input__prefix .icon-calendar{font-size:15px}.date-icon[data-v-78242d0d] .bkc-el-date-editor .bkc-el-input__wrapper .bkc-el-input__inner{display:none}.active-blue-box[data-v-78242d0d]{border-color:var(--bkc-tw-brand-2);background-color:var(--bkc-tw-brand-4);color:var(--bkc-tw-brand-2)}.active-blue-box .code[data-v-78242d0d],.active-blue-box .date-text[data-v-78242d0d],.active-blue-box .date-icon[data-v-78242d0d],.active-blue-box .code[data-v-78242d0d] .bkc-el-date-editor .bkc-el-input__wrapper .bkc-el-input__prefix,.active-blue-box .date-text[data-v-78242d0d] .bkc-el-date-editor .bkc-el-input__wrapper .bkc-el-input__prefix,.active-blue-box .date-icon[data-v-78242d0d] .bkc-el-date-editor .bkc-el-input__wrapper .bkc-el-input__prefix{color:var(--bkc-tw-brand-2)}.active-red-box[data-v-78242d0d]{border-color:var(--bkc-tw-red-1);background-color:var(--bkc-tw-red-3)}.active-red-box .code[data-v-78242d0d]{color:var(--bkc-tw-brand-2)}.active-red-box .date-text[data-v-78242d0d]{color:var(--bkc-tw-red-1)}.active-red-box .date-icon[data-v-78242d0d],.active-red-box .date-icon[data-v-78242d0d] .bkc-el-date-editor .bkc-el-input__wrapper .bkc-el-input__prefix{color:var(--bkc-tw-brand-2)}.popover-airlines .button-operator[data-v-283a3918]{display:flex;height:30px;align-items:center;justify-content:space-between}.popover-airlines .button-operator div[data-v-283a3918]{display:flex;width:100%;justify-content:space-between}.popover-airlines .button-operator div span[data-v-283a3918]{cursor:pointer;font-size:.75rem;line-height:1rem;color:var(--bkc-tw-gray-4)}.popover-airlines .button-operator div span[data-v-283a3918]:nth-child(1){color:var(--bkc-tw-brand-2)}.popover-airlines[data-v-283a3918] .bkc-checkbox__label{display:flex;width:115px}.popover-airport[data-v-283a3918] .is-checked .code-label{color:var(--bkc-tw-brand-2)}.popover-airport .button-operator[data-v-283a3918]{display:flex;height:30px;align-items:center;justify-content:space-between}.popover-airport .button-operator div[data-v-283a3918]{display:flex;width:100%;justify-content:space-between}.popover-airport .button-operator div span[data-v-283a3918]{cursor:pointer;font-size:.75rem;line-height:1rem;color:var(--bkc-tw-gray-4)}.popover-airport .button-operator div span[data-v-283a3918]:nth-child(1){color:var(--bkc-tw-brand-2)}.popover-airport[data-v-283a3918] .bkc-checkbox__label{display:flex;width:60px}.popover-airport[data-v-283a3918] .bkc-el-checkbox-group{display:flex;flex-direction:column}.popover-airlines .button-operator[data-v-768517f4]{display:flex;height:30px;align-items:center;justify-content:space-between}.popover-airlines .button-operator div[data-v-768517f4]{display:flex;width:100%;justify-content:space-between}.popover-airlines .button-operator div span[data-v-768517f4]{cursor:pointer;font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(140 140 140 / var(--tw-text-opacity))}.popover-airlines .button-operator div span[data-v-768517f4]:nth-child(1){color:var(--bkc-tw-brand-2)}.popover-airlines[data-v-768517f4] .bkc-el-checkbox__label{display:flex;width:190px}.popover-airport[data-v-768517f4] .is-checked .code-label{color:var(--bkc-tw-brand-2)}.popover-airport .button-operator[data-v-768517f4]{display:flex;height:30px;align-items:center;justify-content:space-between}.popover-airport .button-operator div[data-v-768517f4]{display:flex;width:100%;justify-content:space-between}.popover-airport .button-operator div span[data-v-768517f4]{cursor:pointer;font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(140 140 140 / var(--tw-text-opacity))}.popover-airport .button-operator div span[data-v-768517f4]:nth-child(1){color:var(--bkc-tw-brand-2)}.popover-airport[data-v-768517f4] .bkc-el-checkbox__label{display:flex}.operate[data-v-768517f4]{display:flex;height:24px;cursor:pointer;align-items:center;justify-content:center;white-space:nowrap;border-radius:.25rem;border-width:1px;border-color:var(--bkc-tw-brand-2);padding-left:.625rem;padding-right:.625rem;font-size:12px;color:var(--bkc-tw-brand-2)}.direct-flight[data-v-768517f4]{margin-right:20px}.direct-flight[data-v-768517f4] .bkc-el-checkbox__label{font-size:12px;--tw-text-opacity: 1;color:rgb(89 89 89 / var(--tw-text-opacity))}.sort-active[data-v-768517f4]{color:var(--bkc-tw-brand-2)}.sort-box[data-v-768517f4] .bkc-el-icon{height:7px}.disable-to-operate[data-v-768517f4]{border-width:1px;--tw-border-opacity: 1;border-color:rgb(217 217 217 / var(--tw-border-opacity));--tw-text-opacity: 1;color:rgb(217 217 217 / var(--tw-text-opacity))}.airport-popper-av.bkc-el-popover.bkc-el-popper{min-width:80px;padding:0 8px}.airport-popper-av.bkc-el-checkbox{margin:0!important;height:1.75rem!important}.airport-popper-av.bkc-el-checkbox__label{padding-left:.25rem!important}.transfer-proper .bkc-el-checkbox__label{padding-right:6px;padding-left:3px;font-size:.75rem;line-height:1rem;font-weight:400;line-height:1.25}.icon-drop[data-v-8a886d53]{font-size:16px;color:var(--bkc-tw-brand-2)!important}.booked-proper{width:auto!important;min-width:84px!important;padding:6px!important;display:inline-flex;flex-direction:column;align-items:center;justify-content:flex-start;gap:.125rem;border-radius:.125rem;border-width:1px;border-color:var(--bkc-tw-gray-6);background-color:var(--bkc-tw-gray-0);--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.airlines-info .split-label[data-v-01de63a9]{content:"";background-color:var(--bkc-color-gray-4)}.airlines-info[data-v-01de63a9]>:first-child{padding-left:0}.airlines-info[data-v-01de63a9]>:first-child:after{width:0}.flight-common-info-detail-popover{border-width:0px!important;background-color:var(--bkc-tw-gray-3)!important;padding:0 8px!important;font-size:.75rem!important;line-height:1rem!important;color:var(--bkc-tw-gray-0)!important}.flight-common-info-detail-popover .bkc-el-popper__arrow:before{border-width:0px!important;background-color:var(--bkc-tw-gray-3)!important}.flight-item-agent-stop-over-popover{border-width:0px!important;padding:0!important}.flight-item-agent-stop-over-popover .stop-content-company-data>span{margin-right:.25rem}.flight-item-agent-stop-over-popover .stop-content-origination{display:flex}.flight-item-agent-stop-over-popover .stop-content-origination>div:last-child{flex:4}.flight-item-agent-stop-over-popover .stop-content .stop-info{margin-top:14x;margin-bottom:14x;margin-left:0;margin-right:0;display:flex}.flight-item-agent-stop-over-popover .stop-content .stop-info-item{flex:4}.flight-item-agent-stop-over-popover .air-icon{height:1rem;width:1rem}.flight-item-agent-stop-over-popover.bkc-el-popper.is-light .bkc-el-popper__arrow:before{background-color:var(--bkc-tw-gray-3)}[data-v-c80f8073] .bkc-el-divider__text{padding:0}.item-right[data-v-c80f8073]{width:84px}@media screen and (min-width: 1440px){.item-right[data-v-c80f8073]{margin-left:15px;margin-right:20px}}.stop-info[data-v-c80f8073]{flex:4;display:flex;flex-direction:column;align-items:center}.no-stop-info[data-v-c80f8073]{flex:4;display:flex;align-items:center;flex-direction:column}[data-v-c80f8073] .bkc-el-divider{margin:0}[data-v-c80f8073] .bkc-el-date-editor{display:flex;height:20px;width:20px;justify-content:center;background-color:transparent;padding:0;color:var(--bkc-tw-brand-2);--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}[data-v-c80f8073] .bkc-el-date-editor:hover{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}[data-v-c80f8073] .bkc-el-date-editor .bkc-el-range-input,[data-v-c80f8073] .bkc-el-date-editor .bkc-el-range-separator,[data-v-c80f8073] .bkc-el-date-editor .bkc-el-range__close-icon{display:none}[data-v-c80f8073] .bkc-el-date-editor .bkc-el-input__icon{height:20px;width:20px;cursor:pointer;padding:0;color:var(--bkc-tw-brand-2)}.stop-divider[data-v-c80f8073]{display:flex;align-items:center;justify-content:space-between;width:100%}.divider[data-v-c80f8073]{height:1px;border-top:1px solid var(--bkc-color-gray-5);width:55px}.stop-point-circle[data-v-c80f8073]{width:6px;height:6px;border-radius:50%;background-color:var(--bkc-el-color-primary)}.stop-point-circle-white[data-v-c80f8073]{width:5px;height:5px;border-radius:50%;background-color:var(--bkc-color-gray-5)}.stop-point-circle-triangle[data-v-c80f8073]{width:0;height:0;border-top:4px solid transparent;border-bottom:4px solid transparent;border-left:6px solid var(--bkc-color-gray-5)}.min-h-box-20[data-v-c80f8073]{min-height:20px;line-height:20px}.min-h-box-22[data-v-c80f8073]{min-height:22px;line-height:22px}.md-box[data-v-c80f8073]{display:none}@media (max-width: 1439px){.need-resolution .md-hidden[data-v-c80f8073]{display:none}.need-resolution .flight-base-info[data-v-c80f8073]{margin-right:15px}}@media (min-width: 1920px){.need-resolution .md-box[data-v-c80f8073]{display:none}}@media (min-width: 1440px) and (max-width: 1919px){.need-resolution .md-box[data-v-c80f8073]{display:none}}@media (max-width: 1439px){.need-resolution .md-box[data-v-c80f8073]{display:flex}.need-resolution .md-item-right[data-v-c80f8073]{display:block}}@media (min-width: 1920px){.md-header-right-asr[data-v-c80f8073]{width:36px}}@media (min-width: 1440px) and (max-width: 1919px){.md-header-right-asr[data-v-c80f8073]{width:36px}}@media (max-width: 1439px){.md-header-right-asr[data-v-c80f8073]{display:flex;width:84px;flex-direction:row;justify-content:flex-end}}.multi-day-box .bkc-el-input__icon[data-v-c80f8073]{height:20px;width:84px;cursor:pointer;padding:0;color:var(--bkc-tw-brand-2)}.multi-day-box[data-v-c80f8073] .bkc-el-input__wrapper{flex-grow:0!important}.multi-day-box[data-v-c80f8073] .bkc-el-date-editor{width:84px!important;cursor:pointer!important;justify-content:flex-end!important;padding:0!important}.cabin-info .cabin-item[data-v-235747be]{display:inline-block;height:21px;width:30px;cursor:pointer;border-radius:.125rem;text-align:center;font-size:12px;line-height:22px;color:var(--bkc-tw-brand-2)}.cabin-info .cabin-item.cabin-item-active[data-v-235747be]{background-color:var(--bkc-tw-brand-2);--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.cabin-info .cabin-item-yellow[data-v-235747be]{color:var(--bkc-tw-yellow-1)}.cabin-info .cabin-item-yellow.cabin-item-active[data-v-235747be]{background-color:var(--bkc-tw-yellow-1);--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.cabin-info .cabin-item-gray[data-v-235747be]{color:var(--bkc-tw-gray-4)}.cabin-info .cabin-item-gray.cabin-item-active[data-v-235747be]{background-color:var(--bkc-tw-gray-4);--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity))}.cabin-info .not-selectable[data-v-235747be]{cursor:not-allowed}.cabin-info .cabin-item-sk[data-v-235747be]{width:30px;height:22px;background:var(--bkc-theme-2)}.cabin-info .cabin-item-sk+.cabin-item-sk[data-v-235747be]{margin:0 10px 10px 0}.cabin-info .cabin-item-gray[data-v-235747be]{color:var(--bkc-color-gray-4)}.cabin-info .enable-apply[data-v-235747be]{color:var(--bkc-el-color-warning);cursor:pointer}.cabin-info .disable[data-v-235747be]{color:var(--bkc-color-gray-4);cursor:not-allowed}.cabin-info .enable[data-v-235747be]{background:var(--bkc-theme-2);color:var(--bkc-el-color-primary);cursor:pointer;margin-top:10px}.cabin-info .seize-seat-popover-content .num-input[data-v-235747be]{margin-bottom:10px}.dynamic-scroller[data-v-f1d7d1e5]{display:flex;height:100%;flex-direction:column;overflow:hidden}.dynamic-scroller .infinite-list[data-v-f1d7d1e5]{flex:1 1 0%;overflow-y:scroll}.dynamic-scroller .pagination[data-v-f1d7d1e5]{display:flex;align-items:center;justify-content:center;font-size:.75rem;line-height:1rem}.dynamic-scroller .pagination .operate[data-v-f1d7d1e5]{display:inline-block;display:flex;height:24px;width:56px;cursor:pointer;align-items:center;justify-content:center;white-space:nowrap;border-radius:.25rem;padding-left:10px;padding-right:10px;font-size:.75rem;line-height:1rem;line-height:24px}.dynamic-scroller .pagination .disable-to-operate[data-v-f1d7d1e5]{border-width:1px;--tw-border-opacity: 1;border-color:rgb(217 217 217 / var(--tw-border-opacity));--tw-text-opacity: 1;color:rgb(217 217 217 / var(--tw-text-opacity))}.dynamic-scroller .pagination .able-to-operate[data-v-f1d7d1e5]{border-width:1px;border-color:var(--bkc-tw-brand-2);color:var(--bkc-tw-brand-2)}.dynamic-scroller .pagination .page-panel[data-v-f1d7d1e5]{margin-left:10px;margin-right:10px;--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity))}.dynamic-no-data[data-v-f1d7d1e5]{margin-top:3rem;text-align:center;font-size:1.125rem;line-height:1.75rem;--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity))}.header-av-query-form[data-v-92e010ae]{display:flex;width:100%;flex-wrap:wrap}.header-av-query-form[data-v-92e010ae] .bkc-el-form-item{margin-bottom:10px}.header-av-query-form[data-v-92e010ae] .bkc-el-input__wrapper{border-radius:.125rem;padding-left:6px;padding-right:6px;font-size:12px}.header-av-query-form[data-v-92e010ae] .bkc-el-input__inner{height:2rem;padding-left:0}.header-av-query-form[data-v-92e010ae] .bkc-el-input.is-disabled .bkc-el-input__wrapper{background-color:var(--bkc-tw-gray-7);--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.header-av-query-form[data-v-92e010ae] .bkc-el-form-item__content{height:100%}.header-av-query-form[data-v-92e010ae] .bkc-el-form-item__content .bkc-el-form-item__error{z-index:10}.header-av-query-form .service-book-btn[data-v-92e010ae],.header-av-query-form .service-book-btn[data-v-92e010ae]:hover{outline:none}.header-av-query-form .service-book-btn .iconfont[data-v-92e010ae]{font-size:16px;margin-right:3px}@media (min-width: 1920px){.agent-airport-item[data-v-92e010ae]{min-width:135px}}@media (min-width: 1440px) and (max-width: 1919px){.agent-airport-item[data-v-92e010ae]{min-width:53px}}@media (max-width: 1439px){.agent-airport-item[data-v-92e010ae]{min-width:134px}}.agent-airport-item[data-v-92e010ae] .bkc-el-input__wrapper{border-radius:.125rem}@media (min-width: 1920px){.airport-item[data-v-92e010ae]{max-width:200px}}@media (min-width: 1440px) and (max-width: 1919px){.airport-item[data-v-92e010ae]{max-width:135px}}@media (max-width: 1439px){.airport-item[data-v-92e010ae]{max-width:134px}}.fast-query-airport-item[data-v-92e010ae]{width:187px}.sort-text[data-v-92e010ae]{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));color:var(--bkc-tw-brand-2)}.departure-date[data-v-92e010ae]{margin-left:10px;margin-right:10px}.departure-date[data-v-92e010ae] .bkc-el-form-item__content{width:180px}@media (max-width: 1439px){.departure-date[data-v-92e010ae] .bkc-el-form-item__content{width:180px}}.departure-date[data-v-92e010ae] .bkc-el-input__wrapper{position:relative;height:2rem;border-width:1px;border-style:solid;border-color:transparent}.departure-date[data-v-92e010ae] .bkc-el-input__wrapper .bkc-el-input__prefix{position:absolute;right:0;color:var(--bkc-tw-brand-2)}.departure-date[data-v-92e010ae] .bkc-el-input__wrapper .bkc-el-input__suffix{position:absolute;right:22px}.departure-date[data-v-92e010ae] .bkc-el-form-item.is-error .bkc-el-input__wrapper:hover{--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.round-trip-date[data-v-92e010ae] .bkc-el-form-item__content{width:210px}@media (max-width: 1439px){.round-trip-date[data-v-92e010ae] .bkc-el-form-item__content{width:210px}}.airlines-item-box[data-v-92e010ae]{display:flex;height:100%;align-items:center}.airlines-item-box .airlines-item[data-v-92e010ae]{height:34px!important;cursor:pointer!important;border-top-right-radius:0!important;border-bottom-right-radius:0!important;padding-top:0!important;padding-bottom:0!important}.airlines-input[data-v-92e010ae]{display:flex;width:44px;align-items:center}.airlines-input[data-v-92e010ae] .bkc-el-input__wrapper{width:44px;border-top-left-radius:0;border-bottom-left-radius:0}.small-width[data-v-92e010ae],.flightNumber-input[data-v-92e010ae] .bkc-el-input__wrapper{width:62px}.small-width[data-v-92e010ae]:first-child,.flightNumber-input[data-v-92e010ae] .bkc-el-input__wrapper:first-child{margin-right:10px}.airlines-input-item[data-v-92e010ae] .bkc-el-form-item__error{left:26px}.transit-terminal[data-v-92e010ae] .bkc-el-form-item__content{width:48px}.carrier-only-direct[data-v-92e010ae]{margin-left:16px;margin-right:16px;display:flex;flex-direction:column}.carrier-only-direct[data-v-92e010ae] .bkc-el-checkbox{margin-right:0;height:14px}.carrier-only-direct[data-v-92e010ae] .bkc-el-checkbox:first-child{margin-bottom:4px}.carrier-only-direct[data-v-92e010ae] .bkc-el-checkbox__label{padding-left:4px;font-size:12px}.lowest-price-box[data-v-92e010ae] .bkc-el-checkbox{margin-right:0;height:14px}.lowest-price-box[data-v-92e010ae] .bkc-el-checkbox:first-child{margin-bottom:4px}.lowest-price-box[data-v-92e010ae] .bkc-el-checkbox__label{padding-left:4px;font-size:12px}.radio-selected .airlines-item[data-v-92e010ae]{background-color:var(--bkc-tw-brand-7);--tw-shadow: inset 0 0 0 1px var(--bkc-tw-brand-2);--tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.radio-cancel .bkc-el-input__wrapper[data-v-92e010ae]:hover{--tw-shadow: inset 0 0 0 1px var(--bkc-tw-gray-6);--tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.query-btn[data-v-92e010ae]{height:2rem;width:3rem;padding-left:.625rem;padding-right:.625rem;padding-top:5px;padding-bottom:5px;font-size:.875rem;line-height:1.25rem}.add-ss-flight[data-v-92e010ae]{display:flex;min-height:56px;align-items:center}.add-ss-flight .flight-from[data-v-92e010ae]{margin-bottom:8px;display:flex;align-items:center}.add-ss-flight .open-item[data-v-92e010ae]{margin-right:10px;height:1.25rem}.add-ss-flight .open-item[data-v-92e010ae] .bkc-el-form-item__label{height:1.25rem;padding-right:.5rem;font-size:.75rem;line-height:1.25rem;color:var(--bkc-color-gray-3)}.add-ss-flight .open-item[data-v-92e010ae] .bkc-el-switch{height:.5rem;width:46px}.add-ss-flight .open-item[data-v-92e010ae] .bkc-el-switch .bkc-el-switch__core{width:100%;background-color:var(--bkc-tw-gray-5)}.add-ss-flight .open-item[data-v-92e010ae] .bkc-el-switch .bkc-el-switch__core .bkc-el-switch__inner{padding-right:6px;padding-left:18px}.add-ss-flight .open-item[data-v-92e010ae] .bkc-el-switch.is-checked .bkc-el-switch__core{background-color:var(--bkc-el-color-primary)}.add-ss-flight .open-item[data-v-92e010ae] .bkc-el-switch.is-checked .bkc-el-switch__core .bkc-el-switch__inner{padding-right:18px;padding-left:6px}.add-ss-flight .header-av-query-form[data-v-92e010ae]{width:inherit}.add-ss-flight .header-av-query-form[data-v-92e010ae] .bkc-el-form-item{position:relative;margin-bottom:.875rem}.add-ss-flight .header-av-query-form[data-v-92e010ae] .bkc-el-form-item__label{position:absolute;left:-7px}.add-ss-flight .header-av-query-form[data-v-92e010ae] .bkc-el-form-item__label:before{margin-right:1px!important;font-size:.75rem;line-height:1rem;line-height:32px}@media (min-width: 1440px) and (max-width: 1919px){.add-ss-flight .header-av-query-form .agent-airport-item[data-v-92e010ae]{width:200px}}@media (max-width: 1439px){.add-ss-flight .header-av-query-form .agent-airport-item[data-v-92e010ae]{width:142.5px}}.add-ss-flight .header-av-query-form .airline-item[data-v-92e010ae]{margin-right:.625rem}.add-ss-flight .header-av-query-form .airlines-item[data-v-92e010ae]{width:26px;background-color:var(--bkc-tw-gray-0)}.add-ss-flight .header-av-query-form .airlines-item[data-v-92e010ae]:last-child{border-right-width:1px}.add-ss-flight .search-btn[data-v-92e010ae] .bkc-el-form-item__content{justify-content:flex-end}.add-ss-flight .search-btn[data-v-92e010ae] .bkc-el-form-item__content .bkc-el-button{height:32px}.add-ss-flight[data-v-92e010ae] .bkc-el-form-item__error{z-index:10}.add-ss-flight .currentError[data-v-92e010ae] .bkc-el-form-item__error{z-index:20}.header-av-query-form[data-v-384be854]{display:flex;width:100%;flex-wrap:wrap}.header-av-query-form[data-v-384be854] .bkc-el-form-item{margin-bottom:10px}.header-av-query-form[data-v-384be854] .bkc-el-input__wrapper{border-radius:.125rem;padding-left:6px;padding-right:6px;font-size:12px}.header-av-query-form[data-v-384be854] .bkc-el-input__inner{height:2rem;padding-left:0}.header-av-query-form[data-v-384be854] .bkc-el-input.is-disabled .bkc-el-input__wrapper{background-color:var(--bkc-tw-gray-7);--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.header-av-query-form[data-v-384be854] .bkc-el-form-item__content{height:100%}.header-av-query-form[data-v-384be854] .bkc-el-form-item__content .bkc-el-form-item__error{z-index:10}.header-av-query-form .service-book-btn[data-v-384be854],.header-av-query-form .service-book-btn[data-v-384be854]:hover{outline:none}.header-av-query-form .service-book-btn .iconfont[data-v-384be854]{font-size:16px;margin-right:3px}@media (min-width: 1920px){.agent-airport-item[data-v-384be854]{min-width:135px}}@media (min-width: 1440px) and (max-width: 1919px){.agent-airport-item[data-v-384be854]{min-width:53px}}@media (max-width: 1439px){.agent-airport-item[data-v-384be854]{min-width:134px}}.agent-airport-item[data-v-384be854] .bkc-el-input__wrapper{border-radius:.125rem}@media (min-width: 1920px){.airport-item[data-v-384be854]{max-width:200px}}@media (min-width: 1440px) and (max-width: 1919px){.airport-item[data-v-384be854]{max-width:135px}}@media (max-width: 1439px){.airport-item[data-v-384be854]{max-width:134px}}.fast-query-airport-item[data-v-384be854]{width:187px}.sort-text[data-v-384be854]{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));color:var(--bkc-tw-brand-2)}.departure-date[data-v-384be854]{margin-left:10px;margin-right:10px}.departure-date[data-v-384be854] .bkc-el-form-item__content{width:180px}@media (max-width: 1439px){.departure-date[data-v-384be854] .bkc-el-form-item__content{width:180px}}.departure-date[data-v-384be854] .bkc-el-input__wrapper{position:relative;height:2rem;border-width:1px;border-style:solid;border-color:transparent}.departure-date[data-v-384be854] .bkc-el-input__wrapper .bkc-el-input__prefix{position:absolute;right:0;color:var(--bkc-tw-brand-2)}.departure-date[data-v-384be854] .bkc-el-input__wrapper .bkc-el-input__suffix{position:absolute;right:22px}.departure-date[data-v-384be854] .bkc-el-form-item.is-error .bkc-el-input__wrapper:hover{--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.round-trip-date[data-v-384be854] .bkc-el-form-item__content{width:210px}@media (max-width: 1439px){.round-trip-date[data-v-384be854] .bkc-el-form-item__content{width:210px}}.airlines-item-box[data-v-384be854]{display:flex;height:100%;align-items:center}.airlines-item-box .airlines-item[data-v-384be854]{height:34px!important;cursor:pointer!important;border-top-right-radius:0!important;border-bottom-right-radius:0!important;padding-top:0!important;padding-bottom:0!important}.airlines-input[data-v-384be854]{display:flex;width:44px;align-items:center}.airlines-input[data-v-384be854] .bkc-el-input__wrapper{width:44px;border-top-left-radius:0;border-bottom-left-radius:0}.small-width[data-v-384be854],.flightNumber-input[data-v-384be854] .bkc-el-input__wrapper{width:62px}.small-width[data-v-384be854]:first-child,.flightNumber-input[data-v-384be854] .bkc-el-input__wrapper:first-child{margin-right:10px}.airlines-input-item[data-v-384be854] .bkc-el-form-item__error{left:26px}.transit-terminal[data-v-384be854] .bkc-el-form-item__content{width:48px}.carrier-only-direct[data-v-384be854]{margin-left:16px;margin-right:16px;display:flex;flex-direction:column}.carrier-only-direct[data-v-384be854] .bkc-el-checkbox{margin-right:0;height:14px}.carrier-only-direct[data-v-384be854] .bkc-el-checkbox:first-child{margin-bottom:4px}.carrier-only-direct[data-v-384be854] .bkc-el-checkbox__label{padding-left:4px;font-size:12px}.lowest-price-box[data-v-384be854] .bkc-el-checkbox{margin-right:0;height:14px}.lowest-price-box[data-v-384be854] .bkc-el-checkbox:first-child{margin-bottom:4px}.lowest-price-box[data-v-384be854] .bkc-el-checkbox__label{padding-left:4px;font-size:12px}.radio-selected .airlines-item[data-v-384be854]{background-color:var(--bkc-tw-brand-7);--tw-shadow: inset 0 0 0 1px var(--bkc-tw-brand-2);--tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.radio-cancel .bkc-el-input__wrapper[data-v-384be854]:hover{--tw-shadow: inset 0 0 0 1px var(--bkc-tw-gray-6);--tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.query-btn[data-v-384be854]{height:2rem;width:3rem;padding-left:.625rem;padding-right:.625rem;padding-top:5px;padding-bottom:5px;font-size:.875rem;line-height:1.25rem}.add-group-flight[data-v-384be854] .bkc-el-form{display:flex}.add-group-flight[data-v-384be854] .bkc-el-form .revert-icon{margin-top:6px;margin-right:2px;height:14px;width:14px;cursor:pointer}.add-group-flight[data-v-384be854] .bkc-el-form .agent-airport-item{margin-right:2px}.add-group-flight[data-v-384be854] .bkc-el-form .agent-airport-item .bkc-el-form-item__label{padding-right:1px}.add-group-flight[data-v-384be854] .bkc-el-form .agent-airport-item .bkc-el-form-item__label:before{margin-right:0}.add-group-flight[data-v-384be854] .bkc-el-form .agent-airport-item .bkc-el-input__inner{padding-left:0}.add-group-flight[data-v-384be854] .bkc-el-form .departure-date{margin-left:0;margin-right:0}.flight-op-right[data-v-d622a442]{display:flex;height:100%;width:100%;flex-direction:column;overflow:hidden;padding:10px 4px 10px 10px}.flight-op-right .tab-container[data-v-d622a442]{display:inline-flex;height:1.5rem;cursor:pointer;align-items:center;justify-content:center;gap:.625rem;border-width:1px;padding:.125rem .375rem;font-weight:700;color:var(--bkc-tw-brand-2)}.flight-op-right .tab-container[data-v-d622a442]:hover{border-color:var(--bkc-tw-brand-2);background-color:var(--bkc-tw-brand-4);font-weight:400}.flight-op-right .tab-container .tab-container-box[data-v-d622a442]{text-align:center;font-size:.75rem;line-height:1rem;font-weight:400;line-height:1.25;color:var(--bkc-tw-gray-2)}.flight-op-right .active-container[data-v-d622a442]{border-color:var(--bkc-tw-brand-2);background-color:var(--bkc-tw-brand-7)}.flight-op-right .active-container .tab-container-box[data-v-d622a442]{color:var(--bkc-tw-brand-2)}.flight-op-right .active-container .active-box[data-v-d622a442]:first-line{color:var(--bkc-tw-brand-2)}.flight-op-right .import-flight-from[data-v-d622a442]{margin-bottom:14px;display:flex}.flight-op-right .import-flight-from[data-v-d622a442] .bkc-el-form-item{margin-right:.625rem!important;margin-bottom:0!important}.flight-op-right .import-flight-from[data-v-d622a442] .is-required:not(.is-no-asterisk).asterisk-left>.bkc-el-form-item__label:before{content:""}.flight-op-right .import-flight-from .import-flight-input[data-v-d622a442] .bkc-el-textarea__inner{max-height:92px!important;min-height:32px!important;width:400px!important;border-radius:.125rem!important;padding-top:7px!important}.flight-op-right .ss-box[data-v-d622a442]{display:flex;height:100%}.flight-op-right .ss-box[data-v-d622a442] .bkc-el-scrollbar{flex:1 1 0%}.fast-query-flight-op-right[data-v-d622a442]{display:flex;height:100%;width:100%;flex-direction:column;overflow:hidden}.warning-p-msg .bkc-el-message-box__message{font-size:1.125rem;line-height:1.75rem;font-weight:400;color:var(--bkc-tw-gray-1)}
