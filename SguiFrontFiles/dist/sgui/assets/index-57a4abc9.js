import{L as D,M as Q,e8 as le,q as M,dM as K,O as X,m as Z,v as U,r as T,ac as O,aG as ee,eo as oe,x as re,B as ie,D as ce,A as $,C as ge,_ as ue,iI as Ne,iJ as Te,w as F,o as de,dQ as Ce,G as d,H as I,ht as Pe,hu as we,a1 as Se,V,a2 as Ee,W as $e,F as j,h5 as xe,ep as be,ef as ae,dP as Be,ea as ke,Y as Oe,iK as se,b0 as Re,ar as ze,a5 as Ae,a6 as Me,J as Fe,K as Le,Z as Ie}from"./index-9381ab2b.js";import{c as k}from"./strings-820bea19.js";import{u as Ve}from"./index-729d485f.js";const q=Symbol("tabsRootContextKey"),De=D({tabs:{type:Q(Array),default:()=>le([])}}),ve="ElTabBar",Ke=M({name:ve}),Ue=M({...Ke,props:De,setup(e,{expose:o}){const p=e,R=K(),c=X(q);c||Z(ve,"<el-tabs><el-tab-bar /></el-tabs>");const s=U("tabs"),b=T(),x=T(),u=()=>{let v=0,f=0;const r=["top","bottom"].includes(c.props.tabPosition)?"width":"height",n=r==="width"?"x":"y",B=n==="x"?"left":"top";return p.tabs.every(S=>{var t,P;const _=(P=(t=R.parent)==null?void 0:t.refs)==null?void 0:P[`tab-${S.uid}`];if(!_)return!1;if(!S.active)return!0;v=_[`offset${k(B)}`],f=_[`client${k(r)}`];const w=window.getComputedStyle(_);return r==="width"&&(p.tabs.length>1&&(f-=Number.parseFloat(w.paddingLeft)+Number.parseFloat(w.paddingRight)),v+=Number.parseFloat(w.paddingLeft)),!1}),{[r]:`${f}px`,transform:`translate${k(n)}(${v}px)`}},h=()=>x.value=u();return O(()=>p.tabs,async()=>{await ee(),h()},{immediate:!0}),oe(b,()=>h()),o({ref:b,update:h}),(v,f)=>(re(),ie("div",{ref_key:"barRef",ref:b,class:ce([$(s).e("active-bar"),$(s).is($(c).props.tabPosition)]),style:ge(x.value)},null,6))}});var qe=ue(Ue,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-bar.vue"]]);const He=D({panes:{type:Q(Array),default:()=>le([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),We={tabClick:(e,o,p)=>p instanceof Event,tabRemove:(e,o)=>o instanceof Event},ne="ElTabNav",Ge=M({name:ne,props:He,emits:We,setup(e,{expose:o,emit:p}){const R=K(),c=X(q);c||Z(ne,"<el-tabs><tab-nav /></el-tabs>");const s=U("tabs"),b=Ne(),x=Te(),u=T(),h=T(),v=T(),f=T(),r=T(!1),n=T(0),B=T(!1),S=T(!0),t=F(()=>["top","bottom"].includes(c.props.tabPosition)?"width":"height"),P=F(()=>({transform:`translate${t.value==="width"?"X":"Y"}(-${n.value}px)`})),_=()=>{if(!u.value)return;const l=u.value[`offset${k(t.value)}`],i=n.value;if(!i)return;const a=i>l?i-l:0;n.value=a},w=()=>{if(!u.value||!h.value)return;const l=h.value[`offset${k(t.value)}`],i=u.value[`offset${k(t.value)}`],a=n.value;if(l-a<=i)return;const g=l-a>i*2?a+i:l-i;n.value=g},z=async()=>{const l=h.value;if(!r.value||!v.value||!u.value||!l)return;await ee();const i=v.value.querySelector(".is-active");if(!i)return;const a=u.value,g=["top","bottom"].includes(c.props.tabPosition),N=i.getBoundingClientRect(),y=a.getBoundingClientRect(),E=g?l.offsetWidth-y.width:l.offsetHeight-y.height,C=n.value;let m=C;g?(N.left<y.left&&(m=C-(y.left-N.left)),N.right>y.right&&(m=C+N.right-y.right)):(N.top<y.top&&(m=C-(y.top-N.top)),N.bottom>y.bottom&&(m=C+(N.bottom-y.bottom))),m=Math.max(m,0),n.value=Math.min(m,E)},L=()=>{var l;if(!h.value||!u.value)return;e.stretch&&((l=f.value)==null||l.update());const i=h.value[`offset${k(t.value)}`],a=u.value[`offset${k(t.value)}`],g=n.value;a<i?(r.value=r.value||{},r.value.prev=g,r.value.next=g+a<i,i-g<a&&(n.value=i-a)):(r.value=!1,g>0&&(n.value=0))},pe=l=>{const i=l.code,{up:a,down:g,left:N,right:y}=V;if(![a,g,N,y].includes(i))return;const E=Array.from(l.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)")),C=E.indexOf(l.target);let m;i===N||i===a?C===0?m=E.length-1:m=C-1:C<E.length-1?m=C+1:m=0,E[m].focus({preventScroll:!0}),E[m].click(),te()},te=()=>{S.value&&(B.value=!0)},H=()=>B.value=!1;return O(b,l=>{l==="hidden"?S.value=!1:l==="visible"&&setTimeout(()=>S.value=!0,50)}),O(x,l=>{l?setTimeout(()=>S.value=!0,50):S.value=!1}),oe(v,L),de(()=>setTimeout(()=>z(),0)),Ce(()=>L()),o({scrollToActiveTab:z,removeFocus:H}),O(()=>e.panes,()=>R.update(),{flush:"post",deep:!0}),()=>{const l=r.value?[d("span",{class:[s.e("nav-prev"),s.is("disabled",!r.value.prev)],onClick:_},[d(I,null,{default:()=>[d(Pe,null,null)]})]),d("span",{class:[s.e("nav-next"),s.is("disabled",!r.value.next)],onClick:w},[d(I,null,{default:()=>[d(we,null,null)]})])]:null,i=e.panes.map((a,g)=>{var N,y,E,C;const m=a.uid,W=a.props.disabled,G=(y=(N=a.props.name)!=null?N:a.index)!=null?y:`${g}`,J=!W&&(a.isClosable||e.editable);a.index=`${g}`;const he=J?d(I,{class:"is-icon-close",onClick:A=>p("tabRemove",a,A)},{default:()=>[d(Se,null,null)]}):null,ye=((C=(E=a.slots).label)==null?void 0:C.call(E))||a.props.label,_e=!W&&a.active?0:-1;return d("div",{ref:`tab-${m}`,class:[s.e("item"),s.is(c.props.tabPosition),s.is("active",a.active),s.is("disabled",W),s.is("closable",J),s.is("focus",B.value)],id:`tab-${G}`,key:`tab-${m}`,"aria-controls":`pane-${G}`,role:"tab","aria-selected":a.active,tabindex:_e,onFocus:()=>te(),onBlur:()=>H(),onClick:A=>{H(),p("tabClick",a,G,A)},onKeydown:A=>{J&&(A.code===V.delete||A.code===V.backspace)&&p("tabRemove",a,A)}},[ye,he])});return d("div",{ref:v,class:[s.e("nav-wrap"),s.is("scrollable",!!r.value),s.is(c.props.tabPosition)]},[l,d("div",{class:s.e("nav-scroll"),ref:u},[d("div",{class:[s.e("nav"),s.is(c.props.tabPosition),s.is("stretch",e.stretch&&["top","bottom"].includes(c.props.tabPosition))],ref:h,style:P.value,role:"tablist",onKeydown:pe},[e.type?null:d(qe,{ref:f,tabs:[...e.panes]},null),i])])])}}}),Je=D({type:{type:String,values:["card","border-card",""],default:""},activeName:{type:[String,Number]},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:Q(Function),default:()=>!0},stretch:Boolean}),Y=e=>Be(e)||ke(e),Ye={[be]:e=>Y(e),tabClick:(e,o)=>o instanceof Event,tabChange:e=>Y(e),edit:(e,o)=>["remove","add"].includes(o),tabRemove:e=>Y(e),tabAdd:()=>!0},je=M({name:"ElTabs",props:Je,emits:Ye,setup(e,{emit:o,slots:p,expose:R}){var c,s;const b=U("tabs"),{children:x,addChild:u,removeChild:h}=Ve(K(),"ElTabPane"),v=T(),f=T((s=(c=e.modelValue)!=null?c:e.activeName)!=null?s:"0"),r=async(t,P=!1)=>{var _,w,z;if(!(f.value===t||ae(t)))try{await((_=e.beforeLeave)==null?void 0:_.call(e,t,f.value))!==!1&&(f.value=t,P&&(o(be,t),o("tabChange",t)),(z=(w=v.value)==null?void 0:w.removeFocus)==null||z.call(w))}catch{}},n=(t,P,_)=>{t.props.disabled||(r(P,!0),o("tabClick",t,_))},B=(t,P)=>{t.props.disabled||ae(t.props.name)||(P.stopPropagation(),o("edit",t.props.name,"remove"),o("tabRemove",t.props.name))},S=()=>{o("edit",void 0,"add"),o("tabAdd")};return Ee({from:'"activeName"',replacement:'"model-value" or "v-model"',scope:"ElTabs",version:"2.3.0",ref:"https://element-plus.org/en-US/component/tabs.html#attributes",type:"Attribute"},F(()=>!!e.activeName)),O(()=>e.activeName,t=>r(t)),O(()=>e.modelValue,t=>r(t)),O(f,async()=>{var t;await ee(),(t=v.value)==null||t.scrollToActiveTab()}),$e(q,{props:e,currentName:f,registerPane:u,unregisterPane:h}),R({currentName:f}),()=>{const t=p.addIcon,P=e.editable||e.addable?d("span",{class:b.e("new-tab"),tabindex:"0",onClick:S,onKeydown:z=>{z.code===V.enter&&S()}},[t?j(p,"addIcon"):d(I,{class:b.is("icon-plus")},{default:()=>[d(xe,null,null)]})]):null,_=d("div",{class:[b.e("header"),b.is(e.tabPosition)]},[P,d(Ge,{ref:v,currentName:f.value,editable:e.editable,type:e.type,panes:x.value,stretch:e.stretch,onTabClick:n,onTabRemove:B},null)]),w=d("div",{class:b.e("content")},[j(p,"default")]);return d("div",{class:[b.b(),b.m(e.tabPosition),{[b.m("card")]:e.type==="card",[b.m("border-card")]:e.type==="border-card"}]},[...e.tabPosition!=="bottom"?[_,w]:[w,_]])}}}),Qe=D({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),Xe=["id","aria-hidden","aria-labelledby"],fe="ElTabPane",Ze=M({name:fe}),et=M({...Ze,props:Qe,setup(e){const o=e,p=K(),R=Oe(),c=X(q);c||Z(fe,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const s=U("tab-pane"),b=T(),x=F(()=>o.closable||c.props.closable),u=se(()=>{var n;return c.currentName.value===((n=o.name)!=null?n:b.value)}),h=T(u.value),v=F(()=>{var n;return(n=o.name)!=null?n:b.value}),f=se(()=>!o.lazy||h.value||u.value);O(u,n=>{n&&(h.value=!0)});const r=Re({uid:p.uid,slots:R,props:o,paneName:v,active:u,index:b,isClosable:x});return de(()=>{c.registerPane(r)}),ze(()=>{c.unregisterPane(r.uid)}),(n,B)=>$(f)?Ae((re(),ie("div",{key:0,id:`pane-${$(v)}`,class:ce($(s).b()),role:"tabpanel","aria-hidden":!$(u),"aria-labelledby":`tab-${$(v)}`},[j(n.$slots,"default")],10,Xe)),[[Me,$(u)]]):Fe("v-if",!0)}});var me=ue(et,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-pane.vue"]]);const nt=Le(je,{TabPane:me}),lt=Ie(me);export{lt as E,nt as a};
