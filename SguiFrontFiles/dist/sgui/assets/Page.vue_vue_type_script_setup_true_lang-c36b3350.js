import{E as u}from"./index-716e52e2.js";import{q as c,r as o,ac as m,x as h,B as d,G as v,X as z,A as f}from"./index-9381ab2b.js";const C={style:{display:"flex","flex-direction":"row-reverse","margin-top":"30px","margin-bottom":"20px"}},_=c({__name:"Page",props:{total:{default:0},currentPage:{default:0},pageSize:{default:10},pageSizes:{}},emits:["handleChange"],setup(l,{expose:p,emit:g}){const n=l,r=g,a=o(n.currentPage),t=o(n.pageSize),s=e=>{t.value=e,a.value=1,r("handleChange",a.value,t.value)},i=e=>{a.value=e,r("handleChange",a.value,t.value)};return m(n,e=>{a.value=e.currentPage}),p({handleSizeChange:s}),(e,S)=>(h(),d("div",C,[v(f(u),z({style:{color:"#595959"}},e.$attrs,{"current-page":a.value,"page-size":t.value,"page-sizes":e.pageSizes||[10,20,30,40,50,100],total:e.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:s,onCurrentChange:i}),null,16,["current-page","page-size","page-sizes","total"])]))}});export{_};
