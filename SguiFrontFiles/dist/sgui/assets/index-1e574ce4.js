import{ao as r,b2 as n,a_ as o,ap as a,gP as t}from"./index-9381ab2b.js";const i=(s,e)=>r(`${n}/query/pnr/detail`,{headers:{gid:e}}).post(s).json(),c=(s,e)=>r(`${n}/query/pnr/foreignRT`,{headers:{gid:e}}).post(s).json(),d=(s,e)=>r(`${n}/crs/pnr/query/flight`,{headers:{gid:e}}).post(s).json(),u=(s,e)=>r(`${n}/crs/pnr/query/time`,{headers:{gid:e}}).post(s).json(),h=(s,e)=>r(`${n}/crs/pnr/query/name`,{headers:{gid:e}}).post(s).json(),A=s=>r(`${n}/passenger/config/domesticairline`,{headers:{gid:s}}).get().json(),y=(s,e)=>r(`${n}/passenger/splitPnrByPassenger`,{headers:{gid:e}}).post(s).json(),$=(s,e)=>r(`${n}/crs/pnr/iet?airline=${s.airline}&&pnrNo=${s.pnrNo}`,{headers:{gid:e}}).get().json(),l=(s,e)=>r(`${n}/crs/other/otherDelete`,{headers:{gid:e}}).post(s).json(),j=(s,e)=>r(`${n}/passenger/removname`,{headers:{gid:e}}).post(s).json(),g=(s,e)=>r(`${n}/query/pnr/rtl`,{headers:{gid:e}}).post(s).json(),q=(s,e)=>r(`${n}/crs/pnr/xePnr`,{headers:{gid:e}}).post(s).json(),P=(s,e)=>r(`${n}/crs/pnr/strongRefresh`,{headers:{gid:e}}).post(s).json(),m=(s,e)=>r(`${n}/crs/rrt/convert`,{headers:{gid:e}},{ignoreError:!0,originalValue:!0}).post(s).json(),f=(s,e)=>r(`${n}/crs/rrt/displayPNR`,{headers:{gid:e}}).post(s).json(),R=(s,e)=>r(`${n}/crs/query/pnr/history?pnrNo=${s.pnrNo}`,{headers:{gid:e}}).get().json(),B=(s,e)=>r(`${n}/pnr/deleteExpireSegment`,{headers:{gid:e}}).post(s).json(),E=(s,e)=>r(`${o}/book/pnr`,{headers:{gid:e}}).post(s).json(),S=(s,e)=>r(`${o}/book/groupPnr`,{headers:{gid:e}}).post(s).json(),_=(s,e)=>r(`${n}/crs/issue/retry`,{headers:{gid:e}}).post(s).json(),v=s=>r(`${n}/crs/pnr/queryDftBalance`,{headers:{gid:s}}).post().json(),D=(s,e)=>r(`${n}/crs/pnr/issue`,{headers:{gid:e}}).post(s).json(),I=(s,e)=>r(`${n}/passenger/seat/querySeatMap`,{headers:{gid:e}}).post(s).json(),x=(s,e)=>r(`${a}/crs/rval/rt`,{headers:{gid:e}}).post(s).json(),N=(s,e)=>r(`${n}/pnr/update`,{headers:{gid:e}}).post(s).json(),T=(s,e)=>r(`${n}/crs/issue/structured/preview`,{headers:{gid:e}}).post(s).json(),b=(s,e)=>r(`${n}/crs/pnr/auth/pull`,{headers:{gid:e}}).post(s).json(),k=(s,e)=>r(`${n}/crs/pnr/auth/push`,{headers:{gid:e}}).post(s).json(),L=(s,e)=>r(`${n}/crs/pnr/insuranceIPay`,{headers:{gid:e}}).post(s).json(),C=(s,e)=>r(`${n}/pnr/seat/book`,{headers:{gid:e}}).post(s).json(),V=(s,e)=>r(`${n}/sms/send`,{headers:{gid:e}}).post(s).json(),w=(s,e)=>r(`${n}/departureInfo/getDepartureInfo`,{headers:{gid:e}}).post({crsPnr:s}).json(),M=(s,e)=>r(`${t}/passenger/queryEmployees`,{headers:{gid:e}}).post(s).json(),O=(s,e)=>r(`${t}/employee/queryDetail`,{headers:{gid:e}}).post(s).json();export{g as A,B,T as C,x as D,V as E,m as F,f as G,D as I,i as a,c as b,h as c,u as d,d as e,A as f,w as g,P as h,j as i,_ as j,E as k,S as l,M as m,O as n,C as o,I as p,v as q,l as r,y as s,$ as t,N as u,k as v,b as w,q as x,L as y,R as z};
