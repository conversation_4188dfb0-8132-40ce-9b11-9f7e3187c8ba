import{L as Q,dR as ae,q as O,N as te,O as j,hI as ne,w as E,h_ as re,x as A,B as U,P as R,F as D,D as y,A as e,Q as ie,G as M,z as k,y as q,b9 as de,H as ue,J as K,C as W,_ as X,h$ as ce,M as G,ep as x,hF as fe,dM as me,hr as pe,hm as J,r as I,i0 as ve,a3 as ye,i1 as ge,ac as Y,aG as Ce,o as be,i2 as he,i3 as Z,eb as ke,Y as De,a2 as H,v as Ie,W as Ee,a5 as Te,$ as Ae,a0 as Be,X as Fe,dD as Se,a6 as $e,T as we,a7 as Pe,i4 as Le,K as Re}from"./index-9381ab2b.js";import{c as Me}from"./refs-d6b4edba.js";import{i as Oe}from"./isUndefined-aa0326a0.js";const ee=Symbol("dialogInjectionKey"),oe=Q({center:Boolean,alignCenter:Boolean,closeIcon:{type:ae},customClass:{type:String,default:""},draggable:Boolean,fullscreen:Boolean,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),Ne={close:()=>!0},ze=["aria-level"],Ve=["aria-label"],_e=["id"],Ue=O({name:"ElDialogContent"}),qe=O({...Ue,props:oe,emits:Ne,setup(o){const t=o,{t:u}=te(),{Close:S}=ce,{dialogRef:n,headerRef:c,bodyId:T,ns:a,style:g}=j(ee),{focusTrapRef:r}=j(ne),f=E(()=>[a.b(),a.is("fullscreen",t.fullscreen),a.is("draggable",t.draggable),a.is("align-center",t.alignCenter),{[a.m("center")]:t.center},t.customClass]),m=Me(r,n),C=E(()=>t.draggable);return re(n,c,C),(s,d)=>(A(),U("div",{ref:e(m),class:y(e(f)),style:W(e(g)),tabindex:"-1"},[R("header",{ref_key:"headerRef",ref:c,class:y(e(a).e("header"))},[D(s.$slots,"header",{},()=>[R("span",{role:"heading","aria-level":s.ariaLevel,class:y(e(a).e("title"))},ie(s.title),11,ze)]),s.showClose?(A(),U("button",{key:0,"aria-label":e(u)("el.dialog.close"),class:y(e(a).e("headerbtn")),type:"button",onClick:d[0]||(d[0]=$=>s.$emit("close"))},[M(e(ue),{class:y(e(a).e("close"))},{default:k(()=>[(A(),q(de(s.closeIcon||e(S))))]),_:1},8,["class"])],10,Ve)):K("v-if",!0)],2),R("div",{id:e(T),class:y(e(a).e("body"))},[D(s.$slots,"default")],10,_e),s.$slots.footer?(A(),U("footer",{key:0,class:y(e(a).e("footer"))},[D(s.$slots,"footer")],2)):K("v-if",!0)],6))}});var Ke=X(qe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog-content.vue"]]);const je=Q({...oe,appendToBody:Boolean,appendTo:{type:G(String),default:"body"},beforeClose:{type:G(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:{type:Boolean,default:!1},headerAriaLevel:{type:String,default:"2"}}),Ge={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[x]:o=>fe(o),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},Je=(o,t)=>{var u;const n=me().emit,{nextZIndex:c}=pe();let T="";const a=J(),g=J(),r=I(!1),f=I(!1),m=I(!1),C=I((u=o.zIndex)!=null?u:c());let s,d;const $=ve("namespace",he),N=E(()=>{const i={},h=`--${$.value}-dialog`;return o.fullscreen||(o.top&&(i[`${h}-margin-top`]=o.top),o.width&&(i[`${h}-width`]=ye(o.width))),i}),z=E(()=>o.alignCenter?{display:"flex"}:{});function w(){n("opened")}function V(){n("closed"),n(x,!1),o.destroyOnClose&&(m.value=!1)}function _(){n("close")}function P(){d==null||d(),s==null||s(),o.openDelay&&o.openDelay>0?{stop:s}=Z(()=>L(),o.openDelay):L()}function B(){s==null||s(),d==null||d(),o.closeDelay&&o.closeDelay>0?{stop:d}=Z(()=>l(),o.closeDelay):l()}function F(){function i(h){h||(f.value=!0,r.value=!1)}o.beforeClose?o.beforeClose(i):B()}function p(){o.closeOnClickModal&&F()}function L(){ke&&(r.value=!0)}function l(){r.value=!1}function v(){n("openAutoFocus")}function b(){n("closeAutoFocus")}function le(i){var h;((h=i.detail)==null?void 0:h.focusReason)==="pointer"&&i.preventDefault()}o.lockScroll&&ge(r);function se(){o.closeOnPressEscape&&F()}return Y(()=>o.modelValue,i=>{i?(f.value=!1,P(),m.value=!0,C.value=Oe(o.zIndex)?c():C.value++,Ce(()=>{n("open"),t.value&&(t.value.scrollTop=0)})):r.value&&B()}),Y(()=>o.fullscreen,i=>{t.value&&(i?(T=t.value.style.transform,t.value.style.transform=""):t.value.style.transform=T)}),be(()=>{o.modelValue&&(r.value=!0,m.value=!0,P())}),{afterEnter:w,afterLeave:V,beforeLeave:_,handleClose:F,onModalClick:p,close:B,doClose:l,onOpenAutoFocus:v,onCloseAutoFocus:b,onCloseRequested:se,onFocusoutPrevented:le,titleId:a,bodyId:g,closed:f,style:N,overlayDialogStyle:z,rendered:m,visible:r,zIndex:C}},Ye=["aria-label","aria-labelledby","aria-describedby"],Ze=O({name:"ElDialog",inheritAttrs:!1}),He=O({...Ze,props:je,emits:Ge,setup(o,{expose:t}){const u=o,S=De();H({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},E(()=>!!S.title)),H({scope:"el-dialog",from:"custom-class",replacement:"class",version:"2.3.0",ref:"https://element-plus.org/en-US/component/dialog.html#attributes",type:"Attribute"},E(()=>!!u.customClass));const n=Ie("dialog"),c=I(),T=I(),a=I(),{visible:g,titleId:r,bodyId:f,style:m,overlayDialogStyle:C,rendered:s,zIndex:d,afterEnter:$,afterLeave:N,beforeLeave:z,handleClose:w,onModalClick:V,onOpenAutoFocus:_,onCloseAutoFocus:P,onCloseRequested:B,onFocusoutPrevented:F}=Je(u,c);Ee(ee,{dialogRef:c,headerRef:T,bodyId:f,ns:n,rendered:s,style:m});const p=Le(V),L=E(()=>u.draggable&&!u.fullscreen);return t({visible:g,dialogContentRef:a}),(l,v)=>(A(),q(Pe,{to:l.appendTo,disabled:l.appendTo!=="body"?!1:!l.appendToBody},[M(we,{name:"dialog-fade",onAfterEnter:e($),onAfterLeave:e(N),onBeforeLeave:e(z),persisted:""},{default:k(()=>[Te(M(e(Ae),{"custom-mask-event":"",mask:l.modal,"overlay-class":l.modalClass,"z-index":e(d)},{default:k(()=>[R("div",{role:"dialog","aria-modal":"true","aria-label":l.title||void 0,"aria-labelledby":l.title?void 0:e(r),"aria-describedby":e(f),class:y(`${e(n).namespace.value}-overlay-dialog`),style:W(e(C)),onClick:v[0]||(v[0]=(...b)=>e(p).onClick&&e(p).onClick(...b)),onMousedown:v[1]||(v[1]=(...b)=>e(p).onMousedown&&e(p).onMousedown(...b)),onMouseup:v[2]||(v[2]=(...b)=>e(p).onMouseup&&e(p).onMouseup(...b))},[M(e(Be),{loop:"",trapped:e(g),"focus-start-el":"container",onFocusAfterTrapped:e(_),onFocusAfterReleased:e(P),onFocusoutPrevented:e(F),onReleaseRequested:e(B)},{default:k(()=>[e(s)?(A(),q(Ke,Fe({key:0,ref_key:"dialogContentRef",ref:a},l.$attrs,{"custom-class":l.customClass,center:l.center,"align-center":l.alignCenter,"close-icon":l.closeIcon,draggable:e(L),fullscreen:l.fullscreen,"show-close":l.showClose,title:l.title,"aria-level":l.headerAriaLevel,onClose:e(w)}),Se({header:k(()=>[l.$slots.title?D(l.$slots,"title",{key:1}):D(l.$slots,"header",{key:0,close:e(w),titleId:e(r),titleClass:e(n).e("title")})]),default:k(()=>[D(l.$slots,"default")]),_:2},[l.$slots.footer?{name:"footer",fn:k(()=>[D(l.$slots,"footer")])}:void 0]),1040,["custom-class","center","align-center","close-icon","draggable","fullscreen","show-close","title","aria-level","onClose"])):K("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,Ye)]),_:3},8,["mask","overlay-class","z-index"]),[[$e,e(g)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["to","disabled"]))}});var Qe=X(He,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog.vue"]]);const eo=Re(Qe);export{eo as E,Ge as a,je as d,Je as u};
