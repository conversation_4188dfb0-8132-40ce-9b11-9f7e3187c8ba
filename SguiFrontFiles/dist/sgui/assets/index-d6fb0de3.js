import{L as $e,M as X,V as h,_ as G,q as A,r as $,O as _,w as C,W,hH as Te,d1 as Y,A as O,ac as ce,u as Fe,F as K,a4 as w,x as N,y as H,z as m,G as k,d2 as ke,es as Re,hm as pe,aG as Se,al as fe,H as me,h3 as Oe,dM as ve,v as ee,N as De,a3 as Pe,e9 as Ne,em as Be,B as j,dD as Ke,X as V,D as Z,J as Q,P as Le,b9 as Me,E as x,ai as Ge,hI as Ae,C as ze,K as He,Z as ge}from"./index-9381ab2b.js";import{c as D,E as Ue,O as Ye,w as ie}from"./index-c19c3f80.js";import{E as Ve}from"./index-34c19038.js";import{c as Je,E as We,d as je,a as we,C as qe,b as Xe,e as Ze,f as Qe,g as xe,F as eo,L as oo}from"./dropdown-67e5d658.js";import{c as no}from"./castArray-25c7c99e.js";import{c as be}from"./refs-d6b4edba.js";const to=$e({style:{type:X([String,Array,Object])},currentTabId:{type:X(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:X(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:ro,ElCollectionItem:lo,COLLECTION_INJECTION_KEY:oe,COLLECTION_ITEM_INJECTION_KEY:so}=Je("RovingFocusGroup"),ne=Symbol("elRovingFocusGroup"),Ie=Symbol("elRovingFocusGroupItem"),ao={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},io=(e,o)=>{if(o!=="rtl")return e;switch(e){case h.right:return h.left;case h.left:return h.right;default:return e}},uo=(e,o,i)=>{const t=io(e.key,i);if(!(o==="vertical"&&[h.left,h.right].includes(t))&&!(o==="horizontal"&&[h.up,h.down].includes(t)))return ao[t]},co=(e,o)=>e.map((i,t)=>e[(t+o)%e.length]),te=e=>{const{activeElement:o}=document;for(const i of e)if(i===o||(i.focus(),o!==document.activeElement))return},de="currentTabIdChange",ue="rovingFocusGroup.entryFocus",po={bubbles:!1,cancelable:!0},fo=A({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:to,emits:[de,"entryFocus"],setup(e,{emit:o}){var i;const t=$((i=e.currentTabId||e.defaultCurrentTabId)!=null?i:null),c=$(!1),u=$(!1),s=$(null),{getItems:l}=_(oe,void 0),p=C(()=>[{outline:"none"},e.style]),v=a=>{o(de,a)},g=()=>{c.value=!0},b=D(a=>{var f;(f=e.onMousedown)==null||f.call(e,a)},()=>{u.value=!0}),E=D(a=>{var f;(f=e.onFocus)==null||f.call(e,a)},a=>{const f=!O(u),{target:B,currentTarget:R}=a;if(B===R&&f&&!O(c)){const L=new Event(ue,po);if(R==null||R.dispatchEvent(L),!L.defaultPrevented){const I=l().filter(F=>F.focusable),y=I.find(F=>F.active),T=I.find(F=>F.id===O(t)),M=[y,T,...I].filter(Boolean).map(F=>F.ref);te(M)}}u.value=!1}),n=D(a=>{var f;(f=e.onBlur)==null||f.call(e,a)},()=>{c.value=!1}),d=(...a)=>{o("entryFocus",...a)};W(ne,{currentTabbedId:Te(t),loop:Y(e,"loop"),tabIndex:C(()=>O(c)?-1:0),rovingFocusGroupRef:s,rovingFocusGroupRootStyle:p,orientation:Y(e,"orientation"),dir:Y(e,"dir"),onItemFocus:v,onItemShiftTab:g,onBlur:n,onFocus:E,onMousedown:b}),ce(()=>e.currentTabId,a=>{t.value=a??null}),Fe(s,ue,d)}});function mo(e,o,i,t,c,u){return K(e.$slots,"default")}var vo=G(fo,[["render",mo],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/roving-focus-group/src/roving-focus-group-impl.vue"]]);const go=A({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:ro,ElRovingFocusGroupImpl:vo}});function wo(e,o,i,t,c,u){const s=w("el-roving-focus-group-impl"),l=w("el-focus-group-collection");return N(),H(l,null,{default:m(()=>[k(s,ke(Re(e.$attrs)),{default:m(()=>[K(e.$slots,"default")]),_:3},16)]),_:3})}var bo=G(go,[["render",wo],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/roving-focus-group/src/roving-focus-group.vue"]]);const Io=A({components:{ElRovingFocusCollectionItem:lo},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:o}){const{currentTabbedId:i,loop:t,onItemFocus:c,onItemShiftTab:u}=_(ne,void 0),{getItems:s}=_(oe,void 0),l=pe(),p=$(null),v=D(n=>{o("mousedown",n)},n=>{e.focusable?c(O(l)):n.preventDefault()}),g=D(n=>{o("focus",n)},()=>{c(O(l))}),b=D(n=>{o("keydown",n)},n=>{const{key:d,shiftKey:a,target:f,currentTarget:B}=n;if(d===h.tab&&a){u();return}if(f!==B)return;const R=uo(n);if(R){n.preventDefault();let I=s().filter(y=>y.focusable).map(y=>y.ref);switch(R){case"last":{I.reverse();break}case"prev":case"next":{R==="prev"&&I.reverse();const y=I.indexOf(B);I=t.value?co(I,y+1):I.slice(y+1);break}}Se(()=>{te(I)})}}),E=C(()=>i.value===O(l));return W(Ie,{rovingFocusGroupItemRef:p,tabIndex:C(()=>O(E)?0:-1),handleMousedown:v,handleFocus:g,handleKeydown:b}),{id:l,handleKeydown:b,handleFocus:g,handleMousedown:v}}});function ho(e,o,i,t,c,u){const s=w("el-roving-focus-collection-item");return N(),H(s,{id:e.id,focusable:e.focusable,active:e.active},{default:m(()=>[K(e.$slots,"default")]),_:3},8,["id","focusable","active"])}var Eo=G(Io,[["render",ho],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/roving-focus-group/src/roving-focus-item.vue"]]);const q=Symbol("elDropdown"),{ButtonGroup:_o}=fe,Co=A({name:"ElDropdown",components:{ElButton:fe,ElButtonGroup:_o,ElScrollbar:Ve,ElDropdownCollection:We,ElTooltip:Ue,ElRovingFocusGroup:bo,ElOnlyChild:Ye,ElIcon:me,ArrowDown:Oe},props:je,emits:["visible-change","click","command"],setup(e,{emit:o}){const i=ve(),t=ee("dropdown"),{t:c}=De(),u=$(),s=$(),l=$(null),p=$(null),v=$(null),g=$(null),b=$(!1),E=[h.enter,h.space,h.down],n=C(()=>({maxHeight:Pe(e.maxHeight)})),d=C(()=>[t.m(y.value)]),a=C(()=>no(e.trigger)),f=pe().value,B=C(()=>e.id||f);ce([u,a],([r,S],[z])=>{var le,se,ae;(le=z==null?void 0:z.$el)!=null&&le.removeEventListener&&z.$el.removeEventListener("pointerenter",P),(se=r==null?void 0:r.$el)!=null&&se.removeEventListener&&r.$el.removeEventListener("pointerenter",P),(ae=r==null?void 0:r.$el)!=null&&ae.addEventListener&&S.includes("hover")&&r.$el.addEventListener("pointerenter",P)},{immediate:!0}),Ne(()=>{var r,S;(S=(r=u.value)==null?void 0:r.$el)!=null&&S.removeEventListener&&u.value.$el.removeEventListener("pointerenter",P)});function R(){L()}function L(){var r;(r=l.value)==null||r.onClose()}function I(){var r;(r=l.value)==null||r.onOpen()}const y=Be();function T(...r){o("command",...r)}function P(){var r,S;(S=(r=u.value)==null?void 0:r.$el)==null||S.focus()}function M(){}function F(){const r=O(p);a.value.includes("hover")&&(r==null||r.focus()),g.value=null}function re(r){g.value=r}function J(r){b.value||(r.preventDefault(),r.stopImmediatePropagation())}function U(){o("visible-change",!0)}function Ce(r){(r==null?void 0:r.type)==="keydown"&&p.value.focus()}function ye(){o("visible-change",!1)}return W(q,{contentRef:p,role:C(()=>e.role),triggerId:B,isUsingKeyboard:b,onItemEnter:M,onItemLeave:F}),W("elDropdown",{instance:i,dropdownSize:y,handleClick:R,commandHandler:T,trigger:Y(e,"trigger"),hideOnClick:Y(e,"hideOnClick")}),{t:c,ns:t,scrollbar:v,wrapStyle:n,dropdownTriggerKls:d,dropdownSize:y,triggerId:B,triggerKeys:E,currentTabId:g,handleCurrentTabIdChange:re,handlerMainButtonClick:r=>{o("click",r)},handleEntryFocus:J,handleClose:L,handleOpen:I,handleBeforeShowTooltip:U,handleShowTooltip:Ce,handleBeforeHideTooltip:ye,onFocusAfterTrapped:r=>{var S,z;r.preventDefault(),(z=(S=p.value)==null?void 0:S.focus)==null||z.call(S,{preventScroll:!0})},popperRef:l,contentRef:p,triggeringElementRef:u,referenceElementRef:s}}});function yo(e,o,i,t,c,u){var s;const l=w("el-dropdown-collection"),p=w("el-roving-focus-group"),v=w("el-scrollbar"),g=w("el-only-child"),b=w("el-tooltip"),E=w("el-button"),n=w("arrow-down"),d=w("el-icon"),a=w("el-button-group");return N(),j("div",{class:Z([e.ns.b(),e.ns.is("disabled",e.disabled)])},[k(b,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":e.trigger==="hover"?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":(s=e.referenceElementRef)==null?void 0:s.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":e.trigger==="hover"?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:e.teleported,pure:"",persistent:"",onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},Ke({content:m(()=>[k(v,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:m(()=>[k(p,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:m(()=>[k(l,null,{default:m(()=>[K(e.$slots,"dropdown")]),_:3})]),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])]),_:3},8,["wrap-style","view-class"])]),_:2},[e.splitButton?void 0:{name:"default",fn:m(()=>[k(g,{id:e.triggerId,ref:"triggeringElementRef",role:"button",tabindex:e.tabindex},{default:m(()=>[K(e.$slots,"default")]),_:3},8,["id","tabindex"])])}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","teleported","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(N(),H(a,{key:0},{default:m(()=>[k(E,V({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:m(()=>[K(e.$slots,"default")]),_:3},16,["size","type","disabled","tabindex","onClick"]),k(E,V({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:m(()=>[k(d,{class:Z(e.ns.e("icon"))},{default:m(()=>[k(n)]),_:1},8,["class"])]),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])]),_:3})):Q("v-if",!0)],2)}var $o=G(Co,[["render",yo],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dropdown/src/dropdown.vue"]]);const To=A({name:"DropdownItemImpl",components:{ElIcon:me},props:we,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:o}){const i=ee("dropdown"),{role:t}=_(q,void 0),{collectionItemRef:c}=_(qe,void 0),{collectionItemRef:u}=_(so,void 0),{rovingFocusGroupItemRef:s,tabIndex:l,handleFocus:p,handleKeydown:v,handleMousedown:g}=_(Ie,void 0),b=be(c,u,s),E=C(()=>t.value==="menu"?"menuitem":t.value==="navigation"?"link":"button"),n=D(d=>{const{code:a}=d;if(a===h.enter||a===h.space)return d.preventDefault(),d.stopImmediatePropagation(),o("clickimpl",d),!0},v);return{ns:i,itemRef:b,dataset:{[Xe]:""},role:E,tabIndex:l,handleFocus:p,handleKeydown:n,handleMousedown:g}}}),Fo=["aria-disabled","tabindex","role"];function ko(e,o,i,t,c,u){const s=w("el-icon");return N(),j(Ge,null,[e.divided?(N(),j("li",V({key:0,role:"separator",class:e.ns.bem("menu","item","divided")},e.$attrs),null,16)):Q("v-if",!0),Le("li",V({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:o[0]||(o[0]=l=>e.$emit("clickimpl",l)),onFocus:o[1]||(o[1]=(...l)=>e.handleFocus&&e.handleFocus(...l)),onKeydown:o[2]||(o[2]=x((...l)=>e.handleKeydown&&e.handleKeydown(...l),["self"])),onMousedown:o[3]||(o[3]=(...l)=>e.handleMousedown&&e.handleMousedown(...l)),onPointermove:o[4]||(o[4]=l=>e.$emit("pointermove",l)),onPointerleave:o[5]||(o[5]=l=>e.$emit("pointerleave",l))}),[e.icon?(N(),H(s,{key:0},{default:m(()=>[(N(),H(Me(e.icon)))]),_:1})):Q("v-if",!0),K(e.$slots,"default")],16,Fo)],64)}var Ro=G(To,[["render",ko],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dropdown/src/dropdown-item-impl.vue"]]);const he=()=>{const e=_("elDropdown",{}),o=C(()=>e==null?void 0:e.dropdownSize);return{elDropdown:e,_elDropdownSize:o}},So=A({name:"ElDropdownItem",components:{ElDropdownCollectionItem:Ze,ElRovingFocusItem:Eo,ElDropdownItemImpl:Ro},inheritAttrs:!1,props:we,emits:["pointermove","pointerleave","click"],setup(e,{emit:o,attrs:i}){const{elDropdown:t}=he(),c=ve(),u=$(null),s=C(()=>{var n,d;return(d=(n=O(u))==null?void 0:n.textContent)!=null?d:""}),{onItemEnter:l,onItemLeave:p}=_(q,void 0),v=D(n=>(o("pointermove",n),n.defaultPrevented),ie(n=>{if(e.disabled){p(n);return}const d=n.currentTarget;d===document.activeElement||d.contains(document.activeElement)||(l(n),n.defaultPrevented||d==null||d.focus())})),g=D(n=>(o("pointerleave",n),n.defaultPrevented),ie(n=>{p(n)})),b=D(n=>{if(!e.disabled)return o("click",n),n.type!=="keydown"&&n.defaultPrevented},n=>{var d,a,f;if(e.disabled){n.stopImmediatePropagation();return}(d=t==null?void 0:t.hideOnClick)!=null&&d.value&&((a=t.handleClick)==null||a.call(t)),(f=t.commandHandler)==null||f.call(t,e.command,c,n)}),E=C(()=>({...e,...i}));return{handleClick:b,handlePointerMove:v,handlePointerLeave:g,textContent:s,propsAndAttrs:E}}});function Oo(e,o,i,t,c,u){var s;const l=w("el-dropdown-item-impl"),p=w("el-roving-focus-item"),v=w("el-dropdown-collection-item");return N(),H(v,{disabled:e.disabled,"text-value":(s=e.textValue)!=null?s:e.textContent},{default:m(()=>[k(p,{focusable:!e.disabled},{default:m(()=>[k(l,V(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:m(()=>[K(e.$slots,"default")]),_:3},16,["onPointerleave","onPointermove","onClickimpl"])]),_:3},8,["focusable"])]),_:3},8,["disabled","text-value"])}var Ee=G(So,[["render",Oo],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dropdown/src/dropdown-item.vue"]]);const Do=A({name:"ElDropdownMenu",props:Qe,setup(e){const o=ee("dropdown"),{_elDropdownSize:i}=he(),t=i.value,{focusTrapRef:c,onKeydown:u}=_(Ae,void 0),{contentRef:s,role:l,triggerId:p}=_(q,void 0),{collectionRef:v,getItems:g}=_(xe,void 0),{rovingFocusGroupRef:b,rovingFocusGroupRootStyle:E,tabIndex:n,onBlur:d,onFocus:a,onMousedown:f}=_(ne,void 0),{collectionRef:B}=_(oe,void 0),R=C(()=>[o.b("menu"),o.bm("menu",t==null?void 0:t.value)]),L=be(s,v,c,b,B),I=D(T=>{var P;(P=e.onKeydown)==null||P.call(e,T)},T=>{const{currentTarget:P,code:M,target:F}=T;if(P.contains(F),h.tab===M&&T.stopImmediatePropagation(),T.preventDefault(),F!==O(s)||!eo.includes(M))return;const J=g().filter(U=>!U.disabled).map(U=>U.ref);oo.includes(M)&&J.reverse(),te(J)});return{size:t,rovingFocusGroupRootStyle:E,tabIndex:n,dropdownKls:R,role:l,triggerId:p,dropdownListWrapperRef:L,handleKeydown:T=>{I(T),u(T)},onBlur:d,onFocus:a,onMousedown:f}}}),Po=["role","aria-labelledby"];function No(e,o,i,t,c,u){return N(),j("ul",{ref:e.dropdownListWrapperRef,class:Z(e.dropdownKls),style:ze(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:o[0]||(o[0]=(...s)=>e.onBlur&&e.onBlur(...s)),onFocus:o[1]||(o[1]=(...s)=>e.onFocus&&e.onFocus(...s)),onKeydown:o[2]||(o[2]=x((...s)=>e.handleKeydown&&e.handleKeydown(...s),["self"])),onMousedown:o[3]||(o[3]=x((...s)=>e.onMousedown&&e.onMousedown(...s),["self"]))},[K(e.$slots,"default")],46,Po)}var _e=G(Do,[["render",No],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dropdown/src/dropdown-menu.vue"]]);const Uo=He($o,{DropdownItem:Ee,DropdownMenu:_e}),Yo=ge(Ee),Vo=ge(_e);export{Yo as E,Vo as a,Uo as b};
