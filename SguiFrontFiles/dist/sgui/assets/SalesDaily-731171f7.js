import{_ as ta}from"./theme-light_empty-0081a108.js";import{q as rt,x as r,y as be,z as b,B as d,ai as Q,aj as ee,G as m,A as e,aV as aa,ak as he,Q as u,H as xt,ao as ua,ha as da,ab as Nt,r as F,b0 as ot,s as Tt,o as lt,aT as Me,aF as ma,aY as na,ad as st,R as Pe,au as Rt,a5 as Ct,P as a,J as Ee,D as Ne,ah as oa,al as St,aZ as wt,c5 as Y,a9 as _t,w as De,aH as kt,gw as ya,bt as fa,hb as ga,ac as Zt,bD as ha,at as ea,a4 as ba,b3 as vt,am as ka,an as va}from"./index-9381ab2b.js";import{F as Se}from"./FilterSelect-fd814d40.js";import{E as ct,a as it}from"./index-094198d8.js";import{E as $t,a as Et}from"./index-951011fc.js";import{E as la,a as sa}from"./index-6bc0a2f0.js";import{E as ra}from"./index-1dbceb27.js";import{E as ca}from"./index-7b8ec8cc.js";import{e as Ta}from"./exceljs.min-5aff0268.js";import{s as Ve}from"./subtract-e226dc5c.js";import{a as _a}from"./date-c42a8141.js";import{T as xa}from"./TicketRefundForm-c10fc473.js";import{P as Na}from"./PrintNoSelect-c0b0bc1c.js";import{D as Ra}from"./regular-crs-0d781ceb.js";import{q as Ca}from"./ticketOperationApi-8106707a.js";import{_ as Sa}from"./Page.vue_vue_type_script_setup_true_lang-c36b3350.js";import{E as wa}from"./index-1d08351c.js";import{E as $a}from"./index-e22833ad.js";import{_ as Ea}from"./_plugin-vue_export-helper-c27b6911.js";import"./index-a7943392.js";import"./isEqual-a619023a.js";import"./flatten-85480810.js";import"./index-34c19038.js";import"./index-d6fb0de3.js";import"./index-c19c3f80.js";import"./isUndefined-aa0326a0.js";import"./dropdown-67e5d658.js";import"./castArray-25c7c99e.js";import"./refs-d6b4edba.js";import"./strings-820bea19.js";import"./index-a4ffe93f.js";import"./_createMathOperation-15113527.js";import"./refundUtil-234919d2.js";import"./decimal-56a2735b.js";import"./throttle-9e041729.js";import"./index-847d31f7.js";import"./index-716e52e2.js";import"./index-06c3636a.js";const Da={key:1,class:"ml-4"},Fa=rt({__name:"TicketTypeSelect",props:{ticketType:{},tktTypes:{}},emits:["update:ticket-type"],setup(h,{emit:c}){const t=c,S=i=>{t("update:ticket-type",i)};return(i,g)=>{const k=xt,O=ct,D=it;return r(),be(D,{"model-value":i.ticketType,placeholder:i.$t("app.agentReport.selectTicketType"),onChange:S},{default:b(()=>[(r(!0),d(Q,null,ee(i.tktTypes,A=>(r(),be(O,{key:A.value,value:A.value,label:i.$t(`app.agentReport.${A.label}`)},{default:b(()=>[i.ticketType===A.value?(r(),be(k,{key:0,class:"bg-inherit"},{default:b(()=>[m(e(aa))]),_:1})):(r(),d("span",Da)),he(" "+u(i.$t(`app.agentReport.${A.label}`)),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["model-value","placeholder"])}}}),nt=(h,c)=>ua(`${da}/crs/apiReport/crsDailySales`,{headers:{gid:c}}).post(h).json(),Aa=/^[1-9][0-9]{0,4}$/,Ia=(h,c)=>{const{t}=Nt(),S=F(),i=F({saleStatusCode:["ISSU","RFND"],deviceNumber:"",tktType:"BSP"}),g=F(["ISSU","RFND"]),k=F(),O=ot({saleStatusCode:[{required:!0,message:t("app.agentReport.selectStatus"),trigger:"change"}],deviceNumber:[{required:!0,message:t("app.agentReport.printTicketNumber"),trigger:"change"},{pattern:Aa,message:t("app.agentReport.deviceNumberErr"),trigger:["change","blur"]}]}),D=F([]),A=F({}),H=F([]),L=Tt(!1),V=F(!1);let T={};const R=F([]);let I=[];const W=l=>!l||l==="",f=async l=>{const v=document.createElement("input");v.value=l,document.body.appendChild(v),v.select(),document.execCommand("Copy"),v.style.display="none",na({message:t("app.agentReport.tip"),type:"success",duration:2*1e3})},U=async l=>{D.value[l].showDropDown(!D.value[l].getVisibleValue())},z=l=>{p(),st.setLink(`/v2/crs/pnrManagement?pnrNo=${l}`)},G=(l,v)=>{p(),st.setLink(`/v2/crs/ticketOperation?ticketNumber=${l}&secondFactorCode=CN&secondFactorValue=${v??""}`)},j=l=>{var M;const v=[];return(((M=A.value)==null?void 0:M.items)??[]).forEach(ae=>{ae[l]&&!v.includes(ae[l])&&v.push(ae[l])}),v},y=l=>l.ticketType==="4"&&l.passengerType==="Y"?t("app.ticketType.GPDomesticTicket"):l.ticketType==="2"&&l.passengerType==="Y"?t("app.ticketType.GPInternationalTicket"):t("app.ticketType.BSPDomesticTicket"),$=l=>{switch(l==null?void 0:l.ticketType){case"1":return t("app.ticketType.internationalAirlineTickets");case"2":return t("app.ticketType.BSPInternationalTicket");case"3":return t("app.ticketType.domesticAirlineTickets");case"4":return y(l);case"7":return t("app.ticketType.BOPTicket");default:return l==null?void 0:l.ticketType}},P=()=>{const l={};l.ticketTypes=j("saleStatusCode"),l.jobNos=j("agent"),l.ticketKinds=j("ticketType"),l.payTypes=j("paymentTypeCode"),A.value.filter=l},J=()=>{T={},k.value={},R.value=[],A.value={},I=[]},te=async()=>{var l;(l=S.value)==null||l.validate(async v=>{var ae,fe,ke,ne,se,N,w,Te,_e,K;if(!v)return;V.value=!1,J();const M={deviceNumber:i.value.deviceNumber,saleStatusCodes:i.value.saleStatusCode,tktType:i.value.tktType,value:"2",date:Pe().format("YYYY-MM-DD"),outerSign:!1};L.value=!0;try{const Re=Rt("09300109"),ve=(await nt(M,Re)).data.value;ve.items=(ae=ve==null?void 0:ve.items)==null?void 0:ae.map(C=>({...C,ticketType:$(C)})),A.value=ve,k.value={...ve.queryTSLHeader,ticketingDate:(fe=ve==null?void 0:ve.queryTSLHeader)!=null&&fe.ticketingDate?Pe(ve.queryTSLHeader.ticketingDate).format("YYYY-MM-DD"):""},P(),H.value=Me((ke=A.value)==null?void 0:ke.items),I=((ne=A.value)==null?void 0:ne.items)??[];const Ue=((se=c==null?void 0:c.salesForm)==null?void 0:se.prntNo)===M.deviceNumber&&((N=c==null?void 0:c.salesForm)==null?void 0:N.tktType)===M.tktType;(w=c==null?void 0:c.salesForm)!=null&&w.tktType&&Ue&&h("errorNumber",(Te=A==null?void 0:A.value)==null?void 0:Te.totalErrorNumber,(_e=A==null?void 0:A.value)==null?void 0:_e.issueErrorNumber,(K=A==null?void 0:A.value)==null?void 0:K.refundErrorNumber)}finally{L.value=!1}})},le=(l,v)=>{if(v!=null&&v.length)R.value.includes(l)||R.value.push(l);else{const M=R.value.indexOf(l);M!==-1&&R.value.splice(M,1)}},X=(l,v,M)=>{var fe,ke,ne,se,N,w,Te,_e;le(l,v),V.value=!0;const ae=Me(v);M&&ae.push(M),T[l]=ae,I=((fe=A.value)==null?void 0:fe.items)??[],((ke=T==null?void 0:T.airline)==null?void 0:ke.length)>0&&(I=I.filter(K=>T.airline.includes(K.airline))),((ne=T==null?void 0:T.prntNo)==null?void 0:ne.length)>0&&(I=I.filter(K=>T.prntNo.includes(K.pnr))),((se=T==null?void 0:T.ticketStatus)==null?void 0:se.length)>0&&(I=I.filter(K=>T.ticketStatus.includes(K.saleStatusCode)),I.sort((K,Re)=>K.salesDateTime.localeCompare(Re.salesDateTime))),((N=T==null?void 0:T.payType)==null?void 0:N.length)>0&&(I=I.filter(K=>T.payType.includes(K.paymentTypeCode))),((w=T==null?void 0:T.jobNo)==null?void 0:w.length)>0&&(I=I.filter(K=>T.jobNo.includes(K.agent))),((Te=T==null?void 0:T.ticketType)==null?void 0:Te.length)>0&&(I=I.filter(K=>T.ticketType.includes(K.ticketType))),((_e=T==null?void 0:T.currencyType)==null?void 0:_e.length)>0&&(I=I.filter(K=>T.currencyType.includes(K.currency))),H.value=I},p=()=>{h("update:modelValue",!1)};return lt(()=>{var v,M,ae,fe,ke,ne,se,N,w,Te,_e,K;i.value.saleStatusCode=g.value.map(Re=>Re),i.value.deviceNumber=(v=c==null?void 0:c.salesForm)==null?void 0:v.prntNo,i.value.tktType=((M=c==null?void 0:c.salesForm)==null?void 0:M.tktType)??"BSP",((ae=i.value)==null?void 0:ae.tktType)===((fe=c==null?void 0:c.salesForm)==null?void 0:fe.tktType)&&((ke=i.value)==null?void 0:ke.deviceNumber)===((ne=c==null?void 0:c.salesForm)==null?void 0:ne.prntNo)&&i.value.saleStatusCode.includes("ISSU")&&i.value.saleStatusCode.includes("RFND")&&((w=(N=(se=c==null?void 0:c.storeTodayError)==null?void 0:se.querySaleDailyErrorRes)==null?void 0:N.items)==null?void 0:w.length)>0&&(A.value=(Te=c==null?void 0:c.storeTodayError)==null?void 0:Te.querySaleDailyErrorRes,H.value=Me((_e=A.value)==null?void 0:_e.items),I=((K=A.value)==null?void 0:K.items)??[])}),ma(()=>{O.deviceNumber[0].required=["BSP","CDS","PYN"].includes(i.value.tktType)}),{todayExceptionsFormRef:S,todayExceptionsForm:i,saleStatusCodeList:g,todayExceptionsRules:O,isLoading:L,todayExceptionsList:H,filterRptRef:D,filterTypeList:R,querySaleDailyRes:A,headDetail:k,handleSearch:te,buildTicketType:$,isEmptyData:W,filterChange:X,jumpToPnrEtQuery:z,jumpToTcTicketQuery:G,doCopy:f,openFilter:U,closeDialog:p}},Oa=Ia,Pa={class:"today-exceptions-dialog-content"},Va={class:"flex"},Ua={key:0,class:"h-8 w-full px-2.5 bg-[#f6f6f6] rounded justify-start items-center gap-2.5 flex flex-1"},ja=a("span",{class:"text-[#8c8c8c] text-xs font-normal leading-tight"},"OFFICE",-1),Ma={class:"text-neutral-800 text-xs font-normal leading-tight"},Ba=a("span",{class:"text-[#8c8c8c] text-xs font-normal leading-tight"},"IATA NO",-1),Ha={class:"text-neutral-800 text-xs font-normal leading-tight"},La={class:"text-[#8c8c8c] text-xs font-normal leading-tight"},za={class:"text-neutral-800 text-xs font-normal leading-tight"},Ya={key:0,class:"result"},Ga=["onClick"],qa=["onClick"],Qa={key:1},Xa=["onClick"],Ka=["onClick"],Wa={key:1,class:"empty-info"},Ja=a("img",{src:ta,alt:"$t('app.agentReport.noErrordata')"},null,-1),Za={class:"main-info"},en=rt({__name:"TodayExceptionsDialog",props:{salesForm:{},storeTodayError:{},tktTypes:{}},emits:["update:modelValue","errorNumber"],setup(h,{emit:c}){const t=h,S=c,{todayExceptionsFormRef:i,todayExceptionsForm:g,headDetail:k,saleStatusCodeList:O,todayExceptionsRules:D,isLoading:A,todayExceptionsList:H,filterRptRef:L,filterTypeList:V,querySaleDailyRes:T,handleSearch:R,openFilter:I,filterChange:W,jumpToTcTicketQuery:f,doCopy:U,isEmptyData:z,jumpToPnrEtQuery:G,closeDialog:j}=Oa(S,t);return(y,$)=>{const P=$t,J=ct,te=it,le=oa,X=St,p=Et,l=la,v=ra,M=sa,ae=ca,fe=wt;return r(),be(ae,{width:"1000",title:y.$t("app.agentReport.abnormalRecords"),"close-on-click-modal":!1,class:"today-exceptions-dialog",onClose:e(j)},{footer:b(()=>[m(X,{class:"w-[80px]",onClick:e(j)},{default:b(()=>[he(u(y.$t("app.agentReport.close")),1)]),_:1},8,["onClick"])]),default:b(()=>{var ke,ne,se;return[Ct((r(),d("div",Pa,[a("div",Va,[m(p,{ref_key:"todayExceptionsFormRef",ref:i,model:e(g),inline:!0,rules:e(D),"require-asterisk-position":"right"},{default:b(()=>[m(P,{label:y.$t("app.agentReport.tktType"),class:"ticket-type",required:""},{default:b(()=>[m(Fa,{"tkt-types":y.tktTypes,"ticket-type":e(g).tktType,"onUpdate:ticketType":$[0]||($[0]=N=>e(g).tktType=N)},null,8,["tkt-types","ticket-type"])]),_:1},8,["label"]),m(P,{label:y.$t("app.agentReport.saleStatus"),prop:"saleStatusCode",class:"sale-status"},{default:b(()=>[m(te,{modelValue:e(g).saleStatusCode,"onUpdate:modelValue":$[1]||($[1]=N=>e(g).saleStatusCode=N),multiple:"","collapse-tags":"",placeholder:y.$t("app.agentReport.selectTicketType")},{default:b(()=>[(r(!0),d(Q,null,ee(e(O),N=>(r(),be(J,{key:N,label:N,value:N},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),m(P,{label:y.$t("app.agentReport.ticketMachineNumber"),prop:"deviceNumber",class:"print-ticket-number"},{default:b(()=>[m(le,{modelValue:e(g).deviceNumber,"onUpdate:modelValue":$[2]||($[2]=N=>e(g).deviceNumber=N),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:y.$t("app.agentReport.printTicketNumber")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),m(P,null,{default:b(()=>[m(X,{"data-gid":"11280201",type:"primary",onClick:e(R)},{default:b(()=>[he(u(y.$t("app.agentReport.search")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"]),(ke=e(k))!=null&&ke.office?(r(),d("div",Ua,[a("div",null,[ja,a("span",Ma,"："+u(e(k).office),1)]),a("div",null,[Ba,a("span",Ha,"："+u(e(k).iataNumber),1)]),a("div",null,[a("span",La,u(y.$t("app.agentReport.saleDate")),1),a("span",za,"："+u(e(k).ticketingDate),1)])])):Ee("",!0)]),(se=(ne=e(T))==null?void 0:ne.items)!=null&&se.length?(r(),d("div",Ya,[m(M,{ref:"todayExceptionsRef",height:"550px",data:e(H),stripe:"",style:{"min-width":"100%"},class:"sales-daily-table",onFilterChange:e(W)},{default:b(()=>[m(l,{prop:"ticket",label:y.$t("app.agentReport.tktNo"),width:"150px",flexible:"true"},{default:b(({row:N})=>[a("span",{class:"pointer-span",onClick:w=>e(f)(N.ticket,N.pnr)},u(N.ticket),9,Ga),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:w=>e(U)(N.ticket)},null,8,qa)]),_:1},8,["label"]),m(l,{prop:"ticketStatus",width:"110px"},{header:b(()=>{var N;return[a("span",{class:Ne(["pointer-span-drop",e(V).includes("ticketStatus")?"text-brand-2":""]),onClick:$[3]||($[3]=w=>e(I)(1))},u(y.$t("app.agentReport.tktStatus")),3),m(Se,{ref:w=>{w&&(e(L)[1]=w)},filters:(N=e(T).filter)==null?void 0:N.ticketTypes,"column-key":"ticketStatus",onHandleConfrim:e(W)},null,8,["filters","onHandleConfrim"])]}),default:b(({row:N})=>[m(v,{class:"tag"},{default:b(()=>[he(u(N.ticketStatus),1)]),_:2},1024)]),_:1}),m(l,{prop:"jobNo","min-width":"80px"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(V).includes("jobNo")?"text-brand-2":""]),onClick:$[4]||($[4]=N=>e(I)(3))},u(y.$t("app.agentReport.agent")),3),m(Se,{ref:N=>{N&&(e(L)[3]=N)},filters:e(T).filter.jobNos,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":y.$t("app.agentReport.agentTip"),"column-key":"jobNo",onHandleConfrim:e(W)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),m(l,{prop:"desArr",label:y.$t("app.agentReport.segSE"),"min-width":"90px"},null,8,["label"]),m(l,{prop:"pnr",label:"PNR","min-width":"105px"},{default:b(({row:N})=>[e(z)(N.pnr)?(r(),d(Q,{key:0},[],64)):(r(),d("div",Qa,[a("span",{class:"pointer-span",onClick:w=>e(G)(N.pnr)},u(N.pnr),9,Xa),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:w=>e(U)(N.pnr)},null,8,Ka)]))]),_:1}),m(l,{prop:"ticketType","min-width":"125px"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(V).includes("ticketType")?"text-brand-2":""]),onClick:$[5]||($[5]=N=>e(I)(6))},u(y.$t("app.agentReport.tktType")),3),m(Se,{ref:N=>{N&&(e(L)[6]=N)},filters:e(T).filter.ticketKinds,"column-key":"ticketType",onHandleConfrim:e(W)},null,8,["filters","onHandleConfrim"])]),_:1}),m(l,{prop:"payType","min-width":"100px"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(V).includes("payType")?"text-brand-2":""]),onClick:$[6]||($[6]=N=>e(I)(4))},u(y.$t("app.agentReport.payment")),3),m(Se,{ref:N=>{N&&(e(L)[4]=N)},filters:e(T).filter.payTypes,"column-key":"payType",onHandleConfrim:e(W)},null,8,["filters","onHandleConfrim"])]),_:1})]),_:1},8,["data","onFilterChange"])])):(r(),d("div",Wa,[Ja,a("div",Za,u(y.$t("app.agentReport.noErrordata")),1)]))])),[[fe,e(A)]])]}),_:1},8,["title","onClose"])}}});const tn=[{name:Y.global.t("app.agentReport.tktNo"),wch:14.75},{name:Y.global.t("app.agentReport.segSE"),wch:10.5},{name:Y.global.t("app.agentReport.tktSettle"),wch:15.63},{name:Y.global.t("app.agentReport.tax"),wch:12},{name:Y.global.t("app.agentReport.agency"),wch:12},{name:Y.global.t("app.agentReport.agencyRate"),wch:12},{name:"PNR",wch:9.75},{name:Y.global.t("app.agentReport.agent"),wch:8.63},{name:Y.global.t("app.agentReport.tktType"),wch:13.5},{name:Y.global.t("app.agentReport.ticketMachineNumber"),wch:8.75},{name:Y.global.t("app.agentReport.payment"),wch:8.75}],an=h=>{const c=(h.items??[]).filter(t=>t.ticketStatus==="ISSU"||t.ticketStatus==="EXCH").map(t=>({ticket:t.ticket,desArr:t.desArr,amount:t.amount,taxAmount:t.taxAmount,agencyFee:t.agencyFee,agencyFeePercent:t.agencyFeePercent,pnr:t.pnr,jobNo:t.jobNo,tktType:t.ticketType,ticketMachineNumber:t.prntNo,payment:t.payType}));return{officeNo:h==null?void 0:h.office,iataNO:h==null?void 0:h.iata,salesDate:h.items[0].salesDate,items:c}},nn=h=>{const c={},t={},S={},i={};return(h??[]).forEach(g=>{c[g.currencyType]=c[g.currencyType]?c[g.currencyType]+Number(g.amount):Number(g.amount),t[g.currencyType]=t[g.currencyType]?t[g.currencyType]+Number(g.taxAmount):Number(g.taxAmount),S[g.currencyType]=S[g.currencyType]?S[g.currencyType]+Number(g.agencyFee):Number(g.agencyFee);const k=Ve(Number(g.amount),Number(g.agencyFee));i[g.currencyType]=i[g.currencyType]?i[g.currencyType]+k:k}),{totalAmount:c,taxAmount:t,agencyFee:S,carriers:i}},on=(h,c)=>{var le,X;const t=c.addWorksheet(Y.global.t("app.agentReport.issueChangeReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:K1"),t.getRow(1).height=30;const S=t.getCell("A1");S.value=Y.global.t("app.agentReport.issueChangeReport"),S.alignment={vertical:"middle",horizontal:"center"},S.font={size:14,bold:!0};const i=an(h),g=(i==null?void 0:i.officeNo)??"",k=(i==null?void 0:i.iataNO)??"",O=(i==null?void 0:i.salesDate)??"",D=[`OFFICE:${g}`,null,`IATA NO:${k}`,null,null,`日期:${O}`],A=t.addRow(D);A.font={size:14,bold:!0};const H=t.addRow([]);H.height=9;const L=[],V=[];tn.forEach(p=>{L.push(p.name),V.push({wch:p.wch})}),t.addRow(L).eachCell(p=>{p.font={bold:!0}}),t.columns.forEach((p,l)=>{p.width=V[l].wch});const I=[3,4,5,6];(i.items??[]).forEach(p=>{t.addRow(Object.values(p)).eachCell((v,M)=>{I.includes(M)&&(v.value=Number(v.value),v.numFmt="0.00",v.alignment={horizontal:"left"})})});const W=t.addRow([]),{totalAmount:f,taxAmount:U,agencyFee:z,carriers:G}=nn(h.items.filter(p=>p.ticketStatus==="ISSU"||p.ticketStatus==="EXCH")),j=["TICKETS ISSU",((le=h.total)==null?void 0:le.totalTicket)??"","NORMAL FARE","-- AMOUNT："];for(const p in f)if(f[p]){const l=f[p];j.push(l),j.push(p)}const y=[null,null,"NORMAL TAX","-- AMOUNT："];for(const p in U)if(U[p]){const l=U[p];y.push(l),y.push(p)}const $=[null,null,"NORMAL COMMIT","-- AMOUNT："];for(const p in z)if(z[p]){const l=z[p];$.push(l),$.push(p)}const P=["TICKETS EXCHANGE",((X=h.total)==null?void 0:X.totalExchange)??"","CARRIERS","-- AMOUNT："];for(const p in G)if(G[p]){const l=G[p];P.push(l),P.push(p)}t.addRow(j),t.addRow(P),t.addRow(y),t.addRow($);const J=[1,2,3,5,6],te=W.number+1;for(let p=te;p<=te+3;p++)for(let l=1;l<=11;l++){const v=t.getCell(p,l);J.includes(l)&&(v.alignment={horizontal:"right"}),l==5&&(v.value=Number(v.value),v.numFmt="0.00"),v.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},ln=[{name:Y.global.t("app.agentReport.tktNo"),wch:14.75},{name:Y.global.t("app.agentReport.refundOrder"),wch:10.5},{name:Y.global.t("app.agentReport.refundAmount"),wch:15.63},{name:Y.global.t("app.agentReport.handFee"),wch:12},{name:Y.global.t("app.agentReport.refundTax"),wch:12},{name:Y.global.t("app.agentReport.agency"),wch:9.75},{name:Y.global.t("app.agentReport.agencyRate"),wch:9.75},{name:Y.global.t("app.agentReport.agent"),wch:8.63},{name:Y.global.t("app.agentReport.tktType"),wch:13.5},{name:Y.global.t("app.agentReport.ticketMachineNumber"),wch:8.75},{name:Y.global.t("app.agentReport.payment"),wch:8.75}],sn=h=>{const c=(h.items??[]).filter(t=>t.ticketStatus==="RFND").map(t=>({ticket:t.ticket,refundNo:!t.refundNo||t.refundNo===""?"-":t.refundNo,amount:t.amount,serviceCharge:t.serviceCharge,taxAmount:t.taxAmount,agencyFee:t.agencyFee,agencyFeePercent:t.agencyFeePercent,jobNo:t.jobNo,tktType:t.ticketType,ticketMachineNumber:t.prntNo,payment:t.payType}));return{officeNo:h==null?void 0:h.office,iataNO:h==null?void 0:h.iata,salesDate:h.items[0].salesDate,items:c}},rn=h=>{const c={},t={},S={},i={},g={};return(h??[]).forEach(k=>{c[k.currencyType]=c[k.currencyType]?c[k.currencyType]+Number(k.amount):Number(k.amount),t[k.currencyType]=t[k.currencyType]?t[k.currencyType]+Number(k.taxAmount):Number(k.taxAmount),S[k.currencyType]=S[k.currencyType]?S[k.currencyType]+Number(k.agencyFee):Number(k.agencyFee),i[k.currencyType]=i[k.currencyType]?i[k.currencyType]+Number(k.serviceCharge):Number(k.serviceCharge);const O=Ve(Number(k.amount),Number(k.agencyFee));g[k.currencyType]=g[k.currencyType]?g[k.currencyType]+O:O}),{totalAmount:c,taxAmount:t,agencyFee:S,refundAmount:i,carriers:g}},cn=(h,c)=>{var p;const t=c.addWorksheet(Y.global.t("app.agentReport.refundReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:K1"),t.getRow(1).height=30;const S=t.getCell("A1");S.value=Y.global.t("app.agentReport.refundReport"),S.alignment={vertical:"middle",horizontal:"center"},S.font={size:14,bold:!0};const i=sn(h),g=(i==null?void 0:i.officeNo)??"",k=(i==null?void 0:i.iataNO)??"",O=(i==null?void 0:i.salesDate)??"",D=[`OFFICE:${g}`,null,`IATA NO:${k}`,null,null,`日期:${O}`],A=t.addRow(D);A.font={size:14,bold:!0};const H=t.addRow([]);H.height=9;const L=[],V=[];ln.forEach(l=>{L.push(l.name),V.push({wch:l.wch})}),t.addRow(L).eachCell(l=>{l.font={bold:!0}}),t.columns.forEach((l,v)=>{l.width=V[v].wch});const I=[3,4,5,6,7];(i.items??[]).forEach(l=>{t.addRow(Object.values(l)).eachCell((M,ae)=>{I.includes(ae)&&(M.value=Number(M.value),M.numFmt="0.00",M.alignment={horizontal:"left"})})});const W=t.addRow([]),{totalAmount:f,taxAmount:U,agencyFee:z,refundAmount:G,carriers:j}=rn(h.items.filter(l=>l.ticketStatus==="RFND")),y=["TICKETS REFUND",((p=h.total)==null?void 0:p.totalRefund)??"","REFUND FARE","-- AMOUNT："];for(const l in f)if(f[l]){const v=f[l];y.push(v),y.push(l)}const $=[null,null,"REFUND TAX","-- AMOUNT："];for(const l in U)if(U[l]){const v=U[l];$.push(v),$.push(l)}const P=[null,null,"DEDUCTION","-- AMOUNT："];for(const l in G)if(G[l]){const v=G[l];P.push(v),P.push(l)}const J=[null,null,"COMMIT","-- AMOUNT："];for(const l in z)if(z[l]){const v=z[l];J.push(v),J.push(l)}const te=[null,null,"CARRIERS","-- AMOUNT："];for(const l in j)if(j[l]){const v=j[l];te.push(v),te.push(l)}t.addRow(y),t.addRow(te),t.addRow($),t.addRow(P),t.addRow(J);const le=[1,2,3,5,6],X=W.number+1;for(let l=X;l<=X+4;l++)for(let v=1;v<=11;v++){const M=t.getCell(l,v);le.includes(v)&&(M.alignment={horizontal:"right"}),v==5&&(M.value=Number(M.value),M.numFmt="0.00"),M.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},pn=[{name:Y.global.t("app.agentReport.tktNo"),wch:14.75},{name:Y.global.t("app.agentReport.airline"),wch:8.2},{name:Y.global.t("app.agentReport.ticketMachineNumber"),wch:10.5},{name:"PNR",wch:9.75},{name:Y.global.t("app.agentReport.agent"),wch:8.63},{name:Y.global.t("app.agentReport.tktType"),wch:13.5},{name:Y.global.t("app.agentReport.payment"),wch:8.75}],un=h=>{const c=(h.items??[]).filter(t=>t.ticketStatus==="VOID").map(t=>({ticket:t.ticket,airline:t.airline,deviceNo:t.prntNo,pnr:t.pnr,jobNo:t.jobNo,tktType:t.ticketType,payment:t.payType}));return{officeNo:h==null?void 0:h.office,iataNO:h==null?void 0:h.iata,salesDate:h.items[0].salesDate,items:c}},dn=(h,c)=>{var j;const t=c.addWorksheet(Y.global.t("app.agentReport.voidReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:H1"),t.getRow(1).height=30;const S=t.getCell("A1");S.value=Y.global.t("app.agentReport.voidReport"),S.alignment={vertical:"middle",horizontal:"center"},S.font={size:14,bold:!0};const i=un(h),g=(i==null?void 0:i.officeNo)??"",k=(i==null?void 0:i.iataNO)??"",O=(i==null?void 0:i.salesDate)??"",D=[`OFFICE:${g}`,null,`IATA NO:${k}`,null,null,`日期:${O}`],A=t.addRow(D);A.font={size:14,bold:!0};const H=t.addRow([]);H.height=9;const L=[],V=[];pn.forEach(y=>{L.push(y.name),V.push({wch:y.wch})}),t.addRow(L).eachCell(y=>{y.font={bold:!0}}),t.columns.forEach((y,$)=>{var P;y.width=((P=V[$])==null?void 0:P.wch)??12}),(i.items??[]).forEach(y=>{t.addRow(Object.values(y))});const I=t.addRow([]),W=["TICKETS VOID",((j=h.total)==null?void 0:j.totalVoid)??"","NORMAL FARE","-- AMOUNT：",null,null],f=[null,null,"NORMAL TAX","-- AMOUNT：",null,null],U=[null,null,"NORMAL COMMIT","-- AMOUNT：",null,null];t.addRow(W),t.addRow([null,null,"CARRIERS","-- AMOUNT：",null,null]),t.addRow(f),t.addRow(U);const z=[1,2,3,5,6],G=I.number+1;for(let y=G;y<=G+3;y++)for(let $=1;$<=8;$++){const P=t.getCell(y,$);z.includes($)&&(P.alignment={horizontal:"right"}),$==5&&(P.value=Number(P.value),P.numFmt="0.00"),P.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},mn=h=>{const{t:c}=Nt(),t=_t(),S=_t(),i=F(""),g=F(),k=F(""),O=F(),D=F(),A=F(!0),H=De(()=>{var s;return((s=t.state.user)==null?void 0:s.externalLink)==="SAT"}),L=F(""),V=F(),T=F([]),R=F(),I=F(!1),W=ot({querySaleDailyErrorRes:{},totalNumber:0,issueNumber:0,refundNumber:0}),f=F(),U=F(),z=F(0),G=F(0),j=F(0),y=F([]),$=F(),P=F({}),J=F(0),te=Tt(!1),le=F([]),X=F(!1);let p={};const l=De(()=>{var s;return((s=S.state.user)==null?void 0:s.defaultOfficeInternational)??!1}),v=De(()=>S.getters.userPreferences??{}),M=De(()=>{var s;return((s=S.state.user)==null?void 0:s.entityType)??""}),ae=[{label:"BSP",value:"BSP"},{label:"BOP",value:"BOP"},{label:"CDS",value:"CDS"},{label:"PYN",value:"PYN"},{label:"GP_BSP",value:"GP"}],fe=F([]),ke=[{label:"AL",value:"AL"},{label:"CA",value:"CA"},{label:"CC",value:"CC"},{label:"CK",value:"CK"}],ne=F([]),se=F(h.cmd),N=F({pageNumber:1,pageSize:20});let w=[];const Te=F(!1),_e=F(!1),K=F(!1),Re=F({}),ve=F({}),Ue=s=>!s||s==="",C=ot({searchDate:Pe(new Date).format("YYYY-MM-DD"),prntNo:"",ticketNumber:"",isFilterTime:!0,tktType:"",filterPayType:"AL",filterPayTypeCopy:""}),pt=Tt({render(){return kt("em",{class:"iconfont icon-calendar"})}}),Be=De(()=>Pe(new Date).format("YYYY-MM-DD")===C.searchDate),ut=De(()=>({searchDate:[{required:!0,message:c("app.agentReport.selectDate"),trigger:"change"}],prntNo:[{required:H.value&&(Be.value||I.value)&&!(C.tktType==="BOP"||C.tktType==="GP"||C.tktType==="CDS"),message:c("app.agentReport.printTicketNumber"),trigger:"change"},{pattern:ya,message:c("app.agentReport.deviceNumberErr"),trigger:["change","blur"]}],ticketNum:[{required:!1,message:c("app.agentReport.ticketNumber"),trigger:"change"},{pattern:fa,message:c("app.agentReport.ticketNumTip"),trigger:["change","blur"]}],filterPayType:[{pattern:ga,message:c("app.agentReport.payTypeTip"),trigger:["change","blur"]}]})),dt=()=>{X.value||(C.isFilterTime?y.value=we(le.value):y.value=we(w))};function _(s){return s.ticketNo="",s.passengerName="",s.airlineCode="",s.ticketNoEnd="",s.currency="",s.payMethod="",s.ticketType="",s.segmentInfos={},s.segInfos={},s.cmdNo="",s.cmdOption="",s.operator="",s.office="",s.taxInfo={},s.taxInfos={},s.check="",s.passengerType="",s.conjunction=0,s.couponNo=["0"],s.grossRefund=0,s.refund="Y",s.remark="",s.ticketNoEnd="",s.netRefund=0,s.creditCard="",s.commission=0,s.commissionRate=0,s.deduction=0,s.querySuccess=!1,s.totalTaxs=0,s}const B=s=>{Re.value=s,Te.value=!0},mt=async s=>{const E=document.createElement("input");E.value=s,document.body.appendChild(E),E.select(),document.execCommand("Copy"),E.style.display="none",na({message:c("app.agentReport.tip"),type:"success",duration:2*1e3})},$e=async s=>{$.value=s,_e.value=!0,L.value=s!=null&&s.ticketTypeCode?s.ticketTypeCode:""},He=async s=>{T.value[s].showDropDown(!T.value[s].getVisibleValue())},Ie=async()=>{var s;(s=V.value)==null||s.handleSizeChange(20)},Le=s=>{st.setLink(`/v2/crs/pnrManagement?pnrNo=${s}`)},ze=(s,E)=>{st.setLink(`/v2/crs/ticketOperation?ticketNumber=${s}&secondFactorCode=CN&secondFactorValue=${E??""}`)},we=s=>(s??[]).slice((N.value.pageNumber-1)*N.value.pageSize,N.value.pageNumber*N.value.pageSize),je=()=>{N.value.pageNumber=1,p={},ne.value=[],P.value={},f.value={},w=[]},Ye=s=>{const E=s??[],q={},re={},ge={},de={},ce={amount:{},taxAmount:{},agencyFee:{},carriers:{}},ie={amount:{},taxAmount:{},agencyFee:{},carriers:{}},pe={amount:{},taxAmount:{},agencyFee:{},carriers:{}},ue={amount:{},taxAmount:{},agencyFee:{},carriers:{}};return E.filter(n=>n.currencyType).forEach(n=>{if(n.ticketStatus==="ISSU"){ce.amount[n.currencyType]=ce.amount[n.currencyType]?ce.amount[n.currencyType]+Number(n.amount):Number(n.amount),ce.taxAmount[n.currencyType]=ce.taxAmount[n.currencyType]?ce.taxAmount[n.currencyType]+Number(n.taxAmount):Number(n.taxAmount),ce.agencyFee[n.currencyType]=ce.agencyFee[n.currencyType]?ce.agencyFee[n.currencyType]+Number(n.agencyFee):Number(n.agencyFee);const me=Ve(Number(n.amount),Number(n.agencyFee));ce.carriers[n.currencyType]=ce.carriers[n.currencyType]?ce.carriers[n.currencyType]+me:me}if(n.ticketStatus==="RFND"){ie.amount[n.currencyType]=ie.amount[n.currencyType]?ie.amount[n.currencyType]+Number(n.amount):Number(n.amount),ie.taxAmount[n.currencyType]=ie.taxAmount[n.currencyType]?ie.taxAmount[n.currencyType]+Number(n.taxAmount):Number(n.taxAmount),ie.agencyFee[n.currencyType]=ie.agencyFee[n.currencyType]?ie.agencyFee[n.currencyType]+Number(n.agencyFee):Number(n.agencyFee);const me=Ve(Number(n.amount),Number(n.agencyFee));ie.carriers[n.currencyType]=ie.carriers[n.currencyType]?ie.carriers[n.currencyType]+me:me}if(n.ticketStatus==="EXCH"){pe.amount[n.currencyType]=pe.amount[n.currencyType]?pe.amount[n.currencyType]+Number(n.amount):Number(n.amount),pe.taxAmount[n.currencyType]=pe.taxAmount[n.currencyType]?pe.taxAmount[n.currencyType]+Number(n.taxAmount):Number(n.taxAmount),pe.agencyFee[n.currencyType]=pe.agencyFee[n.currencyType]?pe.agencyFee[n.currencyType]+Number(n.agencyFee):Number(n.agencyFee);const me=Ve(Number(n.amount),Number(n.agencyFee));pe.carriers[n.currencyType]=pe.carriers[n.currencyType]?pe.carriers[n.currencyType]+me:me}if(n.ticketStatus==="VOID"){ue.amount[n.currencyType]=ue.amount[n.currencyType]?ue.amount[n.currencyType]+Number(n.amount):Number(n.amount),ue.taxAmount[n.currencyType]=ue.taxAmount[n.currencyType]?ue.taxAmount[n.currencyType]+Number(n.taxAmount):Number(n.taxAmount),ue.agencyFee[n.currencyType]=ue.agencyFee[n.currencyType]?ue.agencyFee[n.currencyType]+Number(n.agencyFee):Number(n.agencyFee);const me=Ve(Number(n.amount),Number(n.agencyFee));ue.carriers[n.currencyType]=ue.carriers[n.currencyType]?ue.carriers[n.currencyType]+me:me}q[n.currencyType]=q[n.currencyType]?q[n.currencyType]+Number(n.amount):Number(n.amount),re[n.currencyType]=re[n.currencyType]?re[n.currencyType]+Number(n.taxAmount):Number(n.taxAmount),ge[n.currencyType]=ge[n.currencyType]?ge[n.currencyType]+Number(n.agencyFee):Number(n.agencyFee),de[n.currencyType]=de[n.currencyType]?de[n.currencyType]+Number(n.serviceCharge):Number(n.serviceCharge)}),{ISSUA:ce,REFUND:ie,EXCHANGE:pe,VOID:ue,amount:q,taxAmount:re,agencyFee:ge,refundAmount:de,totalTicket:E.filter(n=>n.ticketStatus==="ISSU").length??0,totalVoid:E.filter(n=>n.ticketStatus==="VOID").length??0,totalRefund:E.filter(n=>n.ticketStatus==="RFND").length??0,totalExchange:E.filter(n=>n.ticketStatus==="EXCH").length??0}},Ae=()=>{if(g.value&&g.value.getBoundingClientRect().width!==0){const s=g.value.getBoundingClientRect(),E=y.value.length*50+50,q=`${document.documentElement.clientHeight-77-45-62-150-20-100}`;E>parseInt(q,10)?s.left<100?(k.value=`${document.documentElement.clientHeight-70-100+50+78+20}px`,i.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50+50+78+20-50}`):(k.value=`${document.documentElement.clientHeight-70-100+50+20}px`,i.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50+50+20-50}`):(k.value="100%",i.value=E.toString())}else{const s=y.value.length*50+50,E=`${document.documentElement.clientHeight-77-45-62-150-20-100}`;s>parseInt(E,10)?(k.value=`${document.documentElement.clientHeight-70-100}px`,i.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50}`):(k.value="100%",i.value=s.toString())}},Z=async()=>{var s;X.value=!1,Ie(),(s=O.value)==null||s.validate(async E=>{var ge,de,ce,ie,pe,ue,n,me,oe,Oe,Dt,Ft,At,It,Ot,Pt,Vt,Ut,jt,Mt,Bt,Ht,Lt,zt,Yt,Gt,qt,Qt,Xt,Kt;if(!E)return;je();const q={date:C.searchDate,deviceNumber:C.prntNo,tktType:C.tktType,saleStatusCodes:[],value:"1",outerSign:!1},re={date:C.searchDate,deviceNumber:C.prntNo,tktType:C.tktType,saleStatusCodes:["ISSU","RFND"],value:"2",outerSign:!0};te.value=!0;try{const ht=Rt("09300108");if(C.searchDate!==Pe().format("YYYY-MM-DD")){const xe=await nt(q,ht);f.value=xe.data.value}else{const[xe,Ce]=await Promise.allSettled([nt(q,ht),nt(re,ht)]);xe.status==="fulfilled"&&(f.value=(de=(ge=xe==null?void 0:xe.value)==null?void 0:ge.data)==null?void 0:de.value),Ce.status==="fulfilled"&&(U.value=(ie=(ce=Ce==null?void 0:Ce.value)==null?void 0:ce.data)==null?void 0:ie.value,j.value=(pe=U.value)==null?void 0:pe.totalErrorNumber,z.value=(ue=U.value)==null?void 0:ue.issueErrorNumber,G.value=(n=U.value)==null?void 0:n.refundErrorNumber,W.querySaleDailyErrorRes=U.value,W.totalNumber=j.value,W.issueNumber=z.value,W.refundNumber=G.value)}if(C.ticketNumber||C.filterPayType){const xe=[],Ce=[],Je=[],Ze=[],et=[],tt=[],at=[];C.ticketNumber&&(f.value.items=(me=f==null?void 0:f.value)==null?void 0:me.items.filter(ye=>{var Wt,Jt;return ye.ticket===((Wt=C==null?void 0:C.ticketNumber)!=null&&Wt.includes("-")?C.ticketNumber:(Jt=C.ticketNumber)==null?void 0:Jt.replace(/(\d{3})(\d{7})/,"$1-$2"))}),xe.push((oe=f.value.items[0])==null?void 0:oe.airline),Ce.push((Oe=f.value.items[0])==null?void 0:Oe.ticketStatus),Je.push((Dt=f.value.items[0])==null?void 0:Dt.prntNo),Ze.push((Ft=f.value.items[0])==null?void 0:Ft.jobNo),et.push((At=f.value.items[0])==null?void 0:At.ticketType),tt.push((It=f.value.items[0])==null?void 0:It.payType),at.push((Ot=f.value.items[0])==null?void 0:Ot.currencyType),f.value.filter.airlines=xe,f.value.filter.currencyTypes=at,f.value.filter.jobNos=Ze,f.value.filter.ticketKinds=et,f.value.filter.ticketTypes=Ce,f.value.filter.prntNos=Je,f.value.filter.payTypes=tt),C.filterPayType&&(f.value.items=(C==null?void 0:C.filterPayType)==="AL"?(Pt=f==null?void 0:f.value)==null?void 0:Pt.items:(Vt=f==null?void 0:f.value)==null?void 0:Vt.items.filter(ye=>ye.payType===(C==null?void 0:C.filterPayType)),(C==null?void 0:C.filterPayType)!=="AL"&&(xe.push(...Array.from(new Set((Ut=f.value.items)==null?void 0:Ut.map(ye=>ye.airline)))),Ce.push(...Array.from(new Set((jt=f.value.items)==null?void 0:jt.map(ye=>ye.ticketStatus)))),Je.push(...Array.from(new Set((Mt=f.value.items)==null?void 0:Mt.map(ye=>ye.prntNo)))),Ze.push(...Array.from(new Set((Bt=f.value.items)==null?void 0:Bt.map(ye=>ye.jobNo)))),et.push(...Array.from(new Set((Ht=f.value.items)==null?void 0:Ht.map(ye=>ye.ticketType)))),tt.push(...Array.from(new Set((Lt=f.value.items)==null?void 0:Lt.map(ye=>ye.payType)))),at.push(...Array.from(new Set((zt=f.value.items)==null?void 0:zt.map(ye=>ye.currencyType)))),f.value.filter.airlines=xe,f.value.filter.currencyTypes=at,f.value.filter.jobNos=Ze,f.value.filter.ticketKinds=et,f.value.filter.ticketTypes=Ce,f.value.filter.prntNos=Je,f.value.filter.payTypes=tt))}P.value=Ye((Yt=f.value)==null?void 0:Yt.items);const bt=Me((Gt=f.value)==null?void 0:Gt.items);le.value=bt==null?void 0:bt.sort((xe,Ce)=>new Date(xe.salesTime).getTime()-new Date(Ce.salesTime).getTime()).reverse(),y.value=C.isFilterTime?we(le.value):we((qt=f.value)==null?void 0:qt.items),J.value=(Xt=(Qt=f.value)==null?void 0:Qt.items)==null?void 0:Xt.length,w=((Kt=f.value)==null?void 0:Kt.items)??[],Ae()}finally{te.value=!1}})},yt=(s,E)=>{if(E!=null&&E.length)ne.value.includes(s)||ne.value.push(s);else{const q=ne.value.indexOf(s);q!==-1&&ne.value.splice(q,1)}},ft=(s,E,q)=>{var ge,de,ce,ie,pe,ue,n,me;yt(s,E),X.value=!0;const re=Me(E);q&&re.push(q),p[s]=re,w=((ge=f.value)==null?void 0:ge.items)??[],((de=p==null?void 0:p.airline)==null?void 0:de.length)>0&&(w=w.filter(oe=>p.airline.includes(oe.airline))),((ce=p==null?void 0:p.prntNo)==null?void 0:ce.length)>0&&(w=w.filter(oe=>p.prntNo.includes(oe.prntNo))),((ie=p==null?void 0:p.ticketStatus)==null?void 0:ie.length)>0&&(w=w.filter(oe=>p.ticketStatus.includes(oe.ticketStatus)),w.sort((oe,Oe)=>oe.salesTime.localeCompare(Oe.salesTime))),((pe=p==null?void 0:p.payType)==null?void 0:pe.length)>0&&(w=w.filter(oe=>p.payType.includes(oe.payType))),((ue=p==null?void 0:p.jobNo)==null?void 0:ue.length)>0&&(w=w.filter(oe=>p.jobNo.includes(oe.jobNo))),((n=p==null?void 0:p.ticketType)==null?void 0:n.length)>0&&(w=w.filter(oe=>p.ticketType.includes(oe.ticketType))),((me=p==null?void 0:p.currencyType)==null?void 0:me.length)>0&&(w=w.filter(oe=>p.currencyType.includes(oe.currencyType))),P.value=Ye(w),w=w.sort((oe,Oe)=>new Date(oe.salesTime).getTime()-new Date(Oe.salesTime).getTime()).reverse(),N.value.pageNumber=1,y.value=we(w),J.value=w.length,Ae()},gt=(s,E)=>{N.value.pageNumber=s===0?1:s,N.value.pageSize=E,C.isFilterTime&&!X.value?y.value=we(le.value):y.value=we(w),Ae()},Ge=[{name:c("app.agentReport.airline"),wch:17},{name:c("app.agentReport.tktNo"),wch:21},{name:c("app.agentReport.tktStatus"),wch:19},{name:c("app.agentReport.deviceNo"),wch:12},{name:c("app.agentReport.agent"),wch:8},{name:c("app.agentReport.segSE"),wch:12},{name:c("app.agentReport.tktSettle"),wch:14},{name:c("app.agentReport.tax"),wch:10},{name:c("app.agentReport.obTax"),wch:10},{name:c("app.agentReport.agency"),wch:12},{name:c("app.agentReport.agencyRate"),wch:12},{name:c("app.agentReport.handFee"),wch:12},{name:c("app.agentReport.refundNo"),wch:12},{name:"PNR",wch:8},{name:c("app.agentReport.payment"),wch:10},{name:c("app.agentReport.tktSymbol"),wch:10},{name:c("app.agentReport.saleDate"),wch:14},{name:c("app.agentReport.salesTime"),wch:14},{name:c("app.agentReport.curryType"),wch:12},{name:c("app.agentReport.tktType"),wch:18}],qe=s=>{const E=new Ta.Workbook;on({...f.value,items:w,total:P.value},E),cn({...f.value,items:w,total:P.value},E),dn({...f.value,items:w,total:P.value},E),E.xlsx.writeBuffer().then(q=>{const re=new Blob([q],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),ge=URL.createObjectURL(re),de=document.createElement("a");de.href=ge,de.download=`${s}.xlsx`,de.click(),URL.revokeObjectURL(ge)})},Qe=()=>{const s=[],E=[];Ge.forEach(q=>{s.push(q.name),E.push({wch:q.wch})}),qe(`${C.searchDate}销售日报`)},Xe=s=>{if(s)switch(s){case"全部":C.filterPayType="AL";break;case"(CA) 现金":C.filterPayType="CA";break;case"(CC) 信用卡":C.filterPayType="CC";break;case"(CK) 支票":C.filterPayType="CK";break;default:C.filterPayType=s;break}else C.filterPayType=C.filterPayTypeCopy;C.filterPayType=C.filterPayType.trim().toUpperCase()},Ke=()=>{C.filterPayTypeCopy=C.filterPayType},We=async()=>{await ea.confirm(c("app.agentReport.confirmExport"),"",{icon:kt("em",{class:"iconfont icon-info-circle-line"}),customClass:"sales-daily-msg-box crs-btn-ui",confirmButtonText:c("app.agentReport.confirm"),cancelButtonText:c("app.agentReport.cancel"),showClose:!1,autofocus:!1}),(w??[]).length<1?await ea.alert(c("app.agentReport.dataNoEmpty"),{icon:kt("em",{class:"iconfont icon-info-circle-line"}),customClass:"iconStyle",showConfirmButton:!1,type:"warning"}):Qe()};lt(()=>{window.addEventListener("resize",()=>{Ae()}),Ae(),Ie(),_(Re.value)});const o=()=>{var ge,de;if(!se.value||!se.value.includes("-"))return;const s=se.value.split("-")[0],E=((de=(ge=s==null?void 0:s.substring(s.indexOf("TSL:")+4))==null?void 0:ge.trim())==null?void 0:de.split("/"))??[],q=E[0]??"",re=E[1];C.searchDate=Pe(re?_a(re):new Date).format("YYYY-MM-DD"),C.prntNo=q,Z()},x=async()=>{K.value=!0},ia=(s,E,q)=>{j.value=s,z.value=E,G.value=q},pa=(s,E)=>{if(E==="")return[];if(E==="$$$")return s;const q=E.split(";").map(re=>re.trim()==="本票"?"PYN":re.trim().toUpperCase());return s.filter(re=>q.includes(re.value))};return Zt(se,()=>{o()}),Zt(v,()=>{var s;l.value&&(C.prntNo=((s=v.value)==null?void 0:s.internationalPrinterno)??"")}),lt(async()=>{var E,q;l.value&&(C.prntNo=((E=v.value)==null?void 0:E.internationalPrinterno)??""),fe.value=pa(ae,M.value),C.tktType=((q=fe.value[0])==null?void 0:q.label)??"";const s=await ha({code:"BUSINESS.DAILYSALES.SWITCH"},"09300114");I.value=((s==null?void 0:s.data)??"")==="1",H.value||(Z(),o())}),{isSatLink:H,salesForm:C,filterTypeList:ne,querySaleDailyRes:f,salesDaliyDatas:y,rowSalesDaliyData:$,salesRules:ut,handleSearch:Z,salesRef:O,daliyTableRef:R,handleChangePage:gt,totalInfo:P,handleExport:We,filterChange:ft,pageTotal:J,isEmptyData:Ue,pageInfo:N,refundTicket:Re,showDialog:Te,showTodayExceptionsDialog:K,showDialogFun:$e,showPrintNoDialog:_e,jumpToPnrEtQuery:Le,jumpToTcTicketQuery:ze,doCopy:mt,params:ve,innerHeight:i,wrapHeight:k,salesDailyRef:g,openFilter:He,filterRptRef:T,updatePageSizeNum:Ie,pageRef:V,isSalesDaily:A,datePrefix:pt,isLoading:te,filterSaleTime:dt,handleTodayException:x,tktTypes:fe,isToday:Be,totalNumber:j,issueNumber:z,refundNumber:G,getErrorNumber:ia,storeTodayError:W,payTypes:ke,filterPayTypeRef:D,selectPayTypeBlur:Xe,selectPayTypeFocus:Ke,setRefundData:B,refundTicketType:L,hasDailySalesAuth:I}},yn=(h,c)=>{const{t}=Nt(),S=_t(),i=De(()=>{var U;return(U=S.state.user)==null?void 0:U.entityType}),g=F(),k=De(()=>c.rowSalesDaliyData),O=F(!1),D=ot({printerNo:"",ticketOrganization:""}),A={ticketOrganization:[{required:!0,message:t("app.ticketStatus.deviceNumNull"),trigger:"blur"}],printerNo:[{required:!0,message:t("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:Ra,trigger:"blur",message:t("app.ticketStatus.deviceError")}]},H=F([]),L={BSP:{label:t("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:t("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:t("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:t("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:t("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:t("app.agentTicketQuery.OWNTicket"),value:"ARL"}},V=De(()=>!["CDS","GPCDS"].includes(D.ticketOrganization)),T=()=>{g.value.validate(U=>{!U&&k.value||I("",k.value.refundNo,k.value.ticketTypeCode=="D",D.printerNo,D.ticketOrganization)})},R=(U,z,G,j,y)=>({ticketNo:U,refundNo:z,ticketType:G?"D":"I",printerNo:j,ticketManagementOrganizationCode:y}),I=async(U,z,G,j,y)=>{var $;try{O.value=!0;const P=Rt("09200108"),{data:J}=await Ca(R(U,z,G,j,y),P);h("setRefundData",($=J.value)==null?void 0:$.data),h("update:modelValue",!1),O.value=!1}finally{O.value=!1}},W=()=>{h("update:modelValue",!1)},f=()=>{var U,z,G,j,y,$,P,J,te,le,X,p;((U=i.value)!=null&&U.includes("$$$")||(z=i.value)!=null&&z.includes("BSP"))&&(H.value.push(L.BSP),H.value.push(L.GPBSP)),!((G=i.value)!=null&&G.includes("BSP"))&&((j=i.value)!=null&&j.includes("GP"))&&H.value.push(L.GPBSP),((y=i.value)!=null&&y.includes("$$$")||($=i.value)!=null&&$.includes("BOP"))&&H.value.push(L.BOPBSP),((P=i.value)!=null&&P.includes("$$$")||(J=i.value)!=null&&J.includes("CDS"))&&(H.value.push(L.CDS),H.value.push(L.GPCDS)),((te=i.value)!=null&&te.includes("$$$")||(le=i.value)!=null&&le.includes("本票"))&&H.value.push(L.ARL),D.ticketOrganization=((p=(X=H.value)==null?void 0:X[0])==null?void 0:p.value)??""};return lt(()=>{f(),D.printerNo=k.value.prntNo}),{formDate:g,printNoFrom:D,PRINTER_NO_RULES:A,ticketOrganizationList:H,isShowPrintNo:V,confirmPrinterNo:T,closeDialog:W,loading:O}},fn=yn,gn=a("i",{class:"iconfont icon-close"},null,-1),hn=[gn],bn={class:"carType-option-panel"},kn=rt({__name:"RefundParameterDialog",props:{rowSalesDaliyData:{}},emits:["update:modelValue","setRefundData"],setup(h,{emit:c}){const t=c,S=h,{formDate:i,printNoFrom:g,PRINTER_NO_RULES:k,ticketOrganizationList:O,isShowPrintNo:D,confirmPrinterNo:A,closeDialog:H,loading:L}=fn(t,S);return(V,T)=>{const R=xt,I=ct,W=it,f=$t,U=Et,z=St,G=ca,j=wt;return r(),be(G,{title:V.$t("app.queryRefunds.selectPrint"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(H)},{footer:b(()=>[a("div",null,[Ct((r(),be(z,{type:"primary","data-gid":"091T0107",onClick:T[4]||(T[4]=y=>e(A)())},{default:b(()=>[he(u(V.$t("app.ticketStatus.confirmBtn")),1)]),_:1})),[[j,e(L),void 0,{fullscreen:!0,lock:!0}]]),m(z,{onClick:e(H)},{default:b(()=>[he(u(V.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:b(()=>[a("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:T[0]||(T[0]=(...y)=>e(H)&&e(H)(...y))},hn),m(U,{ref_key:"formDate",ref:i,model:e(g),rules:e(k),"label-position":"left","require-asterisk-position":"right"},{default:b(()=>[m(f,{prop:"ticketOrganization",label:V.$t("app.agentTicketQuery.ticketOrganization")},{default:b(()=>[m(W,{modelValue:e(g).ticketOrganization,"onUpdate:modelValue":T[1]||(T[1]=y=>e(g).ticketOrganization=y),class:"ticket-management-organization",disabled:e(g).ticketOrganization==="",placeholder:e(g).ticketOrganization===""?V.$t("app.agentTicketQuery.noData"):""},{default:b(()=>[(r(!0),d(Q,null,ee(e(O),y=>(r(),be(I,{key:y.value,label:y.label,value:y.value},{default:b(()=>[a("div",bn,[a("div",{class:Ne(e(g).ticketOrganization===y.value?"show-select":"hidden-select")},[m(R,null,{default:b(()=>[m(e(aa))]),_:1})],2),a("span",null,u(y.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label"]),e(D)?(r(),be(f,{key:0,prop:"printerNo",label:V.$t("app.ticketStatus.deviceNum")},{default:b(()=>[m(Na,{modelValue:e(g).printerNo,"onUpdate:modelValue":[T[2]||(T[2]=y=>e(g).printerNo=y),T[3]||(T[3]=y=>e(i).validateField("printerNo"))],"select-class":"w-[340px]"},null,8,["modelValue"])]),_:1},8,["label"])):Ee("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const Fe=h=>(ka("data-v-bf0b6b03"),h=h(),va(),h),vn={class:"sales-daily-flex"},Tn={key:0,class:"card-checked"},_n={key:1,class:"card-no-checked"},xn={key:0,class:"card-checked"},Nn={key:1,class:"card-no-checked"},Rn={class:"inline-block todayAbnormal"},Cn={key:1,class:"text-red-1 inline-block bg-red-3 leading-8 text-sm height-[32px] error-tip"},Sn={key:0,class:"result"},wn={class:"search-info min-w-[380px]"},$n=Fe(()=>a("span",{class:"search-info-title"},"OFFICE：",-1)),En=Fe(()=>a("span",{class:"search-info-title space-span"},"IATA NO：",-1)),Dn={class:"search-info-title space-span"},Fn={key:0},An=["onClick"],In=["onClick"],On={key:1},Pn=["onClick"],Vn=["onClick"],Un={key:1},jn=["onClick"],Mn=["onClick"],Bn=Fe(()=>a("div",{class:"w-[100%] h-[135px] bg-white"},null,-1)),Hn={class:"gap-5 shadow-[0px_-2px_7px_0px_rgba(0,0,0,0.04)] bottom-wrap"},Ln={class:"bottom-wrap-title"},zn={class:"bottom-wrap-title-item"},Yn={class:"min-w-[220px] h-[22px] bg-[#f3ffe9] rounded-sm justify-start items-start gap-1 flex"},Gn=Fe(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#d9f7be] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#237804] text-sm font-normal"},"TICKETS ISSU ")],-1)),qn={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},Qn={class:"min-w-[220px] h-[22px] bg-[#fff7f7] rounded-sm justify-start items-start gap-1 flex"},Xn=Fe(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#fce6e6] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#ff3636] text-sm font-normal"},"TICKETS REFUND ")],-1)),Kn={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},Wn={class:"min-w-[220px] h-[22px] bg-[#fff7de] rounded-sm justify-start items-start gap-1 flex"},Jn=Fe(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#feeeb9] rounded-sm justify-start items-center flex"},[a("div",{class:"text-center text-[#d48806] text-sm font-normal"},"TICKETS EXCHANGE ")],-1)),Zn={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},eo={class:"min-w-[220px] h-[22px] bg-[#ebf2ff] rounded-sm justify-start items-start gap-1 flex"},to=Fe(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#d6e4ff] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#143889] text-sm font-normal"},"TICKETS VOID")],-1)),ao={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},no={class:"bottom-wrap-desc gap-[30px]"},oo={class:"bottom-wrap-desc-item"},lo={class:"text-[#8c8c8c] text-sm font-normal"},so={class:"!gap-2.5"},ro={key:0,class:"text-neutral-800 text-sm font-normal"},co={class:"text-[#8c8c8c] text-sm font-normal"},io={class:"!gap-2.5"},po={key:0,class:"text-neutral-800 text-sm font-normal"},uo={class:"text-[#8c8c8c] text-sm font-normal"},mo={class:"!gap-2.5"},yo={key:0,class:"text-neutral-800 text-sm font-normal"},fo={class:"text-[#8c8c8c] text-sm font-normal"},go={class:"!gap-2.5"},ho={key:0,class:"text-neutral-800 text-sm font-normal"},bo={class:"bottom-wrap-desc-item"},ko={class:"text-[#8c8c8c] text-sm font-normal"},vo={class:"!gap-2.5"},To={key:0,class:"text-neutral-800 text-sm font-normal"},_o={class:"text-[#8c8c8c] text-sm font-normal"},xo={class:"!gap-2.5"},No={key:0,class:"text-neutral-800 text-sm font-normal"},Ro={class:"text-[#8c8c8c] text-sm font-normal"},Co={class:"!gap-2.5"},So={key:0,class:"text-neutral-800 text-sm font-normal"},wo={class:"text-[#8c8c8c] text-sm font-normal"},$o={class:"!gap-2.5"},Eo={key:0,class:"text-neutral-800 text-sm font-normal"},Do={class:"bottom-wrap-desc-item"},Fo={class:"text-[#8c8c8c] text-sm font-normal"},Ao={class:"!gap-2.5"},Io={key:0,class:"text-neutral-800 text-sm font-normal"},Oo={class:"text-[#8c8c8c] text-sm font-normal"},Po={class:"!gap-2.5"},Vo={key:0,class:"text-neutral-800 text-sm font-normal"},Uo={class:"text-[#8c8c8c] text-sm font-normal"},jo={class:"!gap-2.5"},Mo={key:0,class:"text-neutral-800 text-sm font-normal"},Bo={class:"text-[#8c8c8c] text-sm font-normal"},Ho={class:"!gap-2.5"},Lo={key:0,class:"text-neutral-800 text-sm font-normal"},zo={class:"bottom-wrap-desc-item"},Yo={class:"text-[#8c8c8c] text-sm font-normal"},Go={class:"!gap-2.5"},qo={key:0,class:"text-neutral-800 text-sm font-normal"},Qo={class:"text-[#8c8c8c] text-sm font-normal"},Xo={class:"!gap-2.5"},Ko={key:0,class:"text-neutral-800 text-sm font-normal"},Wo={class:"text-[#8c8c8c] text-sm font-normal"},Jo={class:"!gap-2.5"},Zo={key:0,class:"text-neutral-800 text-sm font-normal"},el={class:"text-[#8c8c8c] text-sm font-normal"},tl={class:"!gap-2.5"},al={key:0,class:"text-neutral-800 text-sm font-normal"},nl={class:"bottom-wrap-desc-item"},ol=Fe(()=>a("div",null,null,-1)),ll={class:"text-[#8c8c8c] text-sm font-normal"},sl={class:"!gap-2.5"},rl={key:0,class:"text-neutral-800 text-sm font-normal"},cl={key:1,class:"empty-info"},il=Fe(()=>a("img",{src:ta,alt:"$t('app.agentReport.nodata')"},null,-1)),pl={class:"main-info"},ul={name:"SalesDaily"},dl=rt({...ul,props:{cmd:{}},setup(h){const c=h,{isSatLink:t,salesForm:S,salesRef:i,filterTypeList:g,daliyTableRef:k,querySaleDailyRes:O,salesDaliyDatas:D,rowSalesDaliyData:A,salesRules:H,handleSearch:L,filterChange:V,handleChangePage:T,totalInfo:R,handleExport:I,pageTotal:W,salesDailyRef:f,filterSaleTime:U,showPrintNoDialog:z,isEmptyData:G,pageInfo:j,refundTicket:y,showDialog:$,handleTodayException:P,showTodayExceptionsDialog:J,doCopy:te,params:le,openFilter:X,filterRptRef:p,pageRef:l,jumpToPnrEtQuery:v,jumpToTcTicketQuery:M,isLoading:ae,isSalesDaily:fe,datePrefix:ke,showDialogFun:ne,tktTypes:se,isToday:N,totalNumber:w,issueNumber:Te,refundNumber:_e,getErrorNumber:K,storeTodayError:Re,payTypes:ve,filterPayTypeRef:Ue,selectPayTypeBlur:C,selectPayTypeFocus:pt,setRefundData:Be,refundTicketType:ut,hasDailySalesAuth:dt}=mn(c);return(_,B)=>{var Ge,qe,Qe,Xe,Ke,We;const mt=wa,$e=$t,He=ba("Select"),Ie=xt,Le=ct,ze=it,we=oa,je=St,Ye=$a,Ae=Et,Z=la,yt=ra,ft=sa,gt=wt;return Ct((r(),d("div",{ref_key:"salesDailyRef",ref:f,class:"crs-new-ui-init-cls sales-daily crs-btn-ui h-[100%]"},[a("div",vn,[m(Ae,{ref_key:"salesRef",ref:i,model:e(S),inline:!0,rules:e(H),"require-asterisk-position":"right",class:"sale-form"},{default:b(()=>[m($e,{label:_.$t("app.agentReport.date"),prop:"searchDate",class:"title-label"},{default:b(()=>[m(mt,{modelValue:e(S).searchDate,"onUpdate:modelValue":B[0]||(B[0]=o=>e(S).searchDate=o),editable:!1,type:"date",clearable:!1,"prefix-icon":e(ke),"value-format":"YYYY-MM-DD"},null,8,["modelValue","prefix-icon"])]),_:1},8,["label"]),e(t)&&(e(N)||e(dt))?(r(),be($e,{key:0,label:_.$t("app.agentReport.tktType")},{default:b(()=>[m(ze,{modelValue:e(S).tktType,"onUpdate:modelValue":B[1]||(B[1]=o=>e(S).tktType=o),class:"card-type-select","popper-class":"select_card_popper"},{default:b(()=>[(r(!0),d(Q,null,ee(e(se),o=>(r(),be(Le,{key:o.value,label:_.$t(`app.agentReport.${o.label}`),value:o.value},{default:b(()=>[e(S).tktType===o.value?(r(),d("span",Tn,[m(Ie,{size:14},{default:b(()=>[m(He)]),_:1})])):(r(),d("span",_n)),a("span",null,u(_.$t(`app.agentReport.${o.label}`)),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):Ee("",!0),m($e,{label:_.$t("app.agentReport.ticketMachineNumber"),prop:"prntNo",class:"print-ticket-number"},{default:b(()=>[m(we,{modelValue:e(S).prntNo,"onUpdate:modelValue":B[2]||(B[2]=o=>e(S).prntNo=o),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:_.$t("app.agentReport.printTicketNumber")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),m($e,{label:_.$t("app.agentReport.ticketNumber"),prop:"ticketNum",class:"print-ticket-number"},{default:b(()=>[m(we,{modelValue:e(S).ticketNumber,"onUpdate:modelValue":B[3]||(B[3]=o=>e(S).ticketNumber=o),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:_.$t("app.agentReport.ticketNumHolder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),m($e,{label:_.$t("app.queryRefunds.payType"),prop:"filterPayType",class:"print-ticket-number"},{default:b(()=>[m(ze,{ref_key:"filterPayTypeRef",ref:Ue,modelValue:e(S).filterPayType,"onUpdate:modelValue":B[4]||(B[4]=o=>e(S).filterPayType=o),modelModifiers:{trim:!0},"popper-class":"select_card_popper",clearable:"",filterable:"","allow-create":"","default-first-option":"","reserve-keyword":!1,style:{width:"240px"},onChange:e(C),onVisibleChange:e(pt)},{default:b(()=>[(r(!0),d(Q,null,ee(e(ve),o=>(r(),be(Le,{key:o.value,label:_.$t(`app.agentReport.${o.label}`),value:o.value},{default:b(()=>[e(S).filterPayType===o.value?(r(),d("span",xn,[m(Ie,{size:14},{default:b(()=>[m(He)]),_:1})])):(r(),d("span",Nn)),a("span",null,u(_.$t(`app.agentReport.${o.label}`)),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","onChange","onVisibleChange"])]),_:1},8,["label"]),m($e,null,{default:b(()=>[m(je,{"data-gid":"11280201",type:"primary",onClick:e(L)},{default:b(()=>[he(u(_.$t("app.agentReport.search")),1)]),_:1},8,["onClick"])]),_:1}),a("div",Rn,[e(t)?(r(),be(je,{key:0,class:"today-error",type:"error",onClick:e(P)},{default:b(()=>[he(u(_.$t("app.agentReport.todayAbnormal")),1)]),_:1},8,["onClick"])):Ee("",!0),e(t)?(r(),d("span",Cn,u(_.$t("app.agentReport.totalError",{total:e(w),issueError:e(Te),refundError:e(_e)})),1)):Ee("",!0)]),m($e,{label:_.$t("app.agentReport.sortedTime")},{default:b(()=>[m(Ye,{modelValue:e(S).isFilterTime,"onUpdate:modelValue":B[5]||(B[5]=o=>e(S).isFilterTime=o),"inline-prompt":"","active-text":_.$t("app.queryRefunds.yes"),"inactive-text":_.$t("app.queryRefunds.no"),onChange:e(U)},null,8,["modelValue","active-text","inactive-text","onChange"])]),_:1},8,["label"])]),_:1},8,["model","rules"]),(Ge=e(O))!=null&&Ge.office?(r(),d("div",Sn,[a("div",wn,[$n,he(u((qe=e(O))==null?void 0:qe.office)+" ",1),En,he(u((Qe=e(O))==null?void 0:Qe.iata)+" ",1),a("span",Dn,u(_.$t("app.agentReport.saleDate"))+"：",1),he(u((Xe=e(O))!=null&&Xe.items[0]?(Ke=e(O))==null?void 0:Ke.items[0].salesDate:e(S).searchDate),1)])])):Ee("",!0),m(je,{class:"export-btn","data-gid":"11280202",onClick:e(I)},{default:b(()=>[he(u(_.$t("app.agentReport.export")),1)]),_:1},8,["onClick"])]),(We=e(O))!=null&&We.office?(r(),d("div",Fn,[m(ft,{ref_key:"daliyTableRef",ref:k,data:e(D),stripe:"",style:{"min-width":"100%"},class:"sales-daily-table",onFilterChange:e(V)},{default:b(()=>[m(Z,{prop:"airline",width:"60px",fixed:"left"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(g).includes("airline")?"text-brand-2":""]),onClick:B[6]||(B[6]=o=>e(X)(0))},u(_.$t("app.agentReport.airline")),3),m(Se,{ref:o=>{o&&(e(p)[0]=o)},filters:e(O).filter.airlines,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":_.$t("app.agentReport.agentTip"),"column-key":"airline",onHandleConfrim:e(V)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),m(Z,{prop:"ticket",label:_.$t("app.agentReport.tktNo"),width:"150px",flexible:"true",fixed:"left"},{default:b(({row:o})=>[a("span",{class:"pointer-span",onClick:x=>e(M)(o.ticket,o.pnr)},u(o.ticket),9,An),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:x=>e(te)(o.ticket)},null,8,In)]),_:1},8,["label"]),m(Z,{prop:"ticketStatus",width:"88px",fixed:"left"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(g).includes("ticketStatus")?"text-brand-2":""]),onClick:B[7]||(B[7]=o=>e(X)(1))},u(_.$t("app.agentReport.tktStatus")),3),m(Se,{ref:o=>{o&&(e(p)[1]=o)},filters:e(O).filter.ticketTypes,"column-key":"ticketStatus",onHandleConfrim:e(V)},null,8,["filters","onHandleConfrim"])]),default:b(({row:o})=>[m(yt,{class:Ne(`${o.ticketStatus}-tag`)},{default:b(()=>[he(u(o.ticketStatus),1)]),_:2},1032,["class"])]),_:1}),m(Z,{prop:"prntNo",width:"84px",fixed:""},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(g).includes("prntNo")?"text-brand-2":""]),onClick:B[8]||(B[8]=o=>e(X)(2))},u(_.$t("app.agentReport.ticketMachineNumber")),3),m(Se,{ref:o=>{o&&(e(p)[2]=o)},filters:e(O).filter.prntNos,"column-key":"prntNo",onHandleConfrim:e(V)},null,8,["filters","onHandleConfrim"])]),_:1}),m(Z,{prop:"jobNo","min-width":"80px"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(g).includes("jobNo")?"text-brand-2":""]),onClick:B[9]||(B[9]=o=>e(X)(3))},u(_.$t("app.agentReport.agent")),3),m(Se,{ref:o=>{o&&(e(p)[3]=o)},filters:e(O).filter.jobNos,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":_.$t("app.agentReport.agentTip"),"column-key":"jobNo",onHandleConfrim:e(V)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),m(Z,{prop:"desArr",label:_.$t("app.agentReport.segSE"),"min-width":"90px"},null,8,["label"]),m(Z,{prop:"amount",label:_.$t("app.agentReport.tktSettle"),"min-width":"80px"},null,8,["label"]),m(Z,{prop:"taxAmount",label:_.$t("app.agentReport.tax"),"min-width":"80px"},null,8,["label"]),m(Z,{prop:"obTax",label:_.$t("app.agentReport.obTax"),"min-width":"80px"},{default:b(({row:o})=>[o.obTax==="0"?(r(),d(Q,{key:0},[he("0.00")],64)):(r(),d(Q,{key:1},[he(u(o.obTax),1)],64))]),_:1},8,["label"]),m(Z,{prop:"agencyFee",label:_.$t("app.agentReport.agency"),"min-width":"75px"},null,8,["label"]),m(Z,{prop:"agencyFeePercent",label:_.$t("app.agentReport.agencyRate"),"min-width":"75px"},null,8,["label"]),m(Z,{prop:"serviceCharge",label:_.$t("app.agentReport.handFee"),"min-width":"75px"},null,8,["label"]),m(Z,{prop:"refundNo",label:_.$t("app.agentReport.refundNo"),"min-width":"120px"},{default:b(({row:o})=>[e(G)(o.refundNo)?(r(),d(Q,{key:0},[he(" - ")],64)):(r(),d("div",On,[a("span",{class:"pointer-span",onClick:x=>e(ne)(o)},u(o.refundNo),9,Pn),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:x=>e(te)(o.refundNo)},null,8,Vn)]))]),_:1},8,["label"]),m(Z,{prop:"pnr",label:"PNR","min-width":"105px"},{default:b(({row:o})=>[e(G)(o.pnr)?(r(),d(Q,{key:0},[],64)):(r(),d("div",Un,[a("span",{class:"pointer-span",onClick:x=>e(v)(o.pnr)},u(o.pnr),9,jn),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:x=>e(te)(o.pnr)},null,8,Mn)]))]),_:1}),m(Z,{prop:"ticketType","min-width":"125px"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(g).includes("ticketType")?"text-brand-2":""]),onClick:B[10]||(B[10]=o=>e(X)(6))},u(_.$t("app.agentReport.tktType")),3),m(Se,{ref:o=>{o&&(e(p)[6]=o)},filters:e(O).filter.ticketKinds,"column-key":"ticketType",onHandleConfrim:e(V)},null,8,["filters","onHandleConfrim"])]),_:1}),m(Z,{prop:"payType","min-width":"100px"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(g).includes("payType")?"text-brand-2":""]),onClick:B[11]||(B[11]=o=>e(X)(4))},u(_.$t("app.agentReport.payment")),3),m(Se,{ref:o=>{o&&(e(p)[4]=o)},filters:e(O).filter.payTypes,"column-key":"payType",onHandleConfrim:e(V)},null,8,["filters","onHandleConfrim"])]),_:1}),m(Z,{prop:"couponNo",label:_.$t("app.agentReport.tktSymbol")},null,8,["label"]),m(Z,{prop:"currencyType","min-width":"75px"},{header:b(()=>[a("span",{class:Ne(["pointer-span-drop",e(g).includes("currencyType")?"text-brand-2":""]),onClick:B[12]||(B[12]=o=>e(X)(5))},u(_.$t("app.agentReport.curryType")),3),m(Se,{ref:o=>{o&&(e(p)[5]=o)},filters:e(O).filter.currencyTypes,"column-key":"currencyType",onHandleConfrim:e(V)},null,8,["filters","onHandleConfrim"])]),_:1})]),_:1},8,["data","onFilterChange"]),m(e(Sa),{ref:o=>{o&&(l.value=o)},class:"sales-daily-page crs-pagination-ui",total:e(W),"current-page":e(j).pageNumber,"page-size":20,onHandleChange:e(T)},null,8,["total","current-page","onHandleChange"]),Bn,a("div",Hn,[a("div",Ln,[a("div",zn,[a("div",null,[a("div",Yn,[Gn,a("div",qn,u(e(R).totalTicket),1)])]),a("div",null,[a("div",Qn,[Xn,a("div",Kn,u(e(R).totalRefund),1)])]),a("div",null,[a("div",Wn,[Jn,a("div",Zn,u(e(R).totalExchange),1)])]),a("div",null,[a("div",eo,[to,a("div",ao,u(e(R).totalVoid),1)])])])]),a("div",no,[a("div",oo,[a("div",null,[a("label",lo,u(_.$t("app.agentReport.tktSettleBottom")),1),a("div",so,[e(D).length<1||Object.keys(e(R).ISSUA.amount).length===0?(r(),d("span",ro,"0")):(r(!0),d(Q,{key:1},ee(e(R).ISSUA.amount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",co,u(_.$t("app.agentReport.tktSettleBottom")),1),a("div",io,[e(D).length<1||Object.keys(e(R).REFUND.amount).length===0?(r(),d("span",po,"0")):(r(!0),d(Q,{key:1},ee(e(R).REFUND.amount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",uo,u(_.$t("app.agentReport.tktSettleBottom")),1),a("div",mo,[e(D).length<1||Object.keys(e(R).EXCHANGE.amount).length===0?(r(),d("span",yo,"0")):(r(!0),d(Q,{key:1},ee(e(R).EXCHANGE.amount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",fo,u(_.$t("app.agentReport.tktSettleBottom")),1),a("div",go,[e(D).length<1||Object.keys(e(R).VOID.amount).length===0?(r(),d("span",ho,"0")):(r(!0),d(Q,{key:1},ee(e(R).VOID.amount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])])]),a("div",bo,[a("div",null,[a("label",ko,u(_.$t("app.agentReport.taxBottom")),1),a("div",vo,[e(D).length<1||Object.keys(e(R).ISSUA.taxAmount).length===0?(r(),d("span",To,"0")):(r(!0),d(Q,{key:1},ee(e(R).ISSUA.taxAmount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",_o,u(_.$t("app.agentReport.taxBottom")),1),a("div",xo,[e(D).length<1||Object.keys(e(R).REFUND.taxAmount).length===0?(r(),d("span",No,"0")):(r(!0),d(Q,{key:1},ee(e(R).REFUND.taxAmount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Ro,u(_.$t("app.agentReport.taxBottom")),1),a("div",Co,[e(D).length<1||Object.keys(e(R).EXCHANGE.taxAmount).length===0?(r(),d("span",So,"0")):(r(!0),d(Q,{key:1},ee(e(R).EXCHANGE.taxAmount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",wo,u(_.$t("app.agentReport.taxBottom")),1),a("div",$o,[e(D).length<1||Object.keys(e(R).VOID.taxAmount).length===0?(r(),d("span",Eo,"0")):(r(!0),d(Q,{key:1},ee(e(R).VOID.taxAmount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])])]),a("div",Do,[a("div",null,[a("label",Fo,u(_.$t("app.agentReport.agencyBottom")),1),a("div",Ao,[e(D).length<1||Object.keys(e(R).ISSUA.agencyFee).length===0?(r(),d("span",Io,"0")):(r(!0),d(Q,{key:1},ee(e(R).ISSUA.agencyFee,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Oo,u(_.$t("app.agentReport.agencyBottom")),1),a("div",Po,[e(D).length<1||Object.keys(e(R).REFUND.agencyFee).length===0?(r(),d("span",Vo,"0")):(r(!0),d(Q,{key:1},ee(e(R).REFUND.agencyFee,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Uo,u(_.$t("app.agentReport.agencyBottom")),1),a("div",jo,[e(D).length<1||Object.keys(e(R).EXCHANGE.agencyFee).length===0?(r(),d("span",Mo,"0")):(r(!0),d(Q,{key:1},ee(e(R).EXCHANGE.agencyFee,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Bo,u(_.$t("app.agentReport.agencyBottom")),1),a("div",Ho,[e(D).length<1||Object.keys(e(R).VOID.agencyFee).length===0?(r(),d("span",Lo,"0")):(r(!0),d(Q,{key:1},ee(e(R).VOID.agencyFee,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])])]),a("div",zo,[a("div",null,[a("label",Yo,u(_.$t("app.agentReport.carriers")),1),a("div",Go,[e(D).length<1||Object.keys(e(R).ISSUA.carriers).length===0?(r(),d("span",qo,"0")):(r(!0),d(Q,{key:1},ee(e(R).ISSUA.carriers,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Qo,u(_.$t("app.agentReport.carriers")),1),a("div",Xo,[e(D).length<1||Object.keys(e(R).REFUND.carriers).length===0?(r(),d("span",Ko,"0")):(r(!0),d(Q,{key:1},ee(e(R).REFUND.carriers,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Wo,u(_.$t("app.agentReport.carriers")),1),a("div",Jo,[e(D).length<1||Object.keys(e(R).EXCHANGE.carriers).length===0?(r(),d("span",Zo,"0")):(r(!0),d(Q,{key:1},ee(e(R).EXCHANGE.carriers,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",el,u(_.$t("app.agentReport.carriers")),1),a("div",tl,[e(D).length<1||Object.keys(e(R).VOID.carriers).length===0?(r(),d("span",al,"0")):(r(!0),d(Q,{key:1},ee(e(R).VOID.carriers,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])])]),a("div",nl,[ol,a("div",null,[a("label",ll,u(_.$t("app.agentReport.handFee")),1),a("div",sl,[e(D).length<1||Object.keys(e(R).refundAmount).length===0?(r(),d("span",rl,"0")):(r(!0),d(Q,{key:1},ee(e(R).refundAmount,(o,x)=>(r(),d("span",{key:x,class:"text-neutral-800 text-sm font-normal"},u(`${x} ${o.toFixed(2)}`),1))),128))])])])])]),e($)?(r(),be(xa,{key:0,modelValue:e($),"onUpdate:modelValue":B[13]||(B[13]=o=>vt($)?$.value=o:null),"printer-no":e(le).printerNo,"printer-type":e(ut),"is-supplement-refund":!1,"refund-operation-condition":e(le),"refund-ticket-data":e(y),"is-sales-daily":e(fe),onReSalesDaily:e(L)},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data","is-sales-daily","onReSalesDaily"])):Ee("",!0)])):(r(),d("div",cl,[a("div",null,[il,a("div",pl,u(_.$t("app.agentReport.nodata")),1)])])),e(J)?(r(),be(en,{key:2,modelValue:e(J),"onUpdate:modelValue":B[14]||(B[14]=o=>vt(J)?J.value=o:null),"sales-form":e(S),"store-today-error":e(Re),"tkt-types":e(se),onErrorNumber:e(K)},null,8,["modelValue","sales-form","store-today-error","tkt-types","onErrorNumber"])):Ee("",!0),e(z)?(r(),be(kn,{key:3,modelValue:e(z),"onUpdate:modelValue":B[15]||(B[15]=o=>vt(z)?z.value=o:null),"row-sales-daliy-data":e(A),onSetRefundData:e(Be)},null,8,["modelValue","row-sales-daliy-data","onSetRefundData"])):Ee("",!0)])),[[gt,e(ae)]])}}});const Wl=Ea(dl,[["__scopeId","data-v-bf0b6b03"]]);export{Wl as default};
