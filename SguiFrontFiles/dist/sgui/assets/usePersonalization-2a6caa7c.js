import{s as d,o as h,h8 as g,ao as t}from"./index-9381ab2b.js";const z=r=>{const e=d(null),i=o=>!r||!(o||[]).length?null:o.find(n=>n.uniqueIndex===r)||null,l=()=>{const o=g();e.value=i(o.rules)},u=async o=>{var n;(n=e.value)!=null&&n.url&&await t(e.value.url,{headers:{"Cross-Method":"CORS","Content-Type":"application/json","App-Id":"SGUI"}},{ignoreError:!0}).post(o)},p=async o=>{var n;(n=e.value)!=null&&n.url&&await t(e.value.url,{headers:{"Cross-Method":"CORS","Content-Type":"application/json","App-Id":"SGUI"}},{ignoreError:!0}).put(o)},c=async o=>{var n;if((n=e.value)!=null&&n.url){const a=new URLSearchParams;return Object.keys(o).forEach(s=>{a.append(s,o[s])}),t(`${e.value.url}?${a.toString()}`,{headers:{"Cross-Method":"CORS","Content-Type":"application/json","App-Id":"SGUI"}},{ignoreError:!0}).get().json()}return{}};return h(()=>{l()}),{personalizationRules:e,postToAIGPersonalization:u,putToAIGPersonalization:p,getToAIGPersonalization:c}},I=z;export{I as u};
