import{q as K,r as d,w as R,ac as j,o as H,x as s,B as x,G as h,z as n,y as c,J,ai as L,aj as M,ak as E,Q as w,P as k,H as Q,ah as T,al as W,am as X,an as Y}from"./index-9381ab2b.js";import{E as Z,a as q}from"./index-a7943392.js";import{E as ee}from"./index-34c19038.js";import{b as le}from"./index-d6fb0de3.js";import{_ as oe}from"./_plugin-vue_export-helper-c27b6911.js";const B=u=>(X("data-v-b25f494f"),u=u(),Y(),u),te={class:"sales-daily-filter"},ae=B(()=>k("em",{class:"iconfont icon-down"},null,-1)),ne=B(()=>k("em",{class:"iconfont icon-up"},null,-1)),se={class:"filter-bottom-sales crs-btn-ui"},re={name:"FilterSelect"},ue=K({...re,props:{filters:{},columnKey:{},showFilterInput:{type:Boolean,default:!1},filterInputPlaceholder:{default:""},notDisabled:{type:Boolean}},emits:["handleConfrim"],setup(u,{expose:D,emit:F}){const V=u,S=F,m=d(),i=d(!1),f=R(()=>V.filters),v=d([]),r=d([]),t=d(""),O=e=>{i.value=e},y=e=>{i.value=e,N(e)},$=()=>i.value,N=e=>{e?m.value.handleOpen():m.value.handleClose()},C=()=>{v.value=f.value},I=()=>{i.value=!1,m.value.handleClose(),S("handleConfrim",V.columnKey,r.value,t.value)};j(()=>f.value,()=>{C()},{deep:!0});const g=e=>{if(e.includes(",")){const _=e.split(",").filter(a=>a!==""&&a!=null).map(a=>f.value.filter(p=>p.indexOf(a)!==-1)).reduce((a,p)=>a.concat(p),[]);v.value=Array.from(new Set(_))}else v.value=f.value.filter(l=>l.indexOf(e)!==-1)};H(()=>{C()});const P=()=>{r.value=[],t.value="",C(),I()};return D({showDropDown:y,getVisibleValue:$}),(e,l)=>{const _=Q,a=T,p=Z,U=ee,z=q,A=W,G=le;return s(),x("div",te,[h(G,{ref_key:"dropdown",ref:m,trigger:"contextmenu",placement:"bottom","popper-class":"sale-daily-filter-popper",onVisibleChange:O},{dropdown:n(()=>{var b;return[e.notDisabled&&e.showFilterInput?(s(),c(a,{key:0,modelValue:t.value,"onUpdate:modelValue":l[2]||(l[2]=o=>t.value=o),class:"filter-input",placeholder:e.filterInputPlaceholder,onInput:l[3]||(l[3]=o=>{t.value=t.value.toUpperCase(),g(t.value)})},null,8,["modelValue","placeholder"])):e.showFilterInput?(s(),c(a,{key:1,modelValue:t.value,"onUpdate:modelValue":l[4]||(l[4]=o=>t.value=o),disabled:r.value.length>0,class:"filter-input",placeholder:e.filterInputPlaceholder,onInput:l[5]||(l[5]=o=>g(t.value))},null,8,["modelValue","disabled","placeholder"])):J("",!0),h(z,{modelValue:r.value,"onUpdate:modelValue":l[6]||(l[6]=o=>r.value=o)},{default:n(()=>[h(U,{"max-height":"300px",height:"fitcontent","min-size":"20"},{default:n(()=>[(s(!0),x(L,null,M(v.value,o=>(s(),c(p,{key:o,label:o,class:"scrollbar-demo-item"},{default:n(()=>[E(w(o),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1},8,["modelValue"]),k("div",se,[h(A,{disabled:((b=r.value)==null?void 0:b.length)===0,link:"",type:"primary",size:"small",onClick:I},{default:n(()=>[E(w(e.$t("app.agentReport.confirmBtn")),1)]),_:1},8,["disabled"]),k("div",{onClick:P},w(e.$t("app.agentReport.reset")),1)])]}),default:n(()=>[i.value?(s(),c(_,{key:1,color:"var(--bkc-el-color-primary)",onClick:l[1]||(l[1]=b=>y(!1))},{default:n(()=>[ne]),_:1})):(s(),c(_,{key:0,color:"var(--bkc-el-color-primary)",onClick:l[0]||(l[0]=b=>y(!0))},{default:n(()=>[ae]),_:1}))]),_:1},512)])}}});const fe=oe(ue,[["__scopeId","data-v-b25f494f"]]);export{fe as F};
