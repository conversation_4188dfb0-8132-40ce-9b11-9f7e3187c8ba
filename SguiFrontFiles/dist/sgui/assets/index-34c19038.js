import{L as X,_ as Y,q as P,O as Z,v as q,m as ee,r as c,w,e9 as te,u as A,d1 as K,x as k,y as W,z as F,a5 as ae,P as $,D as H,A as m,C as D,a6 as le,T as oe,eb as se,B as J,G as j,ai as re,M as ne,ea as E,a3 as U,ac as G,eo as ie,aG as I,W as ce,b0 as ue,o as ve,dQ as fe,F as me,b9 as de,J as pe,dI as he,K as be}from"./index-9381ab2b.js";const g=4,ye={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},ge=({move:d,size:u,bar:r})=>({[r.size]:u,transform:`translate${r.axis}(${d}%)`}),Q=Symbol("scrollbarContextKey"),we=X({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean}),Se="Thumb",ze=P({__name:"thumb",props:we,setup(d){const u=d,r=Z(Q),l=q("scrollbar");r||ee(Se,"can not inject scrollbar context");const i=c(),v=c(),s=c({}),f=c(!1);let a=!1,h=!1,b=se?document.onselectstart:null;const t=w(()=>ye[u.vertical?"vertical":"horizontal"]),S=w(()=>ge({size:u.size,move:u.move,bar:t.value})),z=w(()=>i.value[t.value.offset]**2/r.wrapElement[t.value.scrollSize]/u.ratio/v.value[t.value.offset]),T=o=>{var e;if(o.stopPropagation(),o.ctrlKey||[1,2].includes(o.button))return;(e=window.getSelection())==null||e.removeAllRanges(),M(o);const n=o.currentTarget;n&&(s.value[t.value.axis]=n[t.value.offset]-(o[t.value.client]-n.getBoundingClientRect()[t.value.direction]))},R=o=>{if(!v.value||!i.value||!r.wrapElement)return;const e=Math.abs(o.target.getBoundingClientRect()[t.value.direction]-o[t.value.client]),n=v.value[t.value.offset]/2,p=(e-n)*100*z.value/i.value[t.value.offset];r.wrapElement[t.value.scroll]=p*r.wrapElement[t.value.scrollSize]/100},M=o=>{o.stopImmediatePropagation(),a=!0,document.addEventListener("mousemove",C),document.addEventListener("mouseup",y),b=document.onselectstart,document.onselectstart=()=>!1},C=o=>{if(!i.value||!v.value||a===!1)return;const e=s.value[t.value.axis];if(!e)return;const n=(i.value.getBoundingClientRect()[t.value.direction]-o[t.value.client])*-1,p=v.value[t.value.offset]-e,_=(n-p)*100*z.value/i.value[t.value.offset];r.wrapElement[t.value.scroll]=_*r.wrapElement[t.value.scrollSize]/100},y=()=>{a=!1,s.value[t.value.axis]=0,document.removeEventListener("mousemove",C),document.removeEventListener("mouseup",y),B(),h&&(f.value=!1)},O=()=>{h=!1,f.value=!!u.size},x=()=>{h=!0,f.value=a};te(()=>{B(),document.removeEventListener("mouseup",y)});const B=()=>{document.onselectstart!==b&&(document.onselectstart=b)};return A(K(r,"scrollbarElement"),"mousemove",O),A(K(r,"scrollbarElement"),"mouseleave",x),(o,e)=>(k(),W(oe,{name:m(l).b("fade"),persisted:""},{default:F(()=>[ae($("div",{ref_key:"instance",ref:i,class:H([m(l).e("bar"),m(l).is(m(t).key)]),onMousedown:R},[$("div",{ref_key:"thumb",ref:v,class:H(m(l).e("thumb")),style:D(m(S)),onMousedown:T},null,38)],34),[[le,o.always||f.value]])]),_:1},8,["name"]))}});var V=Y(ze,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/thumb.vue"]]);const _e=X({always:{type:Boolean,default:!0},width:String,height:String,ratioX:{type:Number,default:1},ratioY:{type:Number,default:1}}),Ee=P({__name:"bar",props:_e,setup(d,{expose:u}){const r=d,l=c(0),i=c(0);return u({handleScroll:s=>{if(s){const f=s.offsetHeight-g,a=s.offsetWidth-g;i.value=s.scrollTop*100/f*r.ratioY,l.value=s.scrollLeft*100/a*r.ratioX}}}),(s,f)=>(k(),J(re,null,[j(V,{move:l.value,ratio:s.ratioX,size:s.width,always:s.always},null,8,["move","ratio","size","always"]),j(V,{move:i.value,ratio:s.ratioY,size:s.height,vertical:"",always:s.always},null,8,["move","ratio","size","always"])],64))}});var ke=Y(Ee,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/bar.vue"]]);const He=X({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:{type:Boolean,default:!1},wrapStyle:{type:ne([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20},id:String,role:String,ariaLabel:String,ariaOrientation:{type:String,values:["horizontal","vertical"]}}),Te={scroll:({scrollTop:d,scrollLeft:u})=>[d,u].every(E)},Ce="ElScrollbar",Be=P({name:Ce}),Le=P({...Be,props:He,emits:Te,setup(d,{expose:u,emit:r}){const l=d,i=q("scrollbar");let v,s;const f=c(),a=c(),h=c(),b=c("0"),t=c("0"),S=c(),z=c(1),T=c(1),R=w(()=>{const e={};return l.height&&(e.height=U(l.height)),l.maxHeight&&(e.maxHeight=U(l.maxHeight)),[l.wrapStyle,e]}),M=w(()=>[l.wrapClass,i.e("wrap"),{[i.em("wrap","hidden-default")]:!l.native}]),C=w(()=>[i.e("view"),l.viewClass]),y=()=>{var e;a.value&&((e=S.value)==null||e.handleScroll(a.value),r("scroll",{scrollTop:a.value.scrollTop,scrollLeft:a.value.scrollLeft}))};function O(e,n){he(e)?a.value.scrollTo(e):E(e)&&E(n)&&a.value.scrollTo(e,n)}const x=e=>{E(e)&&(a.value.scrollTop=e)},B=e=>{E(e)&&(a.value.scrollLeft=e)},o=()=>{if(!a.value)return;const e=a.value.offsetHeight-g,n=a.value.offsetWidth-g,p=e**2/a.value.scrollHeight,_=n**2/a.value.scrollWidth,L=Math.max(p,l.minSize),N=Math.max(_,l.minSize);z.value=p/(e-p)/(L/(e-L)),T.value=_/(n-_)/(N/(n-N)),t.value=L+g<e?`${L}px`:"",b.value=N+g<n?`${N}px`:""};return G(()=>l.noresize,e=>{e?(v==null||v(),s==null||s()):({stop:v}=ie(h,o),s=A("resize",o))},{immediate:!0}),G(()=>[l.maxHeight,l.height],()=>{l.native||I(()=>{var e;o(),a.value&&((e=S.value)==null||e.handleScroll(a.value))})}),ce(Q,ue({scrollbarElement:f,wrapElement:a})),ve(()=>{l.native||I(()=>{o()})}),fe(()=>o()),u({wrapRef:a,update:o,scrollTo:O,setScrollTop:x,setScrollLeft:B,handleScroll:y}),(e,n)=>(k(),J("div",{ref_key:"scrollbarRef",ref:f,class:H(m(i).b())},[$("div",{ref_key:"wrapRef",ref:a,class:H(m(M)),style:D(m(R)),onScroll:y},[(k(),W(de(e.tag),{id:e.id,ref_key:"resizeRef",ref:h,class:H(m(C)),style:D(e.viewStyle),role:e.role,"aria-label":e.ariaLabel,"aria-orientation":e.ariaOrientation},{default:F(()=>[me(e.$slots,"default")]),_:3},8,["id","class","style","role","aria-label","aria-orientation"]))],38),e.native?pe("v-if",!0):(k(),W(ke,{key:0,ref_key:"barRef",ref:S,height:t.value,width:b.value,always:e.always,"ratio-x":T.value,"ratio-y":z.value},null,8,["height","width","always","ratio-x","ratio-y"]))],2))}});var Ne=Y(Le,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/scrollbar.vue"]]);const Re=be(Ne);export{ye as B,Re as E};
