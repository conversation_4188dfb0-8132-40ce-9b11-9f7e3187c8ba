import{aS as g,a$ as _,R as A}from"./index-9381ab2b.js";import{q as D}from"./dictApi-2d93d62f.js";const T=/[\u4e00-\u9fa5]+/,o=/\(UM\d+\)$/,p=/[A-Za-z]+$/,E=/^\(UM\d+\)$/;let i=[];const P=(t,s)=>!t||t===""||!A((t==null?void 0:t.updateTime)??"").isSame(s,"day"),y=async()=>{var e;const t=await g("PASSENGER_SPECIAL_TYPE_DATA"),s=new Date().getTime();if(P(t,s)){const r={pageNumber:1,pageSize:500,content:{dictCode:"",dictName:"",dictTypeCode:"PASSENGER_TYPE_DETAIL"}},{data:a}=await D(r,"091V0831"),c=((e=a==null?void 0:a.value)==null?void 0:e.content)??"";return await _("PASSENGER_SPECIAL_TYPE_DATA",JSON.stringify(c),s),c}return JSON.parse(t==null?void 0:t.localData)},L=async(t,s)=>{let n=s??"";if(n!=null&&n.includes("UM")){if(!E.test(s))return t;n="UM"}const e=(i??[]).find(r=>r.field1===t&&r.field2===n);return(e==null?void 0:e.field3)??t},h=async(t,s)=>{const n=s??"";if(s.includes("UM")&&E.test(s))return s;const e=(i??[]).find(r=>r.field1===t&&r.field2===n);return(e==null?void 0:e.field2)??""},I=t=>{const s=t.match(o);return s?s[0]:""},M=t=>{const s=t.match(o);if(s)return s[0];const n=t.match(p);return n&&i.some(r=>r.field2===n[0])?n[0]:""},U=async(t,s,n)=>{var c;let e="",r="",a="";if(n){const d=t.lastIndexOf(n)===-1?t.length:t.lastIndexOf(n);r=(c=t==null?void 0:t.substring(0,d))==null?void 0:c.trim(),a=n,e=await L(s,a)}else r=t,e=s;return{specialPassengerType:e,fullName:r,nameSuffix:a}},S=async(t,s)=>{if(!t.trim())return{};const n=t.trim();let e;if(i=await y(),T.test(n))e=M(t);else if(e=I(t),!e){const a=t.split("/")[1],c=a==null?void 0:a.lastIndexOf(" ");c>-1&&(e=await h(s,a.substring(c+1)))}return await U(t,s,e??"")};export{y as a,S as g};
