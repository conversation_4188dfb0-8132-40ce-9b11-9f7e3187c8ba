import{hc as Me,fM as Pe,t as we,ab as Ee,a9 as Be,w as me,r as E,b0 as Ue,o as De,ac as ze,eL as oe,q as de,x as g,y as S,z as v,P as e,Q as l,A as a,B as k,G as h,ai as Q,aj as Y,D as R,aV as He,J as X,ak as B,ag as Le,H as Ge,ah as Ae,al as he,am as xe,an as ye,b5 as je,C as qe,s as le,aG as Qe,eZ as Ye,au as Re,bH as Te,aY as Je,a5 as Ne,b3 as $e,aZ as Ke}from"./index-9381ab2b.js";import{P as Xe}from"./PrintNoSelect-c0b0bc1c.js";import{p as Ve,f as We,h as Ze}from"./refundUtil-234919d2.js";import{a as Z,D as be}from"./decimal-56a2735b.js";import{k as Ce,D as et,P as tt,b as ne,c as st,d as at,t as nt,f as Ie,g as Se,h as ot,i as it,j as rt,S as lt}from"./regular-crs-0d781ceb.js";import{c as Fe}from"./_createMathOperation-15113527.js";import{t as dt,E as ut,a as ct}from"./index-094198d8.js";import{s as ce}from"./subtract-e226dc5c.js";import{E as pt,a as ft}from"./index-951011fc.js";import{E as mt}from"./index-c19c3f80.js";import{E as Oe,a as gt}from"./index-a7943392.js";import{E as ht}from"./index-e22833ad.js";import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";import{E as ke}from"./index-7b8ec8cc.js";import{t as xt}from"./throttle-9e041729.js";import{E as yt}from"./index-847d31f7.js";import{F as vt,G as bt}from"./ticketOperationApi-8106707a.js";var _t=Me.isFinite,kt=Math.min;function wt(o){var u=Math[o];return function(x,t){if(x=Pe(x),t=t==null?0:kt(dt(t),292),t&&_t(x)){var b=(we(x)+"e").split("e"),s=u(b[0]+"e"+(+b[1]+t));return b=(we(s)+"e").split("e"),+(b[0]+"e"+(+b[1]-t))}return u(x)}}var Rt=Fe(function(o,u){return o/u},1);const pe=Rt;var Tt=wt("floor");const _e=Tt;var Nt=Fe(function(o,u){return o*u},1);const fe=Nt,$t=o=>{const{t:u}=Ee(),x=Be(),t=me(()=>{var f;return(f=x.state.user)==null?void 0:f.entityType}),b=E(),s=Ue({...o.data}),_=E([]),r=E([]),y=me(()=>!["CDS","GPCDS"].includes(s.ticketManagementOrganizationCode??""));!o.disabled&&Ce.test(s.name)&&(s.name="");const N=(f,i,n)=>{s.payType==="TC"&&(i.length===0?n(u("app.agentTicketRefund.creditCardNotEmpty")):!o.isDragonBoatOffice&&!ot.test(i)?n(u("app.agentTicketRefund.creditCardInput")):o.isDragonBoatOffice&&!it.test(i)&&n(u("app.agentTicketRefund.dragonBoatOfficeInput"))),n()},O=(f,i,n)=>{var d;(i??"")!==""?((d=b.value)==null||d.validateField("remarkCode"),(s.remarkCode??"")==="IC"?rt.test(`${s.remarkCode}${i}`)?n():n(u("app.agentTicketRefund.remarkIC")):(s.remarkCode??"")!==""&&!Se.test(`${s.remarkCode}${i}`)?n(u("app.agentTicketRefund.remarkHint")):n()):s.remarkCode?n(u("app.agentTicketRefund.remarkHint")):n()},$=(f,i,n)=>{(s.remark??"")!==""&&!Ie.test(i??"")?n(u("app.agentTicketRefund.formatError")):n()},D=(f,i,n)=>{var m;const d=f.field.split(".")[1];s.taxs[Number(d)].taxAmount!==""&&i===""?n(u("app.agentTicketRefund.taxes")):(s.taxs[Number(d)].taxAmount===""&&i===""&&((m=b.value)==null||m.clearValidate(`taxs.${d}.taxAmount`)),n())},A=(f,i,n)=>{var m;const d=f.field.split(".")[1];s.taxs[Number(d)].taxCode!==""&&i===""?n(u("app.agentTicketRefund.taxAmount")):(s.taxs[Number(d)].taxCode===""&&i===""&&((m=b.value)==null||m.clearValidate(`taxs.${d}.taxCode`)),n())},M={ticketManagementOrganizationCode:[{required:!0,message:u("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"}],prntNo:[{required:!0,message:u("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:et,trigger:"blur",message:u("app.ticketStatus.deviceError")}],currency:[{required:!0,message:u("app.agentTicketRefund.currencyNotEmpty"),trigger:"change"}],payType:[{required:!0,message:u("app.agentTicketRefund.paymentSel"),trigger:"change"},{pattern:tt,message:u("app.agentTicketRefund.paymentInput"),trigger:"change"}],price:[{pattern:ne,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxValue:[{pattern:st,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"},{validator:A,trigger:"change"}],taxName:[{pattern:at,message:u("app.agentTicketRefund.taxes"),trigger:"change"},{validator:D,trigger:"change"}],rate:[{pattern:ne,message:u("app.agentTicketRefund.correctRate"),trigger:"change"}],commision:[{pattern:ne,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],psgName:[{pattern:nt,message:u("app.agentTicketRefund.psgNameError"),trigger:"change"}],creditCard:[{validator:N,required:!0,trigger:"change"}],remarkCode:[{pattern:Ie,message:u("app.agentTicketRefund.formatError"),trigger:["change","blur"]},{validator:$,trigger:["change","blur"]}],remark:[{validator:O,trigger:["change","blur"]}],remarkInfo:[{pattern:Se,message:u("app.agentTicketRefund.remarkHint"),trigger:["change","blur"]}],totalAmount:[{pattern:ne,message:u("app.agentTicketRefund.correctPrice"),trigger:"change"}],netRefund:[{required:!0,message:u("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:ne,message:u("app.agentTicketRefund.onlySupportDigits"),trigger:"blur"}]},w=()=>{const{totalAmount:f,totalTaxs:i,otherDeduction:n,commision:d,commisionRate:m}=s;if(!K())if(m){const C=pe(fe(Number(f),Number(m)),100).toString(),P=_e(Number(C),2).toString();s.commision=P;const U=`${ce(Z(Number(f),Number(i)),Z(Number(n),Number(P)))}`;s.netRefund=Number(U).toFixed(2)}else{const C=`${ce(Z(Number(f),Number(i)),Z(Number(n),Number(d)))}`;s.netRefund=Number(C).toFixed(2)}},I=()=>{s.commisionRate||(s.commision="")},F=()=>{let f=new be(0);s.taxs.forEach((i,n)=>{var d;(d=b.value)==null||d.validateField(`taxs.${n}.taxAmount`).then(m=>{m&&i.taxAmount?(s.taxs[n].taxAmount=oe(s.currency)?i.taxAmount:Number(i.taxAmount).toFixed(2),f=f.add(new be(i.taxAmount))):m&&!i.taxAmount&&(f=f.add(new be(0))),s.totalTaxs=oe(s.currency)?f.toString():Number(f).toFixed(2),oe(s.currency)?c(""):w()})})},q=async()=>{const f=[];s.taxs.forEach((i,n)=>{var d,m;f.push((d=b.value)==null?void 0:d.validateField(`taxs.${n}.taxCode`)),f.push((m=b.value)==null?void 0:m.validateField(`taxs.${n}.taxAmount`))}),await Promise.all(f),s.taxs.forEach((i,n)=>{s.taxs[n].taxAmount&&(s.taxs[n].taxAmount=oe(s.currency)?s.taxs[n].taxAmount??0:Number(s.taxs[n].taxAmount??0).toFixed(2))}),F()},j=f=>f&&!ne.test(f.toString()),K=()=>{const{totalAmount:f,otherDeductionRate:i,otherDeduction:n,commision:d,commisionRate:m}=s;return j(f??"")||j(i??"")||j(n??"")||j(d??"")||j(m??"")},c=f=>{if(f==="otherDeductionRate"&&s.otherDeductionRate){const P=pe(fe(Number(s.totalAmount),Number(s.otherDeductionRate)),100).toString();s.otherDeduction=_e(Number(P),2).toString()}const{totalAmount:i,totalTaxs:n,otherDeduction:d,commision:m,commisionRate:C}=s;if(!K())if(C){const P=pe(fe(Number(i),Number(C)),100).toString(),U=_e(Number(P),2).toString();s.commision=U.endsWith(".00")?U.slice(0,-3):U;const ee=`${ce(Z(Number(i),Number(n)),Z(Number(d),Number(U)))}`;s.netRefund=Number(ee).toFixed(2),s.netRefund.endsWith(".00")&&(s.netRefund=s.netRefund.slice(0,-3))}else s.netRefund=`${ce(Z(Number(i),Number(n)),Z(Number(d),Number(m)))}`},p=f=>{if(!K()){if(oe(s.currency)){c(f);return}f==="otherDeductionRate"&&(s.otherDeductionRate??"")!==""&&(s.otherDeductionRate=Number(s.otherDeductionRate).toFixed(2),s.otherDeduction=pe(fe(Number(s.totalAmount),Number(s.otherDeductionRate)),100).toString()),w(),s.totalAmount!==""&&(s.totalAmount=Number(s.totalAmount).toFixed(2)),s.commision!==""&&(s.commision=Number(s.commision).toFixed(2)),s.commisionRate!==""&&(s.commisionRate=Number(s.commisionRate).toFixed(2)),s.otherDeduction!==""&&(s.otherDeduction=Number(s.otherDeduction).toFixed(2))}},z=f=>{f.target.value!==""&&!Ve.some(i=>i.label===f.target.value)&&(s.payType=f.target.value)},H=f=>{var i;return(((i=s.checkedSeg)==null?void 0:i.filter(n=>n.etSegIndex===f.etSegIndex))??[]).length>0},W=async()=>{b.value&&(await b.value.resetFields(),s.etTagNew=o.data.etTagNew,s.checkedSeg=o.data.checkedSeg,s.couponNos=o.couponNoHistory?JSON.parse(JSON.stringify(o.couponNoHistory)):[],s.taxs=JSON.parse(JSON.stringify(o.taxsHistory)),_.value=[],F(),p(""))},T=async()=>{if(!b.value)return!1;try{return await b.value.validate()}catch{return!1}},L=()=>{s.taxs.length!==27&&(s.taxs=s.taxs.length>=25&&s.taxs.length<27?s.taxs.concat(new Array(27-s.taxs.length).fill({taxCode:"",taxAmount:""})).map(f=>({...f})):s.taxs.concat(new Array(5).fill({taxCode:"",taxAmount:""})).map(f=>({...f})))},V=()=>s,se=()=>(_.value=new Array(4).fill(""),(s.couponNos??[]).some(f=>f)?(s.couponNos??[]).forEach((f,i)=>{f&&!lt.test(f)&&(_.value[i]=u("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:4}))}):_.value[0]=u("app.pnrManagement.validate.required"),_.value.every(f=>!f)),ae=()=>{y.value||(s.prntNo="")},ie=f=>{var n,d;return(r.value??[]).some(m=>f===m.value)&&f?f:((d=(n=r.value)==null?void 0:n[0])==null?void 0:d.value)??""},G={BSP:{label:u("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:u("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:u("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:u("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:u("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:u("app.agentTicketQuery.OWNTicket"),value:"ARL"}},re=()=>{var f,i,n,d,m,C,P,U,ee,ue;((f=t.value)!=null&&f.includes("$$$")||(i=t.value)!=null&&i.includes("BSP"))&&(r.value.push(G.BSP),r.value.push(G.GPBSP)),!((n=t.value)!=null&&n.includes("BSP"))&&((d=t.value)!=null&&d.includes("GP"))&&r.value.push(G.GPBSP),((m=t.value)!=null&&m.includes("$$$")||(C=t.value)!=null&&C.includes("BOP"))&&r.value.push(G.BOPBSP),((P=t.value)!=null&&P.includes("$$$")||(U=t.value)!=null&&U.includes("CDS"))&&(r.value.push(G.CDS),r.value.push(G.GPCDS)),((ee=t.value)!=null&&ee.includes("$$$")||(ue=t.value)!=null&&ue.includes("本票"))&&r.value.push(G.ARL),s.ticketManagementOrganizationCode=ie(s.ticketManagementOrganizationCode??"")};return De(async()=>{re()}),ze(()=>o.disabled,()=>{!o.disabled&&Ce.test(s.name)&&(s.name="")}),{segmentErrorMessage:_,formData:s,formRef:b,rules:M,ticketOrganizationList:r,ticketOrganizationListEnum:G,checkTax:q,countAmount:p,bindPaymentValue:z,isCheckSeg:H,resetForm:W,validate:T,getFormDate:V,addTax:L,commisionRateChange:I,validSegment:se,isShowPrintNo:y,changeTicketManagementOrganizationCode:ae}},Ct=$t,te=o=>(xe("data-v-e55a974d"),o=o(),ye(),o),It={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},St={class:"self-stretch justify-start items-start gap-5 inline-flex"},Et={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Dt={class:"w-[84px] text-gray-3 text-xs shrink-0"},At={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},jt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Vt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Ft={key:0,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Ot={key:1,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Mt={key:0,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Pt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Bt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Ut={key:1,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},zt={class:"carType-option-panel"},Ht={key:2,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Lt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Gt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},qt={key:3,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Qt={class:"self-stretch justify-start items-center gap-5 inline-flex"},Yt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Jt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Kt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Xt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Wt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Zt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},es={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ts={class:"w-[84px] text-gray-3 text-xs shrink-0"},ss={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},as={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ns={class:"w-[84px] text-gray-3 text-xs shrink-0"},os={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},is={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},rs={class:"self-stretch justify-start items-start gap-5 inline-flex"},ls={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ds={class:"w-[84px] text-gray-3 text-xs shrink-0"},us={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},cs={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ps={class:"w-[84px] text-gray-3 text-xs shrink-0"},fs={class:"text-gray-2 text-xs font-bold whitespace-nowrap w-[150px]"},ms=te(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),gs={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},hs={class:"w-[84px] text-gray-3 text-xs shrink-0"},xs={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},ys={class:"self-stretch justify-start items-start gap-5 inline-flex"},vs={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},bs={key:0,class:"grow shrink basis-0 min-h-[32px] justify-start flex mb-[10px]"},_s={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0 refund-segment"},ks={key:1,class:"grow shrink basis-0 min-h-[32px] justify-start gap-1 flex mb-[10px]"},ws={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0"},Rs={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Ts={class:"justify-start items-center gap-4 flex"},Ns={class:"text-gray-2 text-xs leading-tight"},$s={key:1,class:"flex-col"},Cs={class:"flex items-center h-[20px] text-gray-3 font-normal text-xs leading-tight"},Is={class:"justify-start items-center gap-4 flex h-[20px]"},Ss={class:"text-gray-2 text-xs leading-tight"},Es={class:"self-stretch justify-start items-start gap-5 inline-flex"},Ds={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},As={key:0,class:"not-required-tip"},js={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Vs={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Fs=te(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},[e("em")],-1)),Os={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},Ms={class:"self-stretch justify-start items-start gap-5 inline-flex"},Ps={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Bs={class:"w-full mb-[10px]"},Us={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},zs={class:"ml-[20px]"},Hs={class:"text-gray-2 font-[700]"},Ls={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},Gs={class:"w-[20px] text-gray-3 text-xs shrink-0"},qs={class:"w-[40px] mr-[6px] shrink-0"},Qs={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},Ys={class:"self-stretch justify-start items-start gap-5 inline-flex"},Js={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ks={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},Xs=te(()=>e("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),Ws={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},Zs=te(()=>e("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px] min-w-[11px]"},"%",-1)),ea={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ta={class:"self-stretch justify-start items-center gap-5 inline-flex"},sa={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},aa={class:"w-[84px] text-gray-3 text-xs shrink-0"},na={class:"justify-start items-center flex text-gray-2 text-xs relative"},oa=te(()=>e("span",{class:"iconfont icon-info-circle-line absolute left-[-40px]"},null,-1)),ia={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},ra={class:"text-gray-2 font-[700]"},la=te(()=>e("span",null,[e("i",{class:"iconfont icon-info-circle-line text-[20px] font-normal ml-[10px] text-gray-4"})],-1)),da={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] print:basis-1/4"},ua={key:1},ca=te(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] print:hidden"},[e("em")],-1)),pa=de({__name:"RefundForm",props:{data:{},refundTickets:{},disabled:{type:Boolean,default:!1},isSupplementRefund:{type:Boolean,default:!1},taxsHistory:{},couponNoHistory:{}},setup(o,{expose:u}){const x=o,{formData:t,formRef:b,rules:s,ticketOrganizationList:_,ticketOrganizationListEnum:r,checkTax:y,countAmount:N,bindPaymentValue:O,isCheckSeg:$,resetForm:D,validate:A,getFormDate:M,addTax:w,commisionRateChange:I,segmentErrorMessage:F,validSegment:q,isShowPrintNo:j,changeTicketManagementOrganizationCode:K}=Ct(x);return u({resetForm:D,validate:A,getFormDate:M,validSegment:q}),(c,p)=>{const z=Ge,H=ut,W=ct,T=pt,L=mt,V=Ae,se=Oe,ae=gt,ie=ht,G=he,re=ft;return g(),S(re,{ref_key:"formRef",ref:b,model:a(t),class:"refund-form","label-position":"left","require-asterisk-position":"right"},{default:v(()=>{var f;return[e("div",It,[e("div",St,[e("div",Et,[e("div",Dt,l(c.$t("app.agentTicketRefund.refundTicketNumber")),1),e("div",At,l(a(t).refundNo),1)]),e("div",jt,[e("div",Vt,l(c.$t("app.agentTicketRefund.rtType")),1),c.isSupplementRefund?(g(),k("div",Ft,l(a(t).refundType),1)):(g(),k("div",Ot,l(a(t).international??"-"),1))]),c.isSupplementRefund?(g(),k("div",Ut,[h(T,{prop:"ticketManagementOrganizationCode",rules:a(s).ticketManagementOrganizationCode,label:c.$t("app.agentTicketQuery.ticketOrganization")},{default:v(()=>[h(W,{modelValue:a(t).ticketManagementOrganizationCode,"onUpdate:modelValue":p[0]||(p[0]=i=>a(t).ticketManagementOrganizationCode=i),class:"ticket-management-organization",disabled:a(t).ticketManagementOrganizationCode==="",placeholder:a(t).ticketManagementOrganizationCode===""?c.$t("app.agentTicketQuery.noData"):"",onChange:a(K)},{default:v(()=>[(g(!0),k(Q,null,Y(a(_),i=>(g(),S(H,{key:i.value,label:i.label,value:i.value},{default:v(()=>[e("div",zt,[e("div",{class:R(a(t).ticketManagementOrganizationCode===i.value?"show-select":"hidden-select")},[h(z,null,{default:v(()=>[h(a(He))]),_:1})],2),e("span",null,l(i.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["rules","label"])])):(g(),k("div",Mt,[e("div",Pt,l(c.$t("app.agentTicketQuery.ticketOrganization")),1),e("div",Bt,l(((f=a(r)[a(t).ticketManagementOrganizationCode??""])==null?void 0:f.label)||"-"),1)])),c.isSupplementRefund?(g(),k("div",qt,[a(j)?(g(),S(T,{key:0,prop:"prntNo",rules:a(s).prntNo,label:c.$t("app.ticketStatus.deviceNum")},{default:v(()=>[h(Xe,{modelValue:a(t).prntNo,"onUpdate:modelValue":[p[1]||(p[1]=i=>a(t).prntNo=i),p[2]||(p[2]=i=>a(b).validateField("prntNo"))],"select-class":""},null,8,["modelValue"])]),_:1},8,["rules","label"])):X("",!0)])):(g(),k("div",Ht,[e("div",Lt,l(c.$t("app.agentTicketRefund.prntNo")),1),e("div",Gt,l(["CDS","GPCDS"].includes(a(t).ticketManagementOrganizationCode??"")?"-":a(t).prntNo),1)]))]),e("div",Qt,[e("div",Yt,[e("div",Jt,l(c.$t("app.agentTicketRefund.refundAgent")),1),e("div",Kt,l(a(t).agent),1)]),e("div",Xt,[e("div",Wt,l(c.$t("app.agentTicketRefund.refundIataNo")),1),e("div",Zt,l(a(t).iata),1)]),e("div",es,[e("div",ts,l(c.$t("app.agentTicketRefund.refundOffice")),1),e("div",ss,l(a(t).office),1)]),e("div",as,[e("div",ns,l(c.$t("app.agentTicketRefund.refundDate")),1),e("div",os,l(a(t).refundDate),1)])])]),e("div",is,[e("div",rs,[e("div",ls,[e("div",ds,l(c.$t("app.agentTicketRefund.refundAirlineSettlementCode")),1),e("div",us,l(a(t).airline),1)]),e("div",cs,[e("div",ps,l(c.$t("app.agentTicketRefund.refundTicketNo")),1),h(L,{placement:"top",trigger:"hover",content:a(t).ticketNoView},{default:v(()=>[e("div",fs,l(a(t).ticketNoView),1)]),_:1},8,["content"])]),ms,e("div",gs,[e("div",hs,l(c.$t("app.agentTicketRefund.numberOfCombinedTickets")),1),e("div",xs,l(a(t).conjunction),1)])]),e("div",ys,[e("div",vs,[h(T,{label:c.$t("app.agentTicketRefund.passName"),prop:"name",rules:a(s).psgName},{default:v(()=>[h(V,{modelValue:a(t).name,"onUpdate:modelValue":p[3]||(p[3]=i=>a(t).name=i),disabled:c.disabled,clearable:"",onInput:p[4]||(p[4]=i=>a(t).name=a(t).name.toUpperCase())},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])]),c.isSupplementRefund?(g(),k("div",ks,[e("div",ws,l(c.$t("app.agentTicketRefund.refundSeg")),1),e("div",Rs,[a(t).conjunction===1?(g(!0),k(Q,{key:0},Y(c.refundTickets,(i,n)=>(g(),S(T,{key:n,prop:`segment[${n}]`},{default:v(()=>[h(ae,{modelValue:a(t).checkedSeg,"onUpdate:modelValue":p[5]||(p[5]=d=>a(t).checkedSeg=d),disabled:c.disabled},{default:v(()=>[e("div",Ts,[(g(!0),k(Q,null,Y(i,(d,m)=>(g(),k("div",{key:m+"segmet",class:"justify-start items-center gap-[2px] flex"},[h(se,{disabled:!d.select,label:d},{default:v(()=>[e("div",Ns,l(d.deptCity)+"-"+l(d.arrivalCity),1)]),_:2},1032,["disabled","label"]),e("div",{class:R([!d.select||!a($)(d)?"text-gray-5":"text-brand-2","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},l(c.$t(`app.queryRefunds.number_${m+1}`)),3)]))),128))])]),_:2},1032,["modelValue","disabled"])]),_:2},1032,["prop"]))),128)):(g(),k("div",$s,[(g(!0),k(Q,null,Y(c.refundTickets,(i,n)=>(g(),k("div",{key:n,class:R({"mb-[10px]":n<c.refundTickets.length-1})},[e("div",Cs,l(c.$t("app.agentTicketRefund.couponNo",{a:a(We)(Number(i[0].conjunctionIndex)-1)})),1),h(T,{prop:`segment[${n}]`},{default:v(()=>[h(ae,{modelValue:a(t).checkedSeg,"onUpdate:modelValue":p[6]||(p[6]=d=>a(t).checkedSeg=d),disabled:c.disabled},{default:v(()=>[e("div",Is,[(g(!0),k(Q,null,Y(i,(d,m)=>(g(),k("div",{key:m+"segmet",class:"justify-start items-center gap-[2px] flex h-[20px]"},[h(se,{disabled:!d.select,label:d},{default:v(()=>[e("div",Ss,l(d.deptCity)+"-"+l(d.arrivalCity),1)]),_:2},1032,["disabled","label"]),e("div",{class:R([!d.select||!a($)(d)?"text-gray-5":"text-brand-2","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},l(c.$t(`app.queryRefunds.number_${m+1}`)),3)]))),128))])]),_:2},1032,["modelValue","disabled"])]),_:2},1032,["prop"])],2))),128))]))])])):(g(),k("div",bs,[e("div",_s,l(c.$t("app.agentTicketRefund.refundSeg")),1),(g(!0),k(Q,null,Y(a(t).couponNos,(i,n)=>(g(),S(T,{key:"couponNo"+n,prop:"couponNos."+n,error:a(F)[n],class:"mr-[10px] coupon-no"},{default:v(()=>[h(V,{modelValue:a(t).couponNos[n],"onUpdate:modelValue":d=>a(t).couponNos[n]=d,modelModifiers:{trim:!0},disabled:c.disabled,clearable:"",onBlur:a(q)},null,8,["modelValue","onUpdate:modelValue","disabled","onBlur"])]),_:2},1032,["prop","error"]))),128))]))]),e("div",Es,[e("div",Ds,[h(T,{label:c.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",rules:a(s).totalAmount,class:R({"not-required-container":!a(t).totalAmount})},{default:v(()=>[h(V,{modelValue:a(t).totalAmount,"onUpdate:modelValue":p[7]||(p[7]=i=>a(t).totalAmount=i),modelModifiers:{trim:!0},disabled:c.disabled,clearable:"",onBlur:p[8]||(p[8]=i=>a(N)(""))},null,8,["modelValue","disabled"]),a(t).totalAmount?X("",!0):(g(),k("div",As,l(c.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),e("div",js,[h(T,{label:c.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:a(s).payType},{default:v(()=>[c.disabled?(g(),k("div",{key:1,class:R([{"bg-brand-7":!c.disabled},"bg-gray-7 w-full h-[32px] px-[12px] py-4[px] flex items-center text-xs font-bold cursor-not-allowed text-[--bkc-el-text-color-placeholder] border-solid border-[1px] border-[--bkc-el-disabled-border-color] rounded-[4px]"])},[B(l(a(Ze)(a(t).payType))+" ",1),h(z,{class:"refundForm-icon brand text-gray-7"},{default:v(()=>[h(a(Le))]),_:1})],2)):(g(),S(W,{key:0,modelValue:a(t).payType,"onUpdate:modelValue":p[9]||(p[9]=i=>a(t).payType=i),modelModifiers:{trim:!0},disabled:c.disabled,class:"pay",filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:c.$t("app.agentTicketRefund.paymentSel"),clearable:"",onBlur:a(O)},{default:v(()=>[(g(!0),k(Q,null,Y(a(Ve),(i,n)=>(g(),S(H,{key:n,label:i.label,value:i.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onBlur"]))]),_:1},8,["label","rules"])]),e("div",Vs,[h(T,{label:c.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:a(s).currency},{default:v(()=>[h(V,{modelValue:a(t).currency,"onUpdate:modelValue":p[10]||(p[10]=i=>a(t).currency=i),modelModifiers:{trim:!0},disabled:c.disabled,clearable:"",onInput:p[11]||(p[11]=i=>a(t).currency=a(t).currency.toUpperCase())},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])]),Fs])]),e("div",Os,[e("div",Ms,[e("div",Ps,[h(T,{label:c.$t("app.agentTicketRefund.etTag")},{default:v(()=>[h(ie,{modelValue:a(t).etTagNew,"onUpdate:modelValue":p[12]||(p[12]=i=>a(t).etTagNew=i),disabled:c.disabled||c.isSupplementRefund,"inline-prompt":"","active-text":"Y","inactive-text":"N"},null,8,["modelValue","disabled"])]),_:1},8,["label"])])]),e("div",Bs,[e("div",Us,[e("div",null,[e("span",null,l(c.$t("app.agentTicketRefund.refundTax")),1),e("span",zs,l(c.$t("app.fare.singleFare.totalTax")),1),e("span",Hs," "+l(a(t).currency)+" "+l(a(t).totalTaxs),1)]),c.disabled?X("",!0):(g(),S(G,{key:0,link:"",type:"primary",size:"small",disabled:a(t).taxs.length===27,onClick:a(w)},{default:v(()=>[B(l(c.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"]))]),e("div",Ls,[(g(!0),k(Q,null,Y(a(t).taxs,(i,n)=>(g(),k("div",{key:n+new Date,class:"grow shrink-0 basis-0 h-[32px] justify-start items-center flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[e("div",Gs,l(n+1),1),e("div",qs,[h(T,{prop:"taxs."+n+".taxCode",rules:a(s).taxName},{default:v(()=>[h(V,{modelValue:i.taxCode,"onUpdate:modelValue":d=>i.taxCode=d,modelModifiers:{trim:!0},disabled:c.disabled,onInput:d=>i.taxCode=i.taxCode.toUpperCase(),onBlur:a(y)},null,8,["modelValue","onUpdate:modelValue","disabled","onInput","onBlur"])]),_:2},1032,["prop","rules"])]),h(T,{prop:"taxs."+n+".taxAmount",rules:a(s).taxValue},{default:v(()=>[h(V,{modelValue:i.taxAmount,"onUpdate:modelValue":d=>i.taxAmount=d,modelModifiers:{trim:!0},disabled:c.disabled,onBlur:a(y)},null,8,["modelValue","onUpdate:modelValue","disabled","onBlur"])]),_:2},1032,["prop","rules"])]))),128))])])]),e("div",Qs,[e("div",Ys,[e("div",Js,[h(T,{label:c.$t("app.agentTicketRefund.commision"),prop:"commision",rules:a(s).price},{default:v(()=>[h(V,{modelValue:a(t).commision,"onUpdate:modelValue":p[13]||(p[13]=i=>a(t).commision=i),modelModifiers:{trim:!0},disabled:c.disabled,clearable:"",placeholder:"0.00",onBlur:p[14]||(p[14]=i=>a(N)(""))},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])]),e("div",Ks,[h(T,{label:c.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:a(s).rate},{default:v(()=>[h(V,{modelValue:a(t).commisionRate,"onUpdate:modelValue":p[15]||(p[15]=i=>a(t).commisionRate=i),modelModifiers:{trim:!0},disabled:c.disabled,clearable:"",placeholder:"0.00",onBlur:p[16]||(p[16]=i=>a(N)("")),onInput:a(I)},null,8,["modelValue","disabled","onInput"]),Xs]),_:1},8,["label","rules"])]),e("div",Ws,[h(T,{label:c.disabled?c.$t("app.agentTicketRefund.otherDeductionRate"):c.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",rules:a(s).rate},{default:v(()=>[h(V,{modelValue:a(t).otherDeductionRate,"onUpdate:modelValue":p[17]||(p[17]=i=>a(t).otherDeductionRate=i),modelModifiers:{trim:!0},class:"min-width-42",disabled:c.disabled,clearable:"",placeholder:"1-100",onBlur:p[18]||(p[18]=i=>a(N)("otherDeductionRate"))},null,8,["modelValue","disabled"]),Zs]),_:1},8,["label","rules"])]),e("div",ea,[h(T,{label:c.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",rules:a(s).price},{default:v(()=>[h(V,{modelValue:a(t).otherDeduction,"onUpdate:modelValue":p[19]||(p[19]=i=>a(t).otherDeduction=i),modelModifiers:{trim:!0},disabled:c.disabled,clearable:"",placeholder:"0.00",onBlur:p[20]||(p[20]=i=>a(N)(""))},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])])]),e("div",ta,[e("div",sa,[e("div",aa,l(c.$t("app.agentTicketRefund.remark")),1),e("div",na,[h(T,{prop:"remarkInfo",rules:a(s).remarkInfo},{default:v(()=>[h(V,{modelValue:a(t).remarkInfo,"onUpdate:modelValue":p[21]||(p[21]=i=>a(t).remarkInfo=i),disabled:c.disabled,clearable:"",placeholder:c.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:p[22]||(p[22]=i=>{var n;return a(t).remarkInfo=(n=a(t).remarkInfo)==null?void 0:n.toUpperCase()})},null,8,["modelValue","disabled","placeholder"])]),_:1},8,["rules"]),h(L,{placemant:"top",content:c.$t("app.agentTicketRefund.remarkTips")},{default:v(()=>[oa]),_:1},8,["content"])])]),e("div",ia,[c.disabled?(g(),S(T,{key:0,label:c.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund",rules:a(s).netRefund},{default:v(()=>[h(V,{modelValue:a(t).netRefund,"onUpdate:modelValue":p[23]||(p[23]=i=>a(t).netRefund=i),modelModifiers:{trim:!0},disabled:c.disabled,clearable:""},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])):(g(),S(T,{key:1,label:c.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund"},{default:v(()=>[e("span",ra,l(a(t).currency)+" "+l(a(t).netRefund),1),h(L,{placement:"top",effect:"dark"},{content:v(()=>[B(l(c.$t("app.agentTicketRefund.netRefundTip")),1)]),default:v(()=>[la]),_:1})]),_:1},8,["label"]))]),e("div",da,[a(t).payType==="TC"?(g(),S(T,{key:0,label:c.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:a(s).creditCard},{default:v(()=>[h(V,{modelValue:a(t).creditCard,"onUpdate:modelValue":p[24]||(p[24]=i=>a(t).creditCard=i),modelModifiers:{trim:!0},disabled:c.disabled,clearable:"",onInput:p[25]||(p[25]=i=>a(t).creditCard=a(t).creditCard.toUpperCase())},null,8,["modelValue","disabled"])]),_:1},8,["label","rules"])):(g(),k("em",ua))]),ca])])]}),_:1},8,["model"])}}});const fa=ve(pa,[["__scopeId","data-v-e55a974d"]]),ma=o=>(xe("data-v-1c46f5f6"),o=o(),ye(),o),ga={class:"w-[460px] h-12 justify-start items-center gap-4 inline-flex mb-[10px]"},ha=ma(()=>e("em",{class:"iconfont icon-info-circle-line text-brand-2"},null,-1)),xa={class:"h-[50px] grow shrink basis-0 text-gray-1 text-lg font-normal leading-normal whitespace-normal"},ya={class:"w-[206px] h-8 justify-end items-center gap-2.5 inline-flex"},va=de({__name:"DeleteConfirm",props:{airlineCode:{}},emits:["update:modelValue","confirm"],setup(o,{emit:u}){const x=u,t=()=>{x("update:modelValue",!1)},s=E(o.airlineCode!=="784");return(_,r)=>{const y=Oe,N=he,O=ke;return g(),S(O,{class:"rval-dialog",width:"500px","close-on-click-modal":!1,"show-close":!1,onClose:r[2]||(r[2]=$=>t())},{footer:v(()=>[e("span",ya,[_.airlineCode!=="784"?(g(),S(y,{key:0,modelValue:s.value,"onUpdate:modelValue":r[0]||(r[0]=$=>s.value=$),label:_.$t("app.refundForm.restore"),size:"large"},null,8,["modelValue","label"])):X("",!0),h(N,{type:"primary",onClick:r[1]||(r[1]=$=>_.$emit("confirm",s.value))},{default:v(()=>[B(l(_.$t("app.refundForm.confirmBtn")),1)]),_:1}),h(N,{plain:"",onClick:t},{default:v(()=>[B(l(_.$t("app.refundForm.cancelBtn")),1)]),_:1})])]),default:v(()=>[e("div",ga,[ha,e("div",xa,l(_.$t("app.refundForm.wainMsg")),1)])]),_:1})}}});const ba=ve(va,[["__scopeId","data-v-1c46f5f6"]]),ge=function(o,u){if(!(this instanceof ge))return new ge(o,u);this.options=this.extend({noPrint:"",style:"",paging:!1},u),typeof o=="string"?this.dom=document.querySelector(o):(this.isDOM(o),this.dom=this.isDOM(o)?o:o.$el),this.init()};ge.prototype={init:function(){var o=this.getStyle()+this.getHtml();this.writeIframe(o)},extend:function(o,u){for(var x in u)o[x]=u[x];return o},getStyle:function(){for(var o="",u=document.querySelectorAll("style,link"),x=0;x<u.length;x++)o+=u[x].outerHTML;return o+="<style>"+(this.options.noPrint?this.options.noPrint:".no-print")+"{display:none;}</style>",this.options.paging&&(o+="<style>html,body{padding: 20px},div{height: auto!important;-webkit-print-color-adjust: exact}</style>"),o},getHtml:function(){for(var o=document.querySelectorAll("input"),u=document.querySelectorAll("textarea"),x=document.querySelectorAll("select"),t=0;t<o.length;t++)o[t].type=="checkbox"||o[t].type=="radio"?o[t].checked==!0?o[t].setAttribute("checked","checked"):o[t].removeAttribute("checked"):(o[t].type=="text",o[t].setAttribute("value",o[t].value));for(var b=0;b<u.length;b++)u[b].type=="textarea"&&(u[b].innerHTML=u[b].value);for(var s=0;s<x.length;s++)if(x[s].type=="select-one"){var _=x[s].children;for(var r in _)_[r].tagName=="OPTION"}return this.options.paging?this.dom.outerHTML:this.wrapperRefDom(this.dom).outerHTML},wrapperRefDom:function(o){let u=null,x=o;if(!this.isInBody(x))return x;for(;x;){if(u){let t=x.cloneNode(!1);t.appendChild(u),u=t}else u=x.cloneNode(!0);x=x.parentElement}return u},isInBody:function(o){return o===document.body?!1:document.body.contains(o)},writeIframe:function(o){o=o+"<style>.print{transform: scale(2) !important;}@page {margin-top: 1mm;margin-bottom: 1mm;}</style>"+this.options.style;var u,x,t=document.createElement("iframe"),b=document.body.appendChild(t);t.id="myIframe",t.setAttribute("style","position:absolute;width:0;height:0;top:-10px;left:-10px;"),u=b.contentWindow||b.contentDocument,x=b.contentDocument||b.contentWindow.document,x.open(),x.write(o),x.close();var s=this;t.onload=function(){s.toPrint(u),setTimeout(function(){document.body.removeChild(t)},100)}},toPrint:function(o){try{setTimeout(function(){o.focus();try{o.document.execCommand("print",!1,null)||o.print()}catch{o.print()}o.close()},10)}catch(u){console.log("err",u)}},isDOM:typeof HTMLElement=="object"?function(o){return o instanceof HTMLElement}:function(o){return o&&typeof o=="object"&&o.nodeType===1&&typeof o.nodeName=="string"}};const J=o=>(xe("data-v-b18dd19e"),o=o(),ye(),o),_a=J(()=>e("div",{class:"w-[1012px] h-6 justify-center items-center gap-2.5 inline-flex relative mb-[10px]"},[e("div",{class:"w-[348px] h-[0px] rotate-180 border border-gray-6"}),e("div",{class:"text-gray-2 text-base font-bold leading-normal"},"AIRLINE / BSP AUTO REFUND FORM"),e("div",{class:"w-[348px] h-[0px] rotate-180 border border-gray-6"})],-1)),ka={class:"w-[1012px] h-[158px] flex-col justify-start items-start gap-2.5 inline-flex"},wa={class:"w-full self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Ra={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ta={class:"text-gray-2 font-bold leading-tight"},Na={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},$a={class:"text-gray-2 font-bold leading-tight"},Ca={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ia={class:"text-gray-2 font-bold leading-tight"},Sa={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ea={class:"text-gray-2 font-bold leading-tight"},Da={class:"w-full self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Aa={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},ja={class:"text-gray-2 font-bold leading-tight"},Va={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Fa={class:"text-gray-2 font-bold leading-tight"},Oa={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ma=J(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"CURRENCY CODE",-1)),Pa=[Ma],Ba={class:"text-gray-2 font-bold leading-tight"},Ua={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},za=J(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"FORM PAYMENT",-1)),Ha=[za],La={class:"text-gray-2 font-bold leading-tight"},Ga={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},qa={class:"text-gray-2 font-bold leading-tight"},Qa={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Ya={class:"grow shrink basis-0 h-5 justify-start items-center gap-2.5 flex"},Ja={class:"text-gray-2 font-bold leading-tight"},Ka={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Xa={class:"text-gray-2 font-bold leading-tight"},Wa={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Za={class:"text-gray-2 font-bold leading-tight"},en={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},tn={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},sn={class:"text-gray-2 font-bold leading-tight"},an={class:"w-[324px] self-stretch justify-start items-center gap-2.5 flex"},nn={class:"text-gray-2 font-bold leading-tight"},on={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},rn={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},ln={class:"text-gray-2 font-bold leading-tight"},dn={class:"w-[324px] self-stretch justify-start items-center gap-2.5 flex"},un={class:"text-gray-2 font-bold leading-tight"},cn={class:"w-[1012px]"},pn={class:"w-[1012px] h-[44px] flex-col justify-start items-start gap-2.5 inline-flex"},fn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},mn=J(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"GROSS REFUND",-1)),gn=[mn],hn={class:"text-gray-2 font-bold leading-tight"},xn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},yn={class:"text-gray-2 font-bold leading-tight"},vn={class:"w-[1012px]"},bn={class:"w-[1012px] h-[100px] flex-col justify-start items-start gap-2.5 inline-flex"},_n={class:"self-stretch h-[18px] justify-start items-center gap-5 inline-flex"},kn={class:"h-5 justify-start items-center gap-2.5 flex"},wn=J(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"-",-1)),Rn={class:"text-gray-2 font-bold leading-tight"},Tn=J(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"=",-1)),Nn={class:"text-gray-2 font-bold leading-tight"},$n=J(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"%",-1)),Cn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},In=J(()=>e("div",{class:"text-gray-2 font-bold leading-tight"},"-",-1)),Sn={class:"text-gray-2 font-bold leading-tight"},En={class:"self-stretch grow shrink basis-0 justify-start items-center gap-2.5 inline-flex"},Dn={class:"text-gray-2 font-bold leading-tight"},An={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},jn=J(()=>e("div",{class:"text-gray-2 font-bold leading-tight"},"= ",-1)),Vn={class:"text-gray-2 font-bold leading-tight"},Fn={class:"text-gray-2 font-bold leading-tight"},On={key:0},Mn=J(()=>e("div",{class:"w-full min-w-[20px] h-[0px] rotate-180 border border-gray-6 inline-block"},null,-1)),Pn={key:0,class:"text-gray-2 font-bold inline-block whitespace-nowrap text-base"},Bn=J(()=>e("div",{class:"w-full min-w-[20px] h-[0px] rotate-180 border border-gray-6 inline-block"},null,-1)),Un=de({__name:"PrintRefundForm",props:{refundInfo:{},originalTickets:{}},setup(o,{expose:u}){const x=E(),t=E(!1),b=me(()=>window.innerHeight-120),s=E(!1),_=E("AIRLINE / BSP AUTO REFUND FORM COMPLETED"),r=xt(y=>{y==="enter"?s.value=!0:s.value=!1},200);return u({captureRef:x,originInfo:t}),(y,N)=>{var D,A,M;const O=yt,$=Ae;return g(),k("div",{ref_key:"captureRef",ref:x,class:"w-full p-[14px]",style:qe({height:b.value+"px"})},[_a,e("div",ka,[e("div",wa,[e("div",Ra,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND NUMBER",2),e("span",Ta,l(((D=y.refundInfo.refundNo)==null?void 0:D.length)===13?y.refundInfo.refundNo.substring(4):y.refundInfo.refundNo),1)]),e("div",Na,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND TYPE",2),e("span",$a,l(y.refundInfo.tktType==="D"?"DOMESTIC":"INTERNATIONAL"),1)]),e("div",Ca,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CITY / OFFICE",2),e("div",Ia,l(y.refundInfo.office),1)]),e("div",Sa,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"AGENT",2),e("span",Ea,l(y.refundInfo.agent),1)])]),e("div",Da,[e("div",Aa,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"IATA NUMBER",2),e("span",ja,l(y.refundInfo.iata),1)]),e("div",Va,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"DATE / TIME",2),e("span",Fa,l(y.refundInfo.refundDate),1)]),e("div",Oa,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},Pa,2),e("span",Ba,l(y.refundInfo.refundFormCurrency),1)]),e("div",Ua,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},Ha,2),e("span",La,l(y.refundInfo.payType),1)])]),e("div",Ga,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"PASSENGER NAM",2),e("span",qa,l(y.refundInfo.name),1)]),e("div",Qa,[e("div",Ya,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"AIRLINE CODE",2),e("span",Ja,l(y.refundInfo.airline),1)]),e("div",Ka,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"TICKET NO.",2),e("span",Xa,l(y.refundInfo.ticketNoView),1)]),e("div",Wa,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CONJUNCTION",2),e("span",Za,l(y.refundInfo.conjunction),1)])]),e("div",en,[e("div",tn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND COUPON-",2),e("div",sn,[(g(!0),k(Q,null,Y(y.refundInfo.couponNos,(w,I)=>(g(),k("span",{key:I},l(I+1)+":"+l(w)+"  ",1))),128))])]),e("div",an,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"ET(Y/N)",2),e("div",nn,l(y.refundInfo.etTagNew?"Y":"N"),1)])]),e("div",on,[e("div",rn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REMARK",2),e("span",ln,l(y.refundInfo.remarkInfo??""?`${(A=y.refundInfo.remarkInfo)==null?void 0:A.substring(0,2)}-${(M=y.refundInfo.remarkInfo)==null?void 0:M.substring(2)}`:""),1)]),e("div",dn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CREDIT CARD",2),e("span",un,l(y.refundInfo.creditCard??""),1)])])]),e("div",cn,[h(O,{"border-style":"dashed",class:"m-y-[6px] w-[1012px]"})]),e("div",pn,[e("div",fn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},gn,2),e("span",hn,l(Number(y.refundInfo.totalAmount).toFixed(2)),1)]),e("div",xn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"ADD TAX(ES)",2),e("span",yn,"+ "+l(y.refundInfo.totalTaxs),1)])]),e("div",vn,[h(O,{"border-style":"dashed",class:"m-y-[6px] w-[1012px]"})]),e("div",bn,[e("div",_n,[e("div",kn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"COMMISSION",2),wn,e("span",Rn,l(y.refundInfo.commision),1),Tn,e("span",Nn,l(y.refundInfo.commisionRate),1),$n])]),e("div",Cn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"OTHER DEDUCTION",2),In,e("span",Sn,l(y.refundInfo.otherDeduction),1)]),e("div",En,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"NET REFUND",2),e("span",Dn,"= "+l(y.refundInfo.netRefund)+" ("+l(y.refundInfo.payType)+" "+l(y.refundInfo.refundFormCurrency)+")",1)]),e("div",An,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"TAX",2),jn,e("span",Vn,l(y.refundInfo.refundFormCurrency),1),e("div",Fn,[(g(!0),k(Q,null,Y((y.refundInfo.taxs??[]).filter(w=>w.taxAmount||w.taxCode),(w,I)=>(g(),k("span",{key:I},[B(l(Number(w.taxAmount).toFixed(2))+l(w.taxCode)+" ",1),I!==(y.refundInfo.taxs??[]).filter(F=>F.taxAmount||F.taxCode).length-1?(g(),k("span",On,"+")):X("",!0)]))),128))])])]),e("div",{class:"w-[1012px] h-[auto] items-center gap-2.5 relative mt-[10px] mb-[20px] inline-flex",onMouseenter:N[2]||(N[2]=w=>a(r)("enter")),onMouseleave:N[3]||(N[3]=w=>a(r)("leave"))},[Mn,s.value?(g(),S($,{key:1,modelValue:_.value,"onUpdate:modelValue":N[0]||(N[0]=w=>_.value=w),class:"end-tilte",maxlength:"60","show-word-limit":"",onInput:N[1]||(N[1]=w=>_.value=_.value.toUpperCase())},null,8,["modelValue"])):(g(),k("div",Pn,l(_.value),1)),Bn],32),y.originalTickets.length>0?(g(!0),k(Q,{key:0},Y(y.originalTickets,(w,I)=>(g(),k("div",{key:I,class:R([t.value?"text-[18px]":"","w-[1012px] p-[10px] mb-[14px] bg-gray-7 text-gray-1 text-[14px] last:mb-[0px]"])},[e("pre",null,l(a(je).decode(w)),1)],2))),128)):X("",!0)],4)}}});const zn=ve(Un,[["__scopeId","data-v-b18dd19e"]]),Hn={class:"electronic-print-refund-form"},Ln={class:"overflow-y-scroll"},Gn={class:"flex justify-center mt-5"},qn=de({__name:"ElectronicPrintRefundForm",props:{modelValue:{type:Boolean},refundInfo:{},originalTickets:{}},emits:["update:modelValue"],setup(o,{emit:u}){const x=o,t=u,b=le(x.modelValue),s=E(),_=E(!1),r=()=>{t("update:modelValue",!1)},y=async()=>{_.value=!0,s.value.originInfo=!0,await Qe(),await ge(s.value,{paging:!0,style:`<style>
          .print-refund-form-panel {
            width: 1050px;
            height: 100%;
          }
          * {
            font-size: 15px;
          }
        </style>`}),s.value.originInfo=!1,_.value=!1},N=me(()=>window.innerHeight-120),O=async()=>{var I,F,q,j;if(!((I=s.value)!=null&&I.captureRef))return;const $=((F=s.value)==null?void 0:F.captureRef.scrollHeight)+20,D=(q=s.value)==null?void 0:q.captureRef.scrollWidth;s.value.captureRef.style.height=$+"px";const A=await Ye((j=s.value)==null?void 0:j.captureRef,{width:D,height:$,scale:1});s.value.captureRef.style.height=N.value+"px";const M=A.toDataURL("image/png"),w=document.createElement("a");w.href=M,w.download=`${x.refundInfo.ticketNo}.png`,w.click()};return($,D)=>{const A=he,M=ke;return g(),k("div",Hn,[h(M,{modelValue:b.value,"onUpdate:modelValue":D[0]||(D[0]=w=>b.value=w),width:"1046","custom-class":"print-refund-form","show-close":!1,"close-on-click-modal":!1,onClose:r},{default:v(()=>[e("div",Ln,[h(zn,{ref_key:"printRef",ref:s,class:R(["print-refund-form-panel",_.value?"":"text-xs"]),"refund-info":$.refundInfo,"original-tickets":$.originalTickets},null,8,["class","refund-info","original-tickets"])]),e("div",Gn,[h(A,{class:"print-btn",type:"primary",onClick:y},{default:v(()=>[B(l($.$t("app.refundForm.print")),1)]),_:1}),h(A,{class:"print-btn text-color",onClick:O},{default:v(()=>[B(l($.$t("app.refundForm.download")),1)]),_:1},8,["onClick"]),h(A,{class:"print-btn text-color",onClick:r},{default:v(()=>[B(l($.$t("app.refundForm.cancelBtn")),1)]),_:1},8,["onClick"])])]),_:1},8,["modelValue"])])}}});const Qn=(o,u)=>{const{t:x}=Ee(),t=le(!0),b=le(""),s=E(),_=le(!1),r=E({}),y=E({}),N=E({}),O=E([]),$=E(!1),D=le(!1),A=E([]),M=E([]),w=(n,d)=>oe(n)?d.toString().endsWith(".00")?d.toString().slice(0,-3):d:Number(d).toFixed(2),I=(n,d)=>{let m=[];return m=n.map(C=>({taxCode:C.taxCode,taxAmount:w(d,C.taxAmount)})),m.length<10?m.concat(new Array(10-m.length).fill({taxCode:"",taxAmount:""})).map(C=>({...C})):m},F=()=>{for(const n in r.value.segmentInfos){const d=[...r.value.segmentInfos[n]];O.value.push(d)}},q=()=>{const n=[];return r.value.segmentInfos&&Object.values(r.value.segmentInfos).forEach(d=>{(d??[]).forEach(m=>{m.ticketStatus==="REFUNDED"&&n.push(m)})}),n},j=(n,d,m)=>d?m!=="ARL"||d.startsWith(n)?d:`${n}-${d}`:"-",K=()=>{if(!r.value.ticketNoEnd&&r.value.ticketNoView){const n=r.value.ticketNoView.length,d=r.value.ticketNoView.substring(n-2);return`${r.value.ticketNo}-${d}`}return r.value.ticketNo===r.value.ticketNoEnd?r.value.ticketNo:`${r.value.ticketNo}-${r.value.ticketNoEnd}`},c=()=>{var n,d,m;F(),A.value=I(r.value.taxInfos,r.value.currency),M.value=r.value.couponNo?JSON.parse(JSON.stringify(r.value.couponNo)):[],N.value={refundNo:o.isSupplementRefund?"-":j(r.value.airlineCode,r.value.cmdNo,r.value.ticketManagementOrganizationCode??""),volunteer:r.value.cmdOption,createUser:r.value.operator,prntNo:r.value.deviceNum,refundType:o.isSupplementRefund?x("app.refundForm.manualRefundType"):"-",refundDate:r.value.refundDate??"",international:r.value.international==="I"?"INTERNATIONAL":"DOMESTIC",agent:r.value.agent,iata:r.value.iataNo,office:r.value.office,tktType:r.value.ticketType==="D"?"D":"I",ticketManagementOrganizationCode:r.value.ticketManagementOrganizationCode,ticketNo:K(),ticketNoView:r.value.ticketNoView,conjunction:r.value.conjunction,name:r.value.passengerName,psgType:r.value.passengerType,totalAmount:w(r.value.currency,r.value.grossRefund),payType:r.value.payMethod,currency:r.value.currency,etTagNew:r.value.refund==="Y",taxs:I(r.value.taxInfos,r.value.currency),totalTaxs:w(r.value.currency,r.value.totalTaxs),commision:w(r.value.currency,r.value.commission),commisionRate:Number(r.value.commissionRate)>0?w(r.value.currency,r.value.commissionRate):"",rate:r.value.commissionRate?"1":"0",otherDeductionRate:"",otherDeduction:w(r.value.currency,r.value.deduction),remark:r.value.remark?r.value.remark.substring(2):"",netRefund:w(r.value.currency,r.value.netRefund),creditCard:r.value.creditCard,airline:r.value.airlineCode,crsPnrNo:"",pnr:"",isCoupon:"",receiptPrinted:"",check:"",remarkCode:r.value.remark?r.value.remark.substring(0,2):"",remarkInfo:r.value.remark,checkedSeg:q(),couponNos:r.value.couponNo??[],printRefundFormTicketNo:`${(n=r.value.ticketNo)==null?void 0:n.substring(3)}-${(d=r.value.ticketNoSecond)==null?void 0:d.substring(3)}`,refundFormCurrency:((m=r.value)==null?void 0:m.refundFormCurrency)??""}},p=()=>{u("update:modelValue",!1)},z=()=>{t.value=!1,s.value.resetForm()},H=async()=>{D.value=!0},W=()=>{$.value=!0},T=async n=>{var m,C,P;$.value=!1;const d={refundNo:(m=s.value)==null?void 0:m.getFormDate().refundNo,ticketNo:o.refundOperationCondition.ticketNo,ticketType:o.printerType?o.printerType:o.refundOperationCondition.ticketType,ticketManagementOrganizationCode:(C=s.value)==null?void 0:C.getFormDate().ticketManagementOrganizationCode,printerNo:(P=s.value)==null?void 0:P.getFormDate().prntNo,resetTicketStatus:n};try{const U=Re("091T0105");_.value=!0,await vt(d,U),_.value=!1,await Te(x("app.refundForm.successMsg")),(o==null?void 0:o.isSalesDaily)??!1?u("reSalesDaily"):await u("reQueryTicket"),await p()}finally{_.value=!1}},L=()=>{t.value=!1},V=()=>{t.value=!0},se=()=>{s.value.resetForm()},ae=n=>({refund:n.etTagNew?"Y":"N",currency:n.currency,payMethod:n.payType,remark:n.remarkInfo??"",creditCard:n.creditCard?n.creditCard:"",couponNos:n.couponNos,name:je.encode((n.name??"").trim())}),ie=n=>{const d=[];return n.forEach(m=>{if(m.taxAmount&&m.taxCode){const C={taxCode:m.taxCode,taxAmount:Number(m.taxAmount)};d.push(C)}}),d},G=n=>({commission:n.commision&&Number(n.commision)>0?n.commision.toString():"0",commissionRate:n.commisionRate.toString()??"",grossRefund:n.totalAmount.toString(),deduction:n.otherDeduction.toString(),netRefund:n.netRefund.toString(),taxInfos:ie(n.taxs)}),re=n=>({refundNo:n.refundNo,ticketNo:n.ticketNo.includes("-")?n.ticketNo.split("-")[0]:n.ticketNo,ticketType:o.printerType?o.printerType:n.tktType,printerNo:n.prntNo,ticketManagementOrganizationCode:n.ticketManagementOrganizationCode??"",refundFormPassengerItem:ae(n),refundFormPriceItem:G(n)}),f=async n=>{_.value=!0;const d=re(n);let m;try{const C=Re("091T0106");m=(await bt(d,C)).data.value,(m==null?void 0:m.code)==="200"&&(await Te(x("app.refundForm.editSuccess")),u("update:modelValue",!1))}finally{_.value=!1}},i=async()=>{var P,U,ee;const n=(P=s.value)==null?void 0:P.getFormDate();if(!n.couponNos.some(ue=>ue!=="0000")){Je({type:"warning",message:x("app.agentTicketRefund.selSeg")});return}const m=await((U=s.value)==null?void 0:U.validate()),C=(ee=s.value)==null?void 0:ee.validSegment();m&&C&&f(n)};return De(()=>{t.value=o.isSupplementRefund,b.value=t.value?x("app.refundForm.supplementaryRefundInfo"):x("app.refundForm.refundFormInfo"),r.value=o.refundTicketData,o.refundTicketData&&c()}),{taxsHistory:A,refundTicketData:r,renderData:y,showEdit:t,title:b,fullscreenLoading:_,showDeleteConfirm:$,closeDialog:p,deleteRefund:W,editRefund:V,print:H,showRefundInfo:L,refundSegmentInfo:O,formInfo:N,formRef:s,initFormData:se,handleCommit:i,cancelEdit:z,onDeleteConfirm:T,showPrintRefundForm:D,couponNoHistory:M}},Yn=Qn,Jn=o=>(xe("data-v-91c467a0"),o=o(),ye(),o),Kn=Jn(()=>e("i",{class:"iconfont icon-close"},null,-1)),Xn=[Kn],Wn={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},Zn={key:0},eo={key:0,class:"mt-[10px] py-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},to={key:1},so={class:"mt-[10px] pt-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},ao=de({__name:"TicketRefundForm",props:{isSupplementRefund:{type:Boolean},printerNo:{},printerType:{},refundOperationCondition:{},refundTicketData:{},isSalesDaily:{type:Boolean},disableOperateButton:{type:Boolean}},emits:["update:modelValue","reQueryTicket","reSalesDaily"],setup(o,{emit:u}){const x=o,t=u,{showEdit:b,title:s,fullscreenLoading:_,closeDialog:r,deleteRefund:y,editRefund:N,print:O,refundSegmentInfo:$,formInfo:D,formRef:A,initFormData:M,handleCommit:w,cancelEdit:I,showDeleteConfirm:F,onDeleteConfirm:q,showPrintRefundForm:j,taxsHistory:K,couponNoHistory:c}=Yn(x,t);return(p,z)=>{const H=he,W=ke,T=Ke;return g(),S(W,{title:a(s),width:"1040px",class:"refund-form-dialog","show-close":!1,"close-on-click-modal":!1,onClose:a(r)},{default:v(()=>[e("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:z[0]||(z[0]=(...L)=>a(r)&&a(r)(...L))},Xn),e("div",null,[e("div",Wn,l(p.$t("app.agentTicketRefund.refundInformationForm")),1),h(fa,{ref_key:"formRef",ref:A,data:a(D),"coupon-no-history":a(c),"taxs-history":a(K),"refund-tickets":a($),disabled:!a(b)},null,8,["data","coupon-no-history","taxs-history","refund-tickets","disabled"])]),p.disableOperateButton?X("",!0):(g(),k("div",Zn,[a(b)?(g(),k("div",to,[e("div",so,[Ne((g(),S(H,{type:"primary",onClick:a(w)},{default:v(()=>[B(l(p.$t("app.agentTicketRefund.submit")),1)]),_:1},8,["onClick"])),[[T,a(_),void 0,{fullscreen:!0,lock:!0}]]),h(H,{onClick:a(M)},{default:v(()=>[B(l(p.$t("app.agentTicketRefund.reset")),1)]),_:1},8,["onClick"]),h(H,{onClick:a(I)},{default:v(()=>[B(l(p.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])])])):(g(),k("div",eo,[p.isSalesDaily?X("",!0):(g(),S(H,{key:0,type:"primary",onClick:a(N)},{default:v(()=>[B(l(p.$t("app.refundForm.edit")),1)]),_:1},8,["onClick"])),Ne((g(),S(H,{onClick:a(y)},{default:v(()=>[B(l(p.$t("app.refundForm.delete")),1)]),_:1},8,["onClick"])),[[T,a(_),void 0,{fullscreen:!0,lock:!0}]]),h(H,{onClick:a(O)},{default:v(()=>[B(l(p.$t("app.refundForm.print")),1)]),_:1},8,["onClick"])]))])),h(ba,{modelValue:a(F),"onUpdate:modelValue":z[1]||(z[1]=L=>$e(F)?F.value=L:null),"airline-code":p.refundTicketData.airlineCode,onConfirm:a(q)},null,8,["modelValue","airline-code","onConfirm"]),a(j)?(g(),S(qn,{key:1,modelValue:a(j),"onUpdate:modelValue":z[2]||(z[2]=L=>$e(j)?j.value=L:null),"refund-info":a(D),"original-tickets":p.refundTicketData.originalTickets??[]},null,8,["modelValue","refund-info","original-tickets"])):X("",!0)]),_:1},8,["title","onClose"])}}});const ko=ve(ao,[["__scopeId","data-v-91c467a0"]]);export{ge as P,fa as R,ko as T,pe as d,_e as f,fe as m};
