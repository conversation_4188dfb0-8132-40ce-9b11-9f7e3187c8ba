import{L as c,q as n,v as p,w as o,W as m,x as y,y as f,z as g,F as w,D as d,A as a,C as _,b9 as v,_ as b,K as C}from"./index-9381ab2b.js";const x=Symbol("rowContextKey"),R=["start","center","end","space-around","space-between","space-evenly"],S=["top","middle","bottom"],h=c({tag:{type:String,default:"div"},gutter:{type:Number,default:0},justify:{type:String,values:R,default:"start"},align:{type:String,values:S}}),j=n({name:"ElRow"}),k=n({...j,props:h,setup(r){const t=r,s=p("row"),l=o(()=>t.gutter);m(x,{gutter:l});const u=o(()=>{const e={};return t.gutter&&(e.marginRight=e.marginLeft=`-${t.gutter/2}px`),e}),i=o(()=>[s.b(),s.is(`justify-${t.justify}`,t.justify!=="start"),s.is(`align-${t.align}`,!!t.align)]);return(e,$)=>(y(),f(v(e.tag),{class:d(a(i)),style:_(a(u))},{default:g(()=>[w(e.$slots,"default")]),_:3},8,["class","style"]))}});var K=b(k,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/row/src/row.vue"]]);const E=C(K);export{E,x as r};
