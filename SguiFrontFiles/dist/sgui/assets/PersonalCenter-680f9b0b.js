import{bs as ie,b0 as ue,ab as ne,r as M,a9 as pe,w as X,cM as We,cN as ke,av as x,bH as me,by as Ae,bz as ge,bB as he,cO as Ge,q as ce,ah as j,al as ae,H as Pe,aI as Qe,aF as Ye,cP as $e,a4 as ve,x as h,B as T,P as a,a5 as O,a6 as Ue,G as l,z as c,J as I,ai as Q,aj as se,D as U,Q as i,C as ze,ak as W,y as B,am as De,an as Re,a8 as Be,bf as Le,cQ as He,cR as Ke,cS as Ze,cT as Je,o as Ce,cU as we,ac as be,aH as re,at as et,cV as tt,aY as Oe,bA as at,bC as st,R as lt,cW as Te,bG as Ee,aa as ye,bF as nt,bM as ot,a$ as xe,A as t,b3 as z,cX as it,cY as Ne,cZ as rt,c_ as Ie,au as Ve,c6 as ut,c$ as pt,ae as Fe,b1 as ct,aZ as dt,b9 as ft}from"./index-9381ab2b.js";import{U as mt}from"./passwordValid-45ecf3f8.js";import{g as fe}from"./encrypt-bd3798f5.js";import{u as gt}from"./usePersonalization-2a6caa7c.js";import{g as ht}from"./config-b573cde3.js";import{a as le,E as q}from"./index-951011fc.js";import{_ as vt}from"./Personalization.vue_vue_type_script_setup_true_lang-5fcfa236.js";import{_ as de}from"./_plugin-vue_export-helper-c27b6911.js";import{i as bt,d as yt,b as Ct}from"./airline-7e25a8f8.js";import{b as _t}from"./common-4e87300a.js";import{E as v}from"./index-d6bcff40.js";import{E as A}from"./index-2b7c36a5.js";import{P as Se}from"./PrintNoSelect-c0b0bc1c.js";import{E as kt}from"./index-1dbceb27.js";import{E as $t}from"./index-a7943392.js";import{E as wt}from"./index-e22833ad.js";import{E as Tt,a as Et}from"./index-b4196982.js";import{a as Me,E as Xe}from"./index-57a4abc9.js";import"./castArray-25c7c99e.js";import"./browser-6cfa1fde.js";import"./ticketOperationApi-8106707a.js";import"./index-094198d8.js";import"./index-c19c3f80.js";import"./isUndefined-aa0326a0.js";import"./index-34c19038.js";import"./strings-820bea19.js";import"./isEqual-a619023a.js";import"./index-a4ffe93f.js";import"./flatten-85480810.js";import"./index-729d485f.js";function Nt(){const s=document.querySelector("#msg-two-loading")?"#msg-two-loading":"#msg-loading";ie.service({target:"#aside-loading",spinner:!0,customClass:"customLoading"}),ie.service({target:"#tabs-loading",spinner:!0,customClass:"customLoading"}),ie.service({target:s,spinner:!0,customClass:"customLoading"}),ie.service({target:"#server-loading",spinner:!0,customClass:"customLoading"})}const H=ue({oldcertificate:"",certificate:"",recertificate:""}),It=()=>({securityTitle:()=>{const y=ue({btn:["简体中文","繁體中文","English"],select:x()==="en"?2:0,language:[0,1,2]});return{title:y,clickActive:n=>{y.select=n}}},securityMessage:()=>{const y=ue({securityInfo:!0,bgColor:"#EBF2FF"});return{showOrClose:y,iconClose:()=>{y.bgColor="#ffffff",y.securityInfo=!1}}}}),Vt=()=>{const{t:s}=ne(),d=M([]),y=[s("app.personal.oauthTokenRule1"),s("app.personal.oauthTokenRule2"),s("app.personal.oauthTokenRule3"),s("app.personal.oauthTokenRule4"),s("app.personal.oauthTokenRule5")],{validLength:E,validVaried:n,validContinuity:N,validKeyboard:p,validRepeat:$}=mt(),C=_=>{const o=[];return E(_)&&o.push(y[0]),n(_)&&o.push(y[1]),N(_)&&o.push(y[2]),p(_)&&o.push(y[3]),$(_)&&o.push(y[4]),o},r=(_,o,u)=>{const{certificate:m,oldcertificate:b}=H;d.value=C(H.certificate),d.value.length!==0?u(new Error("  ")):m===b&&u(s("app.personal.confirmPasswordOldTip")),u()};return{formInline:H,rules:{oldcertificate:[{required:!0,message:" ",trigger:"blur"}],certificate:[{required:!0,validator:r,trigger:"blur"},{required:!0,validator:r,trigger:"change"}],recertificate:[{required:!0,message:" ",trigger:"blur"},{required:!0,validator:(_,o,u)=>{const{certificate:m}=H,{recertificate:b}=H;m===""?u(new Error(" ")):m!==b&&u(s("app.personal.confirmPasswordTip")),u()},trigger:"change"}]},validList:d,validArray:y}},Ft=()=>{const{t:s}=ne(),d=pe(),y=X(()=>d.state.user.userName),E=M(),n=async $=>{try{await me($),await Ae("09300123")}finally{const C=ge();localStorage.clear(),he(C)}},N=async($,C)=>{try{await ke($,C);const r=await Ge("09300122");if([s("app.tips.networkErrCode")].includes((r==null?void 0:r.code)??""))await N($,C);else{const F=ge();localStorage.clear(),he(F)}}finally{}};return{userName:y,formRef:E,onSubmit:()=>{E.value.validate(async $=>{if($){const{oldcertificate:C,certificate:r,recertificate:V}=H,F={oldcertificate:fe(C),certificate:fe(r),recertificate:fe(V)},_=await We(F,"09300132"),o=["SGUI-0142-10","SGUI-0143-11","ARCHETYPE-0142-15","ARCHETYPE-0143-16","SGUI-0150-19","SGUI-07-83W11","SGUI-07-83R11"],u=["SGUI-0141-19","ARCHETYPE-0141-14","SGUI-07-83W11"],m=[s("app.tips.networkErrCode")];_.code==="200"?await n(s("app.tips.oauthTokenUpdatedSuccessfully")):o.includes(_.code)?await n(s("app.tips.notificationSendingFailure")):u.includes(_.code)?await n(s("app.tips.notificationConfigurationIncorrect")):m.includes(_.code)?await N(s("app.tips.networkErrCode"),s("app.tips.notificationNetworkErr")):ke(_.code,_.msg,_.transactionID,null,null,_.satTransactionID,_.slnDesc)}})}}},St=ce({name:"UpdatePassword",components:{ElForm:le,ElFormItem:q,ElInput:j,ElButton:ae,ElIcon:Pe,Close:Qe,Personalization:vt},setup(){const s=M(!1),{securityTitle:d,securityMessage:y}=It(),{title:E,clickActive:n}=d(),{showOrClose:N,iconClose:p}=y(),{formInline:$,rules:C,validArray:r,validList:V}=Vt(),{userName:F,formRef:_,onSubmit:o}=Ft(),{personalizationRules:u}=gt(ht.personalCenter);return Ye(()=>{s.value=$.certificate.length===0&&V.value.length===0}),{showDefault:s,...$e(E),...$e(N),iconClose:p,clickActive:n,validArray:r,formInline:$,rules:C,validList:V,userName:F,formRef:_,onSubmit:o,getLocale:x,personalizationRules:u}}});const G=s=>(De("data-v-abfff5fe"),s=s(),Re(),s),At={key:0,class:"update-password"},Pt={key:0,class:"security-title"},Ut={key:1,class:"security-title"},Dt={key:2,class:"security-title"},Rt=["onClick"],Bt={class:"security-content"},Lt={key:0},Ot=G(()=>a("span",null,"由于近期撞库攻击日益猖獗，敬请各位用户在本系统中",-1)),xt=G(()=>a("span",{style:{color:"red"}},"不要使用与其他网站相同或相近的用户名口令",-1)),Mt=G(()=>a("span",null,"。如因您的故意或疏忽过失，导致您在本系统的用户名口令泄露，对业务造成的影响由您自行承担，对本系统带来的安全危害，",-1)),Xt=G(()=>a("span",null,"系统所有权人中国民航信息网络股份有限公司将保留进一步追究法律责任的权利。——中国民航信息网络股份有限公司",-1)),jt=[Ot,xt,Mt,Xt],qt={key:1},Wt=G(()=>a("span",null,"由於近期撞庫攻擊日益猖獗，敬請各位用戶在本系統中",-1)),Gt=G(()=>a("span",{style:{color:"red"}},"不要使用與其他網站相同或相近的用戶名口令",-1)),Qt=G(()=>a("span",null,"。如因您的故意或疏忽過失，導致您在本系統的用戶名口令泄露，對業務造成的影響由您自行承擔，對",-1)),Yt=G(()=>a("span",null,"本系統帶來的安全危害，系統所有權人中國民航信息網絡股份有限公司將保留進壹步追究法律責任的權利。——中國民航信息網絡股份有限公司",-1)),zt=[Wt,Gt,Qt,Yt],Ht={key:2},Kt={class:"message-ul"},Zt=G(()=>a("span",null,null,-1));function Jt(s,d,y,E,n,N){const p=ve("close"),$=Pe,C=j,r=q,V=ae,F=le,_=ve("Personalization");return h(),T("div",null,[s.personalizationRules?(h(),B(_,{key:1,"rule-info":s.personalizationRules,"user-name":s.userName},null,8,["rule-info","user-name"])):(h(),T("div",At,[a("div",{class:"security-info",style:ze({"background-color":s.bgColor})},[O(a("div",null,[l($,{class:"security-close",onClick:s.iconClose},{default:c(()=>[l(p)]),_:1},8,["onClick"]),a("p",null,[s.language[0]===s.select?(h(),T("span",Pt,"安全声明")):I("",!0),s.language[1]===s.select?(h(),T("span",Ut,"安全聲明")):I("",!0),s.language[2]===s.select?(h(),T("span",Dt,"Security Disclaimer")):I("",!0),(h(!0),T(Q,null,se(s.btn,(o,u)=>(h(),T("span",{key:o,class:U(["title-btn",{"btn-active":u===s.select}]),onClick:m=>s.clickActive(u)},i(o),11,Rt))),128))]),a("div",Bt,[s.language[0]===s.select?(h(),T("p",Lt,jt)):I("",!0),s.language[1]===s.select?(h(),T("p",qt,zt)):I("",!0),s.language[2]===s.select?(h(),T("p",Ht," With the ever-increasing amount of Social Engineering Attack, system users should not reuse the same or similar password of this site on any other sites. In case of any leakage of the username and/or password, no matter intentionally of un-intentionally, you will be liable for the business impact caused. The system owner, Travelsky Limited Inc., will reserve the right to take necessary legal actions against the relevant system users. ——Travelsky Limited Inc ")):I("",!0)])],512),[[Ue,s.securityInfo]])],4),a("div",{class:U(["form",s.getLocale()==="en"?"en-form":"cn-form"])},[l(F,{ref:"formRef","hide-required-asterisk":!1,rules:s.rules,inline:!1,model:s.formInline,"label-width":"95px","label-position":"left"},{default:c(()=>[l(r,{label:s.$t("app.personal.oldPassword"),prop:"oldcertificate"},{default:c(()=>[l(C,{modelValue:s.formInline.oldcertificate,"onUpdate:modelValue":d[0]||(d[0]=o=>s.formInline.oldcertificate=o),type:"password",placeholder:s.$t("app.check.inputOldPwd"),autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(r,{label:s.$t("app.personal.newPassword"),prop:"certificate",class:"form-item"},{default:c(()=>[l(C,{modelValue:s.formInline.certificate,"onUpdate:modelValue":d[1]||(d[1]=o=>s.formInline.certificate=o),type:"password","show-message":!1,placeholder:s.$t("app.check.inputNewPwd"),class:"form-item",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(r,{label:s.$t("app.personal.confirmPassword"),prop:"recertificate",class:"form-item confirm"},{default:c(()=>[l(C,{modelValue:s.formInline.recertificate,"onUpdate:modelValue":d[2]||(d[2]=o=>s.formInline.recertificate=o),type:"password",placeholder:s.$t("app.check.reEnterNewPwd"),class:"form-item",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(r,{class:"passWordButton"},{default:c(()=>[l(V,{type:"primary",class:"form-item btn-submit","data-gid":"081W0102",onClick:s.onSubmit},{default:c(()=>[W(i(s.$t("app.button.confirmeTheChange")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["rules","model"])],2),a("div",{class:U(["valid",s.getLocale()==="en"?"en-valid":"cn-valid"])},[a("div",{class:U(["message",s.getLocale()==="en"?"en-message":"cn-message"])},[a("div",{class:U(["message-left",s.getLocale()==="en"?"en-message-left":"cn-message-left"])},null,2),a("div",Kt,[a("ul",null,[(h(!0),T(Q,null,se(s.validArray,(o,u)=>(h(),T("li",{key:u,class:U({error:s.validList.includes(o),default:s.showDefault,"en-li":s.getLocale()==="en"})},[Zt,W(" "+i(o),1)],2))),128))])])],2)],2)]))])}const ea=de(St,[["render",Jt],["__scopeId","data-v-abfff5fe"]]),ta=()=>{const{t:s}=ne(),d=pe(),y=Be(),E=M(),n=M();let N="";const p=X(()=>$("defaultSellingGuiIataNum","sellingGuiIataNum")),$=(e,g)=>{var w,P,oe;const k=((w=d.state.user)==null?void 0:w[e])??"";return k||(((oe=(((P=d.state.user)==null?void 0:P[g])??"").split(";"))==null?void 0:oe[0])??"")},C={0:s("app.personal.oauthToken"),1:s("app.personal.oauthTokenMessage"),4:s("app.personal.oauthTokenEmail"),5:s("app.personal.oauthTokenMessageEmail"),6:s("app.personal.oauthTokenWechat"),7:s("app.personal.oauthTokenWechatMessage"),8:s("app.personal.oauthTokenWechatEmail"),9:s("app.personal.oauthTokenWechatMessageEmail")},r=ue({roleNames:"",mgrAirline:"",office:"",field1:"",userName:"",defaultRole:"",departmentName:"",locale:"",employeeId:"",employeeName:"",signature:"",certId:"",mobile:"",email:"",switchAirline:"",defaultOffice:"",defaultSystem:"",agentNo:"",agent:"",aviationAssociation:"",currency:"",airportCode:"",operator:!0,defaultRoleWithPid:!0,roleMap:{},flowLimit:"",overFlowed:"",unlockTimes:"",currentVersion:""}),V={switchAirline:[{required:!0,message:s("app.check.input2Word"),trigger:"blur"},{type:"string",pattern:Le,message:s("app.check.input2WordOrNumber"),trigger:"blur"}]},F={aviationAssociation:[{required:!0,message:s("app.check.inputIata"),trigger:"blur"}],agentNo:[{required:!0,message:s("app.check.enterOnly"),trigger:"blur"},{type:"string",pattern:He,message:s("app.check.agentRule"),trigger:"blur"}],defaultOffice:[{required:!0,message:s("app.check.officeRule"),trigger:"blur"},{type:"string",pattern:Ke,message:s("app.check.officeRule"),trigger:"blur"}],currency:[{required:!0,message:s("app.check.upTo10SeperateBySemicolon"),trigger:"blur"},{type:"string",pattern:Ze,message:s("app.check.inputCorrectCurrency"),trigger:"blur"}],airportCode:[{required:!0,message:s("app.check.airportRule"),trigger:"blur"},{type:"string",pattern:Je,message:s("app.check.airportRule"),trigger:"blur"}]},_=async e=>{if(r.userName=e.userName,r.defaultRole=e.defaultRole,x()==="en"){const g=await d.getters.userStrus,k=g==null?void 0:g.filter(w=>w.struName===e.departmentName);r.departmentName=k&&k.length>0?k[0].struEnName:e.departmentName}else r.departmentName=e.departmentName;r.locale=e.locale==="en"?s("app.personal.english"):s("app.personal.chinese"),r.employeeId=e.employeeId,r.employeeName=e.employeeName,r.signature=C[e.securityLevel],r.certId=e.certId||"-",r.mobile=e.mobile,r.email=e.email,r.switchAirline=e.switchAirline,r.defaultOffice=e.office,r.defaultSystem=e.defaultSystem,r.agent=e.agent,r.agentNo=e.agentNo,r.aviationAssociation=e.sellingGuiIataNum,r.currency=e.tssellingguicurrency,r.airportCode=e.tssellingguiairportcode,r.operator=e.operator,r.mgrAirline=e.mgrAirline,r.defaultRoleWithPid=e.defaultRoleWithPid,r.flowLimit=e.flowLimit?e.flowLimit:"-",r.overFlowed=e.overFlowed?s("app.personal.exceedFlowLimit"):s("app.personal.nonExceedFlowLimit"),r.unlockTimes=e.unlockTimes?e.unlockTimes:"-",r.expireTime="",r.epidNumber=""},o=()=>{const e=[re("div",{class:"info-tip-title text-[18px] text-gray-1"},s("app.domesticRepresentatives.unbindConfirm",{epidNumber:r.epidNumber})),re("div",{class:"info-tip-title text-[18px] mr-[10px] text-gray-1"},s("app.domesticRepresentatives.unbindConfirmTip"))];et.confirm(re("div",{class:"unbind-title"},e),{icon:re("em",{class:"iconfont icon-info-circle-line text-brand-2 text-[32px]"}),customClass:"alert-message-common crs-btn-ui unbind-epid-message-box min-w-[500px]",confirmButtonText:s("app.domesticRepresentatives.unbind"),cancelButtonText:s("app.ticketStatus.cancelBtn"),closeOnClickModal:!1,showClose:!1}).then(async()=>{const g=y.query.authValue,k=await _t(g);await tt(k,r.epidNumber??"","09300126"),Oe({type:"success",message:s("app.domesticRepresentatives.unbind")}),Ae("09300123");const w=ge();at(),he(w)})},u=async()=>{try{const g=(await st("09300127")).data;if(g){const{expireTime:k,epidNumber:w}=g;r.expireTime=lt(k).format("YYYY-MM-DD"),r.epidNumber=w}}catch{}},m=async()=>{const e=await d.getters.user;if(await _(e),!(e!=null&&e.defaultOfficeInternational)&&(e!=null&&e.crsSystem)&&await u(),r.operator)try{await E.value.validate()}catch{}},b=async e=>{var _e;const g=d.state.user.office,k=ye("currnetHasEtermInfoSessionession",""),w=JSON.parse(k.value?k.value:"{}");if(e===g)return;const P=((_e=(e||"").split(";"))==null?void 0:_e[0])??"",oe=d.state.user.defaultRole,je=d.state.user.defaultUserGroup;if(P){const qe={office:P,roleName:oe,userGroup:je,currentSellingGuiIataNum:"",system:r.defaultSystem,etermId:(w==null?void 0:w.etermId)??"",etermPwd:(w==null?void 0:w.etermPwd)??"",etermServerAddress:(w==null?void 0:w.etermServerAddress)??""};await nt(qe,"09400105")}},S=async e=>{var w;const g=d.state.user.sellingGuiIataNum;if(e===g)return;const k=((w=(e||"").split(";"))==null?void 0:w[0])??"";await ot({sellingGuiIataNum:k},"09400114")},D=async()=>{if(N.trim()!==r.switchAirline.trim()){const e=await bt("09400117"),g=new Date().getTime();await xe("diLocalData",yt(e.data),g)}N=r.switchAirline.trim()},R=async()=>{const e={switchAirline:r.switchAirline,office:r.defaultOffice,agentNo:r.agentNo,sellingGuiIataNum:r.aviationAssociation,tssellingguicurrency:r.currency,tssellingguiairportcode:r.airportCode};await Promise.all([n.value.validate(),E.value.validate()]),await Te(e,"091V0843"),await D(),await me(s("app.tips.success")),await b(r.defaultOffice),await S(r.aviationAssociation);const g=await Ee("09400110"),k=ye("needCallEtermInfoDialog","");g.data.manualInputEtermInfo&&(k.value="need"),d.dispatch("addUser",g.data)},Y=()=>{n.value.validate(async e=>{if(!e)return;await Te({switchAirline:r.switchAirline},"091V0843"),await D(),await me(s("app.tips.success"));const g=await Ee("09400111");d.dispatch("addUser",g.data)})},K=async()=>{!E.value&&!n.value||(r.defaultRoleWithPid?Y():await R())},Z=X({get:()=>{var e;return(e=r.switchAirline)==null?void 0:e.toUpperCase()},set:e=>{r.switchAirline=e==null?void 0:e.toUpperCase()}}),J=X({get:()=>{var e;return(e=r.agentNo)==null?void 0:e.toUpperCase()},set:e=>{r.agentNo=e==null?void 0:e.toUpperCase()}}),L=X({get:()=>{var e;return(e=r.currency)==null?void 0:e.toUpperCase()},set:e=>{r.currency=e==null?void 0:e.toUpperCase()}}),ee=X({get:()=>{var e;return(e=r.aviationAssociation)==null?void 0:e.toUpperCase()},set:e=>{r.aviationAssociation=e==null?void 0:e.toUpperCase()}}),te=X({get:()=>{var e;return(e=r.airportCode)==null?void 0:e.toUpperCase()},set:e=>{r.airportCode=e==null?void 0:e.toUpperCase()}}),f=X({get:()=>{var e;return(e=r.defaultOffice)==null?void 0:e.toUpperCase()},set:e=>{r.defaultOffice=e==null?void 0:e.toUpperCase()}});return Ce(()=>{r.currentVersion=`SGUI_V_${we.slice(0,we.lastIndexOf("."))}`,m().then()}),be(()=>d.getters.user,async(e,g)=>{const k=await g,w=await e;k.defaultRoleWithPid!==w.defaultRoleWithPid&&m()}),{form:r,rules:F,formRef:E,getCurrentUserInfo:m,onSave:K,switchAirline:Z,agentNo:J,currency:L,aviationAssociation:ee,airportCode:te,defaultOffice:f,airlineFormRef:n,unbindEpidClick:o,airlineFormRules:V,defaultSellingGuiIataNum:p}},aa=ta,sa=s=>(De("data-v-7dcff83a"),s=s(),Re(),s),la={class:"base-info"},na={class:"base-info-title"},oa={class:"base-info-container"},ia={class:"base-info-value"},ra={class:"base-info-label"},ua={class:"base-info-value"},pa={class:"base-info-label"},ca={class:"base-info-value"},da={class:"base-info-value"},fa={class:"base-info-label"},ma={class:"base-info-value"},ga={class:"base-info-label"},ha={class:"base-info-value"},va={class:"base-info-value"},ba={class:"base-info-value"},ya={class:"base-info-label"},Ca={class:"base-info-value"},_a={class:"base-info-label"},ka={class:"base-info-value"},$a={class:"base-info-container"},wa={class:"base-info-value"},Ta={class:"base-info-label"},Ea={class:"base-info-value"},Na={class:"base-info-container"},Ia={class:"base-info-value"},Va={class:"base-info-label"},Fa={class:"base-info-value"},Sa={class:"base-info-container"},Aa={class:"base-info-label"},Pa={class:"base-info-value"},Ua={key:0,class:"base-info-container"},Da={class:"base-info-label"},Ra={class:"base-info-value base-info-epid-value"},Ba={class:"base-info-label"},La={class:"base-info-value"},Oa={class:"base-info-container"},xa={class:"base-info-label"},Ma={class:"base-info-value"},Xa={key:0,class:"sell-setting"},ja={class:"base-info-title"},qa={class:"base-info-container"},Wa={class:"base-info-label"},Ga={class:"base-info-value"},Qa=sa(()=>a("label",{class:"base-info-label"},"Office",-1)),Ya={class:"base-info-value"},za={class:"base-info-label base-info-labelw100"},Ha={class:"base-info-value showAgentTop"},Ka={class:"base-info-label"},Za={class:"base-info-value"},Ja={class:"base-info-label"},es={class:"base-info-value"},ts={class:"save-btn"},as=ce({__name:"Information",setup(s){const{form:d,rules:y,formRef:E,onSave:n,switchAirline:N,agentNo:p,currency:$,aviationAssociation:C,airportCode:r,defaultOffice:V,airlineFormRef:F,airlineFormRules:_,unbindEpidClick:o,defaultSellingGuiIataNum:u}=aa();return(m,b)=>(h(),T("div",la,[a("div",null,[a("p",na,i(m.$t("app.personal.settingsProfile")),1),a("div",oa,[l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",{class:U(["base-info-label",t(x)()==="en"?"base-info-label-en":""])},i(m.$t("app.personal.userName")),3),a("span",ia,i(t(d).userName),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",ra,i(m.$t("app.personal.organization")),1),a("span",ua,i(t(d).departmentName),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",pa,i(m.$t("app.personal.language")),1),a("span",ca,i(t(d).locale),1)]),_:1})]),_:1}),l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",{class:U(["base-info-label",t(x)()==="en"?"base-info-label-en":""])},i(m.$t("app.personal.employeeNumber")),3),a("span",da,i(t(d).employeeId),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",fa,i(m.$t("app.personal.name")),1),a("span",ma,i(t(d).employeeName),1)]),_:1}),t(d).agent?(h(),B(t(v),{key:0,span:8},{default:c(()=>[a("label",ga,i(m.$t("app.personal.agent")),1),a("span",ha,i(t(d).agent),1)]),_:1})):(h(),B(t(v),{key:1,span:8}))]),_:1}),l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",{class:U(["base-info-label",t(x)()==="en"?"base-info-label-en":""])},i(m.$t("app.personal.settlementNo")),3),a("span",va,i(t(u)),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",{class:U(["base-info-label",t(x)()==="en"?"base-info-label-en":""])},i(m.$t("app.personal.flowLimit")),3),a("span",ba,i(t(d).flowLimit),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",ya,i(m.$t("app.personal.overFlowed")),1),a("span",Ca,i(t(d).overFlowed),1)]),_:1})]),_:1}),l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",_a,i(m.$t("app.personal.unlockTimes")),1),a("span",ka,i(t(d).unlockTimes),1)]),_:1})]),_:1})]),a("div",$a,[l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",{class:U(["base-info-label",t(x)()==="en"?"base-info-label-en":""])},i(m.$t("app.personal.certificationType")),3),a("span",wa,i(t(d).signature),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",Ta,i(m.$t("app.personal.certificateID")),1),a("span",Ea,i(t(d).certId),1)]),_:1}),l(t(v),{span:8})]),_:1})]),a("div",Na,[l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",{class:U(["base-info-label",t(x)()==="en"?"base-info-label-en":""])},i(m.$t("app.personal.cellphone")),3),a("span",Ia,i(t(d).mobile),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",Va,i(m.$t("app.personal.email")),1),a("span",Fa,i(t(d).email),1)]),_:1}),l(t(v),{span:8})]),_:1})]),a("div",Sa,[l(t(A),null,{default:c(()=>[t(d).operator?(h(),B(t(le),{key:1,ref_key:"airlineFormRef",ref:F,model:t(d),rules:t(_)},{default:c(()=>[l(t(q),{label:m.$t("app.personal.airline"),prop:"switchAirline",class:"airline"},{default:c(()=>[l(t(j),{modelValue:t(N),"onUpdate:modelValue":b[0]||(b[0]=S=>z(N)?N.value=S:null),class:"hasVal",placeholder:m.$t("app.check.input2Word")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])):(h(),B(t(v),{key:0,span:8},{default:c(()=>[a("label",Aa,i(m.$t("app.personal.airline")),1),a("span",Pa,i(t(d).switchAirline),1)]),_:1})),l(t(v),{span:8}),l(t(v),{span:8})]),_:1})]),t(d).epidNumber?(h(),T("div",Ua,[l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",{class:U(["font-bold text-[16px] text-gray-1",t(x)()==="en"?"base-info-label-en":""])},i(m.$t("app.domesticRepresentatives.connectionChannel")),3)]),_:1}),l(t(v),{span:8})]),_:1}),l(t(A),null,{default:c(()=>[l(t(v),{class:"flex items-center"},{default:c(()=>[a("label",Da,i(m.$t("app.domesticRepresentatives.ChannelNumber")),1),a("span",Ra,i(t(d).epidNumber),1),l(t(ae),{type:"primary",size:"small",onClick:t(o)},{default:c(()=>[W(i(m.$t("app.domesticRepresentatives.unbind")),1)]),_:1},8,["onClick"])]),_:1}),l(t(v),{span:16})]),_:1}),l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",Ba,i(m.$t("app.domesticRepresentatives.expiryDate")),1),a("span",La,i(t(d).expireTime),1)]),_:1}),l(t(v),{span:16})]),_:1})])):I("",!0),a("div",Oa,[l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",{class:U(["base-info-label",t(x)()==="en"?"base-info-label-en":""])},i(m.$t("app.personal.versionInfo")),3)]),_:1}),l(t(v),{span:8})]),_:1}),l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",xa,i(m.$t("app.personal.currentVersion")),1),a("span",Ma,i(t(d).currentVersion),1)]),_:1}),l(t(v),{span:16})]),_:1})])]),t(d).defaultRoleWithPid?I("",!0):(h(),T("div",Xa,[a("p",ja,i(m.$t("app.personal.salesAccountConfiguration")),1),a("div",qa,[t(d).operator?(h(),B(t(le),{key:1,ref_key:"formRef",ref:E,"label-width":"74px","label-position":"left",model:t(d),rules:t(y)},{default:c(()=>[l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[l(t(q),{label:m.$t("app.personal.iata"),class:"aviationNum",prop:"aviationAssociation"},{default:c(()=>[l(t(j),{modelValue:t(C),"onUpdate:modelValue":b[1]||(b[1]=S=>z(C)?C.value=S:null),class:"hasVal",placeholder:m.$t("app.check.enterOnly")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),l(t(v),{span:8},{default:c(()=>[l(t(q),{label:"Office",prop:"defaultOffice"},{default:c(()=>[l(t(j),{modelValue:t(V),"onUpdate:modelValue":b[2]||(b[2]=S=>z(V)?V.value=S:null),placeholder:m.$t("app.check.upTo5SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1})]),_:1}),l(t(v),{span:8},{default:c(()=>[l(t(q),{label:m.$t("app.personal.extraServiceAgent"),class:"agent",prop:"agentNo",style:{"align-items":"flex-start"},"label-width":"125px"},{default:c(()=>[l(t(j),{modelValue:t(p),"onUpdate:modelValue":b[3]||(b[3]=S=>z(p)?p.value=S:null),class:"hasVal",placeholder:m.$t("app.check.enterOnly")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})]),_:1}),l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[l(t(q),{label:m.$t("app.personal.currency"),prop:"currency"},{default:c(()=>[l(t(j),{modelValue:t($),"onUpdate:modelValue":b[4]||(b[4]=S=>z($)?$.value=S:null),placeholder:m.$t("app.check.upTo10SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),l(t(v),{span:8},{default:c(()=>[l(t(q),{label:m.$t("app.personal.airport"),prop:"airportCode"},{default:c(()=>[l(t(j),{modelValue:t(r),"onUpdate:modelValue":b[5]||(b[5]=S=>z(r)?r.value=S:null),placeholder:m.$t("app.check.upTo5SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),l(t(v),{span:8})]),_:1})]),_:1},8,["model","rules"])):(h(),T(Q,{key:0},[l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",Wa,i(m.$t("app.personal.iata")),1),a("span",Ga,i(t(d).aviationAssociation),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[Qa,a("span",Ya,i(t(d).defaultOffice),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",za,i(m.$t("app.personal.extraServiceAgent")),1),a("span",Ha,i(t(d).agentNo),1)]),_:1})]),_:1}),l(t(A),null,{default:c(()=>[l(t(v),{span:8},{default:c(()=>[a("label",Ka,i(m.$t("app.personal.currency")),1),a("span",Za,i(t(d).currency),1)]),_:1}),l(t(v),{span:8},{default:c(()=>[a("label",Ja,i(m.$t("app.personal.airport")),1),a("span",es,i(t(d).airportCode),1)]),_:1}),l(t(v),{span:8})]),_:1})],64))])])),a("div",ts,[t(d).operator?(h(),B(t(ae),{key:0,type:"primary","data-gid":"081W0101",onClick:t(n)},{default:c(()=>[W(i(m.$t("app.button.save")),1)]),_:1},8,["onClick"])):I("",!0)])]))}});const ss=de(as,[["__scopeId","data-v-7dcff83a"]]),ls=s=>{const{t:d}=ne(),y=Be(),E=M(!1),n=M(),N=pe(),p=M({ct:"",ctct:"",ctce:"",airlineSettings:!1,airlinesCTCT:[{airline:"",ctct:""}],unshared:!0,nonstop:!1,autoSelectCabinClass:!1,useQtb:!1,manualQuery:!1,domesticPrinterno:"",internationalPrinterno:"",backfieldEnName:!0,checkTrip:{airlineNameCheckSwitch:!1,cityName:!1,airportName:!1,passengerInformation:!1,idNumber:!1,contact:!1,priceIncludingTax:!1,cabinSpace:!1,flightTime:!1},defaultRemark:!1,remarkList:[""],defaultNatAndIssCountry:!1,autoOccupy:!1,passengerInfo:{passengerNationality:"",visaIssueCountry:""},place:"BOTTOM",followPnr:!1,domesticCities:"",internationalCities:""}),$=X(()=>{var e;return((e=N.state.user)==null?void 0:e.defaultOfficeInternational)??!1}),C=(e,g,k)=>{g||k(new Error(d("app.pnrManagement.validate.required"))),p.value.airlinesCTCT.filter(P=>P.airline.toLocaleUpperCase()===(g==null?void 0:g.toLocaleUpperCase())).length>1?k(new Error(d("app.pnrManagement.validate.contactor"))):k()},r=(e,g,k)=>{g?k():k(new Error(d("app.pnrManagement.validate.required")))},V={ct:[{pattern:it,message:d("app.pnrManagement.validate.formatErr"),trigger:"blur"}],ctct:[{pattern:Ne,message:d("app.pnrManagement.validate.formatErr"),trigger:"blur"}],airlinesCtct:[{pattern:Ne,message:d("app.pnrManagement.validate.formatErr"),trigger:"blur"},{validator:r,trigger:"blur"}],ctce:[{required:!1,pattern:rt,message:d("app.pnrManagement.validate.enterValidEmail"),trigger:["blur","change"]}],airline:[{pattern:Le,trigger:["blur","change"],message:d("app.pnrManagement.validate.characterCode")},{validator:C,trigger:["blur","change"]}],"passengerInfo.visaIssueCountry":[{pattern:/^[a-zA-Z]{2,3}$/,trigger:["change","blur"],message:d("app.intlPassengerForm.enterCharacterCode")}],"passengerInfo.passengerNationality":[{pattern:/^[a-zA-Z]{2,3}$/,trigger:["change","blur"],message:d("app.intlPassengerForm.enterCharacterCode")}],internationalCities:[{pattern:Ie,message:d("app.pnrManagement.validate.cityFormatErr"),trigger:"blur"}],domesticCities:[{pattern:Ie,message:d("app.pnrManagement.validate.cityFormatErr"),trigger:"blur"}]},F=()=>{p.value.airlinesCTCT.push({airline:"",ctct:""})},_=e=>{p.value.airlinesCTCT.splice(e,1)},o=()=>{p.value.remarkList.push("")},u=e=>{p.value.remarkList.splice(e,1)},m=async()=>{n.value.validate(async e=>{if(e)try{p.value.checkTrip.priceIncludingTax=p.value.checkTrip.passengerInformation,E.value=!0;const g={autoOccupy:p.value.autoOccupy,ct:p.value.ct,ctct:p.value.ctct,ctce:p.value.ctce,unshared:p.value.unshared,nonstop:p.value.nonstop,autoSelectCabinClass:p.value.autoSelectCabinClass,useQtb:p.value.useQtb,manualQuery:p.value.manualQuery,backfieldEnName:p.value.backfieldEnName,checkTrip:{airlineNameCheckSwitch:p.value.checkTrip.airlineNameCheckSwitch,cityName:p.value.checkTrip.cityName,airportName:p.value.checkTrip.airportName,passengerInformation:p.value.checkTrip.passengerInformation,idNumber:p.value.checkTrip.idNumber,contact:p.value.checkTrip.contact,priceIncludingTax:p.value.checkTrip.priceIncludingTax,cabinSpace:p.value.checkTrip.cabinSpace,flightTime:p.value.checkTrip.flightTime},airlinesCTCT:p.value.airlineSettings?p.value.airlinesCTCT:[],internationalPrinterno:p.value.internationalPrinterno,domesticPrinterno:p.value.domesticPrinterno,remarkList:p.value.defaultRemark?p.value.remarkList:[""],passengerInfo:{visaIssueCountry:p.value.defaultNatAndIssCountry?p.value.passengerInfo.visaIssueCountry:"",passengerNationality:p.value.defaultNatAndIssCountry?p.value.passengerInfo.passengerNationality:""},place:p.value.place,followPnr:p.value.followPnr,domesticCities:p.value.domesticCities,internationalCities:p.value.internationalCities},k=Ve("081W0103"),{data:w}=await pt(g,k);w.value&&(N.dispatch("updateUserPreferences",g),Oe({message:d("app.tips.success"),type:"success"}));const{data:P}=await Ct("081W0103");P!=null&&P.value&&await xe("COMMON_CITIES",JSON.stringify(P.value))}finally{E.value=!1}})},b=e=>{(e.keyCode===13||e.keyCode===100)&&m()};be(()=>s.activeTab,e=>{e!==d("app.personal.preferences")?window.removeEventListener("keydown",b,!1):window.addEventListener("keydown",b)}),be(()=>y.fullPath,e=>{e!=="/personalCenter"?window.removeEventListener("keydown",b,!1):s.activeTab===d("app.personal.preferences")&&window.addEventListener("keydown",b)},{immediate:!0,deep:!0});const S=e=>{p.value.ct=(e==null?void 0:e.ct)??"",p.value.ctct=(e==null?void 0:e.ctct)??"",p.value.ctce=(e==null?void 0:e.ctce)??"",p.value.airlinesCTCT=(e==null?void 0:e.airlinesCTCT)??[{airline:"",ctct:""}],p.value.airlineSettings=!!(e!=null&&e.airlinesCTCT)},D=e=>{p.value.unshared=(e==null?void 0:e.unshared)??!0,p.value.nonstop=(e==null?void 0:e.nonstop)??!1,p.value.autoSelectCabinClass=(e==null?void 0:e.autoSelectCabinClass)??!1,p.value.autoOccupy=(e==null?void 0:e.autoOccupy)??!1,p.value.domesticCities=(e==null?void 0:e.domesticCities)??"",p.value.internationalCities=(e==null?void 0:e.internationalCities)??""},R=e=>{p.value.useQtb=(e==null?void 0:e.useQtb)??!1,p.value.manualQuery=(e==null?void 0:e.manualQuery)??!1},Y=e=>{p.value.backfieldEnName=(e==null?void 0:e.backfieldEnName)??!0},K=e=>{p.value.followPnr=(e==null?void 0:e.followPnr)??!1},Z=e=>{p.value.domesticPrinterno=(e==null?void 0:e.domesticPrinterno)??"",p.value.internationalPrinterno=(e==null?void 0:e.internationalPrinterno)??""},J=e=>{if(e!=null&&e.checkTrip){const g=e.checkTrip.priceIncludingTax||e.checkTrip.passengerInformation;e.checkTrip.priceIncludingTax=g,e.checkTrip.passengerInformation=g}p.value.checkTrip=(e==null?void 0:e.checkTrip)??{cityName:!1,airportName:!1,cabinSpace:!1,flightTime:!1,passengerInformation:!1,idNumber:!1,contact:!1,priceIncludingTax:!1,airlineNameCheckSwitch:!1}},L=e=>{p.value.defaultRemark=!!((e==null?void 0:e.remarkList)??[]).length,p.value.remarkList=((e==null?void 0:e.remarkList)??[]).length?(e==null?void 0:e.remarkList)??[""]:[""]},ee=e=>{var g,k,w,P;p.value.defaultNatAndIssCountry=!!((g=e==null?void 0:e.passengerInfo)!=null&&g.visaIssueCountry||(k=e==null?void 0:e.passengerInfo)!=null&&k.passengerNationality),p.value.passengerInfo.visaIssueCountry=((w=e==null?void 0:e.passengerInfo)==null?void 0:w.visaIssueCountry)??"",p.value.passengerInfo.passengerNationality=((P=e==null?void 0:e.passengerInfo)==null?void 0:P.passengerNationality)??""},te=e=>{p.value.place=(e==null?void 0:e.place)??"BOTTOM"};function f(e){S(e),D(e),R(e),Y(e),Z(e),J(e),L(e),ee(e),te(e),K(e)}return Ce(async()=>{try{E.value=!0;const e=Ve("081W0104"),{data:g}=await ut(e);f(g.value)}finally{E.value=!1}}),{FORM_RULES:V,formRef:n,formParams:p,isIntlCrs:$,onSave:m,loading:E,addAirline:F,delAirline:_,keyDown:b,deleteRemark:u,addReamrk:o}},ns=ls,os={class:"preference-conf mb-5"},is={class:"mb-2.5 text-gray-1 text-base font-bold"},rs={class:"my-[5px]"},us={class:"text-gray-2 text-xs font-normal leading-tight"},ps=["onClick"],cs={class:"flex-col justify-start items-start gap-3.5 flex mb-7"},ds={class:"self-stretch justify-between items-start inline-flex"},fs={class:"text-gray-1 text-base font-bold leading-normal"},ms={class:"justify-start items-center gap-2 inline-flex"},gs={class:"self-stretch justify-start items-center gap-1.5 flex"},hs={class:"text-gray-3 text-xs font-normal leading-tight"},vs={class:"self-stretch justify-start items-center gap-2 inline-flex"},bs={class:"text-gray-3 text-xs font-normal leading-tight"},ys={class:"self-stretch justify-start items-center gap-2 inline-flex"},Cs={class:"text-gray-3 text-xs font-normal leading-tight"},_s={class:"self-stretch justify-start items-center gap-2 inline-flex"},ks={class:"text-gray-3 text-xs font-normal leading-tight"},$s={class:"h-[54px] flex-col justify-start items-start gap-2.5 flex mb-7"},ws={class:"self-stretch justify-between items-start inline-flex"},Ts={class:"text-gray-1 text-base font-bold leading-normal"},Es={class:"justify-start items-center gap-2 inline-flex"},Ns={class:"self-stretch justify-start items-center gap-1.5 flex"},Is={class:"text-gray-3 text-xs font-normal leading-tight"},Vs={class:"h-[114px] flex-col justify-start items-start gap-2.5 inline-flex"},Fs={class:"self-stretch justify-between items-start inline-flex"},Ss={class:"text-gray-1 text-base font-bold leading-normal"},As={class:"justify-start items-center gap-2 inline-flex"},Ps={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},Us={class:"text-gray-3 text-xs font-normal leading-tight"},Ds={class:"self-stretch h-5 justify-start items-center gap-1.5 inline-flex"},Rs={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},Bs={class:"text-gray-3 text-xs font-normal leading-tight"},Ls={class:"flex-col justify-start items-start gap-2.5 flex mb-3"},Os={class:"self-stretch justify-between items-start inline-flex"},xs={class:"text-gray-1 text-base font-bold leading-normal"},Ms={class:"justify-start items-center gap-2 inline-flex"},Xs={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},js={class:"text-gray-3 text-xs font-normal leading-tight"},qs={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},Ws={class:"text-gray-3 text-xs font-normal leading-tight mb-2.5"},Gs={class:"mr-2.5"},Qs={class:"mr-[6px]"},Ys={class:"mr-[6px]"},zs={class:"flex-col justify-start items-start gap-2.5 flex mb-3"},Hs={class:"self-stretch justify-between items-start inline-flex"},Ks={class:"text-gray-1 text-base font-bold leading-normal"},Zs={class:"justify-start items-center gap-2 inline-flex"},Js={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},el={class:"text-gray-3 text-xs font-normal leading-tight"},tl={class:"flex-col justify-start items-start gap-2.5 inline-flex mb-3"},al={class:"self-stretch justify-between items-start inline-flex"},sl={class:"text-gray-1 text-base font-bold leading-normal"},ll={class:"flex items-center"},nl={class:"text-gray-3 text-xs font-normal leading-tight mr-[20px]"},ol={class:"text-gray-3 text-xs font-normal leading-tight flex"},il={class:"mr-1 py-2"},rl={class:"bg-gray-7 px-1 py-2 rounded-sm flex-col justify-center items-start inline-flex gap-2"},ul={key:0},pl={class:"flex-col inline-flex gap-2"},cl={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},dl={class:"w-70 mb-[5px] self-stretch justify-start items-center gap-1.5 flex"},fl={class:"text-gray-3 text-xs font-normal leading-tight"},ml=["onClick"],gl={key:2},hl={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},vl={class:"w-[100%] h-8 justify-end items-center gap-3.5 inline-flex"},bl=ce({__name:"Preferences",props:{activeTab:{}},setup(s){const d=s,{FORM_RULES:y,formRef:E,formParams:n,isIntlCrs:N,onSave:p,loading:$,addAirline:C,delAirline:r,keyDown:V,deleteRemark:F,addReamrk:_}=ns(d);return(o,u)=>{const m=j,b=q,S=kt,D=$t,R=wt,Y=Tt,K=Et,Z=le,J=ae,L=Fe("trimUpper"),ee=Fe("permission"),te=dt;return O((h(),T("div",os,[a("div",is,i(o.$t("app.personal.setSefaultCTAndCTCT")),1),l(Z,{ref_key:"formRef",ref:E,"require-asterisk-position":"right",model:t(n),inline:!0,rules:t(y),onKeyup:ct(t(V),["enter"])},{default:c(()=>[a("div",null,[l(b,{label:"CT",prop:"ct"},{default:c(()=>[O(l(m,{modelValue:t(n).ct,"onUpdate:modelValue":u[0]||(u[0]=f=>t(n).ct=f)},null,8,["modelValue"]),[[L]])]),_:1}),l(b,{label:"CTCT",prop:"ctct"},{default:c(()=>[O(l(m,{modelValue:t(n).ctct,"onUpdate:modelValue":u[1]||(u[1]=f=>t(n).ctct=f)},null,8,["modelValue"]),[[L]])]),_:1}),l(b,{label:"CTCE",prop:"ctce"},{default:c(()=>[O(l(m,{modelValue:t(n).ctce,"onUpdate:modelValue":u[2]||(u[2]=f=>t(n).ctce=f)},null,8,["modelValue"]),[[L]])]),_:1}),l(S,{size:"large",type:"info"},{default:c(()=>[W(i(o.$t("app.personal.defaultValue")),1)]),_:1})]),a("div",rs,[l(b,{prop:"airlineSettings"},{default:c(()=>[l(D,{modelValue:t(n).airlineSettings,"onUpdate:modelValue":u[3]||(u[3]=f=>t(n).airlineSettings=f),type:"default"},{default:c(()=>[a("span",us,i(o.$t("app.personal.airlineSettings")),1)]),_:1},8,["modelValue"])]),_:1})]),t(n).airlineSettings?(h(!0),T(Q,{key:0},se(t(n).airlinesCTCT,(f,e)=>(h(),T("div",{key:e,class:"mb-[14px]"},[l(b,{label:o.$t("app.personal.airlineCompany"),prop:"airlinesCTCT."+e+".airline",class:"w-[100px]",rules:t(y).airline},{default:c(()=>[O(l(m,{modelValue:f.airline,"onUpdate:modelValue":g=>f.airline=g},null,8,["modelValue","onUpdate:modelValue"]),[[L]])]),_:2},1032,["label","prop","rules"]),l(b,{label:"CTCT",prop:"airlinesCTCT."+e+".ctct",rules:t(y).airlinesCtct},{default:c(()=>[O(l(m,{modelValue:f.ctct,"onUpdate:modelValue":g=>f.ctct=g},null,8,["modelValue","onUpdate:modelValue"]),[[L]])]),_:2},1032,["prop","rules"]),e===0&&t(n).airlinesCTCT.length!==1||e>0?(h(),T("em",{key:0,class:"iconfont icon-minus-square text-[20px] relative text-undefined text-brand-2 mr-[5px] cursor-pointer",onClick:g=>t(r)(e)},null,8,ps)):I("",!0),e===t(n).airlinesCTCT.length-1?(h(),T("em",{key:1,class:"iconfont icon-plus-square text-[20px] relative text-undefined text-brand-2 cursor-pointer",onClick:u[4]||(u[4]=(...g)=>t(C)&&t(C)(...g))})):I("",!0)]))),128)):I("",!0),a("div",cs,[a("div",ds,[a("div",fs,i(o.$t("app.personal.flightQueryConditions")),1)]),a("div",ms,[a("div",gs,[a("div",hs,i(o.$t("app.personal.defaultNonShared")),1),l(R,{modelValue:t(n).unshared,"onUpdate:modelValue":u[5]||(u[5]=f=>t(n).unshared=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),a("div",vs,[a("div",bs,i(o.$t("app.personal.defaultDirectFlightOnly")),1),l(R,{modelValue:t(n).nonstop,"onUpdate:modelValue":u[6]||(u[6]=f=>t(n).nonstop=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),a("div",ys,[a("div",Cs,i(o.$t("app.personal.autoSelectCabinClass")),1),l(R,{modelValue:t(n).autoSelectCabinClass,"onUpdate:modelValue":u[7]||(u[7]=f=>t(n).autoSelectCabinClass=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),a("div",_s,[a("div",ks,i(o.$t("app.personal.autoPlaceholder")),1),l(R,{modelValue:t(n).autoOccupy,"onUpdate:modelValue":u[8]||(u[8]=f=>t(n).autoOccupy=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),l(b,{label:o.$t("app.personal.domesticCities"),prop:"domesticCities",class:"w-[914px]"},{default:c(()=>[l(m,{modelValue:t(n).domesticCities,"onUpdate:modelValue":u[9]||(u[9]=f=>t(n).domesticCities=f),placeholder:o.$t("app.personal.cityPlaceholder"),onInput:u[10]||(u[10]=f=>t(n).domesticCities=t(n).domesticCities.toUpperCase().trim())},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),l(b,{label:o.$t("app.personal.internationalCities"),prop:"internationalCities",class:"w-[914px]"},{default:c(()=>[l(m,{modelValue:t(n).internationalCities,"onUpdate:modelValue":u[11]||(u[11]=f=>t(n).internationalCities=f),placeholder:o.$t("app.personal.cityPlaceholder"),onInput:u[12]||(u[12]=f=>t(n).internationalCities=t(n).internationalCities.toUpperCase().trim())},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),a("div",$s,[a("div",ws,[a("div",Ts,i(o.$t("app.personal.passengerInfo")),1)]),a("div",Es,[a("div",Ns,[a("div",Is,i(o.$t("app.personal.defaultPassengerDocuments")),1),l(R,{modelValue:t(n).defaultNatAndIssCountry,"onUpdate:modelValue":u[13]||(u[13]=f=>t(n).defaultNatAndIssCountry=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),t(n).defaultNatAndIssCountry?(h(),T(Q,{key:0},[l(b,{label:o.$t("app.personal.passengerNationality"),prop:"passengerInfo.passengerNationality"},{default:c(()=>[O(l(m,{modelValue:t(n).passengerInfo.passengerNationality,"onUpdate:modelValue":u[14]||(u[14]=f=>t(n).passengerInfo.passengerNationality=f)},null,8,["modelValue"]),[[L]])]),_:1},8,["label"]),l(b,{label:o.$t("app.personal.visaIssueCountry"),prop:"passengerInfo.visaIssueCountry"},{default:c(()=>[O(l(m,{modelValue:t(n).passengerInfo.visaIssueCountry,"onUpdate:modelValue":u[15]||(u[15]=f=>t(n).passengerInfo.visaIssueCountry=f)},null,8,["modelValue"]),[[L]])]),_:1},8,["label"])],64)):I("",!0)])]),a("div",Vs,[a("div",Fs,[a("div",Ss,i(o.$t("app.personal.fareQueryConditions")),1)]),a("div",As,[a("div",Ps,[a("div",Us,i(o.$t("app.personal.defaultUseQTB")),1),l(R,{modelValue:t(n).useQtb,"onUpdate:modelValue":u[16]||(u[16]=f=>t(n).useQtb=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),a("div",Ds,[a("div",Rs,[a("div",Bs,i(o.$t("app.personal.manualRateQuery")),1),l(R,{modelValue:t(n).manualQuery,"onUpdate:modelValue":u[17]||(u[17]=f=>t(n).manualQuery=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),a("div",Ls,[a("div",Os,[a("div",xs,i(o.$t("app.personal.followPnr")),1)]),a("div",Ms,[a("div",Xs,[a("div",js,i(o.$t("app.personal.followPnrTip")),1),l(R,{modelValue:t(n).followPnr,"onUpdate:modelValue":u[18]||(u[18]=f=>t(n).followPnr=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),a("div",qs,i(o.$t("app.personal.ticketMachine")),1),a("div",Ws,[a("span",Gs,i(o.$t("app.personal.defaultTicketMachine")),1),a("span",Qs,i(o.$t("app.personal.domestic")),1),l(Se,{modelValue:t(n).domesticPrinterno,"onUpdate:modelValue":u[19]||(u[19]=f=>t(n).domesticPrinterno=f),"need-distinguish":!0,"select-class":"w-[100px] mr-2.5"},null,8,["modelValue"]),a("span",Ys,i(o.$t("app.personal.international")),1),l(Se,{modelValue:t(n).internationalPrinterno,"onUpdate:modelValue":u[20]||(u[20]=f=>t(n).internationalPrinterno=f),"is-inter":!0,"need-distinguish":!0,"select-class":"w-[100px]"},null,8,["modelValue"])]),a("div",zs,[a("div",Hs,[a("div",Ks,i(o.$t("app.personal.ticketRefund")),1)]),a("div",Zs,[a("div",Js,[a("div",el,i(o.$t("app.personal.manualRefundName")),1),l(R,{modelValue:t(n).backfieldEnName,"onUpdate:modelValue":u[21]||(u[21]=f=>t(n).backfieldEnName=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),a("div",tl,[a("div",al,[a("div",sl,i(o.$t("app.personal.checkTrip")),1)]),a("div",ll,[a("div",nl,i(o.$t("app.personal.verifyItinerary")),1),l(D,{modelValue:t(n).checkTrip.airlineNameCheckSwitch,"onUpdate:modelValue":u[22]||(u[22]=f=>t(n).checkTrip.airlineNameCheckSwitch=f),label:o.$t("app.personal.airlineName")},null,8,["modelValue","label"]),l(D,{modelValue:t(n).checkTrip.cityName,"onUpdate:modelValue":u[23]||(u[23]=f=>t(n).checkTrip.cityName=f),label:o.$t("app.personal.cityName"),class:"ml-[-10px]"},null,8,["modelValue","label"]),l(D,{modelValue:t(n).checkTrip.airportName,"onUpdate:modelValue":u[24]||(u[24]=f=>t(n).checkTrip.airportName=f),label:o.$t("app.personal.airportName"),class:"ml-[-10px]"},null,8,["modelValue","label"]),l(D,{modelValue:t(n).checkTrip.passengerInformation,"onUpdate:modelValue":u[25]||(u[25]=f=>t(n).checkTrip.passengerInformation=f),label:o.$t("app.personal.pasgInfoAndPriceTax"),class:"ml-[-10px]"},null,8,["modelValue","label"]),l(D,{modelValue:t(n).checkTrip.idNumber,"onUpdate:modelValue":u[26]||(u[26]=f=>t(n).checkTrip.idNumber=f),label:o.$t("app.personal.idNumber"),class:"ml-[-10px]"},null,8,["modelValue","label"]),l(D,{modelValue:t(n).checkTrip.contact,"onUpdate:modelValue":u[27]||(u[27]=f=>t(n).checkTrip.contact=f),label:o.$t("app.personal.contact"),class:"ml-[-10px]"},null,8,["modelValue","label"]),l(D,{modelValue:t(n).checkTrip.cabinSpace,"onUpdate:modelValue":u[28]||(u[28]=f=>t(n).checkTrip.cabinSpace=f),label:o.$t("app.personal.cabinCode"),class:"ml-[-10px]"},null,8,["modelValue","label"]),l(D,{modelValue:t(n).checkTrip.flightTime,"onUpdate:modelValue":u[29]||(u[29]=f=>t(n).checkTrip.flightTime=f),label:o.$t("app.personal.flightTime"),class:"ml-[-10px]"},null,8,["modelValue","label"])]),a("div",ol,[a("div",il,i(o.$t("app.personal.example"))+" :",1),a("div",rl,[a("div",null,[t(n).checkTrip.airlineNameCheckSwitch?(h(),T("span",ul,i(o.$t("app.personal.AirChina")),1)):I("",!0),a("span",null," CA1519 "+i(t(n).checkTrip.cabinSpace?o.$t("app.personal.cabinY"):" ")+" "+i(o.$t("app.personal.date"))+" 2024-10-23("+i(o.$t("app.pnrManagement.flight.Days3"))+")   "+i(t(n).checkTrip.cityName?o.$t("app.personal.BeiJing"):" ")+" PEK"+i(t(n).checkTrip.airportName?o.$t("app.personal.CapitalAirport"):" ")+"T3 ",1),W(" — "+i(t(n).checkTrip.cityName?o.$t("app.personal.Shanghai"):" ")+"SHA"+i(t(n).checkTrip.airportName?o.$t("app.personal.HongqiaoAirport"):" ")+"T2  "+i(o.$t("app.personal.takeOff"))+"0930  "+i(o.$t("app.personal.reach"))+"1155 "+i(t(n).checkTrip.flightTime?o.$t("app.personal.flightTimeExample"):""),1)]),O(a("div",pl,[a("div",null,i(`${o.$t("app.personal.passenger")+"1"}${t(n).checkTrip.passengerInformation||t(n).checkTrip.idNumber||t(n).checkTrip.contact?" 张三 ":""} ${t(n).checkTrip.idNumber?"110106XXXXXXXXXXXXX":""} ${t(n).checkTrip.contact?"150113XXXXX":""} ${t(n).checkTrip.passengerInformation?o.$t("app.personal.taxPrice")+"3000.00":""}`),1),a("div",null,i(`${o.$t("app.personal.passenger")+"2"}${t(n).checkTrip.passengerInformation||t(n).checkTrip.idNumber||t(n).checkTrip.contact?" 李四 ":""} ${t(n).checkTrip.idNumber?"110106XXXXXXXXXXXXX":""} ${t(n).checkTrip.contact?"150113XXXXX":""} ${t(n).checkTrip.passengerInformation?o.$t("app.personal.taxPrice")+"3000.00":""}`),1)],512),[[Ue,t(n).checkTrip.passengerInformation||t(n).checkTrip.contact||t(n).checkTrip.idNumber]])])])]),a("div",cl,i(o.$t("app.personal.remark")),1),l(b,null,{default:c(()=>[a("div",dl,[a("div",fl,i(o.$t("app.personal.defaultRemark")),1),l(R,{modelValue:t(n).defaultRemark,"onUpdate:modelValue":u[30]||(u[30]=f=>t(n).defaultRemark=f),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),_:1}),t(n).defaultRemark?(h(!0),T(Q,{key:1},se(t(n).remarkList,(f,e)=>(h(),T("div",{key:e,class:"mb-2.5 flex items-center"},[l(b,{class:"w-[600px]",label:"RMK"},{default:c(()=>[O(l(m,{modelValue:t(n).remarkList[e],"onUpdate:modelValue":g=>t(n).remarkList[e]=g},null,8,["modelValue","onUpdate:modelValue"]),[[L]])]),_:2},1024),e===0&&t(n).remarkList.length!==1||e>0?(h(),T("em",{key:0,class:"iconfont icon-minus-square text-[20px] relative text-undefined text-brand-2 mr-[5px] cursor-pointer",onClick:g=>t(F)(e)},null,8,ml)):I("",!0),e===t(n).remarkList.length-1&&e<2?(h(),T("em",{key:1,class:"iconfont icon-plus-square text-[20px] relative text-undefined text-brand-2 cursor-pointer",onClick:u[31]||(u[31]=(...g)=>t(_)&&t(_)(...g))})):I("",!0)]))),128)):I("",!0),t(N)?I("",!0):O((h(),T("div",gl,[a("div",hl,i(o.$t("app.personal.frequentlyAskedQuestionsEntrance")),1),l(K,{modelValue:t(n).place,"onUpdate:modelValue":u[32]||(u[32]=f=>t(n).place=f),class:"frequently-asked-questions"},{default:c(()=>[l(Y,{label:"BOTTOM"},{default:c(()=>[W(i(o.$t("app.personal.suspendedDisplay")),1)]),_:1}),l(Y,{label:"TOP"},{default:c(()=>[W(i(o.$t("app.personal.topFixedDisplay")),1)]),_:1})]),_:1},8,["modelValue"])])),[[ee,"frequently-asked-questions-show"]])]),_:1},8,["model","rules","onKeyup"]),a("div",vl,[l(J,{size:"default",type:"primary","data-gid":"081W0103",onClick:t(p)},{default:c(()=>[W(i(o.$t("app.personal.save")),1)]),_:1},8,["onClick"])])])),[[te,t($)]])}}});const yl=de(bl,[["__scopeId","data-v-e7f7aa09"]]),Cl=ce({name:"PersonalCenter",components:{Information:ss,UpdatePassword:ea,Preferences:yl,ElTabs:Me,ElTabPane:Xe},setup(){const{t:s}=ne(),d=pe(),{user:y}=d.state,E=ye("isMini",!1),n=M(!1),N=async()=>(n.value=y.longTimeNoChangePwd,y&&(y.lastLoginTime===null||n.value||y.twoPwdMismatch)),p=M(!1),$=[{component:"",label:s("app.personal.accountMapping"),auth:"setting-personCenter-accountMapping"},{component:"",label:s("app.personal.OnTheFlight"),auth:"setting-personCenter-followFlight"},{component:"Preferences",label:s("app.personal.preferences"),auth:"setting-personCenter-preferences"}],C=M([]),r=M(""),V=async()=>{var o;const _=await d.getters.roleResource;C.value.push({component:"Information",label:s("app.personal.basicInformation"),auth:"setting-personalCenter-baseInfo"}),C.value.push({component:"UpdatePassword",label:s("app.personal.updatePwd"),auth:"setting-personCenter-updatePassword"}),$.forEach(u=>{_.includes(u.auth)&&C.value.push(u)}),r.value=((o=C.value[0])==null?void 0:o.label)??""},F=()=>x()==="en"?r.value===s("app.personal.preferences")?"tabs_short_en":"tabs_en":"";return Ce(async()=>{const o=(await d.getters.user).twoPwdMismatch;await N()||o?(p.value=!0,E.value||Nt()):p.value=!1,V()}),{onlyUpdatePassword:p,activeTab:r,centerComponents:C,getTabENClass:F,longTimeNoChangePwd:n}}});const _l={class:"user-center"};function kl(s,d,y,E,n,N){const p=ve("UpdatePassword"),$=Xe,C=Me;return h(),T("div",_l,[s.onlyUpdatePassword?(h(),B(C,{key:0},{default:c(()=>[l($,{label:s.$t("app.personal.updatePwd")},{default:c(()=>[l(p)]),_:1},8,["label"]),s.longTimeNoChangePwd?(h(),B($,{key:0,disabled:"",label:s.$t("app.personal.longTimeChange")},null,8,["label"])):(h(),B($,{key:1,disabled:"",label:s.$t("app.personal.updateFirst")},null,8,["label"]))]),_:1})):I("",!0),!s.onlyUpdatePassword&&s.centerComponents.length>0?(h(),B(C,{key:1,modelValue:s.activeTab,"onUpdate:modelValue":d[0]||(d[0]=r=>s.activeTab=r),class:U(s.getTabENClass())},{default:c(()=>[(h(!0),T(Q,null,se(s.centerComponents,r=>(h(),B($,{key:r.component,label:r.label,name:r.label},{default:c(()=>[r.component.length>0?(h(),B(ft(r.component),{key:0,"active-tab":s.activeTab},null,8,["active-tab"])):I("",!0)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","class"])):I("",!0)])}const Jl=de(Cl,[["render",kl],["__scopeId","data-v-b6f9bf98"]]);export{Jl as default};
