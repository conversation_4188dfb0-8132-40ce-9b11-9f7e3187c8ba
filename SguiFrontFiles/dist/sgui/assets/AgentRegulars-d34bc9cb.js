import{ao as de,b2 as ue,ab as he,r as R,s as Ce,aH as me,w as ye,fX as Ne,cZ as je,bf as De,fW as ke,ac as Te,d3 as ae,fR as we,au as Ve,aY as ie,q as pe,ae as Le,x as u,y as T,z as o,P as e,Q as l,a5 as A,B as g,G as r,A as s,J as z,ai as H,aj as Z,ak as se,b3 as ve,ah as be,H as Ee,al as fe,aZ as xe,am as Ie,an as Ae,d9 as Ue,cY as Re,g9 as Me,aT as Se,at as Oe,E as Be}from"./index-9381ab2b.js";import{C as _e}from"./CustomDatePicker-0425400e.js";import{n as Ye,w as Ge,I as ze,z as He,k as Ze}from"./usePassengerFormValidateUtils-e3bfe23b.js";import{ad as Qe,T as Xe,v as Je}from"./regular-crs-0d781ceb.js";import{u as We}from"./config-b573cde3.js";import{q as Ke,t as oe}from"./pnrUtils-8a2ea82c.js";import{E as Fe,a as $e}from"./index-951011fc.js";import{E as et}from"./index-2b7c36a5.js";import{E as tt,a as st}from"./index-094198d8.js";import{E as qe}from"./index-7b8ec8cc.js";import{_ as at}from"./_plugin-vue_export-helper-c27b6911.js";import{m as le}from"./Tip-483abcda.js";import{E as lt}from"./index-c19c3f80.js";import{E as nt,a as rt}from"./index-6bc0a2f0.js";import"./index-1d08351c.js";import"./flatten-85480810.js";import"./index-34c19038.js";import"./index-06c3636a.js";import"./index-a4ffe93f.js";import"./isEqual-a619023a.js";import"./passengerSpecialType-e43b2056.js";import"./dictApi-2d93d62f.js";import"./castArray-25c7c99e.js";import"./index-1dbceb27.js";import"./strings-820bea19.js";import"./refs-d6b4edba.js";import"./isUndefined-aa0326a0.js";import"./index-a7943392.js";const ot=(f,j)=>de(`${ue}/agencyFrequent/query`,{headers:{gid:j}}).post(f).json(),it=(f,j)=>de(`${ue}/agencyFrequent/delete`,{headers:{gid:j}}).post({csmIdentifier:f}).json(),dt=(f,j)=>de(`${ue}/agencyFrequent/add`,{headers:{gid:j}}).post(f).json(),ut=(f,j)=>de(`${ue}/agencyFrequent/modify`,{headers:{gid:j}}).post(f).json();var J=(f=>(f[f.close=0]="close",f[f.add=1]="add",f[f.edit=2]="edit",f))(J||{});const ct=(f,j)=>{const{t:y}=he(),F=()=>{const n=oe,$=Ke.filter(a=>!oe.some(v=>a.value===v.value));return[...n,...$]},i=[{label:y("app.intlPassengerForm.male"),value:"M"},{label:y("app.intlPassengerForm.female"),value:"F"}],w=["NI_I","UU","NI"],P="PP_",h=R(F()),I=()=>({csmIdentifier:0,chineseName:"",englishName:"",genderCode:"M",birthDate:"",mobilePhone:"",emailAddress:"",frequentFlyerNumberInfo:"",frequentFlyerLevelNumberInfo:"",documentList:[{documentTypeCode:oe[0].value,documentNumber:"",expiryDate:"",issueCountryCode:"",nationalityCode:"",issueDate:"",id:`document${ae()}`}],remarkList:[],cardList:[]}),D=R(),M=R(!1),k=R(I()),V=Ce({render(){return me("em",{class:"iconfont icon-calendar"})}}),O=ye(()=>!!f.showAgencyFrequentFlyerDialogType),B=ye(()=>({chineseName:[{required:!0,message:y("app.intlPassengerForm.required"),trigger:["blur"]},{validator:Ye}],englishName:[{required:!0,message:y("app.intlPassengerForm.required"),trigger:["blur"]},{validator:Ge}],genderCode:[{required:!0,message:y("app.intlPassengerForm.required")}],birthDate:[{required:!0,message:y("app.intlPassengerForm.required")}],frequentFlyerNumberInfo:[{required:!0,message:y("app.intlPassengerForm.required")}],frequentFlyerLevelNumberInfo:[{required:!0,message:y("app.intlPassengerForm.required")}],mobilePhone:[{required:!0,message:y("app.intlPassengerForm.required")},{pattern:Ne,trigger:["change","blur"],message:y("app.intlPassengerForm.enterValidMobile")}],emailAddress:[{required:!0,message:y("app.intlPassengerForm.required")},{pattern:je,message:y("app.pnrManagement.validate.enterValidEmail")}],airlineCode:[{required:!0,message:y("app.intlPassengerForm.required")},{pattern:De,message:y("app.pnrManagement.validate.characterCode")}],frequentFlyerNumber:[{required:!0,message:y("app.intlPassengerForm.required")},{pattern:ke,message:y("app.intlPassengerForm.validate.regularError")}],documentTypeCode:[{required:!0,message:y("app.intlPassengerForm.required")}],documentNumber:[{required:!0,message:y("app.intlPassengerForm.required")},{validator:L,trigger:["blur"]}],nationalityCode:[{required:!0,message:y("app.intlPassengerForm.required")},{pattern:/^[a-zA-Z]{2,3}$/,message:y("app.intlPassengerForm.enterCharacterCode")}],issueCountryCode:[{required:!0,message:y("app.intlPassengerForm.required")},{pattern:/^[a-zA-Z]{2,3}$/,message:y("app.intlPassengerForm.enterCharacterCode")}],expiryDate:[{required:!0,message:y("app.intlPassengerForm.required")}]})),Y=(n,$)=>n&&!we(n)?y($==="UU"?We.get("documentTypeTip")??"":"app.intlPassengerForm.validate.certError"):"",L=(n,$,a)=>{const q=n.field.split("."),v=parseInt(q[1]),U=k.value.documentList[v].documentTypeCode,K=["PP_P","PP_IP","PP_I","PP_A","PP_F","PP_IN"];if(w.includes(U)){const E=Y($,U);E&&a(E)}else if(K.includes(U))$&&!Qe.test($)&&a(new Error(y("app.intlPassengerForm.validate.certError"))),a();else{const E=ze($,U);if(E.tip){const ee=E.min>1?y(E.tip,{min:E.min,max:E.max}):y(E.tip,{max:E.max});a(ee)}a()}a()},G=(n,$)=>{var a;k.value[n]=(a=k.value[n])==null?void 0:a.filter(q=>q.id!==$)},_=()=>{const n={documentTypeCode:oe[0].value,documentNumber:"",expiryDate:"",issueCountryCode:"",nationalityCode:"",issueDate:"",id:`document${ae()}`};k.value.documentList.push(n)},d=()=>{const n={sequenceNumber:0,remark:"",id:`remark${ae()}`};k.value.remarkList.push(n)},m=()=>{const n={airlineCode:"",frequentFlyerNumber:"",id:`card${ae()}`};k.value.cardList.push(n)},S=n=>{switch(n){case"documentList":_();break;case"remarkList":d();break;case"cardList":m();break}},p=n=>{D.value&&D.value.validateField(`documentList.${n}.documentNumber`)},x=n=>{Q(),j("close",n)},C=n=>{var $;return n.includes("NI")?"NI":n.includes(P)?(($=n.split(P))==null?void 0:$[1])??"":n},N=()=>{const{csmIdentifier:n,chineseName:$,englishName:a,genderCode:q,birthDate:v,mobilePhone:U,emailAddress:K,frequentFlyerNumberInfo:E,frequentFlyerLevelNumberInfo:ee,documentList:t,remarkList:te,cardList:X}=k.value;return{csmIdentifier:f.showAgencyFrequentFlyerDialogType===J.edit?n:0,chineseName:$,englishName:a,genderCode:q,birthDate:v,mobilePhone:U,emailAddress:K,frequentFlyerNumberInfo:E,frequentFlyerLevelNumberInfo:ee,documentList:t.map(b=>({documentTypeCode:C(b.documentTypeCode),documentNumber:b.documentNumber,expiryDate:b.expiryDate,issueCountryCode:b.issueCountryCode,nationalityCode:b.nationalityCode,issueDate:b.issueDate})),remarkList:te.map((b,ne)=>({sequenceNumber:ne,remark:b.remark})),cardList:X.map(b=>({airlineCode:b.airlineCode,frequentFlyerNumber:b.frequentFlyerNumber}))}},W=async()=>{const n=f.showAgencyFrequentFlyerDialogType===J.add;D.value.validate(async $=>{if($)try{M.value=!0;const a=N(),{data:q}=n?await dt(a,Ve("081L0139")):await ut(a,"081L0140");if(q.value==="success"){ie({message:"success",type:"success"}),x(!n);return}ie({message:"error",type:"error"})}finally{M.value=!1}})},Q=()=>{var n;(n=D.value)==null||n.clearValidate(),k.value=I()},c=()=>{const{csmIdentifier:n,chineseName:$,englishName:a,genderCode:q,birthDate:v,mobilePhone:U,emailAddress:K,frequentFlyerNumberInfo:E,frequentFlyerLevelNumberInfo:ee,documentList:t,remarkList:te,cardList:X}=f.editForm,re=b=>{var ge;if(b.includes(w[2]))return w[0];if(b.includes(w[1]))return w[1];const ne=`${P}${b}`;return(ge=h.value)!=null&&ge.some(Pe=>Pe.value===ne)?`${P}${b}`:b};k.value={csmIdentifier:n,chineseName:$,englishName:a,genderCode:q,birthDate:v,mobilePhone:U,emailAddress:K,frequentFlyerNumberInfo:E,frequentFlyerLevelNumberInfo:ee,documentList:t.map(b=>({documentNumber:b.documentNumber,expiryDate:b.expiryDate,issueCountryCode:b.issueCountryCode,nationalityCode:b.nationalityCode,issueDate:b.issueDate,documentTypeCode:re(b.documentTypeCode),id:`document${ae()}`})),remarkList:te.map(b=>({sequenceNumber:b.sequenceNumber,remark:b.remark,id:`remark${ae()}`})),cardList:X.map(b=>({airlineCode:b.airlineCode,frequentFlyerNumber:b.frequentFlyerNumber,id:`card${ae()}`}))}};return Te(()=>f.showAgencyFrequentFlyerDialogType,()=>{f.showAgencyFrequentFlyerDialogType===J.edit&&c()},{immediate:!0}),{formRef:D,genderTypeArr:i,CERT_TYPE_1:w,loading:M,showDialog:O,form:k,datePrefix:V,delList:G,addList:S,documentTypeChange:p,closeDialog:x,confirmClick:W,FORM_RULES:B,documentType:h}},mt=ct,ce=f=>(Ie("data-v-e2ac9dc5"),f=f(),Ae(),f),pt={class:"dialog-main-title"},ft={class:"content-form-box"},gt={class:"user-id-box"},yt={class:"user-id"},_t={class:"user-tag"},ht={class:"info-box base-info"},vt={class:"info-title-box"},bt={class:"title-text"},xt=ce(()=>e("div",{class:"line-split"},null,-1)),Ft={class:"base-info-detail"},$t={class:"info-box"},qt={class:"document-list"},Pt={class:"info-title-box"},Ct={class:"document-title title-text"},Nt=ce(()=>e("div",{class:"line-split"},null,-1)),jt={key:0,class:"del-btn"},Dt={class:"document-info"},kt={key:0},Tt={key:1,class:"inline-block w-[12px]"},wt={class:"add-btn"},Vt={class:"info-box remark-info"},Lt={class:"info-title-box"},Et={class:"title-text"},It=ce(()=>e("div",{class:"line-split"},null,-1)),At={class:"remark-list"},Ut={class:"del-btn"},Rt={class:"add-btn"},Mt={class:"info-box frequent-flyer-info"},St={class:"info-title-box"},Ot={class:"title-text"},Bt=ce(()=>e("div",{class:"line-split"},null,-1)),Yt={class:"frequent-flyer-list"},Gt={class:"del-btn"},zt={class:"add-btn"},Ht={class:"footer-btn-box"},Zt=pe({__name:"AgencyFrequentFlyerDialog",props:{showAgencyFrequentFlyerDialogType:{},headerTitle:{},editForm:{}},emits:["close"],setup(f,{emit:j}){const y=j,F=f,{formRef:i,genderTypeArr:w,loading:P,form:h,showDialog:I,documentType:D,documentTypeChange:M,delList:k,addList:V,datePrefix:O,closeDialog:B,confirmClick:Y,FORM_RULES:L,CERT_TYPE_1:G}=mt(F,y);return(_,d)=>{const m=Fe,S=et,p=be,x=tt,C=st,N=Ee,W=$e,Q=fe,c=qe,n=Le("trimUpper"),$=xe;return u(),T(c,{modelValue:s(I),"onUpdate:modelValue":d[15]||(d[15]=a=>ve(I)?I.value=a:null),width:"1040px",class:"crs-new-ui-init-cls agency-frequent-flyer-dialog long-width crs-btn-dialog-ui","close-on-click-modal":!1,onClose:s(B)},{header:o(()=>[e("span",pt,l(_.headerTitle),1)]),default:o(()=>[A((u(),g("div",null,[e("div",ft,[r(W,{ref_key:"formRef",ref:i,model:s(h),rules:s(L)},{default:o(()=>[_.showAgencyFrequentFlyerDialogType===s(J).edit?(u(),T(S,{key:0},{default:o(()=>[r(m,{label:_.$t("app.intlPassengerForm.frequentTraveler.travelerID"),prop:"csmIdentifier"},{default:o(()=>[e("div",gt,[e("div",yt,l(s(h).csmIdentifier),1),e("div",_t,l(_.$t("app.intlPassengerForm.agencyFrequentFlyer.regularCustomer")),1)])]),_:1},8,["label"])]),_:1})):z("",!0),e("div",ht,[e("div",vt,[e("div",bt,l(_.$t("app.intlPassengerForm.agencyFrequentFlyer.baseInfo")),1),xt]),e("div",Ft,[r(m,{label:_.$t("app.intlPassengerForm.agencyFrequentFlyer.chineseName"),prop:"chineseName"},{default:o(()=>[A(r(p,{modelValue:s(h).chineseName,"onUpdate:modelValue":d[0]||(d[0]=a=>s(h).chineseName=a),disabled:!1},null,8,["modelValue"]),[[n]])]),_:1},8,["label"]),r(m,{label:_.$t("app.intlPassengerForm.agencyFrequentFlyer.englishName"),prop:"englishName"},{default:o(()=>[A(r(p,{modelValue:s(h).englishName,"onUpdate:modelValue":d[1]||(d[1]=a=>s(h).englishName=a),disabled:!1},null,8,["modelValue"]),[[n]])]),_:1},8,["label"]),r(m,{label:_.$t("app.intlPassengerForm.frequentTraveler.gender"),prop:"genderCode"},{default:o(()=>[r(C,{modelValue:s(h).genderCode,"onUpdate:modelValue":d[2]||(d[2]=a=>s(h).genderCode=a)},{default:o(()=>[(u(!0),g(H,null,Z(s(w),a=>(u(),T(x,{key:a.value,label:a.label,value:a.value},{default:o(()=>[se(l(a.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),r(m,{label:_.$t("app.intlPassengerForm.frequentTraveler.birthday"),prop:"birthDate"},{default:o(()=>[r(_e,{modelValue:s(h).birthDate,"onUpdate:modelValue":d[3]||(d[3]=a=>s(h).birthDate=a),type:"date","value-format":"YYYY-MM-DD","prefix-icon":s(O),"disabled-date":s(He)},null,8,["modelValue","prefix-icon","disabled-date"])]),_:1},8,["label"]),r(m,{label:_.$t("app.intlPassengerForm.agencyFrequentFlyer.mobilePhone"),prop:"mobilePhone"},{default:o(()=>[A(r(p,{modelValue:s(h).mobilePhone,"onUpdate:modelValue":d[4]||(d[4]=a=>s(h).mobilePhone=a),disabled:!1},null,8,["modelValue"]),[[n]])]),_:1},8,["label"]),r(m,{label:_.$t("app.intlPassengerForm.frequentTraveler.email"),prop:"emailAddress"},{default:o(()=>[A(r(p,{modelValue:s(h).emailAddress,"onUpdate:modelValue":d[5]||(d[5]=a=>s(h).emailAddress=a),disabled:!1},null,8,["modelValue"]),[[n]])]),_:1},8,["label"]),r(m,{label:_.$t("app.intlPassengerForm.frequentTraveler.clientCode"),prop:"frequentFlyerNumberInfo"},{default:o(()=>[A(r(p,{modelValue:s(h).frequentFlyerNumberInfo,"onUpdate:modelValue":d[6]||(d[6]=a=>s(h).frequentFlyerNumberInfo=a),disabled:!1},null,8,["modelValue"]),[[n]])]),_:1},8,["label"]),r(m,{label:_.$t("app.intlPassengerForm.frequentTraveler.clientLevel"),prop:"frequentFlyerLevelNumberInfo"},{default:o(()=>[A(r(p,{modelValue:s(h).frequentFlyerLevelNumberInfo,"onUpdate:modelValue":d[7]||(d[7]=a=>s(h).frequentFlyerLevelNumberInfo=a),disabled:!1},null,8,["modelValue"]),[[n]])]),_:1},8,["label"])])]),e("div",$t,[e("div",qt,[(u(!0),g(H,null,Z(s(h).documentList,(a,q)=>(u(),g("div",{key:a.id,class:"document-item"},[e("div",Pt,[e("div",Ct,l(`${_.$t("app.intlPassengerForm.agencyFrequentFlyer.certificate")}${q+1}`),1),Nt,s(h).documentList.length>1?(u(),g("div",jt,[r(N,{class:"iconfont icon-delete",onClick:v=>s(k)("documentList",a.id)},null,8,["onClick"])])):z("",!0)]),e("div",Dt,[r(m,{label:_.$t("app.intlPassengerForm.agencyFrequentFlyer.idNoType"),prop:`documentList.${q}.documentTypeCode`,rules:s(L).documentTypeCode},{default:o(()=>[r(C,{modelValue:a.documentTypeCode,"onUpdate:modelValue":v=>a.documentTypeCode=v,onChange:v=>s(M)(q)},{default:o(()=>[(u(!0),g(H,null,Z(s(D),v=>(u(),T(x,{key:v.value,label:v.label,value:v.value},{default:o(()=>[s(D).some(U=>U.value===a.documentTypeCode)?(u(),g("span",kt,[v.value===a.documentTypeCode?(u(),T(N,{key:0,size:12,class:"iconfont icon-right-line"})):(u(),g("span",Tt))])):z("",!0),se(" "+l(v.label),1)]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["label","prop","rules"]),r(m,{label:_.$t("app.intlPassengerForm.agencyFrequentFlyer.passportNumber"),prop:`documentList.${q}.documentNumber`,rules:s(L).documentNumber},{default:o(()=>[A(r(p,{modelValue:a.documentNumber,"onUpdate:modelValue":v=>a.documentNumber=v,disabled:!1},null,8,["modelValue","onUpdate:modelValue"]),[[n]])]),_:2},1032,["label","prop","rules"]),r(m,{label:_.$t("app.intlPassengerForm.agencyFrequentFlyer.certificateExpirationDate"),prop:`documentList.${q}.expiryDate`,rules:s(L).expiryDate},{default:o(()=>[r(_e,{modelValue:a.expiryDate,"onUpdate:modelValue":v=>a.expiryDate=v,type:"date","value-format":"YYYY-MM-DD","prefix-icon":s(O),"disabled-date":s(Ze)},null,8,["modelValue","onUpdate:modelValue","prefix-icon","disabled-date"])]),_:2},1032,["label","prop","rules"]),s(G).includes(a.documentTypeCode)?z("",!0):(u(),T(m,{key:0,label:_.$t("app.intlPassengerForm.issueCountry"),prop:`documentList.${q}.issueCountryCode`,rules:s(L).issueCountryCode},{default:o(()=>[A(r(p,{modelValue:a.issueCountryCode,"onUpdate:modelValue":v=>a.issueCountryCode=v},null,8,["modelValue","onUpdate:modelValue"]),[[n]])]),_:2},1032,["label","prop","rules"])),s(G).includes(a.documentTypeCode)?z("",!0):(u(),T(m,{key:1,label:_.$t("app.intlPassengerForm.nationality"),prop:`documentList.${q}.nationalityCode`,rules:s(L).nationalityCode},{default:o(()=>[A(r(p,{modelValue:a.nationalityCode,"onUpdate:modelValue":v=>a.nationalityCode=v},null,8,["modelValue","onUpdate:modelValue"]),[[n]])]),_:2},1032,["label","prop","rules"]))])]))),128)),e("div",wt,[r(N,{class:"iconfont icon-plus-square primary-color",onClick:d[8]||(d[8]=a=>s(V)("documentList"))}),e("div",{class:"btn-text",onClick:d[9]||(d[9]=a=>s(V)("documentList"))},l(_.$t("app.intlPassengerForm.agencyFrequentFlyer.addCertificate")),1)])])]),e("div",Vt,[e("div",Lt,[e("div",Et,l(_.$t("app.intlPassengerForm.agencyFrequentFlyer.remarkInfo")),1),It]),e("div",At,[(u(!0),g(H,null,Z(s(h).remarkList,a=>(u(),g("div",{key:a.id,class:"remark-item"},[r(m,{label:"RMK",prop:"remark.remark"},{default:o(()=>[A(r(p,{modelValue:a.remark,"onUpdate:modelValue":q=>a.remark=q,disabled:!1},null,8,["modelValue","onUpdate:modelValue"]),[[n]])]),_:2},1024),e("div",Ut,[r(N,{class:"iconfont icon-delete",onClick:q=>s(k)("remarkList",a.id)},null,8,["onClick"])])]))),128)),e("div",Rt,[r(N,{class:"iconfont iconfont icon-plus-square primary-color",onClick:d[10]||(d[10]=a=>s(V)("remarkList"))}),e("div",{class:"btn-text",onClick:d[11]||(d[11]=a=>s(V)("remarkList"))},l(_.$t("app.intlPassengerForm.agencyFrequentFlyer.addRemark")),1)])])]),e("div",Mt,[e("div",St,[e("div",Ot,l(_.$t("app.intlPassengerForm.agencyFrequentFlyer.frequentGuestCard")),1),Bt]),e("div",Yt,[(u(!0),g(H,null,Z(s(h).cardList,(a,q)=>(u(),g("div",{key:a.id,class:"frequent-flyer-item"},[r(m,{label:_.$t("app.intlPassengerForm.frequentTraveler.airline"),prop:`cardList.${q}.airlineCode`,rules:s(L).airlineCode},{default:o(()=>[A(r(p,{modelValue:a.airlineCode,"onUpdate:modelValue":v=>a.airlineCode=v,disabled:!1},null,8,["modelValue","onUpdate:modelValue"]),[[n]])]),_:2},1032,["label","prop","rules"]),r(m,{label:_.$t("app.intlPassengerForm.agencyFrequentFlyer.cardNo"),prop:`cardList.${q}.frequentFlyerNumber`,rules:s(L).frequentFlyerNumber},{default:o(()=>[A(r(p,{modelValue:a.frequentFlyerNumber,"onUpdate:modelValue":v=>a.frequentFlyerNumber=v,disabled:!1},null,8,["modelValue","onUpdate:modelValue"]),[[n]])]),_:2},1032,["label","prop","rules"]),e("div",Gt,[r(N,{class:"iconfont icon-delete",onClick:v=>s(k)("cardList",a.id)},null,8,["onClick"])])]))),128)),e("div",zt,[r(N,{class:"iconfont iconfont icon-plus-square primary-color",onClick:d[12]||(d[12]=a=>s(V)("cardList"))}),e("div",{class:"btn-text",onClick:d[13]||(d[13]=a=>s(V)("cardList"))},l(_.$t("app.intlPassengerForm.agencyFrequentFlyer.addFrequentGuestCard")),1)])])])]),_:1},8,["model","rules"])]),e("div",Ht,[r(Q,{class:"footer-btn",type:"primary",onClick:s(Y)},{default:o(()=>[se(l(_.$t("app.intlPassengerForm.batchPassenger.confirm")),1)]),_:1},8,["onClick"]),r(Q,{class:"footer-btn",plain:"",onClick:d[14]||(d[14]=a=>s(B)(!1))},{default:o(()=>[se(l(_.$t("app.intlPassengerForm.batchPassenger.cancel")),1)]),_:1})])])),[[$,s(P)]])]),_:1},8,["modelValue","onClose"])}}});const Qt=at(Zt,[["__scopeId","data-v-e2ac9dc5"]]),Xt={class:"self-stretch flex justify-between items-center mb-2"},Jt={class:"justify-start text-color-gray-1 text-lg font-bold leading-normal"},Wt={class:"self-stretch h-7 flex justify-start items-center gap-1.5"},Kt={class:"min-w-[64px] flex justify-start items-center"},es={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},ts={class:"flex justify-start items-center gap-0.5"},ss={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},as={"data-length":"unfixed","data-size":"default","data-type":"gray",class:"px-1 bg-cyan-100 rounded-sm flex justify-center items-center"},ls={class:"text-center justify-start text-sky-700 text-xs font-normal leading-[20px]"},ns={class:"dialog-body"},rs={class:"self-stretch flex flex-col justify-start items-start gap-1"},os={class:"self-stretch inline-flex justify-start items-center gap-2.5 my-1.5"},is={class:"flex justify-start items-center gap-1.5"},ds={class:"justify-start text-color-gray-1 text-xs font-bold leading-tight"},us=e("div",{class:"w-full grow shrink basis-0 h-[0px] rotate-180 border-t-[1.25px] border-brand-3 border-dashed"},null,-1),cs={class:"self-stretch inline-flex justify-start items-start gap-3.5"},ms={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},ps={class:"min-w-[64px] flex justify-start items-center"},fs={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},gs={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},ys={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},_s={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},hs={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},vs={class:"min-w-[64px] flex justify-start items-center"},bs={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},xs={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},Fs={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},$s={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},qs={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ps={class:"min-w-[64px] flex justify-start items-center"},Cs={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Ns={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},js={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ds={class:"min-w-[64px] flex justify-start items-center"},ks={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Ts={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ws={class:"self-stretch inline-flex justify-start items-start gap-3.5"},Vs={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ls={class:"min-w-[64px] flex justify-start items-center"},Es={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Is={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},As={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Us={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Rs={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ms={class:"min-w-[64px] flex justify-start items-center"},Ss={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Os={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight truncate ... max-w-[150px]"},Bs={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ys={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Gs={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},zs={class:"min-w-[64px] flex justify-start items-center"},Hs={class:"justify-start text-color-gray-2 text-xs font-normal leading-tight"},Zs={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},Qs={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Xs={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Js={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ws={class:"min-w-[64px] flex justify-start items-center"},Ks={class:"justify-start text-color-gray-2 text-xs font-normal leading-tight"},ea={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight truncate ... max-w-[150px]"},ta={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},sa={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},aa={class:"self-stretch flex justify-start items-center gap-2.5 my-1.5"},la={class:"flex justify-start items-center gap-1.5"},na={class:"justify-start text-color-gray-1 text-xs font-bold leading-tight"},ra=e("div",{class:"w-full grow shrink basis-0 h-[0px] rotate-180 border-t-[1.25px] border-brand-3 border-dashed"},null,-1),oa={class:"self-stretch inline-flex justify-start items-start gap-3.5"},ia={class:"flex-1 h-7 inline-flex justify-start items-center gap-1.5"},da={"data-length":"unfixed","data-size":"default","data-type":"blue",class:"min-w-[64px] px-1 bg-brand-3 rounded-sm flex justify-center items-center"},ua={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[64px]"},ca={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ma={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},pa={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight truncate ... max-w-[175px]"},fa={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ga={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ya={key:0,class:"flex-1 h-7 flex justify-start items-center gap-1.5"},_a={class:"min-w-[64px] flex justify-start items-center"},ha={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},va={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ba={key:1,class:"flex-1 h-7 flex justify-start items-center gap-1.5"},xa={class:"flex justify-start items-center"},Fa={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},$a={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},qa={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Pa={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ca={key:2,class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Na={class:"flex justify-start items-center"},ja={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Da={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},ka={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ta={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},wa={class:"self-stretch flex flex-col justify-start items-start gap-1"},Va={class:"self-stretch flex justify-start items-center gap-2.5 my-1.5"},La={class:"flex justify-start items-center gap-1.5"},Ea={class:"justify-start text-color-gray-1 text-xs font-bold leading-tight"},Ia=e("div",{class:"w-full grow shrink basis-0 h-[0px] rotate-180 border-t-[1.25px] border-brand-3 border-dashed"},null,-1),Aa={key:0,class:"self-stretch inline-flex justify-start items-start gap-3.5"},Ua={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ra=e("div",{class:"min-w-[64px] flex justify-start items-center"},[e("div",{class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},"RMK")],-1),Ma={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[900px]"},Sa={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Oa={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ba={class:"self-stretch flex flex-col justify-start items-start gap-1"},Ya={class:"self-stretch flex justify-start items-center gap-2.5 my-1.5"},Ga={class:"flex justify-start items-center gap-1.5"},za={class:"justify-start text-color-gray-1 text-xs font-bold leading-tight"},Ha=e("div",{class:"w-full grow shrink basis-0 h-[0px] rotate-180 border-t-[1.25px] border-brand-3 border-dashed"},null,-1),Za={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Qa={class:"min-w-[64px] flex justify-start items-center"},Xa={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Ja={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Wa={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ka={class:"min-w-[64px] flex justify-start items-center"},el={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},tl={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},sl=e("div",{class:"flex-1 self-stretch"},null,-1),al=e("div",{class:"flex-1 self-stretch"},null,-1),ll={class:"dialog-footer"},nl=pe({__name:"RegularDetailsDialog",props:{detailData:{}},emits:["update:modelValue"],setup(f,{emit:j}){const y=j,F=()=>y("update:modelValue",!1);return(i,w)=>{const P=lt,h=fe,I=qe;return u(),T(I,{width:"1040px",center:"","close-on-click-modal":!1,class:"agencyDetails-dialog",onClose:w[1]||(w[1]=D=>F())},{header:o(({})=>{var D;return[e("div",Xt,[e("div",Jt,l(i.$t("app.intlPassengerForm.frequentTraveler.details")),1)]),e("div",Wt,[e("div",Kt,[e("div",es,l(i.$t("app.intlPassengerForm.frequentTraveler.travelerID")),1)]),e("div",ts,[e("div",ss,l((D=i.detailData)==null?void 0:D.csmIdentifier),1),e("div",as,[e("div",ls,l(i.$t("app.intlPassengerForm.agencyFrequentFlyer.regularCustomer")),1)])])])]}),footer:o(()=>[e("div",ll,[r(h,{onClick:w[0]||(w[0]=D=>F())},{default:o(()=>[se(l(i.$t("app.intlPassengerForm.frequentTraveler.close")),1)]),_:1})])]),default:o(()=>{var D,M,k,V,O,B,Y,L,G,_,d,m,S,p,x,C,N,W,Q,c,n,$,a,q,v,U,K,E,ee;return[e("div",ns,[e("div",rs,[e("div",os,[e("div",is,[e("div",ds,l(i.$t("app.intlPassengerForm.frequentTraveler.basicInfo")),1)]),us]),e("div",cs,[e("div",ms,[e("div",ps,[e("div",fs,l(i.$t("app.intlPassengerForm.frequentTraveler.chineseName")),1)]),e("div",gs,[((M=(D=i.detailData)==null?void 0:D.chineseName)==null?void 0:M.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:(k=i.detailData)==null?void 0:k.chineseName,placement:"top-start"},{default:o(()=>{var t;return[e("span",ys,l(((t=i.detailData)==null?void 0:t.chineseName)??"-"),1)]}),_:1},8,["content"])):(u(),g("span",_s,l(((V=i.detailData)==null?void 0:V.chineseName)??"-"),1))])]),e("div",hs,[e("div",vs,[e("div",bs,l(i.$t("app.intlPassengerForm.frequentTraveler.englishName")),1)]),e("div",xs,[((B=(O=i.detailData)==null?void 0:O.englishName)==null?void 0:B.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:(Y=i.detailData)==null?void 0:Y.englishName,placement:"top-start"},{default:o(()=>{var t;return[e("span",Fs,l(((t=i.detailData)==null?void 0:t.englishName)??"-"),1)]}),_:1},8,["content"])):(u(),g("span",$s,l(((L=i.detailData)==null?void 0:L.englishName)??"-"),1))])]),e("div",qs,[e("div",Ps,[e("div",Cs,l(i.$t("app.intlPassengerForm.frequentTraveler.gender")),1)]),e("div",Ns,l(i.$t(`app.intlPassengerForm.frequentTraveler.sex_${((G=i.detailData)==null?void 0:G.genderCode)??""}`)),1)]),e("div",js,[e("div",Ds,[e("div",ks,l(i.$t("app.intlPassengerForm.frequentTraveler.birthday")),1)]),e("div",Ts,l((_=i.detailData)==null?void 0:_.birthDate),1)])]),e("div",ws,[e("div",Vs,[e("div",Ls,[e("div",Es,l(i.$t("app.intlPassengerForm.frequentTraveler.mobilePhone")),1)]),e("div",Is,[((m=(d=i.detailData)==null?void 0:d.mobilePhone)==null?void 0:m.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:(S=i.detailData)==null?void 0:S.mobilePhone,placement:"top-start"},{default:o(()=>{var t;return[e("span",As,l(((t=i.detailData)==null?void 0:t.mobilePhone)??"-"),1)]}),_:1},8,["content"])):(u(),g("span",Us,l(((p=i.detailData)==null?void 0:p.mobilePhone)??"-"),1))])]),e("div",Rs,[e("div",Ms,[e("div",Ss,l(i.$t("app.intlPassengerForm.frequentTraveler.email")),1)]),e("div",Os,[((C=(x=i.detailData)==null?void 0:x.emailAddress)==null?void 0:C.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:(N=i.detailData)==null?void 0:N.emailAddress,placement:"top-start"},{default:o(()=>{var t;return[e("span",Bs,l(((t=i.detailData)==null?void 0:t.emailAddress)??"-"),1)]}),_:1},8,["content"])):(u(),g("span",Ys,l(((W=i.detailData)==null?void 0:W.emailAddress)??"-"),1))])]),e("div",Gs,[e("div",zs,[e("div",Hs,l(i.$t("app.intlPassengerForm.frequentTraveler.clientCode")),1)]),e("div",Zs,[((c=(Q=i.detailData)==null?void 0:Q.frequentFlyerNumberInfo)==null?void 0:c.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:(n=i.detailData)==null?void 0:n.frequentFlyerNumberInfo,placement:"top-start"},{default:o(()=>{var t;return[e("span",Qs,l(((t=i.detailData)==null?void 0:t.frequentFlyerNumberInfo)??"-"),1)]}),_:1},8,["content"])):(u(),g("span",Xs,l((($=i.detailData)==null?void 0:$.frequentFlyerNumberInfo)??"-"),1))])]),e("div",Js,[e("div",Ws,[e("div",Ks,l(i.$t("app.intlPassengerForm.frequentTraveler.clientLevel")),1)]),e("div",ea,[((q=(a=i.detailData)==null?void 0:a.frequentFlyerLevelNumberInfo)==null?void 0:q.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:(v=i.detailData)==null?void 0:v.frequentFlyerLevelNumberInfo,placement:"top-start"},{default:o(()=>{var t;return[e("span",ta,l(((t=i.detailData)==null?void 0:t.frequentFlyerLevelNumberInfo)??"-"),1)]}),_:1},8,["content"])):(u(),g("span",sa,l(((U=i.detailData)==null?void 0:U.frequentFlyerLevelNumberInfo)??"-"),1))])])])]),(u(!0),g(H,null,Z((K=i.detailData)==null?void 0:K.documentList,(t,te)=>{var X,re,b,ne;return u(),g("div",{key:te,class:"self-stretch flex flex-col justify-start items-start gap-1 mt-2"},[e("div",aa,[e("div",la,[e("div",na,l(i.$t("app.intlPassengerForm.frequentTraveler.idCard"))+l(te+1),1)]),ra]),e("div",oa,[e("div",ia,[e("div",da,[e("div",ua,[((X=i.$t(`app.intlPassengerForm.PP_${(t==null?void 0:t.documentTypeCode)||"empty"}`))==null?void 0:X.length)>5?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:i.$t(`app.intlPassengerForm.PP_${(t==null?void 0:t.documentTypeCode)||"empty"}`),placement:"top-start"},{default:o(()=>[e("span",ca,l(i.$t(`app.intlPassengerForm.PP_${(t==null?void 0:t.documentTypeCode)||"empty"}`)),1)]),_:2},1032,["content"])):(u(),g("span",ma,l(i.$t(`app.intlPassengerForm.PP_${(t==null?void 0:t.documentTypeCode)||"empty"}`)),1))])]),e("div",pa,[((re=t==null?void 0:t.documentNumber)==null?void 0:re.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:t==null?void 0:t.documentNumber,placement:"top-start"},{default:o(()=>[e("span",fa,l((t==null?void 0:t.documentNumber)??"-"),1)]),_:2},1032,["content"])):(u(),g("span",ga,l((t==null?void 0:t.documentNumber)??"-"),1))])]),t!=null&&t.expiryDate?(u(),g("div",ya,[e("div",_a,[e("div",ha,l(i.$t("app.intlPassengerForm.frequentTraveler.idValidity")),1)]),e("div",va,l(t==null?void 0:t.expiryDate),1)])):z("",!0),t!=null&&t.issueCountryCode?(u(),g("div",ba,[e("div",xa,[e("div",Fa,l(i.$t("app.intlPassengerForm.frequentTraveler.issueCountry")),1)]),e("div",$a,[((b=t==null?void 0:t.issueCountryCode)==null?void 0:b.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:t==null?void 0:t.issueCountryCode,placement:"top-start"},{default:o(()=>[e("span",qa,l((t==null?void 0:t.issueCountryCode)??"-"),1)]),_:2},1032,["content"])):(u(),g("span",Pa,l((t==null?void 0:t.issueCountryCode)??"-"),1))])])):z("",!0),t!=null&&t.nationalityCode?(u(),g("div",Ca,[e("div",Na,[e("div",ja,l(i.$t("app.intlPassengerForm.frequentTraveler.nationality")),1)]),e("div",Da,[((ne=t==null?void 0:t.nationalityCode)==null?void 0:ne.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:t==null?void 0:t.nationalityCode,placement:"top-start"},{default:o(()=>[e("span",ka,l((t==null?void 0:t.nationalityCode)??"-"),1)]),_:2},1032,["content"])):(u(),g("span",Ta,l((t==null?void 0:t.nationalityCode)??"-"),1))])])):z("",!0)])])}),128)),e("div",wa,[e("div",Va,[e("div",La,[e("div",Ea,l(i.$t("app.intlPassengerForm.frequentTraveler.remarks")),1)]),Ia]),(u(!0),g(H,null,Z((E=i.detailData)==null?void 0:E.remarkList,(t,te)=>{var X;return u(),g(H,{key:te},[t?(u(),g("div",Aa,[e("div",Ua,[Ra,e("div",Ma,[((X=t.remark)==null?void 0:X.length)>20?(u(),T(P,{key:0,class:"box-item",effect:"dark",content:t.remark,placement:"top-start"},{default:o(()=>[e("span",Sa,l(t.remark??"-"),1)]),_:2},1032,["content"])):(u(),g("span",Oa,l(t.remark??"-"),1))])])])):z("",!0)],64)}),128))]),e("div",Ba,[e("div",Ya,[e("div",Ga,[e("div",za,l(i.$t("app.intlPassengerForm.frequentTraveler.loyaltyCard")),1)]),Ha]),(u(!0),g(H,null,Z(((ee=i.detailData)==null?void 0:ee.cardList)??1,t=>(u(),g("div",{key:t,class:"self-stretch inline-flex justify-start items-start gap-3.5"},[e("div",Za,[e("div",Qa,[e("div",Xa,l(i.$t("app.intlPassengerForm.frequentTraveler.airline")),1)]),e("div",Ja,l(t==null?void 0:t.airlineCode),1)]),e("div",Wa,[e("div",Ka,[e("div",el,l(i.$t("app.intlPassengerForm.frequentTraveler.phone")),1)]),e("div",tl,l(t==null?void 0:t.frequentFlyerNumber),1)]),sl,al]))),128))])])]}),_:1})}}});const rl=()=>{const{t:f}=he(),j=R(!1),y=R(),F=R(),i=R(J.close),w=R(""),P=R({passengerName:"",documentNumber:"",mobilePhone:"",csmIdentifier:""}),h=R([]),I=R(!1),D=R({}),M=(p,x,C)=>{Xe.test(x)||Je.test(x)?C():C(new Error(f("app.pnrManagement.validate.nameInputError")))},k={documentNumber:[{pattern:Ue,message:f("app.pnrManagement.validate.formatErr"),trigger:"blur"}],mobilePhone:[{pattern:Re,message:f("app.pnrManagement.validate.formatErr"),trigger:"blur"}],passengerName:[{validator:M,trigger:"blur"}],csmIdentifier:[{pattern:Me,message:f("app.pnrManagement.validate.formatErr"),trigger:"blur"}]},V=async()=>{var p,x;h.value=[];try{j.value=!0;const{data:C}=await ot(P.value,"081L0133");h.value=(x=(p=C.value)==null?void 0:p.result)!=null&&x.length?[...C.value.result]:[]}finally{j.value=!1}},O=()=>{var x;if(Object.values(P.value).every(C=>C==="")){ie.warning(f("app.intlPassengerForm.frequentTraveler.queryAgentErrorTips"));return}(x=y.value)==null||x.validate(async C=>{C&&V()})},B=()=>{P.value={passengerName:"",documentNumber:"",mobilePhone:"",csmIdentifier:""}},Y=()=>{i.value=J.add,w.value=f("app.intlPassengerForm.frequentTraveler.create")},L=p=>{var x,C;i.value=J.close,p&&(P.value={passengerName:"",documentNumber:"",mobilePhone:"",csmIdentifier:((C=(x=F.value)==null?void 0:x.csmIdentifier)==null?void 0:C.toString())??""},V())},G=p=>{D.value=p,I.value=!0},_=p=>{F.value=Se(p),i.value=J.edit,w.value=f("app.intlPassengerForm.agencyFrequentFlyer.edit")},d=async p=>{j.value=!0;try{const{data:x}=await it(p,"081L0134");x.value==="OK"&&(ie({message:f("app.qMessage.deleteSuccess"),type:"success"}),V())}finally{j.value=!1}};return{FORM_RULES:k,formRef:y,showAgencyFrequentFlyerDialogType:i,editForm:F,closeAgencyFrequentFlyerDialog:L,headerTitle:w,queryForm:P,resetForm:B,loading:j,search:O,create:Y,customData:h,viewDetails:G,editDetails:_,deleteDetails:async p=>{const x=p.chineseName?p.chineseName:p.englishName??"";await Oe.confirm(me("div",{class:"batch-delete-tip-box"},f("app.intlPassengerForm.frequentTraveler.deleteAgentTips",{delName:x})),{icon:me("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"crs-btn-ui crs-btn-message-ui",confirmButtonText:f("app.button.ensure"),cancelButtonText:f("app.pnrManagement.flight.cancel"),showClose:!1}).then(()=>{d(p.csmIdentifier)})},viewAgentRegularData:D,isShowDetailsDialog:I,getPhoneList:p=>p.match(/[^,，]+/g)}},ol=rl,il={class:"agent-regulars"},dl={class:"justify-start items-center flex"},ul={class:"form-box w-full h-[52px] justify-start items-center gap-1.5 flex p-2.5 rounded-lg shadow"},cl={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},ml={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[140px]"},pl={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},fl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[180px]"},gl={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},yl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[180px]"},_l={class:"w-full self-stretch inline-flex justify-start items-center gap-1 overflow-hidden"},hl={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[80px]"},vl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[140px]"},bl={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},xl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[180px]"},Fl={class:"self-stretch inline-flex justify-start items-center gap-1 overflow-hidden"},$l={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},ql={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[180px]"},Pl=e("em",{class:"primary-color iconfont icon-eye"},null,-1),Cl=e("em",{class:"primary-color iconfont icon-edit"},null,-1),Nl=e("em",{class:"primary-color iconfont icon-delete"},null,-1),sn=pe({__name:"AgentRegulars",setup(f){const{formRef:j,FORM_RULES:y,queryForm:F,resetForm:i,loading:w,create:P,search:h,isShowDetailsDialog:I,customData:D,viewAgentRegularData:M,viewDetails:k,editDetails:V,deleteDetails:O,editForm:B,showAgencyFrequentFlyerDialogType:Y,headerTitle:L,closeAgencyFrequentFlyerDialog:G,getPhoneList:_}=ol();return(d,m)=>{const S=be,p=Fe,x=fe,C=$e,N=nt,W=rt,Q=xe;return A((u(),g("div",il,[e("div",dl,[e("div",ul,[r(C,{ref_key:"formRef",ref:j,inline:!0,model:s(F),rules:s(y),class:"frequent-traveler w-full inline-flex","require-asterisk-position":"right",onSubmit:m[11]||(m[11]=Be(()=>{},["prevent"]))},{default:o(()=>[r(p,{label:d.$t("app.intlPassengerForm.frequentTraveler.travelerID"),prop:"csmIdentifier"},{default:o(()=>[r(S,{modelValue:s(F).csmIdentifier,"onUpdate:modelValue":m[0]||(m[0]=c=>s(F).csmIdentifier=c),modelModifiers:{trim:!0},class:"pnr-input input-uppercase ml-1",clearable:"",onInput:m[1]||(m[1]=c=>{var n;return s(F).csmIdentifier=(n=s(F).csmIdentifier)==null?void 0:n.toUpperCase()})},null,8,["modelValue"])]),_:1},8,["label"]),r(p,{label:d.$t("app.intlPassengerForm.frequentTraveler.travelerName"),prop:"passengerName"},{default:o(()=>[r(S,{modelValue:s(F).passengerName,"onUpdate:modelValue":m[2]||(m[2]=c=>s(F).passengerName=c),modelModifiers:{trim:!0},class:"pnr-input input-uppercase ml-1",clearable:"",onInput:m[3]||(m[3]=c=>{var n;return s(F).passengerName=(n=s(F).passengerName)==null?void 0:n.toUpperCase()})},null,8,["modelValue"])]),_:1},8,["label"]),r(p,{label:d.$t("app.intlPassengerForm.frequentTraveler.travelerCard"),prop:"documentNumber"},{default:o(()=>[r(S,{modelValue:s(F).documentNumber,"onUpdate:modelValue":m[4]||(m[4]=c=>s(F).documentNumber=c),modelModifiers:{trim:!0},class:"pnr-input input-uppercase ml-1",clearable:"",onInput:m[5]||(m[5]=c=>{var n;return s(F).documentNumber=(n=s(F).documentNumber)==null?void 0:n.toUpperCase()})},null,8,["modelValue"])]),_:1},8,["label"]),r(p,{label:d.$t("app.intlPassengerForm.frequentTraveler.travelerTel"),prop:"mobilePhone"},{default:o(()=>[r(S,{modelValue:s(F).mobilePhone,"onUpdate:modelValue":m[6]||(m[6]=c=>s(F).mobilePhone=c),modelModifiers:{trim:!0},class:"pnr-input input-uppercase ml-1",clearable:"",onInput:m[7]||(m[7]=c=>{var n;return s(F).mobilePhone=(n=s(F).mobilePhone)==null?void 0:n.toUpperCase()})},null,8,["modelValue"])]),_:1},8,["label"]),r(p,null,{default:o(()=>[r(x,{type:"primary",onClick:m[8]||(m[8]=c=>s(h)())},{default:o(()=>[se(l(d.$t("app.pnrManagement.btnGroups.search"))+" Enter ",1)]),_:1}),r(x,{onClick:m[9]||(m[9]=c=>s(P)())},{default:o(()=>[se(l(d.$t("app.intlPassengerForm.frequentTraveler.create")),1)]),_:1}),r(x,{onClick:m[10]||(m[10]=c=>s(i)())},{default:o(()=>[se(l(d.$t("app.pnrManagement.btnGroups.reset")),1)]),_:1})]),_:1})]),_:1},8,["model","rules"])])]),r(W,{data:s(D),stripe:""},{default:o(()=>[r(N,{label:d.$t("app.intlPassengerForm.frequentTraveler.travelerID"),"min-width":"80"},{default:o(({row:c})=>[e("div",cl,[e("div",ml,[r(le,{"show-val":c.csmIdentifier},{default:o(()=>[e("span",null,l(c.csmIdentifier??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),r(N,{label:d.$t("app.intlPassengerForm.frequentTraveler.chineseName"),"min-width":"80"},{default:o(({row:c})=>[e("div",pl,[e("div",fl,[r(le,{"show-val":c.chineseName},{default:o(()=>[e("span",null,l(c.chineseName??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),r(N,{label:d.$t("app.intlPassengerForm.frequentTraveler.englishName"),"min-width":"160"},{default:o(({row:c})=>[e("div",gl,[e("div",yl,[r(le,{"show-val":c.englishName},{default:o(()=>[e("span",null,l(c.englishName??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),r(N,{label:d.$t("app.intlPassengerForm.frequentTraveler.documentInfo"),"min-width":"220"},{default:o(({row:c})=>[(u(!0),g(H,null,Z(c.documentList,(n,$)=>(u(),g("div",{key:$,"data-type":"cell-text",class:"w-full self-stretch inline-flex justify-start items-center overflow-hidden"},[e("div",_l,[e("div",hl,[r(le,{"show-val":d.$t(`app.intlPassengerForm.PP_${n.documentTypeCode||"empty"}`)},{default:o(()=>[e("span",null,l(d.$t(`app.intlPassengerForm.PP_${n.documentTypeCode||"empty"}`)),1)]),_:2},1032,["show-val"])]),e("div",vl,[r(le,{"show-val":n.documentNumberDes},{default:o(()=>[e("span",null,l(n.documentNumberDes??"-"),1)]),_:2},1032,["show-val"])])])]))),128))]),_:1},8,["label"]),r(N,{label:d.$t("app.intlPassengerForm.frequentTraveler.gender"),"min-width":"80"},{default:o(({row:c})=>[e("div",null,l(c!=null&&c.genderCode?d.$t(`app.intlPassengerForm.frequentTraveler.sex_${(c==null?void 0:c.genderCode)??""}`):"-"),1)]),_:1},8,["label"]),r(N,{label:d.$t("app.intlPassengerForm.frequentTraveler.birthday"),"min-width":"110"},{default:o(({row:c})=>[e("div",bl,[e("div",xl,[r(le,{"show-val":c.birthDate},{default:o(()=>[e("span",null,l(c.birthDate??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),r(N,{label:d.$t("app.intlPassengerForm.frequentTraveler.mobilePhone"),"min-width":"110"},{default:o(({row:c})=>[(u(!0),g(H,null,Z(s(_)(c.mobilePhoneDes),(n,$)=>(u(),g("div",{key:$,"data-type":"cell-text",class:"self-stretch inline-flex justify-start items-center overflow-hidden"},[e("div",Fl,l(n),1)]))),128))]),_:1},8,["label"]),r(N,{label:d.$t("app.intlPassengerForm.frequentTraveler.email"),"min-width":"160"},{default:o(({row:c})=>[e("div",$l,[e("div",ql,[r(le,{"show-val":c.emailAddress},{default:o(()=>[e("span",null,l(c.emailAddress??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),r(N,{label:d.$t("app.qMessage.operation"),width:"130"},{default:o(({row:c})=>[r(x,{type:"primary",link:"",onClick:n=>s(k)(c)},{default:o(()=>[Pl]),_:2},1032,["onClick"]),r(x,{type:"primary",link:"",onClick:n=>s(V)(c)},{default:o(()=>[Cl]),_:2},1032,["onClick"]),r(x,{type:"primary",link:"",onClick:n=>s(O)(c)},{default:o(()=>[Nl]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"]),s(I)?(u(),T(nl,{key:0,modelValue:s(I),"onUpdate:modelValue":m[12]||(m[12]=c=>ve(I)?I.value=c:null),"detail-data":s(M)},null,8,["modelValue","detail-data"])):z("",!0),s(Y)?(u(),T(Qt,{key:1,"show-agency-frequent-flyer-dialog-type":s(Y),"header-title":s(L),"edit-form":s(B),onClose:s(G)},null,8,["show-agency-frequent-flyer-dialog-type","header-title","edit-form","onClose"])):z("",!0)])),[[Q,s(w)]])}}});export{sn as default};
