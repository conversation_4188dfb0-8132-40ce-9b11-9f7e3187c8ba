import{fe as Q,r as y,ab as R,a9 as $,aD as z,w as D,ac as A,au as F,hd as j,aT as N,q,x as k,y as x,z as E,B,ai as G,aj as H,A as r,J as L,ak as U,Q as J,b3 as K,D as W,H as X}from"./index-9381ab2b.js";import{H as Y}from"./ticketOperationApi-8106707a.js";import{E as Z,a as ee}from"./index-094198d8.js";import{_ as te}from"./_plugin-vue_export-helper-c27b6911.js";const ae=Q("printNo",()=>{const n=y(new Map);return{printNosByOffice:n,setPrintNosByOffice:(u,c)=>{n.value.set(u,c)},deletePrintNosExceptOffice:u=>{const c=[];for(const[l]of n.value)l!==u&&c.push(l);for(const l of c)n.value.delete(l)}}}),ne=(n,g)=>{const{t:o}=R(),u=ae(),c=$(),{printNosByOffice:l}=z(u),f=y(n.modelValue),p=y([]),t=y([]),O=y([]),m=y(n.ticketType??""),P=y(!1),S=D(()=>{var e;return((e=c.state.user)==null?void 0:e.crsSystem)??!1}),C=D(()=>`printNo-select ${n.selectClass??""}`),h=(e,s)=>{var i,v,_;const a=((i=c.state.user)==null?void 0:i[e])??"";return a||(((_=(((v=c.state.user)==null?void 0:v[s])??"").split(";"))==null?void 0:_[0])??"")},V=e=>{const s=(l.value.get(e)??[]).filter(a=>a.type)??[];p.value=s.map(a=>({value:a.devno,label:a.devno,type:`${a.devno} ${o("app.agentTicketQuery.ticketMachine.type_"+a.type)}`})),t.value=N(p.value),O.value=N(t.value),I(n.ticketType??""),n.needDistinguish&&(n.isInter?t.value=t.value.filter(a=>!a.type.includes(o("app.agentTicketQuery.ticketMachine.domestic"))):t.value=t.value.filter(a=>a.type.includes(o("app.agentTicketQuery.ticketMachine.domestic"))))},b=async()=>{var s;if(!S.value)return;const e=h("defaultOffice","office");if(!l.value.get(e)||((s=l.value.get(e))==null?void 0:s.length)===0)try{P.value=!0;const a=F("10270401"),i=(await Y({office:e},a,!0)).data.value;await u.setPrintNosByOffice((i==null?void 0:i.office.office)??e,(i==null?void 0:i.ticketMachines)??[])}finally{P.value=!1}await u.deletePrintNosExceptOffice(e),await V(e)},T=()=>{n.isInter?t.value=t.value.filter(e=>!e.type.includes(o("app.agentTicketQuery.ticketMachine.domestic"))):t.value=t.value.filter(e=>e.type.includes(o("app.agentTicketQuery.ticketMachine.domestic")))},d=e=>{var s,a,i;if(e){let v=[];j.test(e.trim())?v=(p.value??[]).filter(_=>_.value===e):v=(p.value??[]).filter(_=>_.type.includes(e)),t.value=N(v),f.value=((s=t.value)==null?void 0:s.length)>0?(i=(a=t.value)==null?void 0:a[0])==null?void 0:i.value:e.toUpperCase()}else t.value=N(p.value);n.needDistinguish&&T(),t.value=(t.value??[]).filter(v=>v.type.includes(m.value))},I=e=>{e&&(f.value=n.modelValue,m.value=N(e),e==="ARL"&&(m.value=o("app.pnrManagement.paymentMethod.currentTicketReal")),n.needDistinguish&&(t.value=N(p.value),T()),t.value=(t.value??[]).filter(s=>s.type.includes(m.value)))},M=()=>{var s;const e=t.value.find(a=>a.value===f.value);return e?(s=e.type)!=null&&s.includes(o("app.agentTicketQuery.ticketMachine.domestic"))?"D":"I":""},w=()=>{g("update:modelValue",f.value),g("deliverPrintType",M())};return A(()=>n.modelValue,()=>{f.value=n.modelValue}),{printNo:f,printNos:t,loading:P,selectClass:C,filterPrintNo:d,setPrintNo:w,filterPrintToTicketType:I,init:b}},le=ne,se={key:0},ie={key:1,class:"inline-block w-[12px]"},oe=q({__name:"PrintNoSelect",props:{modelValue:{},selectClass:{},isInter:{type:Boolean},needDistinguish:{type:Boolean},ticketType:{}},emits:["update:modelValue","deliverPrintType"],setup(n,{expose:g,emit:o}){const u=n,c=o,{printNo:l,printNos:f,loading:p,selectClass:t,filterPrintNo:O,setPrintNo:m,filterPrintToTicketType:P,init:S}=le(u,c);return g({filterPrintToTicketType:P}),(C,h)=>{const V=X,b=Z,T=ee;return k(),x(T,{modelValue:r(l),"onUpdate:modelValue":h[0]||(h[0]=d=>K(l)?l.value=d:null),class:W(r(t)),"popper-class":"printNo-select-option",placeholder:" ",loading:r(p),filterable:"",clearable:"","filter-method":r(O),onFocus:r(S),onBlur:r(m)},{default:E(()=>[(k(!0),B(G,null,H(r(f),d=>(k(),x(b,{key:d.value,label:d.label,value:d.value},{default:E(()=>[r(f).some(I=>I.value===r(l))?(k(),B("span",se,[d.value===r(l)?(k(),x(V,{key:0,size:12,class:"iconfont icon-right-line"})):(k(),B("span",ie))])):L("",!0),U(" "+J(d.type),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","class","loading","filter-method","onFocus","onBlur"])}}});const de=te(oe,[["__scopeId","data-v-db081c8d"]]);export{de as P};
