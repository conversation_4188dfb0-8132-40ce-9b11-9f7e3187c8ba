var Ro=Object.defineProperty,ko=Object.defineProperties,Io=Object.getOwnPropertyDescriptors,pi=Object.getOwnPropertySymbols,Co=Object.prototype.hasOwnProperty,No=Object.prototype.propertyIsEnumerable,Wn=(r,e,t)=>e in r?Ro(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t,me=(r,e)=>{for(var t in e||(e={}))Co.call(e,t)&&Wn(r,t,e[t]);if(pi)for(var t of pi(e))No.call(e,t)&&Wn(r,t,e[t]);return r},je=(r,e)=>ko(r,Io(e)),re=(r,e,t)=>Wn(r,typeof e!="symbol"?e+"":e,t),Oo=(r,e,t)=>new Promise((a,o)=>{var l=h=>{try{i(t.next(h))}catch(x){o(x)}},d=h=>{try{i(t.throw(h))}catch(x){o(x)}},i=h=>h.done?a(h.value):Promise.resolve(h.value).then(l,d);i((t=t.apply(r,e)).next())});class Wt{constructor(e){re(this,"rootKey"),this.rootKey=e}}const Do=Object.seal({});class ne extends Wt{constructor(e){super(e),re(this,"root"),this.root=new Array}prepForXml(e){var t;e.stack.push(this);const a=this.root.map(o=>o instanceof Wt?o.prepForXml(e):o).filter(o=>o!==void 0);return e.stack.pop(),{[this.rootKey]:a.length?a.length===1&&((t=a[0])!=null&&t._attr)?a[0]:a:Do}}addChildElement(e){return this.root.push(e),this}}class ze extends ne{prepForXml(e){const t=super.prepForXml(e);if(t&&(typeof t[this.rootKey]!="object"||Object.keys(t[this.rootKey]).length))return t}}class fe extends Wt{constructor(e){super("_attr"),re(this,"xmlKeys"),this.root=e}prepForXml(e){const t={};return Object.entries(this.root).forEach(([a,o])=>{if(o!==void 0){const l=this.xmlKeys&&this.xmlKeys[a]||a;t[l]=o}}),{_attr:t}}}class We extends Wt{constructor(e){super("_attr"),this.root=e}prepForXml(e){return{_attr:Object.values(this.root).filter(({value:a})=>a!==void 0).reduce((a,{key:o,value:l})=>je(me({},a),{[o]:l}),{})}}}class _e extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val",color:"w:color",fill:"w:fill",space:"w:space",sz:"w:sz",type:"w:type",rsidR:"w:rsidR",rsidRPr:"w:rsidRPr",rsidSect:"w:rsidSect",w:"w:w",h:"w:h",top:"w:top",right:"w:right",bottom:"w:bottom",left:"w:left",header:"w:header",footer:"w:footer",gutter:"w:gutter",linePitch:"w:linePitch",pos:"w:pos"})}}var Fe=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Yn(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Qt={},Ct={exports:{}},mi;function Jn(){if(mi)return Ct.exports;mi=1;var r=typeof Reflect=="object"?Reflect:null,e=r&&typeof r.apply=="function"?r.apply:function(S,P,B){return Function.prototype.apply.call(S,P,B)},t;r&&typeof r.ownKeys=="function"?t=r.ownKeys:Object.getOwnPropertySymbols?t=function(S){return Object.getOwnPropertyNames(S).concat(Object.getOwnPropertySymbols(S))}:t=function(S){return Object.getOwnPropertyNames(S)};function a(c){console&&console.warn&&console.warn(c)}var o=Number.isNaN||function(S){return S!==S};function l(){l.init.call(this)}Ct.exports=l,Ct.exports.once=u,l.EventEmitter=l,l.prototype._events=void 0,l.prototype._eventsCount=0,l.prototype._maxListeners=void 0;var d=10;function i(c){if(typeof c!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof c)}Object.defineProperty(l,"defaultMaxListeners",{enumerable:!0,get:function(){return d},set:function(c){if(typeof c!="number"||c<0||o(c))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+c+".");d=c}}),l.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},l.prototype.setMaxListeners=function(S){if(typeof S!="number"||S<0||o(S))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+S+".");return this._maxListeners=S,this};function h(c){return c._maxListeners===void 0?l.defaultMaxListeners:c._maxListeners}l.prototype.getMaxListeners=function(){return h(this)},l.prototype.emit=function(S){for(var P=[],B=1;B<arguments.length;B++)P.push(arguments[B]);var W=S==="error",C=this._events;if(C!==void 0)W=W&&C.error===void 0;else if(!W)return!1;if(W){var Z;if(P.length>0&&(Z=P[0]),Z instanceof Error)throw Z;var le=new Error("Unhandled error."+(Z?" ("+Z.message+")":""));throw le.context=Z,le}var N=C[S];if(N===void 0)return!1;if(typeof N=="function")e(N,this,P);else for(var U=N.length,g=T(N,U),B=0;B<U;++B)e(g[B],this,P);return!0};function x(c,S,P,B){var W,C,Z;if(i(P),C=c._events,C===void 0?(C=c._events=Object.create(null),c._eventsCount=0):(C.newListener!==void 0&&(c.emit("newListener",S,P.listener?P.listener:P),C=c._events),Z=C[S]),Z===void 0)Z=C[S]=P,++c._eventsCount;else if(typeof Z=="function"?Z=C[S]=B?[P,Z]:[Z,P]:B?Z.unshift(P):Z.push(P),W=h(c),W>0&&Z.length>W&&!Z.warned){Z.warned=!0;var le=new Error("Possible EventEmitter memory leak detected. "+Z.length+" "+String(S)+" listeners added. Use emitter.setMaxListeners() to increase limit");le.name="MaxListenersExceededWarning",le.emitter=c,le.type=S,le.count=Z.length,a(le)}return c}l.prototype.addListener=function(S,P){return x(this,S,P,!1)},l.prototype.on=l.prototype.addListener,l.prototype.prependListener=function(S,P){return x(this,S,P,!0)};function A(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function R(c,S,P){var B={fired:!1,wrapFn:void 0,target:c,type:S,listener:P},W=A.bind(B);return W.listener=P,B.wrapFn=W,W}l.prototype.once=function(S,P){return i(P),this.on(S,R(this,S,P)),this},l.prototype.prependOnceListener=function(S,P){return i(P),this.prependListener(S,R(this,S,P)),this},l.prototype.removeListener=function(S,P){var B,W,C,Z,le;if(i(P),W=this._events,W===void 0)return this;if(B=W[S],B===void 0)return this;if(B===P||B.listener===P)--this._eventsCount===0?this._events=Object.create(null):(delete W[S],W.removeListener&&this.emit("removeListener",S,B.listener||P));else if(typeof B!="function"){for(C=-1,Z=B.length-1;Z>=0;Z--)if(B[Z]===P||B[Z].listener===P){le=B[Z].listener,C=Z;break}if(C<0)return this;C===0?B.shift():b(B,C),B.length===1&&(W[S]=B[0]),W.removeListener!==void 0&&this.emit("removeListener",S,le||P)}return this},l.prototype.off=l.prototype.removeListener,l.prototype.removeAllListeners=function(S){var P,B,W;if(B=this._events,B===void 0)return this;if(B.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):B[S]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete B[S]),this;if(arguments.length===0){var C=Object.keys(B),Z;for(W=0;W<C.length;++W)Z=C[W],Z!=="removeListener"&&this.removeAllListeners(Z);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(P=B[S],typeof P=="function")this.removeListener(S,P);else if(P!==void 0)for(W=P.length-1;W>=0;W--)this.removeListener(S,P[W]);return this};function k(c,S,P){var B=c._events;if(B===void 0)return[];var W=B[S];return W===void 0?[]:typeof W=="function"?P?[W.listener||W]:[W]:P?I(W):T(W,W.length)}l.prototype.listeners=function(S){return k(this,S,!0)},l.prototype.rawListeners=function(S){return k(this,S,!1)},l.listenerCount=function(c,S){return typeof c.listenerCount=="function"?c.listenerCount(S):y.call(c,S)},l.prototype.listenerCount=y;function y(c){var S=this._events;if(S!==void 0){var P=S[c];if(typeof P=="function")return 1;if(P!==void 0)return P.length}return 0}l.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]};function T(c,S){for(var P=new Array(S),B=0;B<S;++B)P[B]=c[B];return P}function b(c,S){for(;S+1<c.length;S++)c[S]=c[S+1];c.pop()}function I(c){for(var S=new Array(c.length),P=0;P<S.length;++P)S[P]=c[P].listener||c[P];return S}function u(c,S){return new Promise(function(P,B){function W(Z){c.removeListener(S,C),B(Z)}function C(){typeof c.removeListener=="function"&&c.removeListener("error",W),P([].slice.call(arguments))}m(c,S,C,{once:!0}),S!=="error"&&v(c,W,{once:!0})})}function v(c,S,P){typeof c.on=="function"&&m(c,"error",S,P)}function m(c,S,P,B){if(typeof c.on=="function")B.once?c.once(S,P):c.on(S,P);else if(typeof c.addEventListener=="function")c.addEventListener(S,function W(C){B.once&&c.removeEventListener(S,W),P(C)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof c)}return Ct.exports}var Nt={exports:{}},wi;function Ke(){return wi||(wi=1,typeof Object.create=="function"?Nt.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:Nt.exports=function(e,t){if(t){e.super_=t;var a=function(){};a.prototype=t.prototype,e.prototype=new a,e.prototype.constructor=e}}),Nt.exports}function Fo(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var xa={exports:{}},xe=xa.exports={},Oe,De;function qn(){throw new Error("setTimeout has not been defined")}function Gn(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?Oe=setTimeout:Oe=qn}catch{Oe=qn}try{typeof clearTimeout=="function"?De=clearTimeout:De=Gn}catch{De=Gn}})();function Aa(r){if(Oe===setTimeout)return setTimeout(r,0);if((Oe===qn||!Oe)&&setTimeout)return Oe=setTimeout,setTimeout(r,0);try{return Oe(r,0)}catch{try{return Oe.call(null,r,0)}catch{return Oe.call(this,r,0)}}}function Po(r){if(De===clearTimeout)return clearTimeout(r);if((De===Gn||!De)&&clearTimeout)return De=clearTimeout,clearTimeout(r);try{return De(r)}catch{try{return De.call(null,r)}catch{return De.call(this,r)}}}var Ue=[],st=!1,Ye,zt=-1;function Bo(){!st||!Ye||(st=!1,Ye.length?Ue=Ye.concat(Ue):zt=-1,Ue.length&&Ta())}function Ta(){if(!st){var r=Aa(Bo);st=!0;for(var e=Ue.length;e;){for(Ye=Ue,Ue=[];++zt<e;)Ye&&Ye[zt].run();zt=-1,e=Ue.length}Ye=null,st=!1,Po(r)}}xe.nextTick=function(r){var e=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];Ue.push(new Sa(r,e)),Ue.length===1&&!st&&Aa(Ta)};function Sa(r,e){this.fun=r,this.array=e}Sa.prototype.run=function(){this.fun.apply(null,this.array)};xe.title="browser";xe.browser=!0;xe.env={};xe.argv=[];xe.version="";xe.versions={};function qe(){}xe.on=qe;xe.addListener=qe;xe.once=qe;xe.off=qe;xe.removeListener=qe;xe.removeAllListeners=qe;xe.emit=qe;xe.prependListener=qe;xe.prependOnceListener=qe;xe.listeners=function(r){return[]};xe.binding=function(r){throw new Error("process.binding is not supported")};xe.cwd=function(){return"/"};xe.chdir=function(r){throw new Error("process.chdir is not supported")};xe.umask=function(){return 0};var Lo=xa.exports;const we=Fo(Lo);var er,gi;function Ra(){return gi||(gi=1,er=Jn().EventEmitter),er}var tr={},mt={},yi;function Mo(){if(yi)return mt;yi=1,mt.byteLength=i,mt.toByteArray=x,mt.fromByteArray=k;for(var r=[],e=[],t=typeof Uint8Array<"u"?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,l=a.length;o<l;++o)r[o]=a[o],e[a.charCodeAt(o)]=o;e["-".charCodeAt(0)]=62,e["_".charCodeAt(0)]=63;function d(y){var T=y.length;if(T%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var b=y.indexOf("=");b===-1&&(b=T);var I=b===T?0:4-b%4;return[b,I]}function i(y){var T=d(y),b=T[0],I=T[1];return(b+I)*3/4-I}function h(y,T,b){return(T+b)*3/4-b}function x(y){var T,b=d(y),I=b[0],u=b[1],v=new t(h(y,I,u)),m=0,c=u>0?I-4:I,S;for(S=0;S<c;S+=4)T=e[y.charCodeAt(S)]<<18|e[y.charCodeAt(S+1)]<<12|e[y.charCodeAt(S+2)]<<6|e[y.charCodeAt(S+3)],v[m++]=T>>16&255,v[m++]=T>>8&255,v[m++]=T&255;return u===2&&(T=e[y.charCodeAt(S)]<<2|e[y.charCodeAt(S+1)]>>4,v[m++]=T&255),u===1&&(T=e[y.charCodeAt(S)]<<10|e[y.charCodeAt(S+1)]<<4|e[y.charCodeAt(S+2)]>>2,v[m++]=T>>8&255,v[m++]=T&255),v}function A(y){return r[y>>18&63]+r[y>>12&63]+r[y>>6&63]+r[y&63]}function R(y,T,b){for(var I,u=[],v=T;v<b;v+=3)I=(y[v]<<16&16711680)+(y[v+1]<<8&65280)+(y[v+2]&255),u.push(A(I));return u.join("")}function k(y){for(var T,b=y.length,I=b%3,u=[],v=16383,m=0,c=b-I;m<c;m+=v)u.push(R(y,m,m+v>c?c:m+v));return I===1?(T=y[b-1],u.push(r[T>>2]+r[T<<4&63]+"==")):I===2&&(T=(y[b-2]<<8)+y[b-1],u.push(r[T>>10]+r[T>>4&63]+r[T<<2&63]+"=")),u.join("")}return mt}var Ot={};/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */var vi;function Uo(){return vi||(vi=1,Ot.read=function(r,e,t,a,o){var l,d,i=o*8-a-1,h=(1<<i)-1,x=h>>1,A=-7,R=t?o-1:0,k=t?-1:1,y=r[e+R];for(R+=k,l=y&(1<<-A)-1,y>>=-A,A+=i;A>0;l=l*256+r[e+R],R+=k,A-=8);for(d=l&(1<<-A)-1,l>>=-A,A+=a;A>0;d=d*256+r[e+R],R+=k,A-=8);if(l===0)l=1-x;else{if(l===h)return d?NaN:(y?-1:1)*(1/0);d=d+Math.pow(2,a),l=l-x}return(y?-1:1)*d*Math.pow(2,l-a)},Ot.write=function(r,e,t,a,o,l){var d,i,h,x=l*8-o-1,A=(1<<x)-1,R=A>>1,k=o===23?Math.pow(2,-24)-Math.pow(2,-77):0,y=a?0:l-1,T=a?1:-1,b=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(i=isNaN(e)?1:0,d=A):(d=Math.floor(Math.log(e)/Math.LN2),e*(h=Math.pow(2,-d))<1&&(d--,h*=2),d+R>=1?e+=k/h:e+=k*Math.pow(2,1-R),e*h>=2&&(d++,h/=2),d+R>=A?(i=0,d=A):d+R>=1?(i=(e*h-1)*Math.pow(2,o),d=d+R):(i=e*Math.pow(2,R-1)*Math.pow(2,o),d=0));o>=8;r[t+y]=i&255,y+=T,i/=256,o-=8);for(d=d<<o|i,x+=o;x>0;r[t+y]=d&255,y+=T,d/=256,x-=8);r[t+y-T]|=b*128}),Ot}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var bi;function qt(){return bi||(bi=1,function(r){var e=Mo(),t=Uo(),a=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;r.Buffer=i,r.SlowBuffer=v,r.INSPECT_MAX_BYTES=50;var o=**********;r.kMaxLength=o,i.TYPED_ARRAY_SUPPORT=l(),!i.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function l(){try{var E=new Uint8Array(1),n={foo:function(){return 42}};return Object.setPrototypeOf(n,Uint8Array.prototype),Object.setPrototypeOf(E,n),E.foo()===42}catch{return!1}}Object.defineProperty(i.prototype,"parent",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.buffer}}),Object.defineProperty(i.prototype,"offset",{enumerable:!0,get:function(){if(i.isBuffer(this))return this.byteOffset}});function d(E){if(E>o)throw new RangeError('The value "'+E+'" is invalid for option "size"');var n=new Uint8Array(E);return Object.setPrototypeOf(n,i.prototype),n}function i(E,n,s){if(typeof E=="number"){if(typeof n=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return R(E)}return h(E,n,s)}i.poolSize=8192;function h(E,n,s){if(typeof E=="string")return k(E,n);if(ArrayBuffer.isView(E))return T(E);if(E==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof E);if(J(E,ArrayBuffer)||E&&J(E.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(J(E,SharedArrayBuffer)||E&&J(E.buffer,SharedArrayBuffer)))return b(E,n,s);if(typeof E=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var p=E.valueOf&&E.valueOf();if(p!=null&&p!==E)return i.from(p,n,s);var L=I(E);if(L)return L;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof E[Symbol.toPrimitive]=="function")return i.from(E[Symbol.toPrimitive]("string"),n,s);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof E)}i.from=function(E,n,s){return h(E,n,s)},Object.setPrototypeOf(i.prototype,Uint8Array.prototype),Object.setPrototypeOf(i,Uint8Array);function x(E){if(typeof E!="number")throw new TypeError('"size" argument must be of type number');if(E<0)throw new RangeError('The value "'+E+'" is invalid for option "size"')}function A(E,n,s){return x(E),E<=0?d(E):n!==void 0?typeof s=="string"?d(E).fill(n,s):d(E).fill(n):d(E)}i.alloc=function(E,n,s){return A(E,n,s)};function R(E){return x(E),d(E<0?0:u(E)|0)}i.allocUnsafe=function(E){return R(E)},i.allocUnsafeSlow=function(E){return R(E)};function k(E,n){if((typeof n!="string"||n==="")&&(n="utf8"),!i.isEncoding(n))throw new TypeError("Unknown encoding: "+n);var s=m(E,n)|0,p=d(s),L=p.write(E,n);return L!==s&&(p=p.slice(0,L)),p}function y(E){for(var n=E.length<0?0:u(E.length)|0,s=d(n),p=0;p<n;p+=1)s[p]=E[p]&255;return s}function T(E){if(J(E,Uint8Array)){var n=new Uint8Array(E);return b(n.buffer,n.byteOffset,n.byteLength)}return y(E)}function b(E,n,s){if(n<0||E.byteLength<n)throw new RangeError('"offset" is outside of buffer bounds');if(E.byteLength<n+(s||0))throw new RangeError('"length" is outside of buffer bounds');var p;return n===void 0&&s===void 0?p=new Uint8Array(E):s===void 0?p=new Uint8Array(E,n):p=new Uint8Array(E,n,s),Object.setPrototypeOf(p,i.prototype),p}function I(E){if(i.isBuffer(E)){var n=u(E.length)|0,s=d(n);return s.length===0||E.copy(s,0,0,n),s}if(E.length!==void 0)return typeof E.length!="number"||f(E.length)?d(0):y(E);if(E.type==="Buffer"&&Array.isArray(E.data))return y(E.data)}function u(E){if(E>=o)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o.toString(16)+" bytes");return E|0}function v(E){return+E!=E&&(E=0),i.alloc(+E)}i.isBuffer=function(n){return n!=null&&n._isBuffer===!0&&n!==i.prototype},i.compare=function(n,s){if(J(n,Uint8Array)&&(n=i.from(n,n.offset,n.byteLength)),J(s,Uint8Array)&&(s=i.from(s,s.offset,s.byteLength)),!i.isBuffer(n)||!i.isBuffer(s))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(n===s)return 0;for(var p=n.length,L=s.length,q=0,H=Math.min(p,L);q<H;++q)if(n[q]!==s[q]){p=n[q],L=s[q];break}return p<L?-1:L<p?1:0},i.isEncoding=function(n){switch(String(n).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(n,s){if(!Array.isArray(n))throw new TypeError('"list" argument must be an Array of Buffers');if(n.length===0)return i.alloc(0);var p;if(s===void 0)for(s=0,p=0;p<n.length;++p)s+=n[p].length;var L=i.allocUnsafe(s),q=0;for(p=0;p<n.length;++p){var H=n[p];if(J(H,Uint8Array))q+H.length>L.length?i.from(H).copy(L,q):Uint8Array.prototype.set.call(L,H,q);else if(i.isBuffer(H))H.copy(L,q);else throw new TypeError('"list" argument must be an Array of Buffers');q+=H.length}return L};function m(E,n){if(i.isBuffer(E))return E.length;if(ArrayBuffer.isView(E)||J(E,ArrayBuffer))return E.byteLength;if(typeof E!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof E);var s=E.length,p=arguments.length>2&&arguments[2]===!0;if(!p&&s===0)return 0;for(var L=!1;;)switch(n){case"ascii":case"latin1":case"binary":return s;case"utf8":case"utf-8":return w(E).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return s*2;case"hex":return s>>>1;case"base64":return O(E).length;default:if(L)return p?-1:w(E).length;n=(""+n).toLowerCase(),L=!0}}i.byteLength=m;function c(E,n,s){var p=!1;if((n===void 0||n<0)&&(n=0),n>this.length||((s===void 0||s>this.length)&&(s=this.length),s<=0)||(s>>>=0,n>>>=0,s<=n))return"";for(E||(E="utf8");;)switch(E){case"hex":return Q(this,n,s);case"utf8":case"utf-8":return g(this,n,s);case"ascii":return z(this,n,s);case"latin1":case"binary":return se(this,n,s);case"base64":return U(this,n,s);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ce(this,n,s);default:if(p)throw new TypeError("Unknown encoding: "+E);E=(E+"").toLowerCase(),p=!0}}i.prototype._isBuffer=!0;function S(E,n,s){var p=E[n];E[n]=E[s],E[s]=p}i.prototype.swap16=function(){var n=this.length;if(n%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var s=0;s<n;s+=2)S(this,s,s+1);return this},i.prototype.swap32=function(){var n=this.length;if(n%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var s=0;s<n;s+=4)S(this,s,s+3),S(this,s+1,s+2);return this},i.prototype.swap64=function(){var n=this.length;if(n%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var s=0;s<n;s+=8)S(this,s,s+7),S(this,s+1,s+6),S(this,s+2,s+5),S(this,s+3,s+4);return this},i.prototype.toString=function(){var n=this.length;return n===0?"":arguments.length===0?g(this,0,n):c.apply(this,arguments)},i.prototype.toLocaleString=i.prototype.toString,i.prototype.equals=function(n){if(!i.isBuffer(n))throw new TypeError("Argument must be a Buffer");return this===n?!0:i.compare(this,n)===0},i.prototype.inspect=function(){var n="",s=r.INSPECT_MAX_BYTES;return n=this.toString("hex",0,s).replace(/(.{2})/g,"$1 ").trim(),this.length>s&&(n+=" ... "),"<Buffer "+n+">"},a&&(i.prototype[a]=i.prototype.inspect),i.prototype.compare=function(n,s,p,L,q){if(J(n,Uint8Array)&&(n=i.from(n,n.offset,n.byteLength)),!i.isBuffer(n))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof n);if(s===void 0&&(s=0),p===void 0&&(p=n?n.length:0),L===void 0&&(L=0),q===void 0&&(q=this.length),s<0||p>n.length||L<0||q>this.length)throw new RangeError("out of range index");if(L>=q&&s>=p)return 0;if(L>=q)return-1;if(s>=p)return 1;if(s>>>=0,p>>>=0,L>>>=0,q>>>=0,this===n)return 0;for(var H=q-L,ie=p-s,oe=Math.min(H,ie),ae=this.slice(L,q),he=n.slice(s,p),de=0;de<oe;++de)if(ae[de]!==he[de]){H=ae[de],ie=he[de];break}return H<ie?-1:ie<H?1:0};function P(E,n,s,p,L){if(E.length===0)return-1;if(typeof s=="string"?(p=s,s=0):s>**********?s=**********:s<-2147483648&&(s=-2147483648),s=+s,f(s)&&(s=L?0:E.length-1),s<0&&(s=E.length+s),s>=E.length){if(L)return-1;s=E.length-1}else if(s<0)if(L)s=0;else return-1;if(typeof n=="string"&&(n=i.from(n,p)),i.isBuffer(n))return n.length===0?-1:B(E,n,s,p,L);if(typeof n=="number")return n=n&255,typeof Uint8Array.prototype.indexOf=="function"?L?Uint8Array.prototype.indexOf.call(E,n,s):Uint8Array.prototype.lastIndexOf.call(E,n,s):B(E,[n],s,p,L);throw new TypeError("val must be string, number or Buffer")}function B(E,n,s,p,L){var q=1,H=E.length,ie=n.length;if(p!==void 0&&(p=String(p).toLowerCase(),p==="ucs2"||p==="ucs-2"||p==="utf16le"||p==="utf-16le")){if(E.length<2||n.length<2)return-1;q=2,H/=2,ie/=2,s/=2}function oe(Se,Ge){return q===1?Se[Ge]:Se.readUInt16BE(Ge*q)}var ae;if(L){var he=-1;for(ae=s;ae<H;ae++)if(oe(E,ae)===oe(n,he===-1?0:ae-he)){if(he===-1&&(he=ae),ae-he+1===ie)return he*q}else he!==-1&&(ae-=ae-he),he=-1}else for(s+ie>H&&(s=H-ie),ae=s;ae>=0;ae--){for(var de=!0,pe=0;pe<ie;pe++)if(oe(E,ae+pe)!==oe(n,pe)){de=!1;break}if(de)return ae}return-1}i.prototype.includes=function(n,s,p){return this.indexOf(n,s,p)!==-1},i.prototype.indexOf=function(n,s,p){return P(this,n,s,p,!0)},i.prototype.lastIndexOf=function(n,s,p){return P(this,n,s,p,!1)};function W(E,n,s,p){s=Number(s)||0;var L=E.length-s;p?(p=Number(p),p>L&&(p=L)):p=L;var q=n.length;p>q/2&&(p=q/2);for(var H=0;H<p;++H){var ie=parseInt(n.substr(H*2,2),16);if(f(ie))return H;E[s+H]=ie}return H}function C(E,n,s,p){return F(w(n,E.length-s),E,s,p)}function Z(E,n,s,p){return F(j(n),E,s,p)}function le(E,n,s,p){return F(O(n),E,s,p)}function N(E,n,s,p){return F(M(n,E.length-s),E,s,p)}i.prototype.write=function(n,s,p,L){if(s===void 0)L="utf8",p=this.length,s=0;else if(p===void 0&&typeof s=="string")L=s,p=this.length,s=0;else if(isFinite(s))s=s>>>0,isFinite(p)?(p=p>>>0,L===void 0&&(L="utf8")):(L=p,p=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var q=this.length-s;if((p===void 0||p>q)&&(p=q),n.length>0&&(p<0||s<0)||s>this.length)throw new RangeError("Attempt to write outside buffer bounds");L||(L="utf8");for(var H=!1;;)switch(L){case"hex":return W(this,n,s,p);case"utf8":case"utf-8":return C(this,n,s,p);case"ascii":case"latin1":case"binary":return Z(this,n,s,p);case"base64":return le(this,n,s,p);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return N(this,n,s,p);default:if(H)throw new TypeError("Unknown encoding: "+L);L=(""+L).toLowerCase(),H=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function U(E,n,s){return n===0&&s===E.length?e.fromByteArray(E):e.fromByteArray(E.slice(n,s))}function g(E,n,s){s=Math.min(E.length,s);for(var p=[],L=n;L<s;){var q=E[L],H=null,ie=q>239?4:q>223?3:q>191?2:1;if(L+ie<=s){var oe,ae,he,de;switch(ie){case 1:q<128&&(H=q);break;case 2:oe=E[L+1],(oe&192)===128&&(de=(q&31)<<6|oe&63,de>127&&(H=de));break;case 3:oe=E[L+1],ae=E[L+2],(oe&192)===128&&(ae&192)===128&&(de=(q&15)<<12|(oe&63)<<6|ae&63,de>2047&&(de<55296||de>57343)&&(H=de));break;case 4:oe=E[L+1],ae=E[L+2],he=E[L+3],(oe&192)===128&&(ae&192)===128&&(he&192)===128&&(de=(q&15)<<18|(oe&63)<<12|(ae&63)<<6|he&63,de>65535&&de<1114112&&(H=de))}}H===null?(H=65533,ie=1):H>65535&&(H-=65536,p.push(H>>>10&1023|55296),H=56320|H&1023),p.push(H),L+=ie}return ee(p)}var K=4096;function ee(E){var n=E.length;if(n<=K)return String.fromCharCode.apply(String,E);for(var s="",p=0;p<n;)s+=String.fromCharCode.apply(String,E.slice(p,p+=K));return s}function z(E,n,s){var p="";s=Math.min(E.length,s);for(var L=n;L<s;++L)p+=String.fromCharCode(E[L]&127);return p}function se(E,n,s){var p="";s=Math.min(E.length,s);for(var L=n;L<s;++L)p+=String.fromCharCode(E[L]);return p}function Q(E,n,s){var p=E.length;(!n||n<0)&&(n=0),(!s||s<0||s>p)&&(s=p);for(var L="",q=n;q<s;++q)L+=Y[E[q]];return L}function ce(E,n,s){for(var p=E.slice(n,s),L="",q=0;q<p.length-1;q+=2)L+=String.fromCharCode(p[q]+p[q+1]*256);return L}i.prototype.slice=function(n,s){var p=this.length;n=~~n,s=s===void 0?p:~~s,n<0?(n+=p,n<0&&(n=0)):n>p&&(n=p),s<0?(s+=p,s<0&&(s=0)):s>p&&(s=p),s<n&&(s=n);var L=this.subarray(n,s);return Object.setPrototypeOf(L,i.prototype),L};function V(E,n,s){if(E%1!==0||E<0)throw new RangeError("offset is not uint");if(E+n>s)throw new RangeError("Trying to access beyond buffer length")}i.prototype.readUintLE=i.prototype.readUIntLE=function(n,s,p){n=n>>>0,s=s>>>0,p||V(n,s,this.length);for(var L=this[n],q=1,H=0;++H<s&&(q*=256);)L+=this[n+H]*q;return L},i.prototype.readUintBE=i.prototype.readUIntBE=function(n,s,p){n=n>>>0,s=s>>>0,p||V(n,s,this.length);for(var L=this[n+--s],q=1;s>0&&(q*=256);)L+=this[n+--s]*q;return L},i.prototype.readUint8=i.prototype.readUInt8=function(n,s){return n=n>>>0,s||V(n,1,this.length),this[n]},i.prototype.readUint16LE=i.prototype.readUInt16LE=function(n,s){return n=n>>>0,s||V(n,2,this.length),this[n]|this[n+1]<<8},i.prototype.readUint16BE=i.prototype.readUInt16BE=function(n,s){return n=n>>>0,s||V(n,2,this.length),this[n]<<8|this[n+1]},i.prototype.readUint32LE=i.prototype.readUInt32LE=function(n,s){return n=n>>>0,s||V(n,4,this.length),(this[n]|this[n+1]<<8|this[n+2]<<16)+this[n+3]*16777216},i.prototype.readUint32BE=i.prototype.readUInt32BE=function(n,s){return n=n>>>0,s||V(n,4,this.length),this[n]*16777216+(this[n+1]<<16|this[n+2]<<8|this[n+3])},i.prototype.readIntLE=function(n,s,p){n=n>>>0,s=s>>>0,p||V(n,s,this.length);for(var L=this[n],q=1,H=0;++H<s&&(q*=256);)L+=this[n+H]*q;return q*=128,L>=q&&(L-=Math.pow(2,8*s)),L},i.prototype.readIntBE=function(n,s,p){n=n>>>0,s=s>>>0,p||V(n,s,this.length);for(var L=s,q=1,H=this[n+--L];L>0&&(q*=256);)H+=this[n+--L]*q;return q*=128,H>=q&&(H-=Math.pow(2,8*s)),H},i.prototype.readInt8=function(n,s){return n=n>>>0,s||V(n,1,this.length),this[n]&128?(255-this[n]+1)*-1:this[n]},i.prototype.readInt16LE=function(n,s){n=n>>>0,s||V(n,2,this.length);var p=this[n]|this[n+1]<<8;return p&32768?p|4294901760:p},i.prototype.readInt16BE=function(n,s){n=n>>>0,s||V(n,2,this.length);var p=this[n+1]|this[n]<<8;return p&32768?p|4294901760:p},i.prototype.readInt32LE=function(n,s){return n=n>>>0,s||V(n,4,this.length),this[n]|this[n+1]<<8|this[n+2]<<16|this[n+3]<<24},i.prototype.readInt32BE=function(n,s){return n=n>>>0,s||V(n,4,this.length),this[n]<<24|this[n+1]<<16|this[n+2]<<8|this[n+3]},i.prototype.readFloatLE=function(n,s){return n=n>>>0,s||V(n,4,this.length),t.read(this,n,!0,23,4)},i.prototype.readFloatBE=function(n,s){return n=n>>>0,s||V(n,4,this.length),t.read(this,n,!1,23,4)},i.prototype.readDoubleLE=function(n,s){return n=n>>>0,s||V(n,8,this.length),t.read(this,n,!0,52,8)},i.prototype.readDoubleBE=function(n,s){return n=n>>>0,s||V(n,8,this.length),t.read(this,n,!1,52,8)};function D(E,n,s,p,L,q){if(!i.isBuffer(E))throw new TypeError('"buffer" argument must be a Buffer instance');if(n>L||n<q)throw new RangeError('"value" argument is out of bounds');if(s+p>E.length)throw new RangeError("Index out of range")}i.prototype.writeUintLE=i.prototype.writeUIntLE=function(n,s,p,L){if(n=+n,s=s>>>0,p=p>>>0,!L){var q=Math.pow(2,8*p)-1;D(this,n,s,p,q,0)}var H=1,ie=0;for(this[s]=n&255;++ie<p&&(H*=256);)this[s+ie]=n/H&255;return s+p},i.prototype.writeUintBE=i.prototype.writeUIntBE=function(n,s,p,L){if(n=+n,s=s>>>0,p=p>>>0,!L){var q=Math.pow(2,8*p)-1;D(this,n,s,p,q,0)}var H=p-1,ie=1;for(this[s+H]=n&255;--H>=0&&(ie*=256);)this[s+H]=n/ie&255;return s+p},i.prototype.writeUint8=i.prototype.writeUInt8=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,1,255,0),this[s]=n&255,s+1},i.prototype.writeUint16LE=i.prototype.writeUInt16LE=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,2,65535,0),this[s]=n&255,this[s+1]=n>>>8,s+2},i.prototype.writeUint16BE=i.prototype.writeUInt16BE=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,2,65535,0),this[s]=n>>>8,this[s+1]=n&255,s+2},i.prototype.writeUint32LE=i.prototype.writeUInt32LE=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,4,4294967295,0),this[s+3]=n>>>24,this[s+2]=n>>>16,this[s+1]=n>>>8,this[s]=n&255,s+4},i.prototype.writeUint32BE=i.prototype.writeUInt32BE=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,4,4294967295,0),this[s]=n>>>24,this[s+1]=n>>>16,this[s+2]=n>>>8,this[s+3]=n&255,s+4},i.prototype.writeIntLE=function(n,s,p,L){if(n=+n,s=s>>>0,!L){var q=Math.pow(2,8*p-1);D(this,n,s,p,q-1,-q)}var H=0,ie=1,oe=0;for(this[s]=n&255;++H<p&&(ie*=256);)n<0&&oe===0&&this[s+H-1]!==0&&(oe=1),this[s+H]=(n/ie>>0)-oe&255;return s+p},i.prototype.writeIntBE=function(n,s,p,L){if(n=+n,s=s>>>0,!L){var q=Math.pow(2,8*p-1);D(this,n,s,p,q-1,-q)}var H=p-1,ie=1,oe=0;for(this[s+H]=n&255;--H>=0&&(ie*=256);)n<0&&oe===0&&this[s+H+1]!==0&&(oe=1),this[s+H]=(n/ie>>0)-oe&255;return s+p},i.prototype.writeInt8=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,1,127,-128),n<0&&(n=255+n+1),this[s]=n&255,s+1},i.prototype.writeInt16LE=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,2,32767,-32768),this[s]=n&255,this[s+1]=n>>>8,s+2},i.prototype.writeInt16BE=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,2,32767,-32768),this[s]=n>>>8,this[s+1]=n&255,s+2},i.prototype.writeInt32LE=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,4,**********,-2147483648),this[s]=n&255,this[s+1]=n>>>8,this[s+2]=n>>>16,this[s+3]=n>>>24,s+4},i.prototype.writeInt32BE=function(n,s,p){return n=+n,s=s>>>0,p||D(this,n,s,4,**********,-2147483648),n<0&&(n=4294967295+n+1),this[s]=n>>>24,this[s+1]=n>>>16,this[s+2]=n>>>8,this[s+3]=n&255,s+4};function $(E,n,s,p,L,q){if(s+p>E.length)throw new RangeError("Index out of range");if(s<0)throw new RangeError("Index out of range")}function X(E,n,s,p,L){return n=+n,s=s>>>0,L||$(E,n,s,4),t.write(E,n,s,p,23,4),s+4}i.prototype.writeFloatLE=function(n,s,p){return X(this,n,s,!0,p)},i.prototype.writeFloatBE=function(n,s,p){return X(this,n,s,!1,p)};function te(E,n,s,p,L){return n=+n,s=s>>>0,L||$(E,n,s,8),t.write(E,n,s,p,52,8),s+8}i.prototype.writeDoubleLE=function(n,s,p){return te(this,n,s,!0,p)},i.prototype.writeDoubleBE=function(n,s,p){return te(this,n,s,!1,p)},i.prototype.copy=function(n,s,p,L){if(!i.isBuffer(n))throw new TypeError("argument should be a Buffer");if(p||(p=0),!L&&L!==0&&(L=this.length),s>=n.length&&(s=n.length),s||(s=0),L>0&&L<p&&(L=p),L===p||n.length===0||this.length===0)return 0;if(s<0)throw new RangeError("targetStart out of bounds");if(p<0||p>=this.length)throw new RangeError("Index out of range");if(L<0)throw new RangeError("sourceEnd out of bounds");L>this.length&&(L=this.length),n.length-s<L-p&&(L=n.length-s+p);var q=L-p;return this===n&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(s,p,L):Uint8Array.prototype.set.call(n,this.subarray(p,L),s),q},i.prototype.fill=function(n,s,p,L){if(typeof n=="string"){if(typeof s=="string"?(L=s,s=0,p=this.length):typeof p=="string"&&(L=p,p=this.length),L!==void 0&&typeof L!="string")throw new TypeError("encoding must be a string");if(typeof L=="string"&&!i.isEncoding(L))throw new TypeError("Unknown encoding: "+L);if(n.length===1){var q=n.charCodeAt(0);(L==="utf8"&&q<128||L==="latin1")&&(n=q)}}else typeof n=="number"?n=n&255:typeof n=="boolean"&&(n=Number(n));if(s<0||this.length<s||this.length<p)throw new RangeError("Out of range index");if(p<=s)return this;s=s>>>0,p=p===void 0?this.length:p>>>0,n||(n=0);var H;if(typeof n=="number")for(H=s;H<p;++H)this[H]=n;else{var ie=i.isBuffer(n)?n:i.from(n,L),oe=ie.length;if(oe===0)throw new TypeError('The value "'+n+'" is invalid for argument "value"');for(H=0;H<p-s;++H)this[H+s]=ie[H%oe]}return this};var G=/[^+/0-9A-Za-z-_]/g;function _(E){if(E=E.split("=")[0],E=E.trim().replace(G,""),E.length<2)return"";for(;E.length%4!==0;)E=E+"=";return E}function w(E,n){n=n||1/0;for(var s,p=E.length,L=null,q=[],H=0;H<p;++H){if(s=E.charCodeAt(H),s>55295&&s<57344){if(!L){if(s>56319){(n-=3)>-1&&q.push(239,191,189);continue}else if(H+1===p){(n-=3)>-1&&q.push(239,191,189);continue}L=s;continue}if(s<56320){(n-=3)>-1&&q.push(239,191,189),L=s;continue}s=(L-55296<<10|s-56320)+65536}else L&&(n-=3)>-1&&q.push(239,191,189);if(L=null,s<128){if((n-=1)<0)break;q.push(s)}else if(s<2048){if((n-=2)<0)break;q.push(s>>6|192,s&63|128)}else if(s<65536){if((n-=3)<0)break;q.push(s>>12|224,s>>6&63|128,s&63|128)}else if(s<1114112){if((n-=4)<0)break;q.push(s>>18|240,s>>12&63|128,s>>6&63|128,s&63|128)}else throw new Error("Invalid code point")}return q}function j(E){for(var n=[],s=0;s<E.length;++s)n.push(E.charCodeAt(s)&255);return n}function M(E,n){for(var s,p,L,q=[],H=0;H<E.length&&!((n-=2)<0);++H)s=E.charCodeAt(H),p=s>>8,L=s%256,q.push(L),q.push(p);return q}function O(E){return e.toByteArray(_(E))}function F(E,n,s,p){for(var L=0;L<p&&!(L+s>=n.length||L>=E.length);++L)n[L+s]=E[L];return L}function J(E,n){return E instanceof n||E!=null&&E.constructor!=null&&E.constructor.name!=null&&E.constructor.name===n.name}function f(E){return E!==E}var Y=function(){for(var E="0123456789abcdef",n=new Array(256),s=0;s<16;++s)for(var p=s*16,L=0;L<16;++L)n[p+L]=E[s]+E[L];return n}()}(tr)),tr}var rr={},nr={},ir,_i;function ka(){return _i||(_i=1,ir=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var e={},t=Symbol("test"),a=Object(t);if(typeof t=="string"||Object.prototype.toString.call(t)!=="[object Symbol]"||Object.prototype.toString.call(a)!=="[object Symbol]")return!1;var o=42;e[t]=o;for(var l in e)return!1;if(typeof Object.keys=="function"&&Object.keys(e).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(e).length!==0)return!1;var d=Object.getOwnPropertySymbols(e);if(d.length!==1||d[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var i=Object.getOwnPropertyDescriptor(e,t);if(i.value!==o||i.enumerable!==!0)return!1}return!0}),ir}var sr,Ei;function Qn(){if(Ei)return sr;Ei=1;var r=ka();return sr=function(){return r()&&!!Symbol.toStringTag},sr}var ar,xi;function Ia(){return xi||(xi=1,ar=Object),ar}var or,Ai;function Ho(){return Ai||(Ai=1,or=Error),or}var ur,Ti;function jo(){return Ti||(Ti=1,ur=EvalError),ur}var lr,Si;function zo(){return Si||(Si=1,lr=RangeError),lr}var cr,Ri;function Wo(){return Ri||(Ri=1,cr=ReferenceError),cr}var hr,ki;function Ca(){return ki||(ki=1,hr=SyntaxError),hr}var fr,Ii;function Et(){return Ii||(Ii=1,fr=TypeError),fr}var dr,Ci;function qo(){return Ci||(Ci=1,dr=URIError),dr}var pr,Ni;function Go(){return Ni||(Ni=1,pr=Math.abs),pr}var mr,Oi;function Ko(){return Oi||(Oi=1,mr=Math.floor),mr}var wr,Di;function Vo(){return Di||(Di=1,wr=Math.max),wr}var gr,Fi;function $o(){return Fi||(Fi=1,gr=Math.min),gr}var yr,Pi;function Xo(){return Pi||(Pi=1,yr=Math.pow),yr}var vr,Bi;function Zo(){return Bi||(Bi=1,vr=Math.round),vr}var br,Li;function Yo(){return Li||(Li=1,br=Number.isNaN||function(e){return e!==e}),br}var _r,Mi;function Jo(){if(Mi)return _r;Mi=1;var r=Yo();return _r=function(t){return r(t)||t===0?t:t<0?-1:1},_r}var Er,Ui;function Qo(){return Ui||(Ui=1,Er=Object.getOwnPropertyDescriptor),Er}var xr,Hi;function xt(){if(Hi)return xr;Hi=1;var r=Qo();if(r)try{r([],"length")}catch{r=null}return xr=r,xr}var Ar,ji;function Gt(){if(ji)return Ar;ji=1;var r=Object.defineProperty||!1;if(r)try{r({},"a",{value:1})}catch{r=!1}return Ar=r,Ar}var Tr,zi;function eu(){if(zi)return Tr;zi=1;var r=typeof Symbol<"u"&&Symbol,e=ka();return Tr=function(){return typeof r!="function"||typeof Symbol!="function"||typeof r("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:e()},Tr}var Sr,Wi;function Na(){return Wi||(Wi=1,Sr=typeof Reflect<"u"&&Reflect.getPrototypeOf||null),Sr}var Rr,qi;function Oa(){if(qi)return Rr;qi=1;var r=Ia();return Rr=r.getPrototypeOf||null,Rr}var kr,Gi;function tu(){if(Gi)return kr;Gi=1;var r="Function.prototype.bind called on incompatible ",e=Object.prototype.toString,t=Math.max,a="[object Function]",o=function(h,x){for(var A=[],R=0;R<h.length;R+=1)A[R]=h[R];for(var k=0;k<x.length;k+=1)A[k+h.length]=x[k];return A},l=function(h,x){for(var A=[],R=x,k=0;R<h.length;R+=1,k+=1)A[k]=h[R];return A},d=function(i,h){for(var x="",A=0;A<i.length;A+=1)x+=i[A],A+1<i.length&&(x+=h);return x};return kr=function(h){var x=this;if(typeof x!="function"||e.apply(x)!==a)throw new TypeError(r+x);for(var A=l(arguments,1),R,k=function(){if(this instanceof R){var u=x.apply(this,o(A,arguments));return Object(u)===u?u:this}return x.apply(h,o(A,arguments))},y=t(0,x.length-A.length),T=[],b=0;b<y;b++)T[b]="$"+b;if(R=Function("binder","return function ("+d(T,",")+"){ return binder.apply(this,arguments); }")(k),x.prototype){var I=function(){};I.prototype=x.prototype,R.prototype=new I,I.prototype=null}return R},kr}var Ir,Ki;function At(){if(Ki)return Ir;Ki=1;var r=tu();return Ir=Function.prototype.bind||r,Ir}var Cr,Vi;function ei(){return Vi||(Vi=1,Cr=Function.prototype.call),Cr}var Nr,$i;function Da(){return $i||($i=1,Nr=Function.prototype.apply),Nr}var Or,Xi;function ru(){return Xi||(Xi=1,Or=typeof Reflect<"u"&&Reflect&&Reflect.apply),Or}var Dr,Zi;function nu(){if(Zi)return Dr;Zi=1;var r=At(),e=Da(),t=ei(),a=ru();return Dr=a||r.call(t,e),Dr}var Fr,Yi;function iu(){if(Yi)return Fr;Yi=1;var r=At(),e=Et(),t=ei(),a=nu();return Fr=function(l){if(l.length<1||typeof l[0]!="function")throw new e("a function is required");return a(r,t,l)},Fr}var Pr,Ji;function su(){if(Ji)return Pr;Ji=1;var r=iu(),e=xt(),t;try{t=[].__proto__===Array.prototype}catch(d){if(!d||typeof d!="object"||!("code"in d)||d.code!=="ERR_PROTO_ACCESS")throw d}var a=!!t&&e&&e(Object.prototype,"__proto__"),o=Object,l=o.getPrototypeOf;return Pr=a&&typeof a.get=="function"?r([a.get]):typeof l=="function"?function(i){return l(i==null?i:o(i))}:!1,Pr}var Br,Qi;function au(){if(Qi)return Br;Qi=1;var r=Na(),e=Oa(),t=su();return Br=r?function(o){return r(o)}:e?function(o){if(!o||typeof o!="object"&&typeof o!="function")throw new TypeError("getProto: not an object");return e(o)}:t?function(o){return t(o)}:null,Br}var Lr,es;function ou(){if(es)return Lr;es=1;var r=Function.prototype.call,e=Object.prototype.hasOwnProperty,t=At();return Lr=t.call(r,e),Lr}var Mr,ts;function ti(){if(ts)return Mr;ts=1;var r,e=Ia(),t=Ho(),a=jo(),o=zo(),l=Wo(),d=Ca(),i=Et(),h=qo(),x=Go(),A=Ko(),R=Vo(),k=$o(),y=Xo(),T=Zo(),b=Jo(),I=Function,u=function(j){try{return I('"use strict"; return ('+j+").constructor;")()}catch{}},v=xt(),m=Gt(),c=function(){throw new i},S=v?function(){try{return arguments.callee,c}catch{try{return v(arguments,"callee").get}catch{return c}}}():c,P=eu()(),B=au(),W=Oa(),C=Na(),Z=Da(),le=ei(),N={},U=typeof Uint8Array>"u"||!B?r:B(Uint8Array),g={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?r:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?r:ArrayBuffer,"%ArrayIteratorPrototype%":P&&B?B([][Symbol.iterator]()):r,"%AsyncFromSyncIteratorPrototype%":r,"%AsyncFunction%":N,"%AsyncGenerator%":N,"%AsyncGeneratorFunction%":N,"%AsyncIteratorPrototype%":N,"%Atomics%":typeof Atomics>"u"?r:Atomics,"%BigInt%":typeof BigInt>"u"?r:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?r:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?r:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?r:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":t,"%eval%":eval,"%EvalError%":a,"%Float16Array%":typeof Float16Array>"u"?r:Float16Array,"%Float32Array%":typeof Float32Array>"u"?r:Float32Array,"%Float64Array%":typeof Float64Array>"u"?r:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?r:FinalizationRegistry,"%Function%":I,"%GeneratorFunction%":N,"%Int8Array%":typeof Int8Array>"u"?r:Int8Array,"%Int16Array%":typeof Int16Array>"u"?r:Int16Array,"%Int32Array%":typeof Int32Array>"u"?r:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":P&&B?B(B([][Symbol.iterator]())):r,"%JSON%":typeof JSON=="object"?JSON:r,"%Map%":typeof Map>"u"?r:Map,"%MapIteratorPrototype%":typeof Map>"u"||!P||!B?r:B(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":e,"%Object.getOwnPropertyDescriptor%":v,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?r:Promise,"%Proxy%":typeof Proxy>"u"?r:Proxy,"%RangeError%":o,"%ReferenceError%":l,"%Reflect%":typeof Reflect>"u"?r:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?r:Set,"%SetIteratorPrototype%":typeof Set>"u"||!P||!B?r:B(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?r:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":P&&B?B(""[Symbol.iterator]()):r,"%Symbol%":P?Symbol:r,"%SyntaxError%":d,"%ThrowTypeError%":S,"%TypedArray%":U,"%TypeError%":i,"%Uint8Array%":typeof Uint8Array>"u"?r:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?r:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?r:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?r:Uint32Array,"%URIError%":h,"%WeakMap%":typeof WeakMap>"u"?r:WeakMap,"%WeakRef%":typeof WeakRef>"u"?r:WeakRef,"%WeakSet%":typeof WeakSet>"u"?r:WeakSet,"%Function.prototype.call%":le,"%Function.prototype.apply%":Z,"%Object.defineProperty%":m,"%Object.getPrototypeOf%":W,"%Math.abs%":x,"%Math.floor%":A,"%Math.max%":R,"%Math.min%":k,"%Math.pow%":y,"%Math.round%":T,"%Math.sign%":b,"%Reflect.getPrototypeOf%":C};if(B)try{null.error}catch(j){var K=B(B(j));g["%Error.prototype%"]=K}var ee=function j(M){var O;if(M==="%AsyncFunction%")O=u("async function () {}");else if(M==="%GeneratorFunction%")O=u("function* () {}");else if(M==="%AsyncGeneratorFunction%")O=u("async function* () {}");else if(M==="%AsyncGenerator%"){var F=j("%AsyncGeneratorFunction%");F&&(O=F.prototype)}else if(M==="%AsyncIteratorPrototype%"){var J=j("%AsyncGenerator%");J&&B&&(O=B(J.prototype))}return g[M]=O,O},z={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},se=At(),Q=ou(),ce=se.call(le,Array.prototype.concat),V=se.call(Z,Array.prototype.splice),D=se.call(le,String.prototype.replace),$=se.call(le,String.prototype.slice),X=se.call(le,RegExp.prototype.exec),te=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,G=/\\(\\)?/g,_=function(M){var O=$(M,0,1),F=$(M,-1);if(O==="%"&&F!=="%")throw new d("invalid intrinsic syntax, expected closing `%`");if(F==="%"&&O!=="%")throw new d("invalid intrinsic syntax, expected opening `%`");var J=[];return D(M,te,function(f,Y,E,n){J[J.length]=E?D(n,G,"$1"):Y||f}),J},w=function(M,O){var F=M,J;if(Q(z,F)&&(J=z[F],F="%"+J[0]+"%"),Q(g,F)){var f=g[F];if(f===N&&(f=ee(F)),typeof f>"u"&&!O)throw new i("intrinsic "+M+" exists, but is not available. Please file an issue!");return{alias:J,name:F,value:f}}throw new d("intrinsic "+M+" does not exist!")};return Mr=function(M,O){if(typeof M!="string"||M.length===0)throw new i("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof O!="boolean")throw new i('"allowMissing" argument must be a boolean');if(X(/^%?[^%]*%?$/,M)===null)throw new d("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var F=_(M),J=F.length>0?F[0]:"",f=w("%"+J+"%",O),Y=f.name,E=f.value,n=!1,s=f.alias;s&&(J=s[0],V(F,ce([0,1],s)));for(var p=1,L=!0;p<F.length;p+=1){var q=F[p],H=$(q,0,1),ie=$(q,-1);if((H==='"'||H==="'"||H==="`"||ie==='"'||ie==="'"||ie==="`")&&H!==ie)throw new d("property names with quotes must have matching quotes");if((q==="constructor"||!L)&&(n=!0),J+="."+q,Y="%"+J+"%",Q(g,Y))E=g[Y];else if(E!=null){if(!(q in E)){if(!O)throw new i("base intrinsic for "+M+" exists, but the property is not available.");return}if(v&&p+1>=F.length){var oe=v(E,q);L=!!oe,L&&"get"in oe&&!("originalValue"in oe.get)?E=oe.get:E=E[q]}else L=Q(E,q),E=E[q];L&&!n&&(g[Y]=E)}}return E},Mr}var Ur={exports:{}},Hr,rs;function uu(){if(rs)return Hr;rs=1;var r=Gt(),e=Ca(),t=Et(),a=xt();return Hr=function(l,d,i){if(!l||typeof l!="object"&&typeof l!="function")throw new t("`obj` must be an object or a function`");if(typeof d!="string"&&typeof d!="symbol")throw new t("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new t("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new t("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new t("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new t("`loose`, if provided, must be a boolean");var h=arguments.length>3?arguments[3]:null,x=arguments.length>4?arguments[4]:null,A=arguments.length>5?arguments[5]:null,R=arguments.length>6?arguments[6]:!1,k=!!a&&a(l,d);if(r)r(l,d,{configurable:A===null&&k?k.configurable:!A,enumerable:h===null&&k?k.enumerable:!h,value:i,writable:x===null&&k?k.writable:!x});else if(R||!h&&!x&&!A)l[d]=i;else throw new e("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")},Hr}var jr,ns;function lu(){if(ns)return jr;ns=1;var r=Gt(),e=function(){return!!r};return e.hasArrayLengthDefineBug=function(){if(!r)return null;try{return r([],"length",{value:1}).length!==1}catch{return!0}},jr=e,jr}var zr,is;function cu(){if(is)return zr;is=1;var r=ti(),e=uu(),t=lu()(),a=xt(),o=Et(),l=r("%Math.floor%");return zr=function(i,h){if(typeof i!="function")throw new o("`fn` is not a function");if(typeof h!="number"||h<0||h>4294967295||l(h)!==h)throw new o("`length` must be a positive 32-bit integer");var x=arguments.length>2&&!!arguments[2],A=!0,R=!0;if("length"in i&&a){var k=a(i,"length");k&&!k.configurable&&(A=!1),k&&!k.writable&&(R=!1)}return(A||R||!x)&&(t?e(i,"length",h,!0,!0):e(i,"length",h)),i},zr}var ss;function Fa(){return ss||(ss=1,function(r){var e=At(),t=ti(),a=cu(),o=Et(),l=t("%Function.prototype.apply%"),d=t("%Function.prototype.call%"),i=t("%Reflect.apply%",!0)||e.call(d,l),h=Gt(),x=t("%Math.max%");r.exports=function(k){if(typeof k!="function")throw new o("a function is required");var y=i(e,d,arguments);return a(y,1+x(0,k.length-(arguments.length-1)),!0)};var A=function(){return i(e,l,arguments)};h?h(r.exports,"apply",{value:A}):r.exports.apply=A}(Ur)),Ur.exports}var Wr,as;function Pa(){if(as)return Wr;as=1;var r=ti(),e=Fa(),t=e(r("String.prototype.indexOf"));return Wr=function(o,l){var d=r(o,!!l);return typeof d=="function"&&t(o,".prototype.")>-1?e(d):d},Wr}var qr,os;function hu(){if(os)return qr;os=1;var r=Qn()(),e=Pa(),t=e("Object.prototype.toString"),a=function(i){return r&&i&&typeof i=="object"&&Symbol.toStringTag in i?!1:t(i)==="[object Arguments]"},o=function(i){return a(i)?!0:i!==null&&typeof i=="object"&&typeof i.length=="number"&&i.length>=0&&t(i)!=="[object Array]"&&t(i.callee)==="[object Function]"},l=function(){return a(arguments)}();return a.isLegacyArguments=o,qr=l?a:o,qr}var Gr,us;function fu(){if(us)return Gr;us=1;var r=Object.prototype.toString,e=Function.prototype.toString,t=/^\s*(?:function)?\*/,a=Qn()(),o=Object.getPrototypeOf,l=function(){if(!a)return!1;try{return Function("return function*() {}")()}catch{}},d;return Gr=function(h){if(typeof h!="function")return!1;if(t.test(e.call(h)))return!0;if(!a){var x=r.call(h);return x==="[object GeneratorFunction]"}if(!o)return!1;if(typeof d>"u"){var A=l();d=A?o(A):!1}return o(h)===d},Gr}var Kr,ls;function du(){if(ls)return Kr;ls=1;var r=Function.prototype.toString,e=typeof Reflect=="object"&&Reflect!==null&&Reflect.apply,t,a;if(typeof e=="function"&&typeof Object.defineProperty=="function")try{t=Object.defineProperty({},"length",{get:function(){throw a}}),a={},e(function(){throw 42},null,t)}catch(v){v!==a&&(e=null)}else e=null;var o=/^\s*class\b/,l=function(m){try{var c=r.call(m);return o.test(c)}catch{return!1}},d=function(m){try{return l(m)?!1:(r.call(m),!0)}catch{return!1}},i=Object.prototype.toString,h="[object Object]",x="[object Function]",A="[object GeneratorFunction]",R="[object HTMLAllCollection]",k="[object HTML document.all class]",y="[object HTMLCollection]",T=typeof Symbol=="function"&&!!Symbol.toStringTag,b=!(0 in[,]),I=function(){return!1};if(typeof document=="object"){var u=document.all;i.call(u)===i.call(document.all)&&(I=function(m){if((b||!m)&&(typeof m>"u"||typeof m=="object"))try{var c=i.call(m);return(c===R||c===k||c===y||c===h)&&m("")==null}catch{}return!1})}return Kr=e?function(m){if(I(m))return!0;if(!m||typeof m!="function"&&typeof m!="object")return!1;try{e(m,null,t)}catch(c){if(c!==a)return!1}return!l(m)&&d(m)}:function(m){if(I(m))return!0;if(!m||typeof m!="function"&&typeof m!="object")return!1;if(T)return d(m);if(l(m))return!1;var c=i.call(m);return c!==x&&c!==A&&!/^\[object HTML/.test(c)?!1:d(m)},Kr}var Vr,cs;function pu(){if(cs)return Vr;cs=1;var r=du(),e=Object.prototype.toString,t=Object.prototype.hasOwnProperty,a=function(h,x,A){for(var R=0,k=h.length;R<k;R++)t.call(h,R)&&(A==null?x(h[R],R,h):x.call(A,h[R],R,h))},o=function(h,x,A){for(var R=0,k=h.length;R<k;R++)A==null?x(h.charAt(R),R,h):x.call(A,h.charAt(R),R,h)},l=function(h,x,A){for(var R in h)t.call(h,R)&&(A==null?x(h[R],R,h):x.call(A,h[R],R,h))},d=function(h,x,A){if(!r(x))throw new TypeError("iterator must be a function");var R;arguments.length>=3&&(R=A),e.call(h)==="[object Array]"?a(h,x,R):typeof h=="string"?o(h,x,R):l(h,x,R)};return Vr=d,Vr}var $r,hs;function mu(){return hs||(hs=1,$r=["Float32Array","Float64Array","Int8Array","Int16Array","Int32Array","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array"]),$r}var Xr,fs;function wu(){if(fs)return Xr;fs=1;var r=mu(),e=typeof globalThis>"u"?Fe:globalThis;return Xr=function(){for(var a=[],o=0;o<r.length;o++)typeof e[r[o]]=="function"&&(a[a.length]=r[o]);return a},Xr}var Zr,ds;function Ba(){if(ds)return Zr;ds=1;var r=pu(),e=wu(),t=Fa(),a=Pa(),o=xt(),l=a("Object.prototype.toString"),d=Qn()(),i=typeof globalThis>"u"?Fe:globalThis,h=e(),x=a("String.prototype.slice"),A=Object.getPrototypeOf,R=a("Array.prototype.indexOf",!0)||function(I,u){for(var v=0;v<I.length;v+=1)if(I[v]===u)return v;return-1},k={__proto__:null};d&&o&&A?r(h,function(b){var I=new i[b];if(Symbol.toStringTag in I){var u=A(I),v=o(u,Symbol.toStringTag);if(!v){var m=A(u);v=o(m,Symbol.toStringTag)}k["$"+b]=t(v.get)}}):r(h,function(b){var I=new i[b],u=I.slice||I.set;u&&(k["$"+b]=t(u))});var y=function(I){var u=!1;return r(k,function(v,m){if(!u)try{"$"+v(I)===m&&(u=x(m,1))}catch{}}),u},T=function(I){var u=!1;return r(k,function(v,m){if(!u)try{v(I),u=x(m,1)}catch{}}),u};return Zr=function(I){if(!I||typeof I!="object")return!1;if(!d){var u=x(l(I),8,-1);return R(h,u)>-1?u:u!=="Object"?!1:T(I)}return o?y(I):null},Zr}var Yr,ps;function gu(){if(ps)return Yr;ps=1;var r=Ba();return Yr=function(t){return!!r(t)},Yr}var ms;function yu(){return ms||(ms=1,function(r){var e=hu(),t=fu(),a=Ba(),o=gu();function l(p){return p.call.bind(p)}var d=typeof BigInt<"u",i=typeof Symbol<"u",h=l(Object.prototype.toString),x=l(Number.prototype.valueOf),A=l(String.prototype.valueOf),R=l(Boolean.prototype.valueOf);if(d)var k=l(BigInt.prototype.valueOf);if(i)var y=l(Symbol.prototype.valueOf);function T(p,L){if(typeof p!="object")return!1;try{return L(p),!0}catch{return!1}}r.isArgumentsObject=e,r.isGeneratorFunction=t,r.isTypedArray=o;function b(p){return typeof Promise<"u"&&p instanceof Promise||p!==null&&typeof p=="object"&&typeof p.then=="function"&&typeof p.catch=="function"}r.isPromise=b;function I(p){return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?ArrayBuffer.isView(p):o(p)||$(p)}r.isArrayBufferView=I;function u(p){return a(p)==="Uint8Array"}r.isUint8Array=u;function v(p){return a(p)==="Uint8ClampedArray"}r.isUint8ClampedArray=v;function m(p){return a(p)==="Uint16Array"}r.isUint16Array=m;function c(p){return a(p)==="Uint32Array"}r.isUint32Array=c;function S(p){return a(p)==="Int8Array"}r.isInt8Array=S;function P(p){return a(p)==="Int16Array"}r.isInt16Array=P;function B(p){return a(p)==="Int32Array"}r.isInt32Array=B;function W(p){return a(p)==="Float32Array"}r.isFloat32Array=W;function C(p){return a(p)==="Float64Array"}r.isFloat64Array=C;function Z(p){return a(p)==="BigInt64Array"}r.isBigInt64Array=Z;function le(p){return a(p)==="BigUint64Array"}r.isBigUint64Array=le;function N(p){return h(p)==="[object Map]"}N.working=typeof Map<"u"&&N(new Map);function U(p){return typeof Map>"u"?!1:N.working?N(p):p instanceof Map}r.isMap=U;function g(p){return h(p)==="[object Set]"}g.working=typeof Set<"u"&&g(new Set);function K(p){return typeof Set>"u"?!1:g.working?g(p):p instanceof Set}r.isSet=K;function ee(p){return h(p)==="[object WeakMap]"}ee.working=typeof WeakMap<"u"&&ee(new WeakMap);function z(p){return typeof WeakMap>"u"?!1:ee.working?ee(p):p instanceof WeakMap}r.isWeakMap=z;function se(p){return h(p)==="[object WeakSet]"}se.working=typeof WeakSet<"u"&&se(new WeakSet);function Q(p){return se(p)}r.isWeakSet=Q;function ce(p){return h(p)==="[object ArrayBuffer]"}ce.working=typeof ArrayBuffer<"u"&&ce(new ArrayBuffer);function V(p){return typeof ArrayBuffer>"u"?!1:ce.working?ce(p):p instanceof ArrayBuffer}r.isArrayBuffer=V;function D(p){return h(p)==="[object DataView]"}D.working=typeof ArrayBuffer<"u"&&typeof DataView<"u"&&D(new DataView(new ArrayBuffer(1),0,1));function $(p){return typeof DataView>"u"?!1:D.working?D(p):p instanceof DataView}r.isDataView=$;var X=typeof SharedArrayBuffer<"u"?SharedArrayBuffer:void 0;function te(p){return h(p)==="[object SharedArrayBuffer]"}function G(p){return typeof X>"u"?!1:(typeof te.working>"u"&&(te.working=te(new X)),te.working?te(p):p instanceof X)}r.isSharedArrayBuffer=G;function _(p){return h(p)==="[object AsyncFunction]"}r.isAsyncFunction=_;function w(p){return h(p)==="[object Map Iterator]"}r.isMapIterator=w;function j(p){return h(p)==="[object Set Iterator]"}r.isSetIterator=j;function M(p){return h(p)==="[object Generator]"}r.isGeneratorObject=M;function O(p){return h(p)==="[object WebAssembly.Module]"}r.isWebAssemblyCompiledModule=O;function F(p){return T(p,x)}r.isNumberObject=F;function J(p){return T(p,A)}r.isStringObject=J;function f(p){return T(p,R)}r.isBooleanObject=f;function Y(p){return d&&T(p,k)}r.isBigIntObject=Y;function E(p){return i&&T(p,y)}r.isSymbolObject=E;function n(p){return F(p)||J(p)||f(p)||Y(p)||E(p)}r.isBoxedPrimitive=n;function s(p){return typeof Uint8Array<"u"&&(V(p)||G(p))}r.isAnyArrayBuffer=s,["isProxy","isExternal","isModuleNamespaceObject"].forEach(function(p){Object.defineProperty(r,p,{enumerable:!1,value:function(){throw new Error(p+" is not supported in userland")}})})}(nr)),nr}var Jr,ws;function vu(){return ws||(ws=1,Jr=function(e){return e&&typeof e=="object"&&typeof e.copy=="function"&&typeof e.fill=="function"&&typeof e.readUInt8=="function"}),Jr}var gs;function La(){return gs||(gs=1,function(r){var e=Object.getOwnPropertyDescriptors||function($){for(var X=Object.keys($),te={},G=0;G<X.length;G++)te[X[G]]=Object.getOwnPropertyDescriptor($,X[G]);return te},t=/%[sdj%]/g;r.format=function(D){if(!S(D)){for(var $=[],X=0;X<arguments.length;X++)$.push(d(arguments[X]));return $.join(" ")}for(var X=1,te=arguments,G=te.length,_=String(D).replace(t,function(j){if(j==="%%")return"%";if(X>=G)return j;switch(j){case"%s":return String(te[X++]);case"%d":return Number(te[X++]);case"%j":try{return JSON.stringify(te[X++])}catch{return"[Circular]"}default:return j}}),w=te[X];X<G;w=te[++X])v(w)||!C(w)?_+=" "+w:_+=" "+d(w);return _},r.deprecate=function(D,$){if(typeof we<"u"&&we.noDeprecation===!0)return D;if(typeof we>"u")return function(){return r.deprecate(D,$).apply(this,arguments)};var X=!1;function te(){if(!X){if(we.throwDeprecation)throw new Error($);we.traceDeprecation?console.trace($):console.error($),X=!0}return D.apply(this,arguments)}return te};var a={},o=/^$/;if(we.env.NODE_DEBUG){var l=we.env.NODE_DEBUG;l=l.replace(/[|\\{}()[\]^$+?.]/g,"\\$&").replace(/\*/g,".*").replace(/,/g,"$|^").toUpperCase(),o=new RegExp("^"+l+"$","i")}r.debuglog=function(D){if(D=D.toUpperCase(),!a[D])if(o.test(D)){var $=we.pid;a[D]=function(){var X=r.format.apply(r,arguments);console.error("%s %d: %s",D,$,X)}}else a[D]=function(){};return a[D]};function d(D,$){var X={seen:[],stylize:h};return arguments.length>=3&&(X.depth=arguments[2]),arguments.length>=4&&(X.colors=arguments[3]),u($)?X.showHidden=$:$&&r._extend(X,$),B(X.showHidden)&&(X.showHidden=!1),B(X.depth)&&(X.depth=2),B(X.colors)&&(X.colors=!1),B(X.customInspect)&&(X.customInspect=!0),X.colors&&(X.stylize=i),A(X,D,X.depth)}r.inspect=d,d.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},d.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};function i(D,$){var X=d.styles[$];return X?"\x1B["+d.colors[X][0]+"m"+D+"\x1B["+d.colors[X][1]+"m":D}function h(D,$){return D}function x(D){var $={};return D.forEach(function(X,te){$[X]=!0}),$}function A(D,$,X){if(D.customInspect&&$&&N($.inspect)&&$.inspect!==r.inspect&&!($.constructor&&$.constructor.prototype===$)){var te=$.inspect(X,D);return S(te)||(te=A(D,te,X)),te}var G=R(D,$);if(G)return G;var _=Object.keys($),w=x(_);if(D.showHidden&&(_=Object.getOwnPropertyNames($)),le($)&&(_.indexOf("message")>=0||_.indexOf("description")>=0))return k($);if(_.length===0){if(N($)){var j=$.name?": "+$.name:"";return D.stylize("[Function"+j+"]","special")}if(W($))return D.stylize(RegExp.prototype.toString.call($),"regexp");if(Z($))return D.stylize(Date.prototype.toString.call($),"date");if(le($))return k($)}var M="",O=!1,F=["{","}"];if(I($)&&(O=!0,F=["[","]"]),N($)){var J=$.name?": "+$.name:"";M=" [Function"+J+"]"}if(W($)&&(M=" "+RegExp.prototype.toString.call($)),Z($)&&(M=" "+Date.prototype.toUTCString.call($)),le($)&&(M=" "+k($)),_.length===0&&(!O||$.length==0))return F[0]+M+F[1];if(X<0)return W($)?D.stylize(RegExp.prototype.toString.call($),"regexp"):D.stylize("[Object]","special");D.seen.push($);var f;return O?f=y(D,$,X,w,_):f=_.map(function(Y){return T(D,$,X,w,Y,O)}),D.seen.pop(),b(f,M,F)}function R(D,$){if(B($))return D.stylize("undefined","undefined");if(S($)){var X="'"+JSON.stringify($).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return D.stylize(X,"string")}if(c($))return D.stylize(""+$,"number");if(u($))return D.stylize(""+$,"boolean");if(v($))return D.stylize("null","null")}function k(D){return"["+Error.prototype.toString.call(D)+"]"}function y(D,$,X,te,G){for(var _=[],w=0,j=$.length;w<j;++w)se($,String(w))?_.push(T(D,$,X,te,String(w),!0)):_.push("");return G.forEach(function(M){M.match(/^\d+$/)||_.push(T(D,$,X,te,M,!0))}),_}function T(D,$,X,te,G,_){var w,j,M;if(M=Object.getOwnPropertyDescriptor($,G)||{value:$[G]},M.get?M.set?j=D.stylize("[Getter/Setter]","special"):j=D.stylize("[Getter]","special"):M.set&&(j=D.stylize("[Setter]","special")),se(te,G)||(w="["+G+"]"),j||(D.seen.indexOf(M.value)<0?(v(X)?j=A(D,M.value,null):j=A(D,M.value,X-1),j.indexOf(`
`)>-1&&(_?j=j.split(`
`).map(function(O){return"  "+O}).join(`
`).slice(2):j=`
`+j.split(`
`).map(function(O){return"   "+O}).join(`
`))):j=D.stylize("[Circular]","special")),B(w)){if(_&&G.match(/^\d+$/))return j;w=JSON.stringify(""+G),w.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(w=w.slice(1,-1),w=D.stylize(w,"name")):(w=w.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),w=D.stylize(w,"string"))}return w+": "+j}function b(D,$,X){var te=D.reduce(function(G,_){return _.indexOf(`
`)>=0,G+_.replace(/\u001b\[\d\d?m/g,"").length+1},0);return te>60?X[0]+($===""?"":$+`
 `)+" "+D.join(`,
  `)+" "+X[1]:X[0]+$+" "+D.join(", ")+" "+X[1]}r.types=yu();function I(D){return Array.isArray(D)}r.isArray=I;function u(D){return typeof D=="boolean"}r.isBoolean=u;function v(D){return D===null}r.isNull=v;function m(D){return D==null}r.isNullOrUndefined=m;function c(D){return typeof D=="number"}r.isNumber=c;function S(D){return typeof D=="string"}r.isString=S;function P(D){return typeof D=="symbol"}r.isSymbol=P;function B(D){return D===void 0}r.isUndefined=B;function W(D){return C(D)&&g(D)==="[object RegExp]"}r.isRegExp=W,r.types.isRegExp=W;function C(D){return typeof D=="object"&&D!==null}r.isObject=C;function Z(D){return C(D)&&g(D)==="[object Date]"}r.isDate=Z,r.types.isDate=Z;function le(D){return C(D)&&(g(D)==="[object Error]"||D instanceof Error)}r.isError=le,r.types.isNativeError=le;function N(D){return typeof D=="function"}r.isFunction=N;function U(D){return D===null||typeof D=="boolean"||typeof D=="number"||typeof D=="string"||typeof D=="symbol"||typeof D>"u"}r.isPrimitive=U,r.isBuffer=vu();function g(D){return Object.prototype.toString.call(D)}function K(D){return D<10?"0"+D.toString(10):D.toString(10)}var ee=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function z(){var D=new Date,$=[K(D.getHours()),K(D.getMinutes()),K(D.getSeconds())].join(":");return[D.getDate(),ee[D.getMonth()],$].join(" ")}r.log=function(){console.log("%s - %s",z(),r.format.apply(r,arguments))},r.inherits=Ke(),r._extend=function(D,$){if(!$||!C($))return D;for(var X=Object.keys($),te=X.length;te--;)D[X[te]]=$[X[te]];return D};function se(D,$){return Object.prototype.hasOwnProperty.call(D,$)}var Q=typeof Symbol<"u"?Symbol("util.promisify.custom"):void 0;r.promisify=function($){if(typeof $!="function")throw new TypeError('The "original" argument must be of type Function');if(Q&&$[Q]){var X=$[Q];if(typeof X!="function")throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(X,Q,{value:X,enumerable:!1,writable:!1,configurable:!0}),X}function X(){for(var te,G,_=new Promise(function(M,O){te=M,G=O}),w=[],j=0;j<arguments.length;j++)w.push(arguments[j]);w.push(function(M,O){M?G(M):te(O)});try{$.apply(this,w)}catch(M){G(M)}return _}return Object.setPrototypeOf(X,Object.getPrototypeOf($)),Q&&Object.defineProperty(X,Q,{value:X,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(X,e($))},r.promisify.custom=Q;function ce(D,$){if(!D){var X=new Error("Promise was rejected with a falsy value");X.reason=D,D=X}return $(D)}function V(D){if(typeof D!="function")throw new TypeError('The "original" argument must be of type Function');function $(){for(var X=[],te=0;te<arguments.length;te++)X.push(arguments[te]);var G=X.pop();if(typeof G!="function")throw new TypeError("The last argument must be of type Function");var _=this,w=function(){return G.apply(_,arguments)};D.apply(this,X).then(function(j){we.nextTick(w.bind(null,null,j))},function(j){we.nextTick(ce.bind(null,j,w))})}return Object.setPrototypeOf($,Object.getPrototypeOf(D)),Object.defineProperties($,e(D)),$}r.callbackify=V}(rr)),rr}var Qr,ys;function bu(){if(ys)return Qr;ys=1;function r(k,y){var T=Object.keys(k);if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(k);y&&(b=b.filter(function(I){return Object.getOwnPropertyDescriptor(k,I).enumerable})),T.push.apply(T,b)}return T}function e(k){for(var y=1;y<arguments.length;y++){var T=arguments[y]!=null?arguments[y]:{};y%2?r(Object(T),!0).forEach(function(b){t(k,b,T[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(k,Object.getOwnPropertyDescriptors(T)):r(Object(T)).forEach(function(b){Object.defineProperty(k,b,Object.getOwnPropertyDescriptor(T,b))})}return k}function t(k,y,T){return y in k?Object.defineProperty(k,y,{value:T,enumerable:!0,configurable:!0,writable:!0}):k[y]=T,k}function a(k,y){if(!(k instanceof y))throw new TypeError("Cannot call a class as a function")}function o(k,y){for(var T=0;T<y.length;T++){var b=y[T];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(k,b.key,b)}}function l(k,y,T){return y&&o(k.prototype,y),k}var d=qt(),i=d.Buffer,h=La(),x=h.inspect,A=x&&x.custom||"inspect";function R(k,y,T){i.prototype.copy.call(k,y,T)}return Qr=function(){function k(){a(this,k),this.head=null,this.tail=null,this.length=0}return l(k,[{key:"push",value:function(T){var b={data:T,next:null};this.length>0?this.tail.next=b:this.head=b,this.tail=b,++this.length}},{key:"unshift",value:function(T){var b={data:T,next:this.head};this.length===0&&(this.tail=b),this.head=b,++this.length}},{key:"shift",value:function(){if(this.length!==0){var T=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,T}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(T){if(this.length===0)return"";for(var b=this.head,I=""+b.data;b=b.next;)I+=T+b.data;return I}},{key:"concat",value:function(T){if(this.length===0)return i.alloc(0);for(var b=i.allocUnsafe(T>>>0),I=this.head,u=0;I;)R(I.data,b,u),u+=I.data.length,I=I.next;return b}},{key:"consume",value:function(T,b){var I;return T<this.head.data.length?(I=this.head.data.slice(0,T),this.head.data=this.head.data.slice(T)):T===this.head.data.length?I=this.shift():I=b?this._getString(T):this._getBuffer(T),I}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(T){var b=this.head,I=1,u=b.data;for(T-=u.length;b=b.next;){var v=b.data,m=T>v.length?v.length:T;if(m===v.length?u+=v:u+=v.slice(0,T),T-=m,T===0){m===v.length?(++I,b.next?this.head=b.next:this.head=this.tail=null):(this.head=b,b.data=v.slice(m));break}++I}return this.length-=I,u}},{key:"_getBuffer",value:function(T){var b=i.allocUnsafe(T),I=this.head,u=1;for(I.data.copy(b),T-=I.data.length;I=I.next;){var v=I.data,m=T>v.length?v.length:T;if(v.copy(b,b.length-T,0,m),T-=m,T===0){m===v.length?(++u,I.next?this.head=I.next:this.head=this.tail=null):(this.head=I,I.data=v.slice(m));break}++u}return this.length-=u,b}},{key:A,value:function(T,b){return x(this,e({},b,{depth:0,customInspect:!1}))}}]),k}(),Qr}var en,vs;function Ma(){if(vs)return en;vs=1;function r(d,i){var h=this,x=this._readableState&&this._readableState.destroyed,A=this._writableState&&this._writableState.destroyed;return x||A?(i?i(d):d&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,we.nextTick(o,this,d)):we.nextTick(o,this,d)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(d||null,function(R){!i&&R?h._writableState?h._writableState.errorEmitted?we.nextTick(t,h):(h._writableState.errorEmitted=!0,we.nextTick(e,h,R)):we.nextTick(e,h,R):i?(we.nextTick(t,h),i(R)):we.nextTick(t,h)}),this)}function e(d,i){o(d,i),t(d)}function t(d){d._writableState&&!d._writableState.emitClose||d._readableState&&!d._readableState.emitClose||d.emit("close")}function a(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function o(d,i){d.emit("error",i)}function l(d,i){var h=d._readableState,x=d._writableState;h&&h.autoDestroy||x&&x.autoDestroy?d.destroy(i):d.emit("error",i)}return en={destroy:r,undestroy:a,errorOrDestroy:l},en}var tn={},bs;function ft(){if(bs)return tn;bs=1;function r(i,h){i.prototype=Object.create(h.prototype),i.prototype.constructor=i,i.__proto__=h}var e={};function t(i,h,x){x||(x=Error);function A(k,y,T){return typeof h=="string"?h:h(k,y,T)}var R=function(k){r(y,k);function y(T,b,I){return k.call(this,A(T,b,I))||this}return y}(x);R.prototype.name=x.name,R.prototype.code=i,e[i]=R}function a(i,h){if(Array.isArray(i)){var x=i.length;return i=i.map(function(A){return String(A)}),x>2?"one of ".concat(h," ").concat(i.slice(0,x-1).join(", "),", or ")+i[x-1]:x===2?"one of ".concat(h," ").concat(i[0]," or ").concat(i[1]):"of ".concat(h," ").concat(i[0])}else return"of ".concat(h," ").concat(String(i))}function o(i,h,x){return i.substr(0,h.length)===h}function l(i,h,x){return(x===void 0||x>i.length)&&(x=i.length),i.substring(x-h.length,x)===h}function d(i,h,x){return typeof x!="number"&&(x=0),x+h.length>i.length?!1:i.indexOf(h,x)!==-1}return t("ERR_INVALID_OPT_VALUE",function(i,h){return'The value "'+h+'" is invalid for option "'+i+'"'},TypeError),t("ERR_INVALID_ARG_TYPE",function(i,h,x){var A;typeof h=="string"&&o(h,"not ")?(A="must not be",h=h.replace(/^not /,"")):A="must be";var R;if(l(i," argument"))R="The ".concat(i," ").concat(A," ").concat(a(h,"type"));else{var k=d(i,".")?"property":"argument";R='The "'.concat(i,'" ').concat(k," ").concat(A," ").concat(a(h,"type"))}return R+=". Received type ".concat(typeof x),R},TypeError),t("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),t("ERR_METHOD_NOT_IMPLEMENTED",function(i){return"The "+i+" method is not implemented"}),t("ERR_STREAM_PREMATURE_CLOSE","Premature close"),t("ERR_STREAM_DESTROYED",function(i){return"Cannot call "+i+" after a stream was destroyed"}),t("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),t("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),t("ERR_STREAM_WRITE_AFTER_END","write after end"),t("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),t("ERR_UNKNOWN_ENCODING",function(i){return"Unknown encoding: "+i},TypeError),t("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),tn.codes=e,tn}var rn,_s;function Ua(){if(_s)return rn;_s=1;var r=ft().codes.ERR_INVALID_OPT_VALUE;function e(a,o,l){return a.highWaterMark!=null?a.highWaterMark:o?a[l]:null}function t(a,o,l,d){var i=e(o,d,l);if(i!=null){if(!(isFinite(i)&&Math.floor(i)===i)||i<0){var h=d?l:"highWaterMark";throw new r(h,i)}return Math.floor(i)}return a.objectMode?16:16*1024}return rn={getHighWaterMark:t},rn}var nn,Es;function _u(){if(Es)return nn;Es=1,nn=r;function r(t,a){if(e("noDeprecation"))return t;var o=!1;function l(){if(!o){if(e("throwDeprecation"))throw new Error(a);e("traceDeprecation")?console.trace(a):console.warn(a),o=!0}return t.apply(this,arguments)}return l}function e(t){try{if(!Fe.localStorage)return!1}catch{return!1}var a=Fe.localStorage[t];return a==null?!1:String(a).toLowerCase()==="true"}return nn}var sn,xs;function Ha(){if(xs)return sn;xs=1,sn=W;function r(G){var _=this;this.next=null,this.entry=null,this.finish=function(){te(_,G)}}var e;W.WritableState=P;var t={deprecate:_u()},a=Ra(),o=qt().Buffer,l=Fe.Uint8Array||function(){};function d(G){return o.from(G)}function i(G){return o.isBuffer(G)||G instanceof l}var h=Ma(),x=Ua(),A=x.getHighWaterMark,R=ft().codes,k=R.ERR_INVALID_ARG_TYPE,y=R.ERR_METHOD_NOT_IMPLEMENTED,T=R.ERR_MULTIPLE_CALLBACK,b=R.ERR_STREAM_CANNOT_PIPE,I=R.ERR_STREAM_DESTROYED,u=R.ERR_STREAM_NULL_VALUES,v=R.ERR_STREAM_WRITE_AFTER_END,m=R.ERR_UNKNOWN_ENCODING,c=h.errorOrDestroy;Ke()(W,a);function S(){}function P(G,_,w){e=e||ot(),G=G||{},typeof w!="boolean"&&(w=_ instanceof e),this.objectMode=!!G.objectMode,w&&(this.objectMode=this.objectMode||!!G.writableObjectMode),this.highWaterMark=A(this,G,"writableHighWaterMark",w),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var j=G.decodeStrings===!1;this.decodeStrings=!j,this.defaultEncoding=G.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(M){ee(_,M)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=G.emitClose!==!1,this.autoDestroy=!!G.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new r(this)}P.prototype.getBuffer=function(){for(var _=this.bufferedRequest,w=[];_;)w.push(_),_=_.next;return w},function(){try{Object.defineProperty(P.prototype,"buffer",{get:t.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch{}}();var B;typeof Symbol=="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]=="function"?(B=Function.prototype[Symbol.hasInstance],Object.defineProperty(W,Symbol.hasInstance,{value:function(_){return B.call(this,_)?!0:this!==W?!1:_&&_._writableState instanceof P}})):B=function(_){return _ instanceof this};function W(G){e=e||ot();var _=this instanceof e;if(!_&&!B.call(W,this))return new W(G);this._writableState=new P(G,this,_),this.writable=!0,G&&(typeof G.write=="function"&&(this._write=G.write),typeof G.writev=="function"&&(this._writev=G.writev),typeof G.destroy=="function"&&(this._destroy=G.destroy),typeof G.final=="function"&&(this._final=G.final)),a.call(this)}W.prototype.pipe=function(){c(this,new b)};function C(G,_){var w=new v;c(G,w),we.nextTick(_,w)}function Z(G,_,w,j){var M;return w===null?M=new u:typeof w!="string"&&!_.objectMode&&(M=new k("chunk",["string","Buffer"],w)),M?(c(G,M),we.nextTick(j,M),!1):!0}W.prototype.write=function(G,_,w){var j=this._writableState,M=!1,O=!j.objectMode&&i(G);return O&&!o.isBuffer(G)&&(G=d(G)),typeof _=="function"&&(w=_,_=null),O?_="buffer":_||(_=j.defaultEncoding),typeof w!="function"&&(w=S),j.ending?C(this,w):(O||Z(this,j,G,w))&&(j.pendingcb++,M=N(this,j,O,G,_,w)),M},W.prototype.cork=function(){this._writableState.corked++},W.prototype.uncork=function(){var G=this._writableState;G.corked&&(G.corked--,!G.writing&&!G.corked&&!G.bufferProcessing&&G.bufferedRequest&&Q(this,G))},W.prototype.setDefaultEncoding=function(_){if(typeof _=="string"&&(_=_.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((_+"").toLowerCase())>-1))throw new m(_);return this._writableState.defaultEncoding=_,this},Object.defineProperty(W.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}});function le(G,_,w){return!G.objectMode&&G.decodeStrings!==!1&&typeof _=="string"&&(_=o.from(_,w)),_}Object.defineProperty(W.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function N(G,_,w,j,M,O){if(!w){var F=le(_,j,M);j!==F&&(w=!0,M="buffer",j=F)}var J=_.objectMode?1:j.length;_.length+=J;var f=_.length<_.highWaterMark;if(f||(_.needDrain=!0),_.writing||_.corked){var Y=_.lastBufferedRequest;_.lastBufferedRequest={chunk:j,encoding:M,isBuf:w,callback:O,next:null},Y?Y.next=_.lastBufferedRequest:_.bufferedRequest=_.lastBufferedRequest,_.bufferedRequestCount+=1}else U(G,_,!1,J,j,M,O);return f}function U(G,_,w,j,M,O,F){_.writelen=j,_.writecb=F,_.writing=!0,_.sync=!0,_.destroyed?_.onwrite(new I("write")):w?G._writev(M,_.onwrite):G._write(M,O,_.onwrite),_.sync=!1}function g(G,_,w,j,M){--_.pendingcb,w?(we.nextTick(M,j),we.nextTick($,G,_),G._writableState.errorEmitted=!0,c(G,j)):(M(j),G._writableState.errorEmitted=!0,c(G,j),$(G,_))}function K(G){G.writing=!1,G.writecb=null,G.length-=G.writelen,G.writelen=0}function ee(G,_){var w=G._writableState,j=w.sync,M=w.writecb;if(typeof M!="function")throw new T;if(K(w),_)g(G,w,j,_,M);else{var O=ce(w)||G.destroyed;!O&&!w.corked&&!w.bufferProcessing&&w.bufferedRequest&&Q(G,w),j?we.nextTick(z,G,w,O,M):z(G,w,O,M)}}function z(G,_,w,j){w||se(G,_),_.pendingcb--,j(),$(G,_)}function se(G,_){_.length===0&&_.needDrain&&(_.needDrain=!1,G.emit("drain"))}function Q(G,_){_.bufferProcessing=!0;var w=_.bufferedRequest;if(G._writev&&w&&w.next){var j=_.bufferedRequestCount,M=new Array(j),O=_.corkedRequestsFree;O.entry=w;for(var F=0,J=!0;w;)M[F]=w,w.isBuf||(J=!1),w=w.next,F+=1;M.allBuffers=J,U(G,_,!0,_.length,M,"",O.finish),_.pendingcb++,_.lastBufferedRequest=null,O.next?(_.corkedRequestsFree=O.next,O.next=null):_.corkedRequestsFree=new r(_),_.bufferedRequestCount=0}else{for(;w;){var f=w.chunk,Y=w.encoding,E=w.callback,n=_.objectMode?1:f.length;if(U(G,_,!1,n,f,Y,E),w=w.next,_.bufferedRequestCount--,_.writing)break}w===null&&(_.lastBufferedRequest=null)}_.bufferedRequest=w,_.bufferProcessing=!1}W.prototype._write=function(G,_,w){w(new y("_write()"))},W.prototype._writev=null,W.prototype.end=function(G,_,w){var j=this._writableState;return typeof G=="function"?(w=G,G=null,_=null):typeof _=="function"&&(w=_,_=null),G!=null&&this.write(G,_),j.corked&&(j.corked=1,this.uncork()),j.ending||X(this,j,w),this},Object.defineProperty(W.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function ce(G){return G.ending&&G.length===0&&G.bufferedRequest===null&&!G.finished&&!G.writing}function V(G,_){G._final(function(w){_.pendingcb--,w&&c(G,w),_.prefinished=!0,G.emit("prefinish"),$(G,_)})}function D(G,_){!_.prefinished&&!_.finalCalled&&(typeof G._final=="function"&&!_.destroyed?(_.pendingcb++,_.finalCalled=!0,we.nextTick(V,G,_)):(_.prefinished=!0,G.emit("prefinish")))}function $(G,_){var w=ce(_);if(w&&(D(G,_),_.pendingcb===0&&(_.finished=!0,G.emit("finish"),_.autoDestroy))){var j=G._readableState;(!j||j.autoDestroy&&j.endEmitted)&&G.destroy()}return w}function X(G,_,w){_.ending=!0,$(G,_),w&&(_.finished?we.nextTick(w):G.once("finish",w)),_.ended=!0,G.writable=!1}function te(G,_,w){var j=G.entry;for(G.entry=null;j;){var M=j.callback;_.pendingcb--,M(w),j=j.next}_.corkedRequestsFree.next=G}return Object.defineProperty(W.prototype,"destroyed",{enumerable:!1,get:function(){return this._writableState===void 0?!1:this._writableState.destroyed},set:function(_){this._writableState&&(this._writableState.destroyed=_)}}),W.prototype.destroy=h.destroy,W.prototype._undestroy=h.undestroy,W.prototype._destroy=function(G,_){_(G)},sn}var an,As;function ot(){if(As)return an;As=1;var r=Object.keys||function(x){var A=[];for(var R in x)A.push(R);return A};an=d;var e=ja(),t=Ha();Ke()(d,e);for(var a=r(t.prototype),o=0;o<a.length;o++){var l=a[o];d.prototype[l]||(d.prototype[l]=t.prototype[l])}function d(x){if(!(this instanceof d))return new d(x);e.call(this,x),t.call(this,x),this.allowHalfOpen=!0,x&&(x.readable===!1&&(this.readable=!1),x.writable===!1&&(this.writable=!1),x.allowHalfOpen===!1&&(this.allowHalfOpen=!1,this.once("end",i)))}Object.defineProperty(d.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(d.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(d.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}});function i(){this._writableState.ended||we.nextTick(h,this)}function h(x){x.end()}return Object.defineProperty(d.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set:function(A){this._readableState===void 0||this._writableState===void 0||(this._readableState.destroyed=A,this._writableState.destroyed=A)}}),an}var on={},Dt={exports:{}},Ts;function Eu(){return Ts||(Ts=1,function(r,e){var t=qt(),a=t.Buffer;function o(d,i){for(var h in d)i[h]=d[h]}a.from&&a.alloc&&a.allocUnsafe&&a.allocUnsafeSlow?r.exports=t:(o(t,e),e.Buffer=l);function l(d,i,h){return a(d,i,h)}o(a,l),l.from=function(d,i,h){if(typeof d=="number")throw new TypeError("Argument must not be a number");return a(d,i,h)},l.alloc=function(d,i,h){if(typeof d!="number")throw new TypeError("Argument must be a number");var x=a(d);return i!==void 0?typeof h=="string"?x.fill(i,h):x.fill(i):x.fill(0),x},l.allocUnsafe=function(d){if(typeof d!="number")throw new TypeError("Argument must be a number");return a(d)},l.allocUnsafeSlow=function(d){if(typeof d!="number")throw new TypeError("Argument must be a number");return t.SlowBuffer(d)}}(Dt,Dt.exports)),Dt.exports}var Ss;function Kn(){if(Ss)return on;Ss=1;var r=Eu().Buffer,e=r.isEncoding||function(u){switch(u=""+u,u&&u.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function t(u){if(!u)return"utf8";for(var v;;)switch(u){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return u;default:if(v)return;u=(""+u).toLowerCase(),v=!0}}function a(u){var v=t(u);if(typeof v!="string"&&(r.isEncoding===e||!e(u)))throw new Error("Unknown encoding: "+u);return v||u}on.StringDecoder=o;function o(u){this.encoding=a(u);var v;switch(this.encoding){case"utf16le":this.text=R,this.end=k,v=4;break;case"utf8":this.fillLast=h,v=4;break;case"base64":this.text=y,this.end=T,v=3;break;default:this.write=b,this.end=I;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=r.allocUnsafe(v)}o.prototype.write=function(u){if(u.length===0)return"";var v,m;if(this.lastNeed){if(v=this.fillLast(u),v===void 0)return"";m=this.lastNeed,this.lastNeed=0}else m=0;return m<u.length?v?v+this.text(u,m):this.text(u,m):v||""},o.prototype.end=A,o.prototype.text=x,o.prototype.fillLast=function(u){if(this.lastNeed<=u.length)return u.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);u.copy(this.lastChar,this.lastTotal-this.lastNeed,0,u.length),this.lastNeed-=u.length};function l(u){return u<=127?0:u>>5===6?2:u>>4===14?3:u>>3===30?4:u>>6===2?-1:-2}function d(u,v,m){var c=v.length-1;if(c<m)return 0;var S=l(v[c]);return S>=0?(S>0&&(u.lastNeed=S-1),S):--c<m||S===-2?0:(S=l(v[c]),S>=0?(S>0&&(u.lastNeed=S-2),S):--c<m||S===-2?0:(S=l(v[c]),S>=0?(S>0&&(S===2?S=0:u.lastNeed=S-3),S):0))}function i(u,v,m){if((v[0]&192)!==128)return u.lastNeed=0,"�";if(u.lastNeed>1&&v.length>1){if((v[1]&192)!==128)return u.lastNeed=1,"�";if(u.lastNeed>2&&v.length>2&&(v[2]&192)!==128)return u.lastNeed=2,"�"}}function h(u){var v=this.lastTotal-this.lastNeed,m=i(this,u);if(m!==void 0)return m;if(this.lastNeed<=u.length)return u.copy(this.lastChar,v,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);u.copy(this.lastChar,v,0,u.length),this.lastNeed-=u.length}function x(u,v){var m=d(this,u,v);if(!this.lastNeed)return u.toString("utf8",v);this.lastTotal=m;var c=u.length-(m-this.lastNeed);return u.copy(this.lastChar,0,c),u.toString("utf8",v,c)}function A(u){var v=u&&u.length?this.write(u):"";return this.lastNeed?v+"�":v}function R(u,v){if((u.length-v)%2===0){var m=u.toString("utf16le",v);if(m){var c=m.charCodeAt(m.length-1);if(c>=55296&&c<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=u[u.length-2],this.lastChar[1]=u[u.length-1],m.slice(0,-1)}return m}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=u[u.length-1],u.toString("utf16le",v,u.length-1)}function k(u){var v=u&&u.length?this.write(u):"";if(this.lastNeed){var m=this.lastTotal-this.lastNeed;return v+this.lastChar.toString("utf16le",0,m)}return v}function y(u,v){var m=(u.length-v)%3;return m===0?u.toString("base64",v):(this.lastNeed=3-m,this.lastTotal=3,m===1?this.lastChar[0]=u[u.length-1]:(this.lastChar[0]=u[u.length-2],this.lastChar[1]=u[u.length-1]),u.toString("base64",v,u.length-m))}function T(u){var v=u&&u.length?this.write(u):"";return this.lastNeed?v+this.lastChar.toString("base64",0,3-this.lastNeed):v}function b(u){return u.toString(this.encoding)}function I(u){return u&&u.length?this.write(u):""}return on}var un,Rs;function ri(){if(Rs)return un;Rs=1;var r=ft().codes.ERR_STREAM_PREMATURE_CLOSE;function e(l){var d=!1;return function(){if(!d){d=!0;for(var i=arguments.length,h=new Array(i),x=0;x<i;x++)h[x]=arguments[x];l.apply(this,h)}}}function t(){}function a(l){return l.setHeader&&typeof l.abort=="function"}function o(l,d,i){if(typeof d=="function")return o(l,null,d);d||(d={}),i=e(i||t);var h=d.readable||d.readable!==!1&&l.readable,x=d.writable||d.writable!==!1&&l.writable,A=function(){l.writable||k()},R=l._writableState&&l._writableState.finished,k=function(){x=!1,R=!0,h||i.call(l)},y=l._readableState&&l._readableState.endEmitted,T=function(){h=!1,y=!0,x||i.call(l)},b=function(m){i.call(l,m)},I=function(){var m;if(h&&!y)return(!l._readableState||!l._readableState.ended)&&(m=new r),i.call(l,m);if(x&&!R)return(!l._writableState||!l._writableState.ended)&&(m=new r),i.call(l,m)},u=function(){l.req.on("finish",k)};return a(l)?(l.on("complete",k),l.on("abort",I),l.req?u():l.on("request",u)):x&&!l._writableState&&(l.on("end",A),l.on("close",A)),l.on("end",T),l.on("finish",k),d.error!==!1&&l.on("error",b),l.on("close",I),function(){l.removeListener("complete",k),l.removeListener("abort",I),l.removeListener("request",u),l.req&&l.req.removeListener("finish",k),l.removeListener("end",A),l.removeListener("close",A),l.removeListener("finish",k),l.removeListener("end",T),l.removeListener("error",b),l.removeListener("close",I)}}return un=o,un}var ln,ks;function xu(){if(ks)return ln;ks=1;var r;function e(u,v,m){return v in u?Object.defineProperty(u,v,{value:m,enumerable:!0,configurable:!0,writable:!0}):u[v]=m,u}var t=ri(),a=Symbol("lastResolve"),o=Symbol("lastReject"),l=Symbol("error"),d=Symbol("ended"),i=Symbol("lastPromise"),h=Symbol("handlePromise"),x=Symbol("stream");function A(u,v){return{value:u,done:v}}function R(u){var v=u[a];if(v!==null){var m=u[x].read();m!==null&&(u[i]=null,u[a]=null,u[o]=null,v(A(m,!1)))}}function k(u){we.nextTick(R,u)}function y(u,v){return function(m,c){u.then(function(){if(v[d]){m(A(void 0,!0));return}v[h](m,c)},c)}}var T=Object.getPrototypeOf(function(){}),b=Object.setPrototypeOf((r={get stream(){return this[x]},next:function(){var v=this,m=this[l];if(m!==null)return Promise.reject(m);if(this[d])return Promise.resolve(A(void 0,!0));if(this[x].destroyed)return new Promise(function(B,W){we.nextTick(function(){v[l]?W(v[l]):B(A(void 0,!0))})});var c=this[i],S;if(c)S=new Promise(y(c,this));else{var P=this[x].read();if(P!==null)return Promise.resolve(A(P,!1));S=new Promise(this[h])}return this[i]=S,S}},e(r,Symbol.asyncIterator,function(){return this}),e(r,"return",function(){var v=this;return new Promise(function(m,c){v[x].destroy(null,function(S){if(S){c(S);return}m(A(void 0,!0))})})}),r),T),I=function(v){var m,c=Object.create(b,(m={},e(m,x,{value:v,writable:!0}),e(m,a,{value:null,writable:!0}),e(m,o,{value:null,writable:!0}),e(m,l,{value:null,writable:!0}),e(m,d,{value:v._readableState.endEmitted,writable:!0}),e(m,h,{value:function(P,B){var W=c[x].read();W?(c[i]=null,c[a]=null,c[o]=null,P(A(W,!1))):(c[a]=P,c[o]=B)},writable:!0}),m));return c[i]=null,t(v,function(S){if(S&&S.code!=="ERR_STREAM_PREMATURE_CLOSE"){var P=c[o];P!==null&&(c[i]=null,c[a]=null,c[o]=null,P(S)),c[l]=S;return}var B=c[a];B!==null&&(c[i]=null,c[a]=null,c[o]=null,B(A(void 0,!0))),c[d]=!0}),v.on("readable",k.bind(null,c)),c};return ln=I,ln}var cn,Is;function Au(){return Is||(Is=1,cn=function(){throw new Error("Readable.from is not available in the browser")}),cn}var hn,Cs;function ja(){if(Cs)return hn;Cs=1,hn=C;var r;C.ReadableState=W,Jn().EventEmitter;var e=function(F,J){return F.listeners(J).length},t=Ra(),a=qt().Buffer,o=Fe.Uint8Array||function(){};function l(O){return a.from(O)}function d(O){return a.isBuffer(O)||O instanceof o}var i=La(),h;i&&i.debuglog?h=i.debuglog("stream"):h=function(){};var x=bu(),A=Ma(),R=Ua(),k=R.getHighWaterMark,y=ft().codes,T=y.ERR_INVALID_ARG_TYPE,b=y.ERR_STREAM_PUSH_AFTER_EOF,I=y.ERR_METHOD_NOT_IMPLEMENTED,u=y.ERR_STREAM_UNSHIFT_AFTER_END_EVENT,v,m,c;Ke()(C,t);var S=A.errorOrDestroy,P=["error","close","destroy","pause","resume"];function B(O,F,J){if(typeof O.prependListener=="function")return O.prependListener(F,J);!O._events||!O._events[F]?O.on(F,J):Array.isArray(O._events[F])?O._events[F].unshift(J):O._events[F]=[J,O._events[F]]}function W(O,F,J){r=r||ot(),O=O||{},typeof J!="boolean"&&(J=F instanceof r),this.objectMode=!!O.objectMode,J&&(this.objectMode=this.objectMode||!!O.readableObjectMode),this.highWaterMark=k(this,O,"readableHighWaterMark",J),this.buffer=new x,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=O.emitClose!==!1,this.autoDestroy=!!O.autoDestroy,this.destroyed=!1,this.defaultEncoding=O.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,O.encoding&&(v||(v=Kn().StringDecoder),this.decoder=new v(O.encoding),this.encoding=O.encoding)}function C(O){if(r=r||ot(),!(this instanceof C))return new C(O);var F=this instanceof r;this._readableState=new W(O,this,F),this.readable=!0,O&&(typeof O.read=="function"&&(this._read=O.read),typeof O.destroy=="function"&&(this._destroy=O.destroy)),t.call(this)}Object.defineProperty(C.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState===void 0?!1:this._readableState.destroyed},set:function(F){this._readableState&&(this._readableState.destroyed=F)}}),C.prototype.destroy=A.destroy,C.prototype._undestroy=A.undestroy,C.prototype._destroy=function(O,F){F(O)},C.prototype.push=function(O,F){var J=this._readableState,f;return J.objectMode?f=!0:typeof O=="string"&&(F=F||J.defaultEncoding,F!==J.encoding&&(O=a.from(O,F),F=""),f=!0),Z(this,O,F,!1,f)},C.prototype.unshift=function(O){return Z(this,O,null,!0,!1)};function Z(O,F,J,f,Y){h("readableAddChunk",F);var E=O._readableState;if(F===null)E.reading=!1,ee(O,E);else{var n;if(Y||(n=N(E,F)),n)S(O,n);else if(E.objectMode||F&&F.length>0)if(typeof F!="string"&&!E.objectMode&&Object.getPrototypeOf(F)!==a.prototype&&(F=l(F)),f)E.endEmitted?S(O,new u):le(O,E,F,!0);else if(E.ended)S(O,new b);else{if(E.destroyed)return!1;E.reading=!1,E.decoder&&!J?(F=E.decoder.write(F),E.objectMode||F.length!==0?le(O,E,F,!1):Q(O,E)):le(O,E,F,!1)}else f||(E.reading=!1,Q(O,E))}return!E.ended&&(E.length<E.highWaterMark||E.length===0)}function le(O,F,J,f){F.flowing&&F.length===0&&!F.sync?(F.awaitDrain=0,O.emit("data",J)):(F.length+=F.objectMode?1:J.length,f?F.buffer.unshift(J):F.buffer.push(J),F.needReadable&&z(O)),Q(O,F)}function N(O,F){var J;return!d(F)&&typeof F!="string"&&F!==void 0&&!O.objectMode&&(J=new T("chunk",["string","Buffer","Uint8Array"],F)),J}C.prototype.isPaused=function(){return this._readableState.flowing===!1},C.prototype.setEncoding=function(O){v||(v=Kn().StringDecoder);var F=new v(O);this._readableState.decoder=F,this._readableState.encoding=this._readableState.decoder.encoding;for(var J=this._readableState.buffer.head,f="";J!==null;)f+=F.write(J.data),J=J.next;return this._readableState.buffer.clear(),f!==""&&this._readableState.buffer.push(f),this._readableState.length=f.length,this};var U=1073741824;function g(O){return O>=U?O=U:(O--,O|=O>>>1,O|=O>>>2,O|=O>>>4,O|=O>>>8,O|=O>>>16,O++),O}function K(O,F){return O<=0||F.length===0&&F.ended?0:F.objectMode?1:O!==O?F.flowing&&F.length?F.buffer.head.data.length:F.length:(O>F.highWaterMark&&(F.highWaterMark=g(O)),O<=F.length?O:F.ended?F.length:(F.needReadable=!0,0))}C.prototype.read=function(O){h("read",O),O=parseInt(O,10);var F=this._readableState,J=O;if(O!==0&&(F.emittedReadable=!1),O===0&&F.needReadable&&((F.highWaterMark!==0?F.length>=F.highWaterMark:F.length>0)||F.ended))return h("read: emitReadable",F.length,F.ended),F.length===0&&F.ended?w(this):z(this),null;if(O=K(O,F),O===0&&F.ended)return F.length===0&&w(this),null;var f=F.needReadable;h("need readable",f),(F.length===0||F.length-O<F.highWaterMark)&&(f=!0,h("length less than watermark",f)),F.ended||F.reading?(f=!1,h("reading or ended",f)):f&&(h("do read"),F.reading=!0,F.sync=!0,F.length===0&&(F.needReadable=!0),this._read(F.highWaterMark),F.sync=!1,F.reading||(O=K(J,F)));var Y;return O>0?Y=_(O,F):Y=null,Y===null?(F.needReadable=F.length<=F.highWaterMark,O=0):(F.length-=O,F.awaitDrain=0),F.length===0&&(F.ended||(F.needReadable=!0),J!==O&&F.ended&&w(this)),Y!==null&&this.emit("data",Y),Y};function ee(O,F){if(h("onEofChunk"),!F.ended){if(F.decoder){var J=F.decoder.end();J&&J.length&&(F.buffer.push(J),F.length+=F.objectMode?1:J.length)}F.ended=!0,F.sync?z(O):(F.needReadable=!1,F.emittedReadable||(F.emittedReadable=!0,se(O)))}}function z(O){var F=O._readableState;h("emitReadable",F.needReadable,F.emittedReadable),F.needReadable=!1,F.emittedReadable||(h("emitReadable",F.flowing),F.emittedReadable=!0,we.nextTick(se,O))}function se(O){var F=O._readableState;h("emitReadable_",F.destroyed,F.length,F.ended),!F.destroyed&&(F.length||F.ended)&&(O.emit("readable"),F.emittedReadable=!1),F.needReadable=!F.flowing&&!F.ended&&F.length<=F.highWaterMark,G(O)}function Q(O,F){F.readingMore||(F.readingMore=!0,we.nextTick(ce,O,F))}function ce(O,F){for(;!F.reading&&!F.ended&&(F.length<F.highWaterMark||F.flowing&&F.length===0);){var J=F.length;if(h("maybeReadMore read 0"),O.read(0),J===F.length)break}F.readingMore=!1}C.prototype._read=function(O){S(this,new I("_read()"))},C.prototype.pipe=function(O,F){var J=this,f=this._readableState;switch(f.pipesCount){case 0:f.pipes=O;break;case 1:f.pipes=[f.pipes,O];break;default:f.pipes.push(O);break}f.pipesCount+=1,h("pipe count=%d opts=%j",f.pipesCount,F);var Y=(!F||F.end!==!1)&&O!==we.stdout&&O!==we.stderr,E=Y?s:he;f.endEmitted?we.nextTick(E):J.once("end",E),O.on("unpipe",n);function n(de,pe){h("onunpipe"),de===J&&pe&&pe.hasUnpiped===!1&&(pe.hasUnpiped=!0,q())}function s(){h("onend"),O.end()}var p=V(J);O.on("drain",p);var L=!1;function q(){h("cleanup"),O.removeListener("close",oe),O.removeListener("finish",ae),O.removeListener("drain",p),O.removeListener("error",ie),O.removeListener("unpipe",n),J.removeListener("end",s),J.removeListener("end",he),J.removeListener("data",H),L=!0,f.awaitDrain&&(!O._writableState||O._writableState.needDrain)&&p()}J.on("data",H);function H(de){h("ondata");var pe=O.write(de);h("dest.write",pe),pe===!1&&((f.pipesCount===1&&f.pipes===O||f.pipesCount>1&&M(f.pipes,O)!==-1)&&!L&&(h("false write response, pause",f.awaitDrain),f.awaitDrain++),J.pause())}function ie(de){h("onerror",de),he(),O.removeListener("error",ie),e(O,"error")===0&&S(O,de)}B(O,"error",ie);function oe(){O.removeListener("finish",ae),he()}O.once("close",oe);function ae(){h("onfinish"),O.removeListener("close",oe),he()}O.once("finish",ae);function he(){h("unpipe"),J.unpipe(O)}return O.emit("pipe",J),f.flowing||(h("pipe resume"),J.resume()),O};function V(O){return function(){var J=O._readableState;h("pipeOnDrain",J.awaitDrain),J.awaitDrain&&J.awaitDrain--,J.awaitDrain===0&&e(O,"data")&&(J.flowing=!0,G(O))}}C.prototype.unpipe=function(O){var F=this._readableState,J={hasUnpiped:!1};if(F.pipesCount===0)return this;if(F.pipesCount===1)return O&&O!==F.pipes?this:(O||(O=F.pipes),F.pipes=null,F.pipesCount=0,F.flowing=!1,O&&O.emit("unpipe",this,J),this);if(!O){var f=F.pipes,Y=F.pipesCount;F.pipes=null,F.pipesCount=0,F.flowing=!1;for(var E=0;E<Y;E++)f[E].emit("unpipe",this,{hasUnpiped:!1});return this}var n=M(F.pipes,O);return n===-1?this:(F.pipes.splice(n,1),F.pipesCount-=1,F.pipesCount===1&&(F.pipes=F.pipes[0]),O.emit("unpipe",this,J),this)},C.prototype.on=function(O,F){var J=t.prototype.on.call(this,O,F),f=this._readableState;return O==="data"?(f.readableListening=this.listenerCount("readable")>0,f.flowing!==!1&&this.resume()):O==="readable"&&!f.endEmitted&&!f.readableListening&&(f.readableListening=f.needReadable=!0,f.flowing=!1,f.emittedReadable=!1,h("on readable",f.length,f.reading),f.length?z(this):f.reading||we.nextTick($,this)),J},C.prototype.addListener=C.prototype.on,C.prototype.removeListener=function(O,F){var J=t.prototype.removeListener.call(this,O,F);return O==="readable"&&we.nextTick(D,this),J},C.prototype.removeAllListeners=function(O){var F=t.prototype.removeAllListeners.apply(this,arguments);return(O==="readable"||O===void 0)&&we.nextTick(D,this),F};function D(O){var F=O._readableState;F.readableListening=O.listenerCount("readable")>0,F.resumeScheduled&&!F.paused?F.flowing=!0:O.listenerCount("data")>0&&O.resume()}function $(O){h("readable nexttick read 0"),O.read(0)}C.prototype.resume=function(){var O=this._readableState;return O.flowing||(h("resume"),O.flowing=!O.readableListening,X(this,O)),O.paused=!1,this};function X(O,F){F.resumeScheduled||(F.resumeScheduled=!0,we.nextTick(te,O,F))}function te(O,F){h("resume",F.reading),F.reading||O.read(0),F.resumeScheduled=!1,O.emit("resume"),G(O),F.flowing&&!F.reading&&O.read(0)}C.prototype.pause=function(){return h("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(h("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this};function G(O){var F=O._readableState;for(h("flow",F.flowing);F.flowing&&O.read()!==null;);}C.prototype.wrap=function(O){var F=this,J=this._readableState,f=!1;O.on("end",function(){if(h("wrapped end"),J.decoder&&!J.ended){var n=J.decoder.end();n&&n.length&&F.push(n)}F.push(null)}),O.on("data",function(n){if(h("wrapped data"),J.decoder&&(n=J.decoder.write(n)),!(J.objectMode&&n==null)&&!(!J.objectMode&&(!n||!n.length))){var s=F.push(n);s||(f=!0,O.pause())}});for(var Y in O)this[Y]===void 0&&typeof O[Y]=="function"&&(this[Y]=function(s){return function(){return O[s].apply(O,arguments)}}(Y));for(var E=0;E<P.length;E++)O.on(P[E],this.emit.bind(this,P[E]));return this._read=function(n){h("wrapped _read",n),f&&(f=!1,O.resume())},this},typeof Symbol=="function"&&(C.prototype[Symbol.asyncIterator]=function(){return m===void 0&&(m=xu()),m(this)}),Object.defineProperty(C.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(C.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(C.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(F){this._readableState&&(this._readableState.flowing=F)}}),C._fromList=_,Object.defineProperty(C.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}});function _(O,F){if(F.length===0)return null;var J;return F.objectMode?J=F.buffer.shift():!O||O>=F.length?(F.decoder?J=F.buffer.join(""):F.buffer.length===1?J=F.buffer.first():J=F.buffer.concat(F.length),F.buffer.clear()):J=F.buffer.consume(O,F.decoder),J}function w(O){var F=O._readableState;h("endReadable",F.endEmitted),F.endEmitted||(F.ended=!0,we.nextTick(j,F,O))}function j(O,F){if(h("endReadableNT",O.endEmitted,O.length),!O.endEmitted&&O.length===0&&(O.endEmitted=!0,F.readable=!1,F.emit("end"),O.autoDestroy)){var J=F._writableState;(!J||J.autoDestroy&&J.finished)&&F.destroy()}}typeof Symbol=="function"&&(C.from=function(O,F){return c===void 0&&(c=Au()),c(C,O,F)});function M(O,F){for(var J=0,f=O.length;J<f;J++)if(O[J]===F)return J;return-1}return hn}var fn,Ns;function za(){if(Ns)return fn;Ns=1,fn=i;var r=ft().codes,e=r.ERR_METHOD_NOT_IMPLEMENTED,t=r.ERR_MULTIPLE_CALLBACK,a=r.ERR_TRANSFORM_ALREADY_TRANSFORMING,o=r.ERR_TRANSFORM_WITH_LENGTH_0,l=ot();Ke()(i,l);function d(A,R){var k=this._transformState;k.transforming=!1;var y=k.writecb;if(y===null)return this.emit("error",new t);k.writechunk=null,k.writecb=null,R!=null&&this.push(R),y(A);var T=this._readableState;T.reading=!1,(T.needReadable||T.length<T.highWaterMark)&&this._read(T.highWaterMark)}function i(A){if(!(this instanceof i))return new i(A);l.call(this,A),this._transformState={afterTransform:d.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,A&&(typeof A.transform=="function"&&(this._transform=A.transform),typeof A.flush=="function"&&(this._flush=A.flush)),this.on("prefinish",h)}function h(){var A=this;typeof this._flush=="function"&&!this._readableState.destroyed?this._flush(function(R,k){x(A,R,k)}):x(this,null,null)}i.prototype.push=function(A,R){return this._transformState.needTransform=!1,l.prototype.push.call(this,A,R)},i.prototype._transform=function(A,R,k){k(new e("_transform()"))},i.prototype._write=function(A,R,k){var y=this._transformState;if(y.writecb=k,y.writechunk=A,y.writeencoding=R,!y.transforming){var T=this._readableState;(y.needTransform||T.needReadable||T.length<T.highWaterMark)&&this._read(T.highWaterMark)}},i.prototype._read=function(A){var R=this._transformState;R.writechunk!==null&&!R.transforming?(R.transforming=!0,this._transform(R.writechunk,R.writeencoding,R.afterTransform)):R.needTransform=!0},i.prototype._destroy=function(A,R){l.prototype._destroy.call(this,A,function(k){R(k)})};function x(A,R,k){if(R)return A.emit("error",R);if(k!=null&&A.push(k),A._writableState.length)throw new o;if(A._transformState.transforming)throw new a;return A.push(null)}return fn}var dn,Os;function Tu(){if(Os)return dn;Os=1,dn=e;var r=za();Ke()(e,r);function e(t){if(!(this instanceof e))return new e(t);r.call(this,t)}return e.prototype._transform=function(t,a,o){o(null,t)},dn}var pn,Ds;function Su(){if(Ds)return pn;Ds=1;var r;function e(k){var y=!1;return function(){y||(y=!0,k.apply(void 0,arguments))}}var t=ft().codes,a=t.ERR_MISSING_ARGS,o=t.ERR_STREAM_DESTROYED;function l(k){if(k)throw k}function d(k){return k.setHeader&&typeof k.abort=="function"}function i(k,y,T,b){b=e(b);var I=!1;k.on("close",function(){I=!0}),r===void 0&&(r=ri()),r(k,{readable:y,writable:T},function(v){if(v)return b(v);I=!0,b()});var u=!1;return function(v){if(!I&&!u){if(u=!0,d(k))return k.abort();if(typeof k.destroy=="function")return k.destroy();b(v||new o("pipe"))}}}function h(k){k()}function x(k,y){return k.pipe(y)}function A(k){return!k.length||typeof k[k.length-1]!="function"?l:k.pop()}function R(){for(var k=arguments.length,y=new Array(k),T=0;T<k;T++)y[T]=arguments[T];var b=A(y);if(Array.isArray(y[0])&&(y=y[0]),y.length<2)throw new a("streams");var I,u=y.map(function(v,m){var c=m<y.length-1,S=m>0;return i(v,c,S,function(P){I||(I=P),P&&u.forEach(h),!c&&(u.forEach(h),b(I))})});return y.reduce(x)}return pn=R,pn}var mn,Fs;function ni(){if(Fs)return mn;Fs=1,mn=t;var r=Jn().EventEmitter,e=Ke();e(t,r),t.Readable=ja(),t.Writable=Ha(),t.Duplex=ot(),t.Transform=za(),t.PassThrough=Tu(),t.finished=ri(),t.pipeline=Su(),t.Stream=t;function t(){r.call(this)}return t.prototype.pipe=function(a,o){var l=this;function d(y){a.writable&&a.write(y)===!1&&l.pause&&l.pause()}l.on("data",d);function i(){l.readable&&l.resume&&l.resume()}a.on("drain",i),!a._isStdio&&(!o||o.end!==!1)&&(l.on("end",x),l.on("close",A));var h=!1;function x(){h||(h=!0,a.end())}function A(){h||(h=!0,typeof a.destroy=="function"&&a.destroy())}function R(y){if(k(),r.listenerCount(this,"error")===0)throw y}l.on("error",R),a.on("error",R);function k(){l.removeListener("data",d),a.removeListener("drain",i),l.removeListener("end",x),l.removeListener("close",A),l.removeListener("error",R),a.removeListener("error",R),l.removeListener("end",k),l.removeListener("close",k),a.removeListener("close",k)}return l.on("end",k),l.on("close",k),a.on("close",k),a.emit("pipe",l),a},mn}var Ps;function Ru(){return Ps||(Ps=1,function(r){(function(e){e.parser=function(_,w){return new a(_,w)},e.SAXParser=a,e.SAXStream=A,e.createStream=x,e.MAX_BUFFER_LENGTH=64*1024;var t=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];e.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"];function a(_,w){if(!(this instanceof a))return new a(_,w);var j=this;l(j),j.q=j.c="",j.bufferCheckPosition=e.MAX_BUFFER_LENGTH,j.opt=w||{},j.opt.lowercase=j.opt.lowercase||j.opt.lowercasetags,j.looseCase=j.opt.lowercase?"toLowerCase":"toUpperCase",j.tags=[],j.closed=j.closedRoot=j.sawRoot=!1,j.tag=j.error=null,j.strict=!!_,j.noscript=!!(_||j.opt.noscript),j.state=C.BEGIN,j.strictEntities=j.opt.strictEntities,j.ENTITIES=j.strictEntities?Object.create(e.XML_ENTITIES):Object.create(e.ENTITIES),j.attribList=[],j.opt.xmlns&&(j.ns=Object.create(b)),j.trackPosition=j.opt.position!==!1,j.trackPosition&&(j.position=j.line=j.column=0),le(j,"onready")}Object.create||(Object.create=function(_){function w(){}w.prototype=_;var j=new w;return j}),Object.keys||(Object.keys=function(_){var w=[];for(var j in _)_.hasOwnProperty(j)&&w.push(j);return w});function o(_){for(var w=Math.max(e.MAX_BUFFER_LENGTH,10),j=0,M=0,O=t.length;M<O;M++){var F=_[t[M]].length;if(F>w)switch(t[M]){case"textNode":U(_);break;case"cdata":N(_,"oncdata",_.cdata),_.cdata="";break;case"script":N(_,"onscript",_.script),_.script="";break;default:K(_,"Max buffer length exceeded: "+t[M])}j=Math.max(j,F)}var J=e.MAX_BUFFER_LENGTH-j;_.bufferCheckPosition=J+_.position}function l(_){for(var w=0,j=t.length;w<j;w++)_[t[w]]=""}function d(_){U(_),_.cdata!==""&&(N(_,"oncdata",_.cdata),_.cdata=""),_.script!==""&&(N(_,"onscript",_.script),_.script="")}a.prototype={end:function(){ee(this)},write:G,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){d(this)}};var i;try{i=ni().Stream}catch{i=function(){}}var h=e.EVENTS.filter(function(_){return _!=="error"&&_!=="end"});function x(_,w){return new A(_,w)}function A(_,w){if(!(this instanceof A))return new A(_,w);i.apply(this),this._parser=new a(_,w),this.writable=!0,this.readable=!0;var j=this;this._parser.onend=function(){j.emit("end")},this._parser.onerror=function(M){j.emit("error",M),j._parser.error=null},this._decoder=null,h.forEach(function(M){Object.defineProperty(j,"on"+M,{get:function(){return j._parser["on"+M]},set:function(O){if(!O)return j.removeAllListeners(M),j._parser["on"+M]=O,O;j.on(M,O)},enumerable:!0,configurable:!1})})}A.prototype=Object.create(i.prototype,{constructor:{value:A}}),A.prototype.write=function(_){if(typeof Buffer=="function"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(_)){if(!this._decoder){var w=Kn().StringDecoder;this._decoder=new w("utf8")}_=this._decoder.write(_)}return this._parser.write(_.toString()),this.emit("data",_),!0},A.prototype.end=function(_){return _&&_.length&&this.write(_),this._parser.end(),!0},A.prototype.on=function(_,w){var j=this;return!j._parser["on"+_]&&h.indexOf(_)!==-1&&(j._parser["on"+_]=function(){var M=arguments.length===1?[arguments[0]]:Array.apply(null,arguments);M.splice(0,0,_),j.emit.apply(j,M)}),i.prototype.on.call(j,_,w)};var R="[CDATA[",k="DOCTYPE",y="http://www.w3.org/XML/1998/namespace",T="http://www.w3.org/2000/xmlns/",b={xml:y,xmlns:T},I=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,u=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,v=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,m=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function c(_){return _===" "||_===`
`||_==="\r"||_==="	"}function S(_){return _==='"'||_==="'"}function P(_){return _===">"||c(_)}function B(_,w){return _.test(w)}function W(_,w){return!B(_,w)}var C=0;e.STATE={BEGIN:C++,BEGIN_WHITESPACE:C++,TEXT:C++,TEXT_ENTITY:C++,OPEN_WAKA:C++,SGML_DECL:C++,SGML_DECL_QUOTED:C++,DOCTYPE:C++,DOCTYPE_QUOTED:C++,DOCTYPE_DTD:C++,DOCTYPE_DTD_QUOTED:C++,COMMENT_STARTING:C++,COMMENT:C++,COMMENT_ENDING:C++,COMMENT_ENDED:C++,CDATA:C++,CDATA_ENDING:C++,CDATA_ENDING_2:C++,PROC_INST:C++,PROC_INST_BODY:C++,PROC_INST_ENDING:C++,OPEN_TAG:C++,OPEN_TAG_SLASH:C++,ATTRIB:C++,ATTRIB_NAME:C++,ATTRIB_NAME_SAW_WHITE:C++,ATTRIB_VALUE:C++,ATTRIB_VALUE_QUOTED:C++,ATTRIB_VALUE_CLOSED:C++,ATTRIB_VALUE_UNQUOTED:C++,ATTRIB_VALUE_ENTITY_Q:C++,ATTRIB_VALUE_ENTITY_U:C++,CLOSE_TAG:C++,CLOSE_TAG_SAW_WHITE:C++,SCRIPT:C++,SCRIPT_ENDING:C++},e.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},e.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(e.ENTITIES).forEach(function(_){var w=e.ENTITIES[_],j=typeof w=="number"?String.fromCharCode(w):w;e.ENTITIES[_]=j});for(var Z in e.STATE)e.STATE[e.STATE[Z]]=Z;C=e.STATE;function le(_,w,j){_[w]&&_[w](j)}function N(_,w,j){_.textNode&&U(_),le(_,w,j)}function U(_){_.textNode=g(_.opt,_.textNode),_.textNode&&le(_,"ontext",_.textNode),_.textNode=""}function g(_,w){return _.trim&&(w=w.trim()),_.normalize&&(w=w.replace(/\s+/g," ")),w}function K(_,w){return U(_),_.trackPosition&&(w+=`
Line: `+_.line+`
Column: `+_.column+`
Char: `+_.c),w=new Error(w),_.error=w,le(_,"onerror",w),_}function ee(_){return _.sawRoot&&!_.closedRoot&&z(_,"Unclosed root tag"),_.state!==C.BEGIN&&_.state!==C.BEGIN_WHITESPACE&&_.state!==C.TEXT&&K(_,"Unexpected end"),U(_),_.c="",_.closed=!0,le(_,"onend"),a.call(_,_.strict,_.opt),_}function z(_,w){if(typeof _!="object"||!(_ instanceof a))throw new Error("bad call to strictFail");_.strict&&K(_,w)}function se(_){_.strict||(_.tagName=_.tagName[_.looseCase]());var w=_.tags[_.tags.length-1]||_,j=_.tag={name:_.tagName,attributes:{}};_.opt.xmlns&&(j.ns=w.ns),_.attribList.length=0,N(_,"onopentagstart",j)}function Q(_,w){var j=_.indexOf(":"),M=j<0?["",_]:_.split(":"),O=M[0],F=M[1];return w&&_==="xmlns"&&(O="xmlns",F=""),{prefix:O,local:F}}function ce(_){if(_.strict||(_.attribName=_.attribName[_.looseCase]()),_.attribList.indexOf(_.attribName)!==-1||_.tag.attributes.hasOwnProperty(_.attribName)){_.attribName=_.attribValue="";return}if(_.opt.xmlns){var w=Q(_.attribName,!0),j=w.prefix,M=w.local;if(j==="xmlns")if(M==="xml"&&_.attribValue!==y)z(_,"xml: prefix must be bound to "+y+`
Actual: `+_.attribValue);else if(M==="xmlns"&&_.attribValue!==T)z(_,"xmlns: prefix must be bound to "+T+`
Actual: `+_.attribValue);else{var O=_.tag,F=_.tags[_.tags.length-1]||_;O.ns===F.ns&&(O.ns=Object.create(F.ns)),O.ns[M]=_.attribValue}_.attribList.push([_.attribName,_.attribValue])}else _.tag.attributes[_.attribName]=_.attribValue,N(_,"onattribute",{name:_.attribName,value:_.attribValue});_.attribName=_.attribValue=""}function V(_,w){if(_.opt.xmlns){var j=_.tag,M=Q(_.tagName);j.prefix=M.prefix,j.local=M.local,j.uri=j.ns[M.prefix]||"",j.prefix&&!j.uri&&(z(_,"Unbound namespace prefix: "+JSON.stringify(_.tagName)),j.uri=M.prefix);var O=_.tags[_.tags.length-1]||_;j.ns&&O.ns!==j.ns&&Object.keys(j.ns).forEach(function(H){N(_,"onopennamespace",{prefix:H,uri:j.ns[H]})});for(var F=0,J=_.attribList.length;F<J;F++){var f=_.attribList[F],Y=f[0],E=f[1],n=Q(Y,!0),s=n.prefix,p=n.local,L=s===""?"":j.ns[s]||"",q={name:Y,value:E,prefix:s,local:p,uri:L};s&&s!=="xmlns"&&!L&&(z(_,"Unbound namespace prefix: "+JSON.stringify(s)),q.uri=s),_.tag.attributes[Y]=q,N(_,"onattribute",q)}_.attribList.length=0}_.tag.isSelfClosing=!!w,_.sawRoot=!0,_.tags.push(_.tag),N(_,"onopentag",_.tag),w||(!_.noscript&&_.tagName.toLowerCase()==="script"?_.state=C.SCRIPT:_.state=C.TEXT,_.tag=null,_.tagName=""),_.attribName=_.attribValue="",_.attribList.length=0}function D(_){if(!_.tagName){z(_,"Weird empty close tag."),_.textNode+="</>",_.state=C.TEXT;return}if(_.script){if(_.tagName!=="script"){_.script+="</"+_.tagName+">",_.tagName="",_.state=C.SCRIPT;return}N(_,"onscript",_.script),_.script=""}var w=_.tags.length,j=_.tagName;_.strict||(j=j[_.looseCase]());for(var M=j;w--;){var O=_.tags[w];if(O.name!==M)z(_,"Unexpected close tag");else break}if(w<0){z(_,"Unmatched closing tag: "+_.tagName),_.textNode+="</"+_.tagName+">",_.state=C.TEXT;return}_.tagName=j;for(var F=_.tags.length;F-- >w;){var J=_.tag=_.tags.pop();_.tagName=_.tag.name,N(_,"onclosetag",_.tagName);var f={};for(var Y in J.ns)f[Y]=J.ns[Y];var E=_.tags[_.tags.length-1]||_;_.opt.xmlns&&J.ns!==E.ns&&Object.keys(J.ns).forEach(function(n){var s=J.ns[n];N(_,"onclosenamespace",{prefix:n,uri:s})})}w===0&&(_.closedRoot=!0),_.tagName=_.attribValue=_.attribName="",_.attribList.length=0,_.state=C.TEXT}function $(_){var w=_.entity,j=w.toLowerCase(),M,O="";return _.ENTITIES[w]?_.ENTITIES[w]:_.ENTITIES[j]?_.ENTITIES[j]:(w=j,w.charAt(0)==="#"&&(w.charAt(1)==="x"?(w=w.slice(2),M=parseInt(w,16),O=M.toString(16)):(w=w.slice(1),M=parseInt(w,10),O=M.toString(10))),w=w.replace(/^0+/,""),isNaN(M)||O.toLowerCase()!==w?(z(_,"Invalid character entity"),"&"+_.entity+";"):String.fromCodePoint(M))}function X(_,w){w==="<"?(_.state=C.OPEN_WAKA,_.startTagPosition=_.position):c(w)||(z(_,"Non-whitespace before first tag."),_.textNode=w,_.state=C.TEXT)}function te(_,w){var j="";return w<_.length&&(j=_.charAt(w)),j}function G(_){var w=this;if(this.error)throw this.error;if(w.closed)return K(w,"Cannot write after close. Assign an onready handler.");if(_===null)return ee(w);typeof _=="object"&&(_=_.toString());for(var j=0,M="";M=te(_,j++),w.c=M,!!M;)switch(w.trackPosition&&(w.position++,M===`
`?(w.line++,w.column=0):w.column++),w.state){case C.BEGIN:if(w.state=C.BEGIN_WHITESPACE,M==="\uFEFF")continue;X(w,M);continue;case C.BEGIN_WHITESPACE:X(w,M);continue;case C.TEXT:if(w.sawRoot&&!w.closedRoot){for(var O=j-1;M&&M!=="<"&&M!=="&";)M=te(_,j++),M&&w.trackPosition&&(w.position++,M===`
`?(w.line++,w.column=0):w.column++);w.textNode+=_.substring(O,j-1)}M==="<"&&!(w.sawRoot&&w.closedRoot&&!w.strict)?(w.state=C.OPEN_WAKA,w.startTagPosition=w.position):(!c(M)&&(!w.sawRoot||w.closedRoot)&&z(w,"Text data outside of root node."),M==="&"?w.state=C.TEXT_ENTITY:w.textNode+=M);continue;case C.SCRIPT:M==="<"?w.state=C.SCRIPT_ENDING:w.script+=M;continue;case C.SCRIPT_ENDING:M==="/"?w.state=C.CLOSE_TAG:(w.script+="<"+M,w.state=C.SCRIPT);continue;case C.OPEN_WAKA:if(M==="!")w.state=C.SGML_DECL,w.sgmlDecl="";else if(!c(M))if(B(I,M))w.state=C.OPEN_TAG,w.tagName=M;else if(M==="/")w.state=C.CLOSE_TAG,w.tagName="";else if(M==="?")w.state=C.PROC_INST,w.procInstName=w.procInstBody="";else{if(z(w,"Unencoded <"),w.startTagPosition+1<w.position){var F=w.position-w.startTagPosition;M=new Array(F).join(" ")+M}w.textNode+="<"+M,w.state=C.TEXT}continue;case C.SGML_DECL:(w.sgmlDecl+M).toUpperCase()===R?(N(w,"onopencdata"),w.state=C.CDATA,w.sgmlDecl="",w.cdata=""):w.sgmlDecl+M==="--"?(w.state=C.COMMENT,w.comment="",w.sgmlDecl=""):(w.sgmlDecl+M).toUpperCase()===k?(w.state=C.DOCTYPE,(w.doctype||w.sawRoot)&&z(w,"Inappropriately located doctype declaration"),w.doctype="",w.sgmlDecl=""):M===">"?(N(w,"onsgmldeclaration",w.sgmlDecl),w.sgmlDecl="",w.state=C.TEXT):(S(M)&&(w.state=C.SGML_DECL_QUOTED),w.sgmlDecl+=M);continue;case C.SGML_DECL_QUOTED:M===w.q&&(w.state=C.SGML_DECL,w.q=""),w.sgmlDecl+=M;continue;case C.DOCTYPE:M===">"?(w.state=C.TEXT,N(w,"ondoctype",w.doctype),w.doctype=!0):(w.doctype+=M,M==="["?w.state=C.DOCTYPE_DTD:S(M)&&(w.state=C.DOCTYPE_QUOTED,w.q=M));continue;case C.DOCTYPE_QUOTED:w.doctype+=M,M===w.q&&(w.q="",w.state=C.DOCTYPE);continue;case C.DOCTYPE_DTD:w.doctype+=M,M==="]"?w.state=C.DOCTYPE:S(M)&&(w.state=C.DOCTYPE_DTD_QUOTED,w.q=M);continue;case C.DOCTYPE_DTD_QUOTED:w.doctype+=M,M===w.q&&(w.state=C.DOCTYPE_DTD,w.q="");continue;case C.COMMENT:M==="-"?w.state=C.COMMENT_ENDING:w.comment+=M;continue;case C.COMMENT_ENDING:M==="-"?(w.state=C.COMMENT_ENDED,w.comment=g(w.opt,w.comment),w.comment&&N(w,"oncomment",w.comment),w.comment=""):(w.comment+="-"+M,w.state=C.COMMENT);continue;case C.COMMENT_ENDED:M!==">"?(z(w,"Malformed comment"),w.comment+="--"+M,w.state=C.COMMENT):w.state=C.TEXT;continue;case C.CDATA:M==="]"?w.state=C.CDATA_ENDING:w.cdata+=M;continue;case C.CDATA_ENDING:M==="]"?w.state=C.CDATA_ENDING_2:(w.cdata+="]"+M,w.state=C.CDATA);continue;case C.CDATA_ENDING_2:M===">"?(w.cdata&&N(w,"oncdata",w.cdata),N(w,"onclosecdata"),w.cdata="",w.state=C.TEXT):M==="]"?w.cdata+="]":(w.cdata+="]]"+M,w.state=C.CDATA);continue;case C.PROC_INST:M==="?"?w.state=C.PROC_INST_ENDING:c(M)?w.state=C.PROC_INST_BODY:w.procInstName+=M;continue;case C.PROC_INST_BODY:if(!w.procInstBody&&c(M))continue;M==="?"?w.state=C.PROC_INST_ENDING:w.procInstBody+=M;continue;case C.PROC_INST_ENDING:M===">"?(N(w,"onprocessinginstruction",{name:w.procInstName,body:w.procInstBody}),w.procInstName=w.procInstBody="",w.state=C.TEXT):(w.procInstBody+="?"+M,w.state=C.PROC_INST_BODY);continue;case C.OPEN_TAG:B(u,M)?w.tagName+=M:(se(w),M===">"?V(w):M==="/"?w.state=C.OPEN_TAG_SLASH:(c(M)||z(w,"Invalid character in tag name"),w.state=C.ATTRIB));continue;case C.OPEN_TAG_SLASH:M===">"?(V(w,!0),D(w)):(z(w,"Forward-slash in opening tag not followed by >"),w.state=C.ATTRIB);continue;case C.ATTRIB:if(c(M))continue;M===">"?V(w):M==="/"?w.state=C.OPEN_TAG_SLASH:B(I,M)?(w.attribName=M,w.attribValue="",w.state=C.ATTRIB_NAME):z(w,"Invalid attribute name");continue;case C.ATTRIB_NAME:M==="="?w.state=C.ATTRIB_VALUE:M===">"?(z(w,"Attribute without value"),w.attribValue=w.attribName,ce(w),V(w)):c(M)?w.state=C.ATTRIB_NAME_SAW_WHITE:B(u,M)?w.attribName+=M:z(w,"Invalid attribute name");continue;case C.ATTRIB_NAME_SAW_WHITE:if(M==="=")w.state=C.ATTRIB_VALUE;else{if(c(M))continue;z(w,"Attribute without value"),w.tag.attributes[w.attribName]="",w.attribValue="",N(w,"onattribute",{name:w.attribName,value:""}),w.attribName="",M===">"?V(w):B(I,M)?(w.attribName=M,w.state=C.ATTRIB_NAME):(z(w,"Invalid attribute name"),w.state=C.ATTRIB)}continue;case C.ATTRIB_VALUE:if(c(M))continue;S(M)?(w.q=M,w.state=C.ATTRIB_VALUE_QUOTED):(z(w,"Unquoted attribute value"),w.state=C.ATTRIB_VALUE_UNQUOTED,w.attribValue=M);continue;case C.ATTRIB_VALUE_QUOTED:if(M!==w.q){M==="&"?w.state=C.ATTRIB_VALUE_ENTITY_Q:w.attribValue+=M;continue}ce(w),w.q="",w.state=C.ATTRIB_VALUE_CLOSED;continue;case C.ATTRIB_VALUE_CLOSED:c(M)?w.state=C.ATTRIB:M===">"?V(w):M==="/"?w.state=C.OPEN_TAG_SLASH:B(I,M)?(z(w,"No whitespace between attributes"),w.attribName=M,w.attribValue="",w.state=C.ATTRIB_NAME):z(w,"Invalid attribute name");continue;case C.ATTRIB_VALUE_UNQUOTED:if(!P(M)){M==="&"?w.state=C.ATTRIB_VALUE_ENTITY_U:w.attribValue+=M;continue}ce(w),M===">"?V(w):w.state=C.ATTRIB;continue;case C.CLOSE_TAG:if(w.tagName)M===">"?D(w):B(u,M)?w.tagName+=M:w.script?(w.script+="</"+w.tagName,w.tagName="",w.state=C.SCRIPT):(c(M)||z(w,"Invalid tagname in closing tag"),w.state=C.CLOSE_TAG_SAW_WHITE);else{if(c(M))continue;W(I,M)?w.script?(w.script+="</"+M,w.state=C.SCRIPT):z(w,"Invalid tagname in closing tag."):w.tagName=M}continue;case C.CLOSE_TAG_SAW_WHITE:if(c(M))continue;M===">"?D(w):z(w,"Invalid characters in closing tag");continue;case C.TEXT_ENTITY:case C.ATTRIB_VALUE_ENTITY_Q:case C.ATTRIB_VALUE_ENTITY_U:var J,f;switch(w.state){case C.TEXT_ENTITY:J=C.TEXT,f="textNode";break;case C.ATTRIB_VALUE_ENTITY_Q:J=C.ATTRIB_VALUE_QUOTED,f="attribValue";break;case C.ATTRIB_VALUE_ENTITY_U:J=C.ATTRIB_VALUE_UNQUOTED,f="attribValue";break}M===";"?(w[f]+=$(w),w.entity="",w.state=J):B(w.entity.length?m:v,M)?w.entity+=M:(z(w,"Invalid character in entity name"),w[f]+="&"+w.entity+M,w.entity="",w.state=J);continue;default:throw new Error(w,"Unknown state: "+w.state)}return w.position>=w.bufferCheckPosition&&o(w),w}/*! http://mths.be/fromcodepoint v0.1.0 by @mathias */String.fromCodePoint||function(){var _=String.fromCharCode,w=Math.floor,j=function(){var M=16384,O=[],F,J,f=-1,Y=arguments.length;if(!Y)return"";for(var E="";++f<Y;){var n=Number(arguments[f]);if(!isFinite(n)||n<0||n>1114111||w(n)!==n)throw RangeError("Invalid code point: "+n);n<=65535?O.push(n):(n-=65536,F=(n>>10)+55296,J=n%1024+56320,O.push(F,J)),(f+1===Y||O.length>M)&&(E+=_.apply(null,O),O.length=0)}return E};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:j,configurable:!0,writable:!0}):String.fromCodePoint=j}()})(r)}(Qt)),Qt}var wn,Bs;function ii(){return Bs||(Bs=1,wn={isArray:function(r){return Array.isArray?Array.isArray(r):Object.prototype.toString.call(r)==="[object Array]"}}),wn}var gn,Ls;function si(){if(Ls)return gn;Ls=1;var r=ii().isArray;return gn={copyOptions:function(e){var t,a={};for(t in e)e.hasOwnProperty(t)&&(a[t]=e[t]);return a},ensureFlagExists:function(e,t){(!(e in t)||typeof t[e]!="boolean")&&(t[e]=!1)},ensureSpacesExists:function(e){(!("spaces"in e)||typeof e.spaces!="number"&&typeof e.spaces!="string")&&(e.spaces=0)},ensureAlwaysArrayExists:function(e){(!("alwaysArray"in e)||typeof e.alwaysArray!="boolean"&&!r(e.alwaysArray))&&(e.alwaysArray=!1)},ensureKeyExists:function(e,t){(!(e+"Key"in t)||typeof t[e+"Key"]!="string")&&(t[e+"Key"]=t.compact?"_"+e:e)},checkFnExists:function(e,t){return e+"Fn"in t}},gn}var yn,Ms;function Wa(){if(Ms)return yn;Ms=1;var r=Ru(),e=si(),t=ii().isArray,a,o;function l(u){return a=e.copyOptions(u),e.ensureFlagExists("ignoreDeclaration",a),e.ensureFlagExists("ignoreInstruction",a),e.ensureFlagExists("ignoreAttributes",a),e.ensureFlagExists("ignoreText",a),e.ensureFlagExists("ignoreComment",a),e.ensureFlagExists("ignoreCdata",a),e.ensureFlagExists("ignoreDoctype",a),e.ensureFlagExists("compact",a),e.ensureFlagExists("alwaysChildren",a),e.ensureFlagExists("addParent",a),e.ensureFlagExists("trim",a),e.ensureFlagExists("nativeType",a),e.ensureFlagExists("nativeTypeAttributes",a),e.ensureFlagExists("sanitize",a),e.ensureFlagExists("instructionHasAttributes",a),e.ensureFlagExists("captureSpacesBetweenElements",a),e.ensureAlwaysArrayExists(a),e.ensureKeyExists("declaration",a),e.ensureKeyExists("instruction",a),e.ensureKeyExists("attributes",a),e.ensureKeyExists("text",a),e.ensureKeyExists("comment",a),e.ensureKeyExists("cdata",a),e.ensureKeyExists("doctype",a),e.ensureKeyExists("type",a),e.ensureKeyExists("name",a),e.ensureKeyExists("elements",a),e.ensureKeyExists("parent",a),e.checkFnExists("doctype",a),e.checkFnExists("instruction",a),e.checkFnExists("cdata",a),e.checkFnExists("comment",a),e.checkFnExists("text",a),e.checkFnExists("instructionName",a),e.checkFnExists("elementName",a),e.checkFnExists("attributeName",a),e.checkFnExists("attributeValue",a),e.checkFnExists("attributes",a),a}function d(u){var v=Number(u);if(!isNaN(v))return v;var m=u.toLowerCase();return m==="true"?!0:m==="false"?!1:u}function i(u,v){var m;if(a.compact){if(!o[a[u+"Key"]]&&(t(a.alwaysArray)?a.alwaysArray.indexOf(a[u+"Key"])!==-1:a.alwaysArray)&&(o[a[u+"Key"]]=[]),o[a[u+"Key"]]&&!t(o[a[u+"Key"]])&&(o[a[u+"Key"]]=[o[a[u+"Key"]]]),u+"Fn"in a&&typeof v=="string"&&(v=a[u+"Fn"](v,o)),u==="instruction"&&("instructionFn"in a||"instructionNameFn"in a)){for(m in v)if(v.hasOwnProperty(m))if("instructionFn"in a)v[m]=a.instructionFn(v[m],m,o);else{var c=v[m];delete v[m],v[a.instructionNameFn(m,c,o)]=c}}t(o[a[u+"Key"]])?o[a[u+"Key"]].push(v):o[a[u+"Key"]]=v}else{o[a.elementsKey]||(o[a.elementsKey]=[]);var S={};if(S[a.typeKey]=u,u==="instruction"){for(m in v)if(v.hasOwnProperty(m))break;S[a.nameKey]="instructionNameFn"in a?a.instructionNameFn(m,v,o):m,a.instructionHasAttributes?(S[a.attributesKey]=v[m][a.attributesKey],"instructionFn"in a&&(S[a.attributesKey]=a.instructionFn(S[a.attributesKey],m,o))):("instructionFn"in a&&(v[m]=a.instructionFn(v[m],m,o)),S[a.instructionKey]=v[m])}else u+"Fn"in a&&(v=a[u+"Fn"](v,o)),S[a[u+"Key"]]=v;a.addParent&&(S[a.parentKey]=o),o[a.elementsKey].push(S)}}function h(u){if("attributesFn"in a&&u&&(u=a.attributesFn(u,o)),(a.trim||"attributeValueFn"in a||"attributeNameFn"in a||a.nativeTypeAttributes)&&u){var v;for(v in u)if(u.hasOwnProperty(v)&&(a.trim&&(u[v]=u[v].trim()),a.nativeTypeAttributes&&(u[v]=d(u[v])),"attributeValueFn"in a&&(u[v]=a.attributeValueFn(u[v],v,o)),"attributeNameFn"in a)){var m=u[v];delete u[v],u[a.attributeNameFn(v,u[v],o)]=m}}return u}function x(u){var v={};if(u.body&&(u.name.toLowerCase()==="xml"||a.instructionHasAttributes)){for(var m=/([\w:-]+)\s*=\s*(?:"([^"]*)"|'([^']*)'|(\w+))\s*/g,c;(c=m.exec(u.body))!==null;)v[c[1]]=c[2]||c[3]||c[4];v=h(v)}if(u.name.toLowerCase()==="xml"){if(a.ignoreDeclaration)return;o[a.declarationKey]={},Object.keys(v).length&&(o[a.declarationKey][a.attributesKey]=v),a.addParent&&(o[a.declarationKey][a.parentKey]=o)}else{if(a.ignoreInstruction)return;a.trim&&(u.body=u.body.trim());var S={};a.instructionHasAttributes&&Object.keys(v).length?(S[u.name]={},S[u.name][a.attributesKey]=v):S[u.name]=u.body,i("instruction",S)}}function A(u,v){var m;if(typeof u=="object"&&(v=u.attributes,u=u.name),v=h(v),"elementNameFn"in a&&(u=a.elementNameFn(u,o)),a.compact){if(m={},!a.ignoreAttributes&&v&&Object.keys(v).length){m[a.attributesKey]={};var c;for(c in v)v.hasOwnProperty(c)&&(m[a.attributesKey][c]=v[c])}!(u in o)&&(t(a.alwaysArray)?a.alwaysArray.indexOf(u)!==-1:a.alwaysArray)&&(o[u]=[]),o[u]&&!t(o[u])&&(o[u]=[o[u]]),t(o[u])?o[u].push(m):o[u]=m}else o[a.elementsKey]||(o[a.elementsKey]=[]),m={},m[a.typeKey]="element",m[a.nameKey]=u,!a.ignoreAttributes&&v&&Object.keys(v).length&&(m[a.attributesKey]=v),a.alwaysChildren&&(m[a.elementsKey]=[]),o[a.elementsKey].push(m);m[a.parentKey]=o,o=m}function R(u){a.ignoreText||!u.trim()&&!a.captureSpacesBetweenElements||(a.trim&&(u=u.trim()),a.nativeType&&(u=d(u)),a.sanitize&&(u=u.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")),i("text",u))}function k(u){a.ignoreComment||(a.trim&&(u=u.trim()),i("comment",u))}function y(u){var v=o[a.parentKey];a.addParent||delete o[a.parentKey],o=v}function T(u){a.ignoreCdata||(a.trim&&(u=u.trim()),i("cdata",u))}function b(u){a.ignoreDoctype||(u=u.replace(/^ /,""),a.trim&&(u=u.trim()),i("doctype",u))}function I(u){u.note=u}return yn=function(u,v){var m=r.parser(!0,{}),c={};if(o=c,a=l(v),m.opt={strictEntities:!0},m.onopentag=A,m.ontext=R,m.oncomment=k,m.onclosetag=y,m.onerror=I,m.oncdata=T,m.ondoctype=b,m.onprocessinginstruction=x,m.write(u).close(),c[a.elementsKey]){var S=c[a.elementsKey];delete c[a.elementsKey],c[a.elementsKey]=S,delete c.text}return c},yn}var vn,Us;function ku(){if(Us)return vn;Us=1;var r=si(),e=Wa();function t(a){var o=r.copyOptions(a);return r.ensureSpacesExists(o),o}return vn=function(a,o){var l,d,i,h;return l=t(o),d=e(a,l),h="compact"in l&&l.compact?"_parent":"parent","addParent"in l&&l.addParent?i=JSON.stringify(d,function(x,A){return x===h?"_":A},l.spaces):i=JSON.stringify(d,null,l.spaces),i.replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")},vn}var bn,Hs;function qa(){if(Hs)return bn;Hs=1;var r=si(),e=ii().isArray,t,a;function o(m){var c=r.copyOptions(m);return r.ensureFlagExists("ignoreDeclaration",c),r.ensureFlagExists("ignoreInstruction",c),r.ensureFlagExists("ignoreAttributes",c),r.ensureFlagExists("ignoreText",c),r.ensureFlagExists("ignoreComment",c),r.ensureFlagExists("ignoreCdata",c),r.ensureFlagExists("ignoreDoctype",c),r.ensureFlagExists("compact",c),r.ensureFlagExists("indentText",c),r.ensureFlagExists("indentCdata",c),r.ensureFlagExists("indentAttributes",c),r.ensureFlagExists("indentInstruction",c),r.ensureFlagExists("fullTagEmptyElement",c),r.ensureFlagExists("noQuotesForNativeAttributes",c),r.ensureSpacesExists(c),typeof c.spaces=="number"&&(c.spaces=Array(c.spaces+1).join(" ")),r.ensureKeyExists("declaration",c),r.ensureKeyExists("instruction",c),r.ensureKeyExists("attributes",c),r.ensureKeyExists("text",c),r.ensureKeyExists("comment",c),r.ensureKeyExists("cdata",c),r.ensureKeyExists("doctype",c),r.ensureKeyExists("type",c),r.ensureKeyExists("name",c),r.ensureKeyExists("elements",c),r.checkFnExists("doctype",c),r.checkFnExists("instruction",c),r.checkFnExists("cdata",c),r.checkFnExists("comment",c),r.checkFnExists("text",c),r.checkFnExists("instructionName",c),r.checkFnExists("elementName",c),r.checkFnExists("attributeName",c),r.checkFnExists("attributeValue",c),r.checkFnExists("attributes",c),r.checkFnExists("fullTagEmptyElement",c),c}function l(m,c,S){return(!S&&m.spaces?`
`:"")+Array(c+1).join(m.spaces)}function d(m,c,S){if(c.ignoreAttributes)return"";"attributesFn"in c&&(m=c.attributesFn(m,a,t));var P,B,W,C,Z=[];for(P in m)m.hasOwnProperty(P)&&m[P]!==null&&m[P]!==void 0&&(C=c.noQuotesForNativeAttributes&&typeof m[P]!="string"?"":'"',B=""+m[P],B=B.replace(/"/g,"&quot;"),W="attributeNameFn"in c?c.attributeNameFn(P,B,a,t):P,Z.push(c.spaces&&c.indentAttributes?l(c,S+1,!1):" "),Z.push(W+"="+C+("attributeValueFn"in c?c.attributeValueFn(B,P,a,t):B)+C));return m&&Object.keys(m).length&&c.spaces&&c.indentAttributes&&Z.push(l(c,S,!1)),Z.join("")}function i(m,c,S){return t=m,a="xml",c.ignoreDeclaration?"":"<?xml"+d(m[c.attributesKey],c,S)+"?>"}function h(m,c,S){if(c.ignoreInstruction)return"";var P;for(P in m)if(m.hasOwnProperty(P))break;var B="instructionNameFn"in c?c.instructionNameFn(P,m[P],a,t):P;if(typeof m[P]=="object")return t=m,a=B,"<?"+B+d(m[P][c.attributesKey],c,S)+"?>";var W=m[P]?m[P]:"";return"instructionFn"in c&&(W=c.instructionFn(W,P,a,t)),"<?"+B+(W?" "+W:"")+"?>"}function x(m,c){return c.ignoreComment?"":"<!--"+("commentFn"in c?c.commentFn(m,a,t):m)+"-->"}function A(m,c){return c.ignoreCdata?"":"<![CDATA["+("cdataFn"in c?c.cdataFn(m,a,t):m.replace("]]>","]]]]><![CDATA[>"))+"]]>"}function R(m,c){return c.ignoreDoctype?"":"<!DOCTYPE "+("doctypeFn"in c?c.doctypeFn(m,a,t):m)+">"}function k(m,c){return c.ignoreText?"":(m=""+m,m=m.replace(/&amp;/g,"&"),m=m.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"textFn"in c?c.textFn(m,a,t):m)}function y(m,c){var S;if(m.elements&&m.elements.length)for(S=0;S<m.elements.length;++S)switch(m.elements[S][c.typeKey]){case"text":if(c.indentText)return!0;break;case"cdata":if(c.indentCdata)return!0;break;case"instruction":if(c.indentInstruction)return!0;break;case"doctype":case"comment":case"element":return!0;default:return!0}return!1}function T(m,c,S){t=m,a=m.name;var P=[],B="elementNameFn"in c?c.elementNameFn(m.name,m):m.name;P.push("<"+B),m[c.attributesKey]&&P.push(d(m[c.attributesKey],c,S));var W=m[c.elementsKey]&&m[c.elementsKey].length||m[c.attributesKey]&&m[c.attributesKey]["xml:space"]==="preserve";return W||("fullTagEmptyElementFn"in c?W=c.fullTagEmptyElementFn(m.name,m):W=c.fullTagEmptyElement),W?(P.push(">"),m[c.elementsKey]&&m[c.elementsKey].length&&(P.push(b(m[c.elementsKey],c,S+1)),t=m,a=m.name),P.push(c.spaces&&y(m,c)?`
`+Array(S+1).join(c.spaces):""),P.push("</"+B+">")):P.push("/>"),P.join("")}function b(m,c,S,P){return m.reduce(function(B,W){var C=l(c,S,P&&!B);switch(W.type){case"element":return B+C+T(W,c,S);case"comment":return B+C+x(W[c.commentKey],c);case"doctype":return B+C+R(W[c.doctypeKey],c);case"cdata":return B+(c.indentCdata?C:"")+A(W[c.cdataKey],c);case"text":return B+(c.indentText?C:"")+k(W[c.textKey],c);case"instruction":var Z={};return Z[W[c.nameKey]]=W[c.attributesKey]?W:W[c.instructionKey],B+(c.indentInstruction?C:"")+h(Z,c,S)}},"")}function I(m,c,S){var P;for(P in m)if(m.hasOwnProperty(P))switch(P){case c.parentKey:case c.attributesKey:break;case c.textKey:if(c.indentText||S)return!0;break;case c.cdataKey:if(c.indentCdata||S)return!0;break;case c.instructionKey:if(c.indentInstruction||S)return!0;break;case c.doctypeKey:case c.commentKey:return!0;default:return!0}return!1}function u(m,c,S,P,B){t=m,a=c;var W="elementNameFn"in S?S.elementNameFn(c,m):c;if(typeof m>"u"||m===null||m==="")return"fullTagEmptyElementFn"in S&&S.fullTagEmptyElementFn(c,m)||S.fullTagEmptyElement?"<"+W+"></"+W+">":"<"+W+"/>";var C=[];if(c){if(C.push("<"+W),typeof m!="object")return C.push(">"+k(m,S)+"</"+W+">"),C.join("");m[S.attributesKey]&&C.push(d(m[S.attributesKey],S,P));var Z=I(m,S,!0)||m[S.attributesKey]&&m[S.attributesKey]["xml:space"]==="preserve";if(Z||("fullTagEmptyElementFn"in S?Z=S.fullTagEmptyElementFn(c,m):Z=S.fullTagEmptyElement),Z)C.push(">");else return C.push("/>"),C.join("")}return C.push(v(m,S,P+1,!1)),t=m,a=c,c&&C.push((B?l(S,P,!1):"")+"</"+W+">"),C.join("")}function v(m,c,S,P){var B,W,C,Z=[];for(W in m)if(m.hasOwnProperty(W))for(C=e(m[W])?m[W]:[m[W]],B=0;B<C.length;++B){switch(W){case c.declarationKey:Z.push(i(C[B],c,S));break;case c.instructionKey:Z.push((c.indentInstruction?l(c,S,P):"")+h(C[B],c,S));break;case c.attributesKey:case c.parentKey:break;case c.textKey:Z.push((c.indentText?l(c,S,P):"")+k(C[B],c));break;case c.cdataKey:Z.push((c.indentCdata?l(c,S,P):"")+A(C[B],c));break;case c.doctypeKey:Z.push(l(c,S,P)+R(C[B],c));break;case c.commentKey:Z.push(l(c,S,P)+x(C[B],c));break;default:Z.push(l(c,S,P)+u(C[B],W,c,S,I(C[B],c)))}P=P&&!Z.length}return Z.join("")}return bn=function(m,c){c=o(c);var S=[];return t=m,a="_root_",c.compact?S.push(v(m,c,0,!0)):(m[c.declarationKey]&&S.push(i(m[c.declarationKey],c,0)),m[c.elementsKey]&&m[c.elementsKey].length&&S.push(b(m[c.elementsKey],c,0,!S.length))),S.join("")},bn}var _n,js;function Iu(){if(js)return _n;js=1;var r=qa();return _n=function(e,t){e instanceof Buffer&&(e=e.toString());var a=null;if(typeof e=="string")try{a=JSON.parse(e)}catch{throw new Error("The JSON structure is invalid")}else a=e;return r(a,t)},_n}var En,zs;function Cu(){if(zs)return En;zs=1;var r=Wa(),e=ku(),t=qa(),a=Iu();return En={xml2js:r,xml2json:e,js2xml:t,json2xml:a},En}var Ga=Cu();const ai=r=>{switch(r.type){case void 0:case"element":const e=new Ou(r.name,r.attributes),t=r.elements||[];for(const a of t){const o=ai(a);o!==void 0&&e.push(o)}return e;case"text":return r.text;default:return}};class Nu extends fe{}class Ou extends ne{static fromXmlString(e){const t=Ga.xml2js(e,{compact:!1});return ai(t)}constructor(e,t){super(e),t&&this.root.push(new Nu(t))}push(e){this.root.push(e)}}class Du extends ne{constructor(e){super(""),this._attr=e}prepForXml(e){return{_attr:this._attr}}}class Ka extends ne{constructor(e,t){super(e),t&&(this.root=t.root)}}const Te=r=>{if(isNaN(r))throw new Error(`Invalid value '${r}' specified. Must be an integer.`);return Math.floor(r)},Kt=r=>{const e=Te(r);if(e<0)throw new Error(`Invalid value '${r}' specified. Must be a positive integer.`);return e},Va=(r,e)=>{const t=e*2;if(r.length!==t||isNaN(+`0x${r}`))throw new Error(`Invalid hex value '${r}'. Expected ${t} digit hex value`);return r},Ws=r=>Va(r,1),oi=r=>{const e=r.slice(-2),t=r.substring(0,r.length-2);return`${Number(t)}${e}`},$a=r=>{const e=oi(r);if(parseFloat(e)<0)throw new Error(`Invalid value '${e}' specified. Expected a positive number.`);return e},ut=r=>{if(r==="auto")return r;const e=r.charAt(0)==="#"?r.substring(1):r;return Va(e,3)},He=r=>typeof r=="string"?oi(r):Te(r),Fu=r=>typeof r=="string"?$a(r):Kt(r),Ae=r=>typeof r=="string"?$a(r):Kt(r),Pu=r=>{const e=r.substring(0,r.length-1);return`${Number(e)}%`},Xa=r=>typeof r=="number"?Te(r):r.slice(-1)==="%"?Pu(r):oi(r),Bu=Kt,Lu=Kt,Mu=r=>r.toISOString();class ue extends ne{constructor(e,t=!0){super(e),t!==!0&&this.root.push(new _e({val:t}))}}class xn extends ne{constructor(e,t){super(e),this.root.push(new _e({val:Fu(t)}))}}class Qe extends ne{constructor(e,t){super(e),this.root.push(new _e({val:t}))}}const wt=(r,e)=>new ge({name:r,attributes:{value:{key:"w:val",value:e}}});class bt extends ne{constructor(e,t){super(e),this.root.push(new _e({val:t}))}}class Uu extends ne{constructor(e,t){super(e),this.root.push(new _e({val:t}))}}class $e extends ne{constructor(e,t){super(e),this.root.push(t)}}class ge extends ne{constructor({name:e,attributes:t,children:a}){super(e),t&&this.root.push(new We(t)),a&&this.root.push(...a)}}const Ne={START:"start",CENTER:"center",END:"end",BOTH:"both",MEDIUM_KASHIDA:"mediumKashida",DISTRIBUTE:"distribute",NUM_TAB:"numTab",HIGH_KASHIDA:"highKashida",LOW_KASHIDA:"lowKashida",THAI_DISTRIBUTE:"thaiDistribute",LEFT:"left",RIGHT:"right",JUSTIFIED:"both"};class Hu extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class Za extends ne{constructor(e){super("w:jc"),this.root.push(new Hu({val:e}))}}class ve extends ne{constructor(e,{color:t,size:a,space:o,style:l}){super(e),this.root.push(new ju({style:l,color:t===void 0?void 0:ut(t),size:a===void 0?void 0:Bu(a),space:o===void 0?void 0:Lu(o)}))}}class ju extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{style:"w:val",color:"w:color",size:"w:sz",space:"w:space"})}}const ui={SINGLE:"single",DASH_DOT_STROKED:"dashDotStroked",DASHED:"dashed",DASH_SMALL_GAP:"dashSmallGap",DOT_DASH:"dotDash",DOT_DOT_DASH:"dotDotDash",DOTTED:"dotted",DOUBLE:"double",DOUBLE_WAVE:"doubleWave",INSET:"inset",NIL:"nil",NONE:"none",OUTSET:"outset",THICK:"thick",THICK_THIN_LARGE_GAP:"thickThinLargeGap",THICK_THIN_MEDIUM_GAP:"thickThinMediumGap",THICK_THIN_SMALL_GAP:"thickThinSmallGap",THIN_THICK_LARGE_GAP:"thinThickLargeGap",THIN_THICK_MEDIUM_GAP:"thinThickMediumGap",THIN_THICK_SMALL_GAP:"thinThickSmallGap",THIN_THICK_THIN_LARGE_GAP:"thinThickThinLargeGap",THIN_THICK_THIN_MEDIUM_GAP:"thinThickThinMediumGap",THIN_THICK_THIN_SMALL_GAP:"thinThickThinSmallGap",THREE_D_EMBOSS:"threeDEmboss",THREE_D_ENGRAVE:"threeDEngrave",TRIPLE:"triple",WAVE:"wave"};class zu extends ze{constructor(e){super("w:pBdr"),e.top&&this.root.push(new ve("w:top",e.top)),e.bottom&&this.root.push(new ve("w:bottom",e.bottom)),e.left&&this.root.push(new ve("w:left",e.left)),e.right&&this.root.push(new ve("w:right",e.right))}}class Wu extends ne{constructor(){super("w:pBdr");const e=new ve("w:bottom",{color:"auto",space:1,style:ui.SINGLE,size:6});this.root.push(e)}}class qu extends ne{constructor({start:e,end:t,left:a,right:o,hanging:l,firstLine:d}){super("w:ind"),this.root.push(new We({start:{key:"w:start",value:e===void 0?void 0:He(e)},end:{key:"w:end",value:t===void 0?void 0:He(t)},left:{key:"w:left",value:a===void 0?void 0:He(a)},right:{key:"w:right",value:o===void 0?void 0:He(o)},hanging:{key:"w:hanging",value:l===void 0?void 0:Ae(l)},firstLine:{key:"w:firstLine",value:d===void 0?void 0:Ae(d)}}))}}let Gu=class extends ne{constructor(){super("w:br")}};const li={BEGIN:"begin",END:"end",SEPARATE:"separate"};class ci extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{type:"w:fldCharType",dirty:"w:dirty"})}}class Ft extends ne{constructor(e){super("w:fldChar"),this.root.push(new ci({type:li.BEGIN,dirty:e}))}}class Pt extends ne{constructor(e){super("w:fldChar"),this.root.push(new ci({type:li.SEPARATE,dirty:e}))}}class Bt extends ne{constructor(e){super("w:fldChar"),this.root.push(new ci({type:li.END,dirty:e}))}}const lt={DEFAULT:"default",PRESERVE:"preserve"};class ct extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{space:"xml:space"})}}class Ku extends ne{constructor(){super("w:instrText"),this.root.push(new ct({space:lt.PRESERVE})),this.root.push("PAGE")}}class Vu extends ne{constructor(){super("w:instrText"),this.root.push(new ct({space:lt.PRESERVE})),this.root.push("NUMPAGES")}}class $u extends ne{constructor(){super("w:instrText"),this.root.push(new ct({space:lt.PRESERVE})),this.root.push("SECTIONPAGES")}}class Xu extends ne{constructor(){super("w:instrText"),this.root.push(new ct({space:lt.PRESERVE})),this.root.push("SECTION")}}class Zu extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{fill:"w:fill",color:"w:color",type:"w:val"})}}class Vt extends ne{constructor({fill:e,color:t,type:a}){super("w:shd"),this.root.push(new Zu({fill:e===void 0?void 0:ut(e),color:t===void 0?void 0:ut(t),type:a}))}}class Yu extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{id:"w:id",author:"w:author",date:"w:date"})}}const Ju={DOT:"dot"};class Qu extends ne{constructor(e){super("w:em"),this.root.push(new _e({val:e}))}}class el extends Qu{constructor(e=Ju.DOT){super(e)}}class tl extends ne{constructor(e){super("w:spacing"),this.root.push(new _e({val:He(e)}))}}class rl extends ne{constructor(e){super("w:color"),this.root.push(new _e({val:ut(e)}))}}class nl extends ne{constructor(e){super("w:highlight"),this.root.push(new _e({val:e}))}}class il extends ne{constructor(e){super("w:highlightCs"),this.root.push(new _e({val:e}))}}const sl=r=>new ge({name:"w:lang",attributes:{value:{key:"w:val",value:r.value},eastAsia:{key:"w:eastAsia",value:r.eastAsia},bidirectional:{key:"w:bidi",value:r.bidirectional}}});class qs extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{ascii:"w:ascii",cs:"w:cs",eastAsia:"w:eastAsia",hAnsi:"w:hAnsi",hint:"w:hint"})}}class An extends ne{constructor(e,t){if(super("w:rFonts"),typeof e=="string"){const a=e;this.root.push(new qs({ascii:a,cs:a,eastAsia:a,hAnsi:a,hint:t}))}else{const a=e;this.root.push(new qs(a))}}}let Ya=class extends ne{constructor(e){super("w:vertAlign"),this.root.push(new _e({val:e}))}};class al extends Ya{constructor(){super("superscript")}}class ol extends Ya{constructor(){super("subscript")}}const Ja={SINGLE:"single",WORDS:"words",DOUBLE:"double",THICK:"thick",DOTTED:"dotted",DOTTEDHEAVY:"dottedHeavy",DASH:"dash",DASHEDHEAVY:"dashedHeavy",DASHLONG:"dashLong",DASHLONGHEAVY:"dashLongHeavy",DOTDASH:"dotDash",DASHDOTHEAVY:"dashDotHeavy",DOTDOTDASH:"dotDotDash",DASHDOTDOTHEAVY:"dashDotDotHeavy",WAVE:"wave",WAVYHEAVY:"wavyHeavy",WAVYDOUBLE:"wavyDouble",NONE:"none"};class ul extends ne{constructor(e=Ja.SINGLE,t){super("w:u"),this.root.push(new _e({val:e,color:t===void 0?void 0:ut(t)}))}}class et extends ze{constructor(e){var t,a;if(super("w:rPr"),!e)return;e.noProof!==void 0&&this.push(new ue("w:noProof",e.noProof)),e.bold!==void 0&&this.push(new ue("w:b",e.bold)),(e.boldComplexScript===void 0&&e.bold!==void 0||e.boldComplexScript)&&this.push(new ue("w:bCs",(t=e.boldComplexScript)!=null?t:e.bold)),e.italics!==void 0&&this.push(new ue("w:i",e.italics)),(e.italicsComplexScript===void 0&&e.italics!==void 0||e.italicsComplexScript)&&this.push(new ue("w:iCs",(a=e.italicsComplexScript)!=null?a:e.italics)),e.underline&&this.push(new ul(e.underline.type,e.underline.color)),e.effect&&this.push(new Qe("w:effect",e.effect)),e.emphasisMark&&this.push(new el(e.emphasisMark.type)),e.color&&this.push(new rl(e.color)),e.kern&&this.push(new xn("w:kern",e.kern)),e.position&&this.push(new Qe("w:position",e.position)),e.size!==void 0&&this.push(new xn("w:sz",e.size));const o=e.sizeComplexScript===void 0||e.sizeComplexScript===!0?e.size:e.sizeComplexScript;o&&this.push(new xn("w:szCs",o)),e.rightToLeft!==void 0&&this.push(new ue("w:rtl",e.rightToLeft)),e.smallCaps!==void 0?this.push(new ue("w:smallCaps",e.smallCaps)):e.allCaps!==void 0&&this.push(new ue("w:caps",e.allCaps)),e.strike!==void 0&&this.push(new ue("w:strike",e.strike)),e.doubleStrike!==void 0&&this.push(new ue("w:dstrike",e.doubleStrike)),e.subScript&&this.push(new ol),e.superScript&&this.push(new al),e.style&&this.push(new Qe("w:rStyle",e.style)),e.font&&(typeof e.font=="string"?this.push(new An(e.font)):"name"in e.font?this.push(new An(e.font.name,e.font.hint)):this.push(new An(e.font))),e.highlight&&this.push(new nl(e.highlight));const l=e.highlightComplexScript===void 0||e.highlightComplexScript===!0?e.highlight:e.highlightComplexScript;l&&this.push(new il(l)),e.characterSpacing&&this.push(new tl(e.characterSpacing)),e.emboss!==void 0&&this.push(new ue("w:emboss",e.emboss)),e.imprint!==void 0&&this.push(new ue("w:imprint",e.imprint)),e.shading&&this.push(new Vt(e.shading)),e.revision&&this.push(new ll(e.revision)),e.border&&this.push(new ve("w:bdr",e.border)),e.snapToGrid!==void 0&&this.push(new ue("w:snapToGrid",e.snapToGrid)),e.vanish&&this.push(new ue("w:vanish",e.vanish)),e.specVanish&&this.push(new ue("w:specVanish",e.vanish)),e.scale!==void 0&&this.push(new bt("w:w",e.scale)),e.language&&this.push(sl(e.language)),e.math&&this.push(new ue("w:oMath",e.math))}push(e){this.root.push(e)}}class ll extends ne{constructor(e){super("w:rPrChange"),this.root.push(new Yu({id:e.id,author:e.author,date:e.date})),this.addChildElement(new et(e))}}class Gs extends ne{constructor(e){var t;super("w:t"),typeof e=="string"?(this.root.push(new ct({space:lt.PRESERVE})),this.root.push(e)):(this.root.push(new ct({space:(t=e.space)!=null?t:lt.DEFAULT})),this.root.push(e.text))}}const Lt={CURRENT:"CURRENT",TOTAL_PAGES:"TOTAL_PAGES",TOTAL_PAGES_IN_SECTION:"TOTAL_PAGES_IN_SECTION",CURRENT_SECTION:"SECTION"};class Tt extends ne{constructor(e){if(super("w:r"),re(this,"properties"),this.properties=new et(e),this.root.push(this.properties),e.break)for(let t=0;t<e.break;t++)this.root.push(new Gu);if(e.children)for(const t of e.children){if(typeof t=="string"){switch(t){case Lt.CURRENT:this.root.push(new Ft),this.root.push(new Ku),this.root.push(new Pt),this.root.push(new Bt);break;case Lt.TOTAL_PAGES:this.root.push(new Ft),this.root.push(new Vu),this.root.push(new Pt),this.root.push(new Bt);break;case Lt.TOTAL_PAGES_IN_SECTION:this.root.push(new Ft),this.root.push(new $u),this.root.push(new Pt),this.root.push(new Bt);break;case Lt.CURRENT_SECTION:this.root.push(new Ft),this.root.push(new Xu),this.root.push(new Pt),this.root.push(new Bt);break;default:this.root.push(new Gs(t));break}continue}this.root.push(t)}else e.text!==void 0&&this.root.push(new Gs(e.text))}}class Ks extends Tt{constructor(e){super(typeof e=="string"?{text:e}:e)}}var Tn={},ye={},Sn,Vs;function St(){if(Vs)return Sn;Vs=1,Sn=r;function r(e,t){if(!e)throw new Error(t||"Assertion failed")}return r.equal=function(t,a,o){if(t!=a)throw new Error(o||"Assertion failed: "+t+" != "+a)},Sn}var $s;function Pe(){if($s)return ye;$s=1;var r=St(),e=Ke();ye.inherits=e;function t(N,U){return(N.charCodeAt(U)&64512)!==55296||U<0||U+1>=N.length?!1:(N.charCodeAt(U+1)&64512)===56320}function a(N,U){if(Array.isArray(N))return N.slice();if(!N)return[];var g=[];if(typeof N=="string")if(U){if(U==="hex")for(N=N.replace(/[^a-z0-9]+/ig,""),N.length%2!==0&&(N="0"+N),ee=0;ee<N.length;ee+=2)g.push(parseInt(N[ee]+N[ee+1],16))}else for(var K=0,ee=0;ee<N.length;ee++){var z=N.charCodeAt(ee);z<128?g[K++]=z:z<2048?(g[K++]=z>>6|192,g[K++]=z&63|128):t(N,ee)?(z=65536+((z&1023)<<10)+(N.charCodeAt(++ee)&1023),g[K++]=z>>18|240,g[K++]=z>>12&63|128,g[K++]=z>>6&63|128,g[K++]=z&63|128):(g[K++]=z>>12|224,g[K++]=z>>6&63|128,g[K++]=z&63|128)}else for(ee=0;ee<N.length;ee++)g[ee]=N[ee]|0;return g}ye.toArray=a;function o(N){for(var U="",g=0;g<N.length;g++)U+=i(N[g].toString(16));return U}ye.toHex=o;function l(N){var U=N>>>24|N>>>8&65280|N<<8&16711680|(N&255)<<24;return U>>>0}ye.htonl=l;function d(N,U){for(var g="",K=0;K<N.length;K++){var ee=N[K];U==="little"&&(ee=l(ee)),g+=h(ee.toString(16))}return g}ye.toHex32=d;function i(N){return N.length===1?"0"+N:N}ye.zero2=i;function h(N){return N.length===7?"0"+N:N.length===6?"00"+N:N.length===5?"000"+N:N.length===4?"0000"+N:N.length===3?"00000"+N:N.length===2?"000000"+N:N.length===1?"0000000"+N:N}ye.zero8=h;function x(N,U,g,K){var ee=g-U;r(ee%4===0);for(var z=new Array(ee/4),se=0,Q=U;se<z.length;se++,Q+=4){var ce;K==="big"?ce=N[Q]<<24|N[Q+1]<<16|N[Q+2]<<8|N[Q+3]:ce=N[Q+3]<<24|N[Q+2]<<16|N[Q+1]<<8|N[Q],z[se]=ce>>>0}return z}ye.join32=x;function A(N,U){for(var g=new Array(N.length*4),K=0,ee=0;K<N.length;K++,ee+=4){var z=N[K];U==="big"?(g[ee]=z>>>24,g[ee+1]=z>>>16&255,g[ee+2]=z>>>8&255,g[ee+3]=z&255):(g[ee+3]=z>>>24,g[ee+2]=z>>>16&255,g[ee+1]=z>>>8&255,g[ee]=z&255)}return g}ye.split32=A;function R(N,U){return N>>>U|N<<32-U}ye.rotr32=R;function k(N,U){return N<<U|N>>>32-U}ye.rotl32=k;function y(N,U){return N+U>>>0}ye.sum32=y;function T(N,U,g){return N+U+g>>>0}ye.sum32_3=T;function b(N,U,g,K){return N+U+g+K>>>0}ye.sum32_4=b;function I(N,U,g,K,ee){return N+U+g+K+ee>>>0}ye.sum32_5=I;function u(N,U,g,K){var ee=N[U],z=N[U+1],se=K+z>>>0,Q=(se<K?1:0)+g+ee;N[U]=Q>>>0,N[U+1]=se}ye.sum64=u;function v(N,U,g,K){var ee=U+K>>>0,z=(ee<U?1:0)+N+g;return z>>>0}ye.sum64_hi=v;function m(N,U,g,K){var ee=U+K;return ee>>>0}ye.sum64_lo=m;function c(N,U,g,K,ee,z,se,Q){var ce=0,V=U;V=V+K>>>0,ce+=V<U?1:0,V=V+z>>>0,ce+=V<z?1:0,V=V+Q>>>0,ce+=V<Q?1:0;var D=N+g+ee+se+ce;return D>>>0}ye.sum64_4_hi=c;function S(N,U,g,K,ee,z,se,Q){var ce=U+K+z+Q;return ce>>>0}ye.sum64_4_lo=S;function P(N,U,g,K,ee,z,se,Q,ce,V){var D=0,$=U;$=$+K>>>0,D+=$<U?1:0,$=$+z>>>0,D+=$<z?1:0,$=$+Q>>>0,D+=$<Q?1:0,$=$+V>>>0,D+=$<V?1:0;var X=N+g+ee+se+ce+D;return X>>>0}ye.sum64_5_hi=P;function B(N,U,g,K,ee,z,se,Q,ce,V){var D=U+K+z+Q+V;return D>>>0}ye.sum64_5_lo=B;function W(N,U,g){var K=U<<32-g|N>>>g;return K>>>0}ye.rotr64_hi=W;function C(N,U,g){var K=N<<32-g|U>>>g;return K>>>0}ye.rotr64_lo=C;function Z(N,U,g){return N>>>g}ye.shr64_hi=Z;function le(N,U,g){var K=N<<32-g|U>>>g;return K>>>0}return ye.shr64_lo=le,ye}var Rn={},Xs;function Rt(){if(Xs)return Rn;Xs=1;var r=Pe(),e=St();function t(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}return Rn.BlockHash=t,t.prototype.update=function(o,l){if(o=r.toArray(o,l),this.pending?this.pending=this.pending.concat(o):this.pending=o,this.pendingTotal+=o.length,this.pending.length>=this._delta8){o=this.pending;var d=o.length%this._delta8;this.pending=o.slice(o.length-d,o.length),this.pending.length===0&&(this.pending=null),o=r.join32(o,0,o.length-d,this.endian);for(var i=0;i<o.length;i+=this._delta32)this._update(o,i,i+this._delta32)}return this},t.prototype.digest=function(o){return this.update(this._pad()),e(this.pending===null),this._digest(o)},t.prototype._pad=function(){var o=this.pendingTotal,l=this._delta8,d=l-(o+this.padLength)%l,i=new Array(d+this.padLength);i[0]=128;for(var h=1;h<d;h++)i[h]=0;if(o<<=3,this.endian==="big"){for(var x=8;x<this.padLength;x++)i[h++]=0;i[h++]=0,i[h++]=0,i[h++]=0,i[h++]=0,i[h++]=o>>>24&255,i[h++]=o>>>16&255,i[h++]=o>>>8&255,i[h++]=o&255}else for(i[h++]=o&255,i[h++]=o>>>8&255,i[h++]=o>>>16&255,i[h++]=o>>>24&255,i[h++]=0,i[h++]=0,i[h++]=0,i[h++]=0,x=8;x<this.padLength;x++)i[h++]=0;return i},Rn}var Xe={},Ce={},Zs;function Qa(){if(Zs)return Ce;Zs=1;var r=Pe(),e=r.rotr32;function t(A,R,k,y){if(A===0)return a(R,k,y);if(A===1||A===3)return l(R,k,y);if(A===2)return o(R,k,y)}Ce.ft_1=t;function a(A,R,k){return A&R^~A&k}Ce.ch32=a;function o(A,R,k){return A&R^A&k^R&k}Ce.maj32=o;function l(A,R,k){return A^R^k}Ce.p32=l;function d(A){return e(A,2)^e(A,13)^e(A,22)}Ce.s0_256=d;function i(A){return e(A,6)^e(A,11)^e(A,25)}Ce.s1_256=i;function h(A){return e(A,7)^e(A,18)^A>>>3}Ce.g0_256=h;function x(A){return e(A,17)^e(A,19)^A>>>10}return Ce.g1_256=x,Ce}var kn,Ys;function cl(){if(Ys)return kn;Ys=1;var r=Pe(),e=Rt(),t=Qa(),a=r.rotl32,o=r.sum32,l=r.sum32_5,d=t.ft_1,i=e.BlockHash,h=[1518500249,1859775393,2400959708,3395469782];function x(){if(!(this instanceof x))return new x;i.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.W=new Array(80)}return r.inherits(x,i),kn=x,x.blockSize=512,x.outSize=160,x.hmacStrength=80,x.padLength=64,x.prototype._update=function(R,k){for(var y=this.W,T=0;T<16;T++)y[T]=R[k+T];for(;T<y.length;T++)y[T]=a(y[T-3]^y[T-8]^y[T-14]^y[T-16],1);var b=this.h[0],I=this.h[1],u=this.h[2],v=this.h[3],m=this.h[4];for(T=0;T<y.length;T++){var c=~~(T/20),S=l(a(b,5),d(c,I,u,v),m,y[T],h[c]);m=v,v=u,u=a(I,30),I=b,b=S}this.h[0]=o(this.h[0],b),this.h[1]=o(this.h[1],I),this.h[2]=o(this.h[2],u),this.h[3]=o(this.h[3],v),this.h[4]=o(this.h[4],m)},x.prototype._digest=function(R){return R==="hex"?r.toHex32(this.h,"big"):r.split32(this.h,"big")},kn}var In,Js;function eo(){if(Js)return In;Js=1;var r=Pe(),e=Rt(),t=Qa(),a=St(),o=r.sum32,l=r.sum32_4,d=r.sum32_5,i=t.ch32,h=t.maj32,x=t.s0_256,A=t.s1_256,R=t.g0_256,k=t.g1_256,y=e.BlockHash,T=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function b(){if(!(this instanceof b))return new b;y.call(this),this.h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],this.k=T,this.W=new Array(64)}return r.inherits(b,y),In=b,b.blockSize=512,b.outSize=256,b.hmacStrength=192,b.padLength=64,b.prototype._update=function(u,v){for(var m=this.W,c=0;c<16;c++)m[c]=u[v+c];for(;c<m.length;c++)m[c]=l(k(m[c-2]),m[c-7],R(m[c-15]),m[c-16]);var S=this.h[0],P=this.h[1],B=this.h[2],W=this.h[3],C=this.h[4],Z=this.h[5],le=this.h[6],N=this.h[7];for(a(this.k.length===m.length),c=0;c<m.length;c++){var U=d(N,A(C),i(C,Z,le),this.k[c],m[c]),g=o(x(S),h(S,P,B));N=le,le=Z,Z=C,C=o(W,U),W=B,B=P,P=S,S=o(U,g)}this.h[0]=o(this.h[0],S),this.h[1]=o(this.h[1],P),this.h[2]=o(this.h[2],B),this.h[3]=o(this.h[3],W),this.h[4]=o(this.h[4],C),this.h[5]=o(this.h[5],Z),this.h[6]=o(this.h[6],le),this.h[7]=o(this.h[7],N)},b.prototype._digest=function(u){return u==="hex"?r.toHex32(this.h,"big"):r.split32(this.h,"big")},In}var Cn,Qs;function hl(){if(Qs)return Cn;Qs=1;var r=Pe(),e=eo();function t(){if(!(this instanceof t))return new t;e.call(this),this.h=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]}return r.inherits(t,e),Cn=t,t.blockSize=512,t.outSize=224,t.hmacStrength=192,t.padLength=64,t.prototype._digest=function(o){return o==="hex"?r.toHex32(this.h.slice(0,7),"big"):r.split32(this.h.slice(0,7),"big")},Cn}var Nn,ea;function to(){if(ea)return Nn;ea=1;var r=Pe(),e=Rt(),t=St(),a=r.rotr64_hi,o=r.rotr64_lo,l=r.shr64_hi,d=r.shr64_lo,i=r.sum64,h=r.sum64_hi,x=r.sum64_lo,A=r.sum64_4_hi,R=r.sum64_4_lo,k=r.sum64_5_hi,y=r.sum64_5_lo,T=e.BlockHash,b=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function I(){if(!(this instanceof I))return new I;T.call(this),this.h=[1779033703,4089235720,3144134277,2227873595,1013904242,4271175723,2773480762,1595750129,1359893119,2917565137,2600822924,725511199,528734635,4215389547,1541459225,327033209],this.k=b,this.W=new Array(160)}r.inherits(I,T),Nn=I,I.blockSize=1024,I.outSize=512,I.hmacStrength=192,I.padLength=128,I.prototype._prepareBlock=function(g,K){for(var ee=this.W,z=0;z<32;z++)ee[z]=g[K+z];for(;z<ee.length;z+=2){var se=le(ee[z-4],ee[z-3]),Q=N(ee[z-4],ee[z-3]),ce=ee[z-14],V=ee[z-13],D=C(ee[z-30],ee[z-29]),$=Z(ee[z-30],ee[z-29]),X=ee[z-32],te=ee[z-31];ee[z]=A(se,Q,ce,V,D,$,X,te),ee[z+1]=R(se,Q,ce,V,D,$,X,te)}},I.prototype._update=function(g,K){this._prepareBlock(g,K);var ee=this.W,z=this.h[0],se=this.h[1],Q=this.h[2],ce=this.h[3],V=this.h[4],D=this.h[5],$=this.h[6],X=this.h[7],te=this.h[8],G=this.h[9],_=this.h[10],w=this.h[11],j=this.h[12],M=this.h[13],O=this.h[14],F=this.h[15];t(this.k.length===ee.length);for(var J=0;J<ee.length;J+=2){var f=O,Y=F,E=B(te,G),n=W(te,G),s=u(te,G,_,w,j),p=v(te,G,_,w,j,M),L=this.k[J],q=this.k[J+1],H=ee[J],ie=ee[J+1],oe=k(f,Y,E,n,s,p,L,q,H,ie),ae=y(f,Y,E,n,s,p,L,q,H,ie);f=S(z,se),Y=P(z,se),E=m(z,se,Q,ce,V),n=c(z,se,Q,ce,V,D);var he=h(f,Y,E,n),de=x(f,Y,E,n);O=j,F=M,j=_,M=w,_=te,w=G,te=h($,X,oe,ae),G=x(X,X,oe,ae),$=V,X=D,V=Q,D=ce,Q=z,ce=se,z=h(oe,ae,he,de),se=x(oe,ae,he,de)}i(this.h,0,z,se),i(this.h,2,Q,ce),i(this.h,4,V,D),i(this.h,6,$,X),i(this.h,8,te,G),i(this.h,10,_,w),i(this.h,12,j,M),i(this.h,14,O,F)},I.prototype._digest=function(g){return g==="hex"?r.toHex32(this.h,"big"):r.split32(this.h,"big")};function u(U,g,K,ee,z){var se=U&K^~U&z;return se<0&&(se+=4294967296),se}function v(U,g,K,ee,z,se){var Q=g&ee^~g&se;return Q<0&&(Q+=4294967296),Q}function m(U,g,K,ee,z){var se=U&K^U&z^K&z;return se<0&&(se+=4294967296),se}function c(U,g,K,ee,z,se){var Q=g&ee^g&se^ee&se;return Q<0&&(Q+=4294967296),Q}function S(U,g){var K=a(U,g,28),ee=a(g,U,2),z=a(g,U,7),se=K^ee^z;return se<0&&(se+=4294967296),se}function P(U,g){var K=o(U,g,28),ee=o(g,U,2),z=o(g,U,7),se=K^ee^z;return se<0&&(se+=4294967296),se}function B(U,g){var K=a(U,g,14),ee=a(U,g,18),z=a(g,U,9),se=K^ee^z;return se<0&&(se+=4294967296),se}function W(U,g){var K=o(U,g,14),ee=o(U,g,18),z=o(g,U,9),se=K^ee^z;return se<0&&(se+=4294967296),se}function C(U,g){var K=a(U,g,1),ee=a(U,g,8),z=l(U,g,7),se=K^ee^z;return se<0&&(se+=4294967296),se}function Z(U,g){var K=o(U,g,1),ee=o(U,g,8),z=d(U,g,7),se=K^ee^z;return se<0&&(se+=4294967296),se}function le(U,g){var K=a(U,g,19),ee=a(g,U,29),z=l(U,g,6),se=K^ee^z;return se<0&&(se+=4294967296),se}function N(U,g){var K=o(U,g,19),ee=o(g,U,29),z=d(U,g,6),se=K^ee^z;return se<0&&(se+=4294967296),se}return Nn}var On,ta;function fl(){if(ta)return On;ta=1;var r=Pe(),e=to();function t(){if(!(this instanceof t))return new t;e.call(this),this.h=[3418070365,3238371032,1654270250,914150663,2438529370,812702999,355462360,4144912697,1731405415,4290775857,2394180231,1750603025,3675008525,1694076839,1203062813,3204075428]}return r.inherits(t,e),On=t,t.blockSize=1024,t.outSize=384,t.hmacStrength=192,t.padLength=128,t.prototype._digest=function(o){return o==="hex"?r.toHex32(this.h.slice(0,12),"big"):r.split32(this.h.slice(0,12),"big")},On}var ra;function dl(){return ra||(ra=1,Xe.sha1=cl(),Xe.sha224=hl(),Xe.sha256=eo(),Xe.sha384=fl(),Xe.sha512=to()),Xe}var Dn={},na;function pl(){if(na)return Dn;na=1;var r=Pe(),e=Rt(),t=r.rotl32,a=r.sum32,o=r.sum32_3,l=r.sum32_4,d=e.BlockHash;function i(){if(!(this instanceof i))return new i;d.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.endian="little"}r.inherits(i,d),Dn.ripemd160=i,i.blockSize=512,i.outSize=160,i.hmacStrength=192,i.padLength=64,i.prototype._update=function(I,u){for(var v=this.h[0],m=this.h[1],c=this.h[2],S=this.h[3],P=this.h[4],B=v,W=m,C=c,Z=S,le=P,N=0;N<80;N++){var U=a(t(l(v,h(N,m,c,S),I[R[N]+u],x(N)),y[N]),P);v=P,P=S,S=t(c,10),c=m,m=U,U=a(t(l(B,h(79-N,W,C,Z),I[k[N]+u],A(N)),T[N]),le),B=le,le=Z,Z=t(C,10),C=W,W=U}U=o(this.h[1],c,Z),this.h[1]=o(this.h[2],S,le),this.h[2]=o(this.h[3],P,B),this.h[3]=o(this.h[4],v,W),this.h[4]=o(this.h[0],m,C),this.h[0]=U},i.prototype._digest=function(I){return I==="hex"?r.toHex32(this.h,"little"):r.split32(this.h,"little")};function h(b,I,u,v){return b<=15?I^u^v:b<=31?I&u|~I&v:b<=47?(I|~u)^v:b<=63?I&v|u&~v:I^(u|~v)}function x(b){return b<=15?0:b<=31?1518500249:b<=47?1859775393:b<=63?2400959708:2840853838}function A(b){return b<=15?1352829926:b<=31?1548603684:b<=47?1836072691:b<=63?2053994217:0}var R=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],k=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],y=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],T=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11];return Dn}var Fn,ia;function ml(){if(ia)return Fn;ia=1;var r=Pe(),e=St();function t(a,o,l){if(!(this instanceof t))return new t(a,o,l);this.Hash=a,this.blockSize=a.blockSize/8,this.outSize=a.outSize/8,this.inner=null,this.outer=null,this._init(r.toArray(o,l))}return Fn=t,t.prototype._init=function(o){o.length>this.blockSize&&(o=new this.Hash().update(o).digest()),e(o.length<=this.blockSize);for(var l=o.length;l<this.blockSize;l++)o.push(0);for(l=0;l<o.length;l++)o[l]^=54;for(this.inner=new this.Hash().update(o),l=0;l<o.length;l++)o[l]^=106;this.outer=new this.Hash().update(o)},t.prototype.update=function(o,l){return this.inner.update(o,l),this},t.prototype.digest=function(o){return this.outer.update(this.inner.digest()),this.outer.digest(o)},Fn}var sa;function wl(){return sa||(sa=1,function(r){var e=r;e.utils=Pe(),e.common=Rt(),e.sha=dl(),e.ripemd=pl(),e.hmac=ml(),e.sha1=e.sha.sha1,e.sha256=e.sha.sha256,e.sha224=e.sha.sha224,e.sha384=e.sha.sha384,e.sha512=e.sha.sha512,e.ripemd160=e.ripemd.ripemd160}(Tn)),Tn}var gl=wl();const yl=Yn(gl);let vl="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",bl=(r,e=21)=>(t=e)=>{let a="",o=t|0;for(;o--;)a+=r[Math.random()*r.length|0];return a},_l=(r=21)=>{let e="",t=r|0;for(;t--;)e+=vl[Math.random()*64|0];return e};const ke=r=>Math.floor(r*72*20),$t=(r=0)=>{let e=r;return()=>++e},El=()=>$t(),xl=()=>$t(1),Al=()=>$t(),Tl=()=>$t(),Sl=()=>_l().toLowerCase(),aa=r=>yl.sha1().update(r instanceof ArrayBuffer?new Uint8Array(r):r).digest("hex"),gt=r=>bl("1234567890abcdef",r)(),Rl=()=>`${gt(8)}-${gt(4)}-${gt(4)}-${gt(4)}-${gt(12)}`,kl={CHARACTER:"character",COLUMN:"column",INSIDE_MARGIN:"insideMargin",LEFT_MARGIN:"leftMargin",MARGIN:"margin",OUTSIDE_MARGIN:"outsideMargin",PAGE:"page",RIGHT_MARGIN:"rightMargin"},Il={BOTTOM_MARGIN:"bottomMargin",INSIDE_MARGIN:"insideMargin",LINE:"line",MARGIN:"margin",OUTSIDE_MARGIN:"outsideMargin",PAGE:"page",PARAGRAPH:"paragraph",TOP_MARGIN:"topMargin"},Cl=()=>new ge({name:"wp:simplePos",attributes:{x:{key:"x",value:0},y:{key:"y",value:0}}}),ro=r=>new ge({name:"wp:align",children:[r]}),no=r=>new ge({name:"wp:posOffset",children:[r.toString()]}),Nl=({relative:r,align:e,offset:t})=>new ge({name:"wp:positionH",attributes:{relativeFrom:{key:"relativeFrom",value:r??kl.PAGE}},children:[(()=>{if(e)return ro(e);if(t!==void 0)return no(t);throw new Error("There is no configuration provided for floating position (Align or offset)")})()]}),Ol=({relative:r,align:e,offset:t})=>new ge({name:"wp:positionV",attributes:{relativeFrom:{key:"relativeFrom",value:r??Il.PAGE}},children:[(()=>{if(e)return ro(e);if(t!==void 0)return no(t);throw new Error("There is no configuration provided for floating position (Align or offset)")})()]});class Dl extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{uri:"uri"})}}const Fl=r=>new ge({name:"asvg:svgBlip",attributes:{asvg:{key:"xmlns:asvg",value:"http://schemas.microsoft.com/office/drawing/2016/SVG/main"},embed:{key:"r:embed",value:`rId{${r.fileName}}`}}}),Pl=r=>new ge({name:"a:ext",attributes:{uri:{key:"uri",value:"{96DAC541-7B7A-43D3-8B79-37D633B846F1}"}},children:[Fl(r)]}),Bl=r=>new ge({name:"a:extLst",children:[Pl(r)]}),Ll=r=>new ge({name:"a:blip",attributes:{embed:{key:"r:embed",value:`rId{${r.type==="svg"?r.fallback.fileName:r.fileName}}`},cstate:{key:"cstate",value:"none"}},children:r.type==="svg"?[Bl(r)]:[]});class Ml extends ne{constructor(){super("a:srcRect")}}class Ul extends ne{constructor(){super("a:fillRect")}}class Hl extends ne{constructor(){super("a:stretch"),this.root.push(new Ul)}}class jl extends ne{constructor(e){super("pic:blipFill"),this.root.push(Ll(e)),this.root.push(new Ml),this.root.push(new Hl)}}class zl extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{noChangeAspect:"noChangeAspect",noChangeArrowheads:"noChangeArrowheads"})}}class Wl extends ne{constructor(){super("a:picLocks"),this.root.push(new zl({noChangeAspect:1,noChangeArrowheads:1}))}}class ql extends ne{constructor(){super("pic:cNvPicPr"),this.root.push(new Wl)}}const io=(r,e)=>new ge({name:"a:hlinkClick",attributes:je(me({},e?{xmlns:{key:"xmlns:a",value:"http://schemas.openxmlformats.org/drawingml/2006/main"}}:{}),{id:{key:"r:id",value:`rId${r}`}})});class Gl extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{id:"id",name:"name",descr:"descr"})}}class Kl extends ne{constructor(){super("pic:cNvPr"),this.root.push(new Gl({id:0,name:"",descr:""}))}prepForXml(e){for(let t=e.stack.length-1;t>=0;t--){const a=e.stack[t];if(a instanceof hi){this.root.push(io(a.linkId,!1));break}}return super.prepForXml(e)}}class Vl extends ne{constructor(){super("pic:nvPicPr"),this.root.push(new Kl),this.root.push(new ql)}}class $l extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{xmlns:"xmlns:pic"})}}class Xl extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{cx:"cx",cy:"cy"})}}class Zl extends ne{constructor(e,t){super("a:ext"),re(this,"attributes"),this.attributes=new Xl({cx:e,cy:t}),this.root.push(this.attributes)}}class Yl extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{x:"x",y:"y"})}}class Jl extends ne{constructor(){super("a:off"),this.root.push(new Yl({x:0,y:0}))}}class Ql extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{flipVertical:"flipV",flipHorizontal:"flipH",rotation:"rot"})}}class ec extends ne{constructor(e){var t,a;super("a:xfrm"),re(this,"extents"),this.root.push(new Ql({flipVertical:(t=e.flip)==null?void 0:t.vertical,flipHorizontal:(a=e.flip)==null?void 0:a.horizontal,rotation:e.rotation})),this.extents=new Zl(e.emus.x,e.emus.y),this.root.push(new Jl),this.root.push(this.extents)}}const so=()=>new ge({name:"a:noFill"}),tc=r=>new ge({name:"a:srgbClr",attributes:{value:{key:"val",value:r.value}}}),rc=r=>new ge({name:"a:schemeClr",attributes:{value:{key:"val",value:r.value}}}),oa=r=>new ge({name:"a:solidFill",children:[r.type==="rgb"?tc(r):rc(r)]}),nc=r=>new ge({name:"a:ln",attributes:{width:{key:"w",value:r.width},cap:{key:"cap",value:r.cap},compoundLine:{key:"cmpd",value:r.compoundLine},align:{key:"algn",value:r.align}},children:[r.type==="noFill"?so():r.solidFillType==="rgb"?oa({type:"rgb",value:r.value}):oa({type:"scheme",value:r.value})]});class ic extends ne{constructor(){super("a:avLst")}}class sc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{prst:"prst"})}}class ac extends ne{constructor(){super("a:prstGeom"),this.root.push(new sc({prst:"rect"})),this.root.push(new ic)}}class oc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{bwMode:"bwMode"})}}class uc extends ne{constructor({outline:e,transform:t}){super("pic:spPr"),re(this,"form"),this.root.push(new oc({bwMode:"auto"})),this.form=new ec(t),this.root.push(this.form),this.root.push(new ac),e&&(this.root.push(so()),this.root.push(nc(e)))}}class lc extends ne{constructor({mediaData:e,transform:t,outline:a}){super("pic:pic"),this.root.push(new $l({xmlns:"http://schemas.openxmlformats.org/drawingml/2006/picture"})),this.root.push(new Vl),this.root.push(new jl(e)),this.root.push(new uc({transform:t,outline:a}))}}class cc extends ne{constructor({mediaData:e,transform:t,outline:a}){super("a:graphicData"),re(this,"pic"),this.root.push(new Dl({uri:"http://schemas.openxmlformats.org/drawingml/2006/picture"})),this.pic=new lc({mediaData:e,transform:t,outline:a}),this.root.push(this.pic)}}class hc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{a:"xmlns:a"})}}class ao extends ne{constructor({mediaData:e,transform:t,outline:a}){super("a:graphic"),re(this,"data"),this.root.push(new hc({a:"http://schemas.openxmlformats.org/drawingml/2006/main"})),this.data=new cc({mediaData:e,transform:t,outline:a}),this.root.push(this.data)}}const Mt={NONE:0,SQUARE:1,TIGHT:2,TOP_AND_BOTTOM:3},fc={BOTH_SIDES:"bothSides",LEFT:"left",RIGHT:"right",LARGEST:"largest"};class ua extends ne{constructor(){super("wp:wrapNone")}}class dc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{distT:"distT",distB:"distB",distL:"distL",distR:"distR",wrapText:"wrapText"})}}class pc extends ne{constructor(e,t={top:0,bottom:0,left:0,right:0}){super("wp:wrapSquare"),this.root.push(new dc({wrapText:e.side||fc.BOTH_SIDES,distT:t.top,distB:t.bottom,distL:t.left,distR:t.right}))}}class mc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{distT:"distT",distB:"distB"})}}class wc extends ne{constructor(e={top:0,bottom:0}){super("wp:wrapTight"),this.root.push(new mc({distT:e.top,distB:e.bottom}))}}class gc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{distT:"distT",distB:"distB"})}}class yc extends ne{constructor(e={top:0,bottom:0}){super("wp:wrapTopAndBottom"),this.root.push(new gc({distT:e.top,distB:e.bottom}))}}class oo extends ne{constructor({name:e,description:t,title:a}={name:"",description:"",title:""}){super("wp:docPr"),re(this,"docPropertiesUniqueNumericId",Al());const o={id:{key:"id",value:this.docPropertiesUniqueNumericId()},name:{key:"name",value:e}};t!=null&&(o.description={key:"descr",value:t}),a!=null&&(o.title={key:"title",value:a}),this.root.push(new We(o))}prepForXml(e){for(let t=e.stack.length-1;t>=0;t--){const a=e.stack[t];if(a instanceof hi){this.root.push(io(a.linkId,!0));break}}return super.prepForXml(e)}}const uo=({top:r,right:e,bottom:t,left:a})=>new ge({name:"wp:effectExtent",attributes:{top:{key:"t",value:r},right:{key:"r",value:e},bottom:{key:"b",value:t},left:{key:"l",value:a}}}),lo=({x:r,y:e})=>new ge({name:"wp:extent",attributes:{x:{key:"cx",value:r},y:{key:"cy",value:e}}});class vc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{xmlns:"xmlns:a",noChangeAspect:"noChangeAspect"})}}class bc extends ne{constructor(){super("a:graphicFrameLocks"),this.root.push(new vc({xmlns:"http://schemas.openxmlformats.org/drawingml/2006/main",noChangeAspect:1}))}}const co=()=>new ge({name:"wp:cNvGraphicFramePr",children:[new bc]});class _c extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{distT:"distT",distB:"distB",distL:"distL",distR:"distR",allowOverlap:"allowOverlap",behindDoc:"behindDoc",layoutInCell:"layoutInCell",locked:"locked",relativeHeight:"relativeHeight",simplePos:"simplePos"})}}class Ec extends ne{constructor({mediaData:e,transform:t,drawingOptions:a}){super("wp:anchor");const o=me({allowOverlap:!0,behindDocument:!1,lockAnchor:!1,layoutInCell:!0,verticalPosition:{},horizontalPosition:{}},a.floating);if(this.root.push(new _c({distT:o.margins&&o.margins.top||0,distB:o.margins&&o.margins.bottom||0,distL:o.margins&&o.margins.left||0,distR:o.margins&&o.margins.right||0,simplePos:"0",allowOverlap:o.allowOverlap===!0?"1":"0",behindDoc:o.behindDocument===!0?"1":"0",locked:o.lockAnchor===!0?"1":"0",layoutInCell:o.layoutInCell===!0?"1":"0",relativeHeight:o.zIndex?o.zIndex:t.emus.y})),this.root.push(Cl()),this.root.push(Nl(o.horizontalPosition)),this.root.push(Ol(o.verticalPosition)),this.root.push(lo({x:t.emus.x,y:t.emus.y})),this.root.push(uo({top:0,right:0,bottom:0,left:0})),a.floating!==void 0&&a.floating.wrap!==void 0)switch(a.floating.wrap.type){case Mt.SQUARE:this.root.push(new pc(a.floating.wrap,a.floating.margins));break;case Mt.TIGHT:this.root.push(new wc(a.floating.margins));break;case Mt.TOP_AND_BOTTOM:this.root.push(new yc(a.floating.margins));break;case Mt.NONE:default:this.root.push(new ua)}else this.root.push(new ua);this.root.push(new oo(a.docProperties)),this.root.push(co()),this.root.push(new ao({mediaData:e,transform:t,outline:a.outline}))}}const xc=({mediaData:r,transform:e,docProperties:t,outline:a})=>{var o,l,d,i;return new ge({name:"wp:inline",attributes:{distanceTop:{key:"distT",value:0},distanceBottom:{key:"distB",value:0},distanceLeft:{key:"distL",value:0},distanceRight:{key:"distR",value:0}},children:[lo({x:e.emus.x,y:e.emus.y}),uo(a?{top:((o=a.width)!=null?o:9525)*2,right:((l=a.width)!=null?l:9525)*2,bottom:((d=a.width)!=null?d:9525)*2,left:((i=a.width)!=null?i:9525)*2}:{top:0,right:0,bottom:0,left:0}),new oo(t),co(),new ao({mediaData:r,transform:e,outline:a})]})};class Ac extends ne{constructor(e,t={}){super("w:drawing"),t.floating?this.root.push(new Ec({mediaData:e,transform:e.transformation,drawingOptions:t})):this.root.push(xc({mediaData:e,transform:e.transformation,docProperties:t.docProperties,outline:t.outline}))}}const Tc=r=>{if(typeof atob=="function"){const e=";base64,",t=r.indexOf(e),a=t===-1?0:t+e.length;return new Uint8Array(atob(r.substring(a)).split("").map(o=>o.charCodeAt(0)))}else{const e=require("buffer");return new e.Buffer(r,"base64")}},Sc=r=>typeof r=="string"?Tc(r):r,Pn=(r,e)=>({data:Sc(r.data),fileName:e,transformation:{pixels:{x:Math.round(r.transformation.width),y:Math.round(r.transformation.height)},emus:{x:Math.round(r.transformation.width*9525),y:Math.round(r.transformation.height*9525)},flip:r.transformation.flip,rotation:r.transformation.rotation?r.transformation.rotation*6e4:void 0}});class Ed extends Tt{constructor(e){super({}),re(this,"imageData");const a=`${aa(e.data)}.${e.type}`;this.imageData=e.type==="svg"?je(me({type:e.type},Pn(e,a)),{fallback:me({type:e.fallback.type},Pn(je(me({},e.fallback),{transformation:e.transformation}),`${aa(e.fallback.data)}.${e.fallback.type}`))}):me({type:e.type},Pn(e,a));const o=new Ac(this.imageData,{floating:e.floating,docProperties:e.altText,outline:e.outline});this.root.push(o)}prepForXml(e){return e.file.Media.addImage(this.imageData.fileName,this.imageData),this.imageData.type==="svg"&&e.file.Media.addImage(this.imageData.fallback.fileName,this.imageData.fallback),super.prepForXml(e)}}class Rc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{xmlns:"xmlns"})}}class kc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{id:"Id",type:"Type",target:"Target",targetMode:"TargetMode"})}}const Ic={EXTERNAL:"External"};class Cc extends ne{constructor(e,t,a,o){super("Relationship"),this.root.push(new kc({id:e,type:t,target:a,targetMode:o}))}}class tt extends ne{constructor(){super("Relationships"),this.root.push(new Rc({xmlns:"http://schemas.openxmlformats.org/package/2006/relationships"}))}createRelationship(e,t,a,o){const l=new Cc(`rId${e}`,t,a,o);return this.root.push(l),l}get RelationshipCount(){return this.root.length-1}}class Nc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{id:"w:id",initials:"w:initials",author:"w:author",date:"w:date"})}}class Oc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{"xmlns:cx":"xmlns:cx","xmlns:cx1":"xmlns:cx1","xmlns:cx2":"xmlns:cx2","xmlns:cx3":"xmlns:cx3","xmlns:cx4":"xmlns:cx4","xmlns:cx5":"xmlns:cx5","xmlns:cx6":"xmlns:cx6","xmlns:cx7":"xmlns:cx7","xmlns:cx8":"xmlns:cx8","xmlns:mc":"xmlns:mc","xmlns:aink":"xmlns:aink","xmlns:am3d":"xmlns:am3d","xmlns:o":"xmlns:o","xmlns:r":"xmlns:r","xmlns:m":"xmlns:m","xmlns:v":"xmlns:v","xmlns:wp14":"xmlns:wp14","xmlns:wp":"xmlns:wp","xmlns:w10":"xmlns:w10","xmlns:w":"xmlns:w","xmlns:w14":"xmlns:w14","xmlns:w15":"xmlns:w15","xmlns:w16cex":"xmlns:w16cex","xmlns:w16cid":"xmlns:w16cid","xmlns:w16":"xmlns:w16","xmlns:w16sdtdh":"xmlns:w16sdtdh","xmlns:w16se":"xmlns:w16se","xmlns:wpg":"xmlns:wpg","xmlns:wpi":"xmlns:wpi","xmlns:wne":"xmlns:wne","xmlns:wps":"xmlns:wps"})}}class Dc extends ne{constructor({id:e,initials:t,author:a,date:o=new Date,children:l}){super("w:comment"),this.root.push(new Nc({id:e,initials:t,author:a,date:o.toISOString()}));for(const d of l)this.root.push(d)}}class Fc extends ne{constructor({children:e}){super("w:comments"),re(this,"relationships"),this.root.push(new Oc({"xmlns:cx":"http://schemas.microsoft.com/office/drawing/2014/chartex","xmlns:cx1":"http://schemas.microsoft.com/office/drawing/2015/9/8/chartex","xmlns:cx2":"http://schemas.microsoft.com/office/drawing/2015/10/21/chartex","xmlns:cx3":"http://schemas.microsoft.com/office/drawing/2016/5/9/chartex","xmlns:cx4":"http://schemas.microsoft.com/office/drawing/2016/5/10/chartex","xmlns:cx5":"http://schemas.microsoft.com/office/drawing/2016/5/11/chartex","xmlns:cx6":"http://schemas.microsoft.com/office/drawing/2016/5/12/chartex","xmlns:cx7":"http://schemas.microsoft.com/office/drawing/2016/5/13/chartex","xmlns:cx8":"http://schemas.microsoft.com/office/drawing/2016/5/14/chartex","xmlns:mc":"http://schemas.openxmlformats.org/markup-compatibility/2006","xmlns:aink":"http://schemas.microsoft.com/office/drawing/2016/ink","xmlns:am3d":"http://schemas.microsoft.com/office/drawing/2017/model3d","xmlns:o":"urn:schemas-microsoft-com:office:office","xmlns:r":"http://schemas.openxmlformats.org/officeDocument/2006/relationships","xmlns:m":"http://schemas.openxmlformats.org/officeDocument/2006/math","xmlns:v":"urn:schemas-microsoft-com:vml","xmlns:wp14":"http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing","xmlns:wp":"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing","xmlns:w10":"urn:schemas-microsoft-com:office:word","xmlns:w":"http://schemas.openxmlformats.org/wordprocessingml/2006/main","xmlns:w14":"http://schemas.microsoft.com/office/word/2010/wordml","xmlns:w15":"http://schemas.microsoft.com/office/word/2012/wordml","xmlns:w16cex":"http://schemas.microsoft.com/office/word/2018/wordml/cex","xmlns:w16cid":"http://schemas.microsoft.com/office/word/2016/wordml/cid","xmlns:w16":"http://schemas.microsoft.com/office/word/2018/wordml","xmlns:w16sdtdh":"http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash","xmlns:w16se":"http://schemas.microsoft.com/office/word/2015/wordml/symex","xmlns:wpg":"http://schemas.microsoft.com/office/word/2010/wordprocessingGroup","xmlns:wpi":"http://schemas.microsoft.com/office/word/2010/wordprocessingInk","xmlns:wne":"http://schemas.microsoft.com/office/word/2006/wordml","xmlns:wps":"http://schemas.microsoft.com/office/word/2010/wordprocessingShape"}));for(const t of e)this.root.push(new Dc(t));this.relationships=new tt}get Relationships(){return this.relationships}}class Pc extends ne{constructor(){super("w:pageBreakBefore")}}const Vn={AT_LEAST:"atLeast",EXACTLY:"exactly",EXACT:"exact",AUTO:"auto"};class Bc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{after:"w:after",before:"w:before",line:"w:line",lineRule:"w:lineRule",beforeAutoSpacing:"w:beforeAutospacing",afterAutoSpacing:"w:afterAutospacing"})}}class Lc extends ne{constructor(e){super("w:spacing"),this.root.push(new Bc(e))}}const xd={HEADING_1:"Heading1",HEADING_2:"Heading2",HEADING_3:"Heading3",HEADING_4:"Heading4",HEADING_5:"Heading5",HEADING_6:"Heading6",TITLE:"Title"};let Ut=class extends ne{constructor(e){super("w:pStyle"),this.root.push(new _e({val:e}))}};class Mc extends ne{constructor(e){super("w:tabs");for(const t of e)this.root.push(new Hc(t))}}const la={LEFT:"left",RIGHT:"right",CENTER:"center",BAR:"bar",CLEAR:"clear",DECIMAL:"decimal",END:"end",NUM:"num",START:"start"};class Uc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val",pos:"w:pos",leader:"w:leader"})}}class Hc extends ne{constructor({type:e,position:t,leader:a}){super("w:tab"),this.root.push(new Uc({val:e,pos:t,leader:a}))}}class Bn extends ne{constructor(e,t){super("w:numPr"),this.root.push(new jc(t)),this.root.push(new zc(e))}}class jc extends ne{constructor(e){if(super("w:ilvl"),e>9)throw new Error("Level cannot be greater than 9. Read more here: https://answers.microsoft.com/en-us/msoffice/forum/all/does-word-support-more-than-9-list-levels/d130fdcd-1781-446d-8c84-c6c79124e4d7");this.root.push(new _e({val:e}))}}class zc extends ne{constructor(e){super("w:numId"),this.root.push(new _e({val:typeof e=="string"?`{${e}}`:e}))}}class ho extends ne{constructor(){super(...arguments),re(this,"fileChild",Symbol())}}class Wc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{id:"r:id",history:"w:history",anchor:"w:anchor"})}}class hi extends ne{constructor(e,t,a){super("w:hyperlink"),re(this,"linkId"),this.linkId=t;const o={history:1,anchor:a||void 0,id:a?void 0:`rId${this.linkId}`},l=new Wc(o);this.root.push(l),e.forEach(d=>{this.root.push(d)})}}class qc extends ne{constructor(e){super("w:externalHyperlink"),this.options=e}}class Gc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{id:"w:id",name:"w:name"})}}class Kc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{id:"w:id"})}}class Vc{constructor(e){re(this,"bookmarkUniqueNumericId",Tl()),re(this,"start"),re(this,"children"),re(this,"end");const t=this.bookmarkUniqueNumericId();this.start=new $c(e.id,t),this.children=e.children,this.end=new Xc(t)}}class $c extends ne{constructor(e,t){super("w:bookmarkStart");const a=new Gc({name:e,id:t});this.root.push(a)}}class Xc extends ne{constructor(e){super("w:bookmarkEnd");const t=new Kc({id:e});this.root.push(t)}}class Zc extends ne{constructor(e){super("w:outlineLvl"),this.level=e,this.root.push(new _e({val:e}))}}const Yc={TOP:"top",CENTER:"center",BOTTOM:"bottom"};je(me({},Yc),{BOTH:"both"});class Jc extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{verticalAlign:"w:val"})}}class fo extends ne{constructor(e){super("w:vAlign"),this.root.push(new Jc({verticalAlign:e}))}}class Qc extends ne{constructor({space:e,count:t,separate:a,equalWidth:o,children:l}){super("w:cols"),this.root.push(new We({space:{key:"w:space",value:e===void 0?void 0:Ae(e)},count:{key:"w:num",value:t===void 0?void 0:Te(t)},separate:{key:"w:sep",value:a},equalWidth:{key:"w:equalWidth",value:o}})),!o&&l&&l.forEach(d=>this.addChildElement(d))}}const eh=({type:r,linePitch:e,charSpace:t})=>new ge({name:"w:docGrid",attributes:{type:{key:"w:type",value:r},linePitch:{key:"w:linePitch",value:Te(e)},charSpace:{key:"w:charSpace",value:t?Te(t):void 0}}}),at={DEFAULT:"default",FIRST:"first",EVEN:"even"};class th extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{type:"w:type",id:"r:id"})}}const ca={HEADER:"w:headerReference",FOOTER:"w:footerReference"};class Ln extends ne{constructor(e,t){super(e),this.root.push(new th({type:t.type||at.DEFAULT,id:`rId${t.id}`}))}}const rh=({countBy:r,start:e,restart:t,distance:a})=>new ge({name:"w:lnNumType",attributes:{countBy:{key:"w:countBy",value:r===void 0?void 0:Te(r)},start:{key:"w:start",value:e===void 0?void 0:Te(e)},restart:{key:"w:restart",value:t},distance:{key:"w:distance",value:a===void 0?void 0:Ae(a)}}});class ha extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{display:"w:display",offsetFrom:"w:offsetFrom",zOrder:"w:zOrder"})}}class nh extends ze{constructor(e){if(super("w:pgBorders"),!e)return this;e.pageBorders?this.root.push(new ha({display:e.pageBorders.display,offsetFrom:e.pageBorders.offsetFrom,zOrder:e.pageBorders.zOrder})):this.root.push(new ha({})),e.pageBorderTop&&this.root.push(new ve("w:top",e.pageBorderTop)),e.pageBorderLeft&&this.root.push(new ve("w:left",e.pageBorderLeft)),e.pageBorderBottom&&this.root.push(new ve("w:bottom",e.pageBorderBottom)),e.pageBorderRight&&this.root.push(new ve("w:right",e.pageBorderRight))}}class ih extends ne{constructor(e,t,a,o,l,d,i){super("w:pgMar"),this.root.push(new We({top:{key:"w:top",value:He(e)},right:{key:"w:right",value:Ae(t)},bottom:{key:"w:bottom",value:He(a)},left:{key:"w:left",value:Ae(o)},header:{key:"w:header",value:Ae(l)},footer:{key:"w:footer",value:Ae(d)},gutter:{key:"w:gutter",value:Ae(i)}}))}}class sh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{start:"w:start",formatType:"w:fmt",separator:"w:chapSep"})}}class ah extends ne{constructor({start:e,formatType:t,separator:a}){super("w:pgNumType"),this.root.push(new sh({start:e===void 0?void 0:Te(e),formatType:t,separator:a}))}}const $n={PORTRAIT:"portrait",LANDSCAPE:"landscape"},oh=({width:r,height:e,orientation:t,code:a})=>{const o=Ae(r),l=Ae(e);return new ge({name:"w:pgSz",attributes:{width:{key:"w:w",value:t===$n.LANDSCAPE?l:o},height:{key:"w:h",value:t===$n.LANDSCAPE?o:l},orientation:{key:"w:orient",value:t},code:{key:"w:code",value:a}}})};class uh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class lh extends ne{constructor(e){super("w:textDirection"),this.root.push(new uh({val:e}))}}class ch extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class hh extends ne{constructor(e){super("w:type"),this.root.push(new ch({val:e}))}}const Ze={TOP:1440,RIGHT:1440,BOTTOM:1440,LEFT:1440,HEADER:708,FOOTER:708,GUTTER:0},Mn={WIDTH:11906,HEIGHT:16838,ORIENTATION:$n.PORTRAIT};class fh extends ne{constructor({page:{size:{width:e=Mn.WIDTH,height:t=Mn.HEIGHT,orientation:a=Mn.ORIENTATION}={},margin:{top:o=Ze.TOP,right:l=Ze.RIGHT,bottom:d=Ze.BOTTOM,left:i=Ze.LEFT,header:h=Ze.HEADER,footer:x=Ze.FOOTER,gutter:A=Ze.GUTTER}={},pageNumbers:R={},borders:k,textDirection:y}={},grid:{linePitch:T=360,charSpace:b,type:I}={},headerWrapperGroup:u={},footerWrapperGroup:v={},lineNumbers:m,titlePage:c,verticalAlign:S,column:P,type:B}={}){super("w:sectPr"),this.addHeaderFooterGroup(ca.HEADER,u),this.addHeaderFooterGroup(ca.FOOTER,v),B&&this.root.push(new hh(B)),this.root.push(oh({width:e,height:t,orientation:a})),this.root.push(new ih(o,l,d,i,h,x,A)),k&&this.root.push(new nh(k)),m&&this.root.push(rh(m)),this.root.push(new ah(R)),P&&this.root.push(new Qc(P)),S&&this.root.push(new fo(S)),c!==void 0&&this.root.push(new ue("w:titlePg",c)),y&&this.root.push(new lh(y)),this.root.push(eh({linePitch:T,charSpace:b,type:I}))}addHeaderFooterGroup(e,t){t.default&&this.root.push(new Ln(e,{type:at.DEFAULT,id:t.default.View.ReferenceId})),t.first&&this.root.push(new Ln(e,{type:at.FIRST,id:t.first.View.ReferenceId})),t.even&&this.root.push(new Ln(e,{type:at.EVEN,id:t.even.View.ReferenceId}))}}class dh extends ne{constructor(){super("w:body"),re(this,"sections",[])}addSection(e){const t=this.sections.pop();this.root.push(this.createSectionParagraph(t)),this.sections.push(new fh(e))}prepForXml(e){return this.sections.length===1&&(this.root.splice(0,1),this.root.push(this.sections.pop())),super.prepForXml(e)}push(e){this.root.push(e)}createSectionParagraph(e){const t=new _t({}),a=new ht({});return a.push(e),t.addChildElement(a),t}}const fa={wpc:"http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas",mc:"http://schemas.openxmlformats.org/markup-compatibility/2006",o:"urn:schemas-microsoft-com:office:office",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",m:"http://schemas.openxmlformats.org/officeDocument/2006/math",v:"urn:schemas-microsoft-com:vml",wp14:"http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing",wp:"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing",w10:"urn:schemas-microsoft-com:office:word",w:"http://schemas.openxmlformats.org/wordprocessingml/2006/main",w14:"http://schemas.microsoft.com/office/word/2010/wordml",w15:"http://schemas.microsoft.com/office/word/2012/wordml",wpg:"http://schemas.microsoft.com/office/word/2010/wordprocessingGroup",wpi:"http://schemas.microsoft.com/office/word/2010/wordprocessingInk",wne:"http://schemas.microsoft.com/office/word/2006/wordml",wps:"http://schemas.microsoft.com/office/word/2010/wordprocessingShape",cp:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",xsi:"http://www.w3.org/2001/XMLSchema-instance",cx:"http://schemas.microsoft.com/office/drawing/2014/chartex",cx1:"http://schemas.microsoft.com/office/drawing/2015/9/8/chartex",cx2:"http://schemas.microsoft.com/office/drawing/2015/10/21/chartex",cx3:"http://schemas.microsoft.com/office/drawing/2016/5/9/chartex",cx4:"http://schemas.microsoft.com/office/drawing/2016/5/10/chartex",cx5:"http://schemas.microsoft.com/office/drawing/2016/5/11/chartex",cx6:"http://schemas.microsoft.com/office/drawing/2016/5/12/chartex",cx7:"http://schemas.microsoft.com/office/drawing/2016/5/13/chartex",cx8:"http://schemas.microsoft.com/office/drawing/2016/5/14/chartex",aink:"http://schemas.microsoft.com/office/drawing/2016/ink",am3d:"http://schemas.microsoft.com/office/drawing/2017/model3d",w16cex:"http://schemas.microsoft.com/office/word/2018/wordml/cex",w16cid:"http://schemas.microsoft.com/office/word/2016/wordml/cid",w16:"http://schemas.microsoft.com/office/word/2018/wordml",w16sdtdh:"http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash",w16se:"http://schemas.microsoft.com/office/word/2015/wordml/symex"};class Xt extends fe{constructor(e,t){super(me({Ignorable:t},Object.fromEntries(e.map(a=>[a,fa[a]])))),re(this,"xmlKeys",me({Ignorable:"mc:Ignorable"},Object.fromEntries(Object.keys(fa).map(a=>[a,`xmlns:${a}`]))))}}class ph extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{color:"w:color",themeColor:"w:themeColor",themeShade:"w:themeShade",themeTint:"w:themeTint"})}}class mh extends ne{constructor(e){super("w:background"),this.root.push(new ph({color:e.color===void 0?void 0:ut(e.color),themeColor:e.themeColor,themeShade:e.themeShade===void 0?void 0:Ws(e.themeShade),themeTint:e.themeTint===void 0?void 0:Ws(e.themeTint)}))}}class wh extends ne{constructor(e){super("w:document"),re(this,"body"),this.root.push(new Xt(["wpc","mc","o","r","m","v","wp14","wp","w10","w","w14","w15","wpg","wpi","wne","wps","cx","cx1","cx2","cx3","cx4","cx5","cx6","cx7","cx8","aink","am3d","w16cex","w16cid","w16","w16sdtdh","w16se"],"w14 w15 wp14")),this.body=new dh,e.background&&this.root.push(new mh(e.background)),this.root.push(this.body)}add(e){return this.body.push(e),this}get Body(){return this.body}}class po{constructor(e){re(this,"document"),re(this,"relationships"),this.document=new wh(e),this.relationships=new tt}get View(){return this.document}get Relationships(){return this.relationships}}class gh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class yh extends ne{constructor(){super("w:wordWrap"),this.root.push(new gh({val:0}))}}const vh=r=>{var e,t;return new ge({name:"w:framePr",attributes:{anchorLock:{key:"w:anchorLock",value:r.anchorLock},dropCap:{key:"w:dropCap",value:r.dropCap},width:{key:"w:w",value:r.width},height:{key:"w:h",value:r.height},x:{key:"w:x",value:r.position?r.position.x:void 0},y:{key:"w:y",value:r.position?r.position.y:void 0},anchorHorizontal:{key:"w:hAnchor",value:r.anchor.horizontal},anchorVertical:{key:"w:vAnchor",value:r.anchor.vertical},spaceHorizontal:{key:"w:hSpace",value:(e=r.space)==null?void 0:e.horizontal},spaceVertical:{key:"w:vSpace",value:(t=r.space)==null?void 0:t.vertical},rule:{key:"w:hRule",value:r.rule},alignmentX:{key:"w:xAlign",value:r.alignment?r.alignment.x:void 0},alignmentY:{key:"w:yAlign",value:r.alignment?r.alignment.y:void 0},lines:{key:"w:lines",value:r.lines},wrap:{key:"w:wrap",value:r.wrap}}})};class ht extends ze{constructor(e){var t,a;if(super("w:pPr"),re(this,"numberingReferences",[]),!e)return this;e.heading&&this.push(new Ut(e.heading)),e.bullet&&this.push(new Ut("ListParagraph")),e.numbering&&!e.style&&!e.heading&&(e.numbering.custom||this.push(new Ut("ListParagraph"))),e.style&&this.push(new Ut(e.style)),e.keepNext!==void 0&&this.push(new ue("w:keepNext",e.keepNext)),e.keepLines!==void 0&&this.push(new ue("w:keepLines",e.keepLines)),e.pageBreakBefore&&this.push(new Pc),e.frame&&this.push(vh(e.frame)),e.widowControl!==void 0&&this.push(new ue("w:widowControl",e.widowControl)),e.bullet&&this.push(new Bn(1,e.bullet.level)),e.numbering?(this.numberingReferences.push({reference:e.numbering.reference,instance:(t=e.numbering.instance)!=null?t:0}),this.push(new Bn(`${e.numbering.reference}-${(a=e.numbering.instance)!=null?a:0}`,e.numbering.level))):e.numbering===!1&&this.push(new Bn(0,0)),e.border&&this.push(new zu(e.border)),e.thematicBreak&&this.push(new Wu),e.shading&&this.push(new Vt(e.shading)),e.wordWrap&&this.push(new yh),e.overflowPunctuation&&this.push(new ue("w:overflowPunct",e.overflowPunctuation));const o=[...e.rightTabStop!==void 0?[{type:la.RIGHT,position:e.rightTabStop}]:[],...e.tabStops?e.tabStops:[],...e.leftTabStop!==void 0?[{type:la.LEFT,position:e.leftTabStop}]:[]];o.length>0&&this.push(new Mc(o)),e.bidirectional!==void 0&&this.push(new ue("w:bidi",e.bidirectional)),e.spacing&&this.push(new Lc(e.spacing)),e.indent&&this.push(new qu(e.indent)),e.contextualSpacing!==void 0&&this.push(new ue("w:contextualSpacing",e.contextualSpacing)),e.alignment&&this.push(new Za(e.alignment)),e.outlineLevel!==void 0&&this.push(new Zc(e.outlineLevel)),e.suppressLineNumbers!==void 0&&this.push(new ue("w:suppressLineNumbers",e.suppressLineNumbers)),e.autoSpaceEastAsianText!==void 0&&this.push(new ue("w:autoSpaceDN",e.autoSpaceEastAsianText)),e.run&&this.push(new et(e.run))}push(e){this.root.push(e)}prepForXml(e){if(e.viewWrapper instanceof po)for(const t of this.numberingReferences)e.file.Numbering.createConcreteNumberingInstance(t.reference,t.instance);return super.prepForXml(e)}}class _t extends ho{constructor(e){if(super("w:p"),re(this,"properties"),typeof e=="string")return this.properties=new ht({}),this.root.push(this.properties),this.root.push(new Ks(e)),this;if(this.properties=new ht(e),this.root.push(this.properties),e.text&&this.root.push(new Ks(e.text)),e.children)for(const t of e.children){if(t instanceof Vc){this.root.push(t.start);for(const a of t.children)this.root.push(a);this.root.push(t.end);continue}this.root.push(t)}}prepForXml(e){for(const t of this.root)if(t instanceof qc){const a=this.root.indexOf(t),o=new hi(t.options.children,Sl());e.viewWrapper.Relationships.createRelationship(o.linkId,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",t.options.link,Ic.EXTERNAL),this.root[a]=o}return super.prepForXml(e)}addRunToFront(e){return this.root.splice(1,0,e),this}}class bh extends ne{constructor(e){super("w:tblGrid");for(const t of e)this.root.push(new _h(t))}}class _h extends ne{constructor(e){super("w:gridCol"),e!==void 0&&this.root.push(new We({width:{key:"w:w",value:Ae(e)}}))}}const mo={TABLE:"w:tblCellMar",TABLE_CELL:"w:tcMar"};class wo extends ze{constructor(e,{marginUnitType:t=Xn.DXA,top:a,left:o,bottom:l,right:d}){super(e),a!==void 0&&this.root.push(new Je("w:top",{type:t,size:a})),o!==void 0&&this.root.push(new Je("w:left",{type:t,size:o})),l!==void 0&&this.root.push(new Je("w:bottom",{type:t,size:l})),d!==void 0&&this.root.push(new Je("w:right",{type:t,size:d}))}}const Xn={AUTO:"auto",DXA:"dxa",NIL:"nil",PERCENTAGE:"pct"};class Je extends ne{constructor(e,{type:t=Xn.AUTO,size:a}){super(e);let o=a;t===Xn.PERCENTAGE&&typeof a=="number"&&(o=`${a}%`),this.root.push(new We({type:{key:"w:type",value:t},size:{key:"w:w",value:Xa(o)}}))}}class Eh extends ze{constructor(e){super("w:tcBorders"),e.top&&this.root.push(new ve("w:top",e.top)),e.start&&this.root.push(new ve("w:start",e.start)),e.left&&this.root.push(new ve("w:left",e.left)),e.bottom&&this.root.push(new ve("w:bottom",e.bottom)),e.end&&this.root.push(new ve("w:end",e.end)),e.right&&this.root.push(new ve("w:right",e.right))}}class xh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class Ah extends ne{constructor(e){super("w:gridSpan"),this.root.push(new xh({val:Te(e)}))}}const go={CONTINUE:"continue",RESTART:"restart"};class Th extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class da extends ne{constructor(e){super("w:vMerge"),this.root.push(new Th({val:e}))}}class Sh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class Rh extends ne{constructor(e){super("w:textDirection"),this.root.push(new Sh({val:e}))}}class kh extends ze{constructor(e){super("w:tcPr"),e.width&&this.root.push(new Je("w:tcW",e.width)),e.columnSpan&&this.root.push(new Ah(e.columnSpan)),e.verticalMerge?this.root.push(new da(e.verticalMerge)):e.rowSpan&&e.rowSpan>1&&this.root.push(new da(go.RESTART)),e.borders&&this.root.push(new Eh(e.borders)),e.shading&&this.root.push(new Vt(e.shading)),e.margins&&this.root.push(new wo(mo.TABLE_CELL,e.margins)),e.textDirection&&this.root.push(new Rh(e.textDirection)),e.verticalAlign&&this.root.push(new fo(e.verticalAlign))}}class yo extends ne{constructor(e){super("w:tc"),this.options=e,this.root.push(new kh(e));for(const t of e.children)this.root.push(t)}prepForXml(e){return this.root[this.root.length-1]instanceof _t||this.root.push(new _t({})),super.prepForXml(e)}}const rt={style:ui.NONE,size:0,color:"auto"},nt={style:ui.SINGLE,size:4,color:"auto"};class vo extends ne{constructor(e){super("w:tblBorders"),e.top?this.root.push(new ve("w:top",e.top)):this.root.push(new ve("w:top",nt)),e.left?this.root.push(new ve("w:left",e.left)):this.root.push(new ve("w:left",nt)),e.bottom?this.root.push(new ve("w:bottom",e.bottom)):this.root.push(new ve("w:bottom",nt)),e.right?this.root.push(new ve("w:right",e.right)):this.root.push(new ve("w:right",nt)),e.insideHorizontal?this.root.push(new ve("w:insideH",e.insideHorizontal)):this.root.push(new ve("w:insideH",nt)),e.insideVertical?this.root.push(new ve("w:insideV",e.insideVertical)):this.root.push(new ve("w:insideV",nt))}}re(vo,"NONE",{top:rt,bottom:rt,left:rt,right:rt,insideHorizontal:rt,insideVertical:rt});class Ih extends ne{constructor({horizontalAnchor:e,verticalAnchor:t,absoluteHorizontalPosition:a,relativeHorizontalPosition:o,absoluteVerticalPosition:l,relativeVerticalPosition:d,bottomFromText:i,topFromText:h,leftFromText:x,rightFromText:A,overlap:R}){super("w:tblpPr"),this.root.push(new We({leftFromText:{key:"w:leftFromText",value:x===void 0?void 0:Ae(x)},rightFromText:{key:"w:rightFromText",value:A===void 0?void 0:Ae(A)},topFromText:{key:"w:topFromText",value:h===void 0?void 0:Ae(h)},bottomFromText:{key:"w:bottomFromText",value:i===void 0?void 0:Ae(i)},absoluteHorizontalPosition:{key:"w:tblpX",value:a===void 0?void 0:He(a)},absoluteVerticalPosition:{key:"w:tblpY",value:l===void 0?void 0:He(l)},horizontalAnchor:{key:"w:horzAnchor",value:e===void 0?void 0:e},relativeHorizontalPosition:{key:"w:tblpXSpec",value:o},relativeVerticalPosition:{key:"w:tblpYSpec",value:d},verticalAnchor:{key:"w:vertAnchor",value:t}})),R&&this.root.push(new Uu("w:tblOverlap",R))}}class Ch extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{type:"w:type"})}}class Nh extends ne{constructor(e){super("w:tblLayout"),this.root.push(new Ch({type:e}))}}const Oh={DXA:"dxa"};class bo extends ne{constructor({type:e=Oh.DXA,value:t}){super("w:tblCellSpacing"),this.root.push(new We({type:{key:"w:type",value:e},value:{key:"w:w",value:Xa(t)}}))}}class Dh extends ze{constructor(e){super("w:tblPr"),e.style&&this.root.push(new Qe("w:tblStyle",e.style)),e.float&&this.root.push(new Ih(e.float)),e.visuallyRightToLeft!==void 0&&this.root.push(new ue("w:bidiVisual",e.visuallyRightToLeft)),e.width&&this.root.push(new Je("w:tblW",e.width)),e.alignment&&this.root.push(new Za(e.alignment)),e.indent&&this.root.push(new Je("w:tblInd",e.indent)),e.borders&&this.root.push(new vo(e.borders)),e.shading&&this.root.push(new Vt(e.shading)),e.layout&&this.root.push(new Nh(e.layout)),e.cellMargin&&this.root.push(new wo(mo.TABLE,e.cellMargin)),e.cellSpacing&&this.root.push(new bo(e.cellSpacing))}}class Td extends ho{constructor({rows:e,width:t,columnWidths:a=Array(Math.max(...e.map(y=>y.CellCount))).fill(100),margins:o,indent:l,float:d,layout:i,style:h,borders:x,alignment:A,visuallyRightToLeft:R,cellSpacing:k}){super("w:tbl"),this.root.push(new Dh({borders:x??{},width:t??{size:100},indent:l,float:d,layout:i,style:h,alignment:A,cellMargin:o,visuallyRightToLeft:R,cellSpacing:k})),this.root.push(new bh(a));for(const y of e)this.root.push(y);e.forEach((y,T)=>{if(T===e.length-1)return;let b=0;y.cells.forEach(I=>{if(I.options.rowSpan&&I.options.rowSpan>1){const u=new yo({rowSpan:I.options.rowSpan-1,columnSpan:I.options.columnSpan,borders:I.options.borders,children:[],verticalMerge:go.CONTINUE});e[T+1].addCellToColumnIndex(u,b)}b+=I.options.columnSpan||1})})}}class Fh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{value:"w:val",rule:"w:hRule"})}}class Ph extends ne{constructor(e,t){super("w:trHeight"),this.root.push(new Fh({value:Ae(e),rule:t}))}}class Bh extends ze{constructor(e){super("w:trPr"),e.cantSplit!==void 0&&this.root.push(new ue("w:cantSplit",e.cantSplit)),e.tableHeader!==void 0&&this.root.push(new ue("w:tblHeader",e.tableHeader)),e.height&&this.root.push(new Ph(e.height.value,e.height.rule)),e.cellSpacing&&this.root.push(new bo(e.cellSpacing))}}class Sd extends ne{constructor(e){super("w:tr"),this.options=e,this.root.push(new Bh(e));for(const t of e.children)this.root.push(t)}get CellCount(){return this.options.children.length}get cells(){return this.root.filter(e=>e instanceof yo)}addCellToIndex(e,t){this.root.splice(t+1,0,e)}addCellToColumnIndex(e,t){const a=this.columnIndexToRootIndex(t,!0);this.addCellToIndex(e,a-1)}rootIndexToColumnIndex(e){if(e<1||e>=this.root.length)throw new Error(`cell 'rootIndex' should between 1 to ${this.root.length-1}`);let t=0;for(let a=1;a<e;a++){const o=this.root[a];t+=o.options.columnSpan||1}return t}columnIndexToRootIndex(e,t=!1){if(e<0)throw new Error("cell 'columnIndex' should not less than zero");let a=0,o=1;for(;a<=e;){if(o>=this.root.length){if(t)return this.root.length;throw new Error(`cell 'columnIndex' should not great than ${a-1}`)}const l=this.root[o];o+=1,a+=l&&l.options.columnSpan||1}return o-1}}class Lh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{xmlns:"xmlns",vt:"xmlns:vt"})}}class Mh extends ne{constructor(){super("Properties"),this.root.push(new Lh({xmlns:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes"}))}}class Uh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{xmlns:"xmlns"})}}class Hh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{contentType:"ContentType",extension:"Extension"})}}class Le extends ne{constructor(e,t){super("Default"),this.root.push(new Hh({contentType:e,extension:t}))}}class jh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{contentType:"ContentType",partName:"PartName"})}}class Re extends ne{constructor(e,t){super("Override"),this.root.push(new jh({contentType:e,partName:t}))}}class zh extends ne{constructor(){super("Types"),this.root.push(new Uh({xmlns:"http://schemas.openxmlformats.org/package/2006/content-types"})),this.root.push(new Le("image/png","png")),this.root.push(new Le("image/jpeg","jpeg")),this.root.push(new Le("image/jpeg","jpg")),this.root.push(new Le("image/bmp","bmp")),this.root.push(new Le("image/gif","gif")),this.root.push(new Le("image/svg+xml","svg")),this.root.push(new Le("application/vnd.openxmlformats-package.relationships+xml","rels")),this.root.push(new Le("application/xml","xml")),this.root.push(new Le("application/vnd.openxmlformats-officedocument.obfuscatedFont","odttf")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml","/word/document.xml")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml","/word/styles.xml")),this.root.push(new Re("application/vnd.openxmlformats-package.core-properties+xml","/docProps/core.xml")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.custom-properties+xml","/docProps/custom.xml")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.extended-properties+xml","/docProps/app.xml")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml","/word/numbering.xml")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml","/word/footnotes.xml")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml","/word/settings.xml")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml","/word/comments.xml")),this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.fontTable+xml","/word/fontTable.xml"))}addFooter(e){this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml",`/word/footer${e}.xml`))}addHeader(e){this.root.push(new Re("application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml",`/word/header${e}.xml`))}}class Wh extends ne{constructor(e){super("cp:coreProperties"),this.root.push(new Xt(["cp","dc","dcterms","dcmitype","xsi"])),e.title&&this.root.push(new $e("dc:title",e.title)),e.subject&&this.root.push(new $e("dc:subject",e.subject)),e.creator&&this.root.push(new $e("dc:creator",e.creator)),e.keywords&&this.root.push(new $e("cp:keywords",e.keywords)),e.description&&this.root.push(new $e("dc:description",e.description)),e.lastModifiedBy&&this.root.push(new $e("cp:lastModifiedBy",e.lastModifiedBy)),e.revision&&this.root.push(new $e("cp:revision",String(e.revision))),this.root.push(new pa("dcterms:created")),this.root.push(new pa("dcterms:modified"))}}class qh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{type:"xsi:type"})}}class pa extends ne{constructor(e){super(e),this.root.push(new qh({type:"dcterms:W3CDTF"})),this.root.push(Mu(new Date))}}class Gh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{xmlns:"xmlns",vt:"xmlns:vt"})}}class Kh extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{fmtid:"fmtid",pid:"pid",name:"name"})}}class Vh extends ne{constructor(e,t){super("property"),this.root.push(new Kh({fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:e.toString(),name:t.name})),this.root.push(new $h(t.value))}}class $h extends ne{constructor(e){super("vt:lpwstr"),this.root.push(e)}}class Xh extends ne{constructor(e){super("Properties"),re(this,"nextId"),re(this,"properties",[]),this.root.push(new Gh({xmlns:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes"})),this.nextId=2;for(const t of e)this.addCustomProperty(t)}prepForXml(e){return this.properties.forEach(t=>this.root.push(t)),super.prepForXml(e)}addCustomProperty(e){this.properties.push(new Vh(this.nextId++,e))}}const Ht=({id:r,fontKey:e,subsetted:t},a)=>new ge({name:a,attributes:me({id:{key:"r:id",value:r}},e?{fontKey:{key:"w:fontKey",value:`{${e}}`}}:{}),children:[...t?[new ue("w:subsetted",t)]:[]]}),Zh=({name:r,altName:e,panose1:t,charset:a,family:o,notTrueType:l,pitch:d,sig:i,embedRegular:h,embedBold:x,embedItalic:A,embedBoldItalic:R})=>new ge({name:"w:font",attributes:{name:{key:"w:name",value:r}},children:[...e?[wt("w:altName",e)]:[],...t?[wt("w:panose1",t)]:[],...a?[wt("w:charset",a)]:[],wt("w:family",o),...l?[new ue("w:notTrueType",l)]:[],wt("w:pitch",d),...i?[new ge({name:"w:sig",attributes:{usb0:{key:"w:usb0",value:i.usb0},usb1:{key:"w:usb1",value:i.usb1},usb2:{key:"w:usb2",value:i.usb2},usb3:{key:"w:usb3",value:i.usb3},csb0:{key:"w:csb0",value:i.csb0},csb1:{key:"w:csb1",value:i.csb1}}})]:[],...h?[Ht(h,"w:embedRegular")]:[],...x?[Ht(x,"w:embedBold")]:[],...A?[Ht(A,"w:embedItalic")]:[],...R?[Ht(R,"w:embedBoldItalic")]:[]]}),Yh=({name:r,index:e,fontKey:t,characterSet:a})=>Zh({name:r,sig:{usb0:"E0002AFF",usb1:"C000247B",usb2:"00000009",usb3:"00000000",csb0:"000001FF",csb1:"00000000"},charset:a,family:"auto",pitch:"variable",embedRegular:{fontKey:t,id:`rId${e}`}}),Jh=r=>new ge({name:"w:fonts",attributes:{mc:{key:"xmlns:mc",value:"http://schemas.openxmlformats.org/markup-compatibility/2006"},r:{key:"xmlns:r",value:"http://schemas.openxmlformats.org/officeDocument/2006/relationships"},w:{key:"xmlns:w",value:"http://schemas.openxmlformats.org/wordprocessingml/2006/main"},w14:{key:"xmlns:w14",value:"http://schemas.microsoft.com/office/word/2010/wordml"},w15:{key:"xmlns:w15",value:"http://schemas.microsoft.com/office/word/2012/wordml"},w16cex:{key:"xmlns:w16cex",value:"http://schemas.microsoft.com/office/word/2018/wordml/cex"},w16cid:{key:"xmlns:w16cid",value:"http://schemas.microsoft.com/office/word/2016/wordml/cid"},w16:{key:"xmlns:w16",value:"http://schemas.microsoft.com/office/word/2018/wordml"},w16sdtdh:{key:"xmlns:w16sdtdh",value:"http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash"},w16se:{key:"xmlns:w16se",value:"http://schemas.microsoft.com/office/word/2015/wordml/symex"},Ignorable:{key:"mc:Ignorable",value:"w14 w15 w16se w16cid w16 w16cex w16sdtdh"}},children:r.map((e,t)=>Yh({name:e.name,index:t+1,fontKey:e.fontKey}))});class Qh{constructor(e){re(this,"fontTable"),re(this,"relationships"),re(this,"fontOptionsWithKey",[]),this.options=e,this.fontOptionsWithKey=e.map(t=>je(me({},t),{fontKey:Rl()})),this.fontTable=Jh(this.fontOptionsWithKey),this.relationships=new tt;for(let t=0;t<e.length;t++)this.relationships.createRelationship(t+1,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/font",`fonts/${e[t].name}.odttf`)}get View(){return this.fontTable}get Relationships(){return this.relationships}}class ef extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{wpc:"xmlns:wpc",mc:"xmlns:mc",o:"xmlns:o",r:"xmlns:r",m:"xmlns:m",v:"xmlns:v",wp14:"xmlns:wp14",wp:"xmlns:wp",w10:"xmlns:w10",w:"xmlns:w",w14:"xmlns:w14",w15:"xmlns:w15",wpg:"xmlns:wpg",wpi:"xmlns:wpi",wne:"xmlns:wne",wps:"xmlns:wps",cp:"xmlns:cp",dc:"xmlns:dc",dcterms:"xmlns:dcterms",dcmitype:"xmlns:dcmitype",xsi:"xmlns:xsi",type:"xsi:type"})}}let tf=class extends Ka{constructor(e,t){super("w:ftr",t),re(this,"refId"),this.refId=e,t||this.root.push(new ef({wpc:"http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas",mc:"http://schemas.openxmlformats.org/markup-compatibility/2006",o:"urn:schemas-microsoft-com:office:office",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",m:"http://schemas.openxmlformats.org/officeDocument/2006/math",v:"urn:schemas-microsoft-com:vml",wp14:"http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing",wp:"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing",w10:"urn:schemas-microsoft-com:office:word",w:"http://schemas.openxmlformats.org/wordprocessingml/2006/main",w14:"http://schemas.microsoft.com/office/word/2010/wordml",w15:"http://schemas.microsoft.com/office/word/2012/wordml",wpg:"http://schemas.microsoft.com/office/word/2010/wordprocessingGroup",wpi:"http://schemas.microsoft.com/office/word/2010/wordprocessingInk",wne:"http://schemas.microsoft.com/office/word/2006/wordml",wps:"http://schemas.microsoft.com/office/word/2010/wordprocessingShape"}))}get ReferenceId(){return this.refId}add(e){this.root.push(e)}};class rf{constructor(e,t,a){re(this,"footer"),re(this,"relationships"),this.media=e,this.footer=new tf(t,a),this.relationships=new tt}add(e){this.footer.add(e)}addChildElement(e){this.footer.addChildElement(e)}get View(){return this.footer}get Relationships(){return this.relationships}get Media(){return this.media}}class nf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{type:"w:type",id:"w:id"})}}class sf extends ne{constructor(){super("w:footnoteRef")}}class af extends Tt{constructor(){super({style:"FootnoteReference"}),this.root.push(new sf)}}const ma={SEPERATOR:"separator",CONTINUATION_SEPERATOR:"continuationSeparator"};class Un extends ne{constructor(e){super("w:footnote"),this.root.push(new nf({type:e.type,id:e.id}));for(let t=0;t<e.children.length;t++){const a=e.children[t];t===0&&a.addRunToFront(new af),this.root.push(a)}}}class of extends ne{constructor(){super("w:continuationSeparator")}}class uf extends Tt{constructor(){super({}),this.root.push(new of)}}class lf extends ne{constructor(){super("w:separator")}}class cf extends Tt{constructor(){super({}),this.root.push(new lf)}}class hf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{wpc:"xmlns:wpc",mc:"xmlns:mc",o:"xmlns:o",r:"xmlns:r",m:"xmlns:m",v:"xmlns:v",wp14:"xmlns:wp14",wp:"xmlns:wp",w10:"xmlns:w10",w:"xmlns:w",w14:"xmlns:w14",w15:"xmlns:w15",wpg:"xmlns:wpg",wpi:"xmlns:wpi",wne:"xmlns:wne",wps:"xmlns:wps",Ignorable:"mc:Ignorable"})}}class ff extends ne{constructor(){super("w:footnotes"),this.root.push(new hf({wpc:"http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas",mc:"http://schemas.openxmlformats.org/markup-compatibility/2006",o:"urn:schemas-microsoft-com:office:office",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",m:"http://schemas.openxmlformats.org/officeDocument/2006/math",v:"urn:schemas-microsoft-com:vml",wp14:"http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing",wp:"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing",w10:"urn:schemas-microsoft-com:office:word",w:"http://schemas.openxmlformats.org/wordprocessingml/2006/main",w14:"http://schemas.microsoft.com/office/word/2010/wordml",w15:"http://schemas.microsoft.com/office/word/2012/wordml",wpg:"http://schemas.microsoft.com/office/word/2010/wordprocessingGroup",wpi:"http://schemas.microsoft.com/office/word/2010/wordprocessingInk",wne:"http://schemas.microsoft.com/office/word/2006/wordml",wps:"http://schemas.microsoft.com/office/word/2010/wordprocessingShape",Ignorable:"w14 w15 wp14"}));const e=new Un({id:-1,type:ma.SEPERATOR,children:[new _t({spacing:{after:0,line:240,lineRule:Vn.AUTO},children:[new cf]})]});this.root.push(e);const t=new Un({id:0,type:ma.CONTINUATION_SEPERATOR,children:[new _t({spacing:{after:0,line:240,lineRule:Vn.AUTO},children:[new uf]})]});this.root.push(t)}createFootNote(e,t){const a=new Un({id:e,children:t});this.root.push(a)}}class df{constructor(){re(this,"footnotess"),re(this,"relationships"),this.footnotess=new ff,this.relationships=new tt}get View(){return this.footnotess}get Relationships(){return this.relationships}}class pf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{wpc:"xmlns:wpc",mc:"xmlns:mc",o:"xmlns:o",r:"xmlns:r",m:"xmlns:m",v:"xmlns:v",wp14:"xmlns:wp14",wp:"xmlns:wp",w10:"xmlns:w10",w:"xmlns:w",w14:"xmlns:w14",w15:"xmlns:w15",wpg:"xmlns:wpg",wpi:"xmlns:wpi",wne:"xmlns:wne",wps:"xmlns:wps",cp:"xmlns:cp",dc:"xmlns:dc",dcterms:"xmlns:dcterms",dcmitype:"xmlns:dcmitype",xsi:"xmlns:xsi",type:"xsi:type",cx:"xmlns:cx",cx1:"xmlns:cx1",cx2:"xmlns:cx2",cx3:"xmlns:cx3",cx4:"xmlns:cx4",cx5:"xmlns:cx5",cx6:"xmlns:cx6",cx7:"xmlns:cx7",cx8:"xmlns:cx8",w16cid:"xmlns:w16cid",w16se:"xmlns:w16se"})}}let mf=class extends Ka{constructor(e,t){super("w:hdr",t),re(this,"refId"),this.refId=e,t||this.root.push(new pf({wpc:"http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas",mc:"http://schemas.openxmlformats.org/markup-compatibility/2006",o:"urn:schemas-microsoft-com:office:office",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",m:"http://schemas.openxmlformats.org/officeDocument/2006/math",v:"urn:schemas-microsoft-com:vml",wp14:"http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing",wp:"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing",w10:"urn:schemas-microsoft-com:office:word",w:"http://schemas.openxmlformats.org/wordprocessingml/2006/main",w14:"http://schemas.microsoft.com/office/word/2010/wordml",w15:"http://schemas.microsoft.com/office/word/2012/wordml",wpg:"http://schemas.microsoft.com/office/word/2010/wordprocessingGroup",wpi:"http://schemas.microsoft.com/office/word/2010/wordprocessingInk",wne:"http://schemas.microsoft.com/office/word/2006/wordml",wps:"http://schemas.microsoft.com/office/word/2010/wordprocessingShape",cx:"http://schemas.microsoft.com/office/drawing/2014/chartex",cx1:"http://schemas.microsoft.com/office/drawing/2015/9/8/chartex",cx2:"http://schemas.microsoft.com/office/drawing/2015/10/21/chartex",cx3:"http://schemas.microsoft.com/office/drawing/2016/5/9/chartex",cx4:"http://schemas.microsoft.com/office/drawing/2016/5/10/chartex",cx5:"http://schemas.microsoft.com/office/drawing/2016/5/11/chartex",cx6:"http://schemas.microsoft.com/office/drawing/2016/5/12/chartex",cx7:"http://schemas.microsoft.com/office/drawing/2016/5/13/chartex",cx8:"http://schemas.microsoft.com/office/drawing/2016/5/14/chartex",w16cid:"http://schemas.microsoft.com/office/word/2016/wordml/cid",w16se:"http://schemas.microsoft.com/office/word/2015/wordml/symex"}))}get ReferenceId(){return this.refId}add(e){this.root.push(e)}};class wf{constructor(e,t,a){re(this,"header"),re(this,"relationships"),this.media=e,this.header=new mf(t,a),this.relationships=new tt}add(e){return this.header.add(e),this}addChildElement(e){this.header.addChildElement(e)}get View(){return this.header}get Relationships(){return this.relationships}get Media(){return this.media}}class gf{constructor(){re(this,"map"),this.map=new Map}addImage(e,t){this.map.set(e,t)}get Array(){return Array.from(this.map.values())}}const Me={DECIMAL:"decimal",UPPER_ROMAN:"upperRoman",LOWER_ROMAN:"lowerRoman",UPPER_LETTER:"upperLetter",LOWER_LETTER:"lowerLetter",ORDINAL:"ordinal",CARDINAL_TEXT:"cardinalText",ORDINAL_TEXT:"ordinalText",HEX:"hex",CHICAGO:"chicago",IDEOGRAPH__DIGITAL:"ideographDigital",JAPANESE_COUNTING:"japaneseCounting",AIUEO:"aiueo",IROHA:"iroha",DECIMAL_FULL_WIDTH:"decimalFullWidth",DECIMAL_HALF_WIDTH:"decimalHalfWidth",JAPANESE_LEGAL:"japaneseLegal",JAPANESE_DIGITAL_TEN_THOUSAND:"japaneseDigitalTenThousand",DECIMAL_ENCLOSED_CIRCLE:"decimalEnclosedCircle",DECIMAL_FULL_WIDTH2:"decimalFullWidth2",AIUEO_FULL_WIDTH:"aiueoFullWidth",IROHA_FULL_WIDTH:"irohaFullWidth",DECIMAL_ZERO:"decimalZero",BULLET:"bullet",GANADA:"ganada",CHOSUNG:"chosung",DECIMAL_ENCLOSED_FULLSTOP:"decimalEnclosedFullstop",DECIMAL_ENCLOSED_PARENTHESES:"decimalEnclosedParen",DECIMAL_ENCLOSED_CIRCLE_CHINESE:"decimalEnclosedCircleChinese",IDEOGRAPH_ENCLOSED_CIRCLE:"ideographEnclosedCircle",IDEOGRAPH_TRADITIONAL:"ideographTraditional",IDEOGRAPH_ZODIAC:"ideographZodiac",IDEOGRAPH_ZODIAC_TRADITIONAL:"ideographZodiacTraditional",TAIWANESE_COUNTING:"taiwaneseCounting",IDEOGRAPH_LEGAL_TRADITIONAL:"ideographLegalTraditional",TAIWANESE_COUNTING_THOUSAND:"taiwaneseCountingThousand",TAIWANESE_DIGITAL:"taiwaneseDigital",CHINESE_COUNTING:"chineseCounting",CHINESE_LEGAL_SIMPLIFIED:"chineseLegalSimplified",CHINESE_COUNTING_THOUSAND:"chineseCountingThousand",KOREAN_DIGITAL:"koreanDigital",KOREAN_COUNTING:"koreanCounting",KOREAN_LEGAL:"koreanLegal",KOREAN_DIGITAL2:"koreanDigital2",VIETNAMESE_COUNTING:"vietnameseCounting",RUSSIAN_LOWER:"russianLower",RUSSIAN_UPPER:"russianUpper",NONE:"none",NUMBER_IN_DASH:"numberInDash",HEBREW1:"hebrew1",HEBREW2:"hebrew2",ARABIC_ALPHA:"arabicAlpha",ARABIC_ABJAD:"arabicAbjad",HINDI_VOWELS:"hindiVowels",HINDI_CONSONANTS:"hindiConsonants",HINDI_NUMBERS:"hindiNumbers",HINDI_COUNTING:"hindiCounting",THAI_LETTERS:"thaiLetters",THAI_NUMBERS:"thaiNumbers",THAI_COUNTING:"thaiCounting",BAHT_TEXT:"bahtText",DOLLAR_TEXT:"dollarText",CUSTOM:"custom"};class yf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{ilvl:"w:ilvl",tentative:"w15:tentative"})}}class vf extends ne{constructor(e){super("w:numFmt"),this.root.push(new _e({val:e}))}}class bf extends ne{constructor(e){super("w:lvlText"),this.root.push(new _e({val:e}))}}class _f extends ne{constructor(e){super("w:lvlJc"),this.root.push(new _e({val:e}))}}class Ef extends ne{constructor(e){super("w:suff"),this.root.push(new _e({val:e}))}}class xf extends ne{constructor(){super("w:isLgl")}}class Af extends ne{constructor({level:e,format:t,text:a,alignment:o=Ne.START,start:l=1,style:d,suffix:i,isLegalNumberingStyle:h}){if(super("w:lvl"),re(this,"paragraphProperties"),re(this,"runProperties"),this.root.push(new bt("w:start",Te(l))),t&&this.root.push(new vf(t)),i&&this.root.push(new Ef(i)),h&&this.root.push(new xf),a&&this.root.push(new bf(a)),this.root.push(new _f(o)),this.paragraphProperties=new ht(d&&d.paragraph),this.runProperties=new et(d&&d.run),this.root.push(this.paragraphProperties),this.root.push(this.runProperties),e>9)throw new Error("Level cannot be greater than 9. Read more here: https://answers.microsoft.com/en-us/msoffice/forum/all/does-word-support-more-than-9-list-levels/d130fdcd-1781-446d-8c84-c6c79124e4d7");this.root.push(new yf({ilvl:Te(e),tentative:1}))}}class Tf extends Af{}class Sf extends ne{constructor(e){super("w:multiLevelType"),this.root.push(new _e({val:e}))}}class Rf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{abstractNumId:"w:abstractNumId",restartNumberingAfterBreak:"w15:restartNumberingAfterBreak"})}}class wa extends ne{constructor(e,t){super("w:abstractNum"),re(this,"id"),this.root.push(new Rf({abstractNumId:Te(e),restartNumberingAfterBreak:0})),this.root.push(new Sf("hybridMultilevel")),this.id=e;for(const a of t)this.root.push(new Tf(a))}}class kf extends ne{constructor(e){super("w:abstractNumId"),this.root.push(new _e({val:e}))}}class If extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{numId:"w:numId"})}}class ga extends ne{constructor(e){if(super("w:num"),re(this,"numId"),re(this,"reference"),re(this,"instance"),this.numId=e.numId,this.reference=e.reference,this.instance=e.instance,this.root.push(new If({numId:Te(e.numId)})),this.root.push(new kf(Te(e.abstractNumId))),e.overrideLevels&&e.overrideLevels.length)for(const t of e.overrideLevels)this.root.push(new Nf(t.num,t.start))}}class Cf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{ilvl:"w:ilvl"})}}class Nf extends ne{constructor(e,t){super("w:lvlOverride"),this.root.push(new Cf({ilvl:e})),t!==void 0&&this.root.push(new Df(t))}}class Of extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class Df extends ne{constructor(e){super("w:startOverride"),this.root.push(new Of({val:e}))}}class Ff extends ne{constructor(e){super("w:numbering"),re(this,"abstractNumberingMap",new Map),re(this,"concreteNumberingMap",new Map),re(this,"referenceConfigMap",new Map),re(this,"abstractNumUniqueNumericId",El()),re(this,"concreteNumUniqueNumericId",xl()),this.root.push(new Xt(["wpc","mc","o","r","m","v","wp14","wp","w10","w","w14","w15","wpg","wpi","wne","wps"],"w14 w15 wp14"));const t=new wa(this.abstractNumUniqueNumericId(),[{level:0,format:Me.BULLET,text:"●",alignment:Ne.LEFT,style:{paragraph:{indent:{left:ke(.5),hanging:ke(.25)}}}},{level:1,format:Me.BULLET,text:"○",alignment:Ne.LEFT,style:{paragraph:{indent:{left:ke(1),hanging:ke(.25)}}}},{level:2,format:Me.BULLET,text:"■",alignment:Ne.LEFT,style:{paragraph:{indent:{left:2160,hanging:ke(.25)}}}},{level:3,format:Me.BULLET,text:"●",alignment:Ne.LEFT,style:{paragraph:{indent:{left:2880,hanging:ke(.25)}}}},{level:4,format:Me.BULLET,text:"○",alignment:Ne.LEFT,style:{paragraph:{indent:{left:3600,hanging:ke(.25)}}}},{level:5,format:Me.BULLET,text:"■",alignment:Ne.LEFT,style:{paragraph:{indent:{left:4320,hanging:ke(.25)}}}},{level:6,format:Me.BULLET,text:"●",alignment:Ne.LEFT,style:{paragraph:{indent:{left:5040,hanging:ke(.25)}}}},{level:7,format:Me.BULLET,text:"●",alignment:Ne.LEFT,style:{paragraph:{indent:{left:5760,hanging:ke(.25)}}}},{level:8,format:Me.BULLET,text:"●",alignment:Ne.LEFT,style:{paragraph:{indent:{left:6480,hanging:ke(.25)}}}}]);this.concreteNumberingMap.set("default-bullet-numbering",new ga({numId:1,abstractNumId:t.id,reference:"default-bullet-numbering",instance:0,overrideLevels:[{num:0,start:1}]})),this.abstractNumberingMap.set("default-bullet-numbering",t);for(const a of e.config)this.abstractNumberingMap.set(a.reference,new wa(this.abstractNumUniqueNumericId(),a.levels)),this.referenceConfigMap.set(a.reference,a.levels)}prepForXml(e){for(const t of this.abstractNumberingMap.values())this.root.push(t);for(const t of this.concreteNumberingMap.values())this.root.push(t);return super.prepForXml(e)}createConcreteNumberingInstance(e,t){const a=this.abstractNumberingMap.get(e);if(!a)return;const o=`${e}-${t}`;if(this.concreteNumberingMap.has(o))return;const l=this.referenceConfigMap.get(e),d=l&&l[0].start,i={numId:this.concreteNumUniqueNumericId(),abstractNumId:a.id,reference:e,instance:t,overrideLevels:[d&&Number.isInteger(d)?{num:0,start:d}:{num:0,start:1}]};this.concreteNumberingMap.set(o,new ga(i))}get ConcreteNumbering(){return Array.from(this.concreteNumberingMap.values())}get ReferenceConfig(){return Array.from(this.referenceConfigMap.values())}}class Pf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{version:"w:val",name:"w:name",uri:"w:uri"})}}class Bf extends ne{constructor(e){super("w:compatSetting"),this.root.push(new Pf({version:e,uri:"http://schemas.microsoft.com/office/word",name:"compatibilityMode"}))}}class Lf extends ne{constructor(e){super("w:compat"),e.version&&this.root.push(new Bf(e.version)),e.useSingleBorderforContiguousCells&&this.root.push(new ue("w:useSingleBorderforContiguousCells",e.useSingleBorderforContiguousCells)),e.wordPerfectJustification&&this.root.push(new ue("w:wpJustification",e.wordPerfectJustification)),e.noTabStopForHangingIndent&&this.root.push(new ue("w:noTabHangInd",e.noTabStopForHangingIndent)),e.noLeading&&this.root.push(new ue("w:noLeading",e.noLeading)),e.spaceForUnderline&&this.root.push(new ue("w:spaceForUL",e.spaceForUnderline)),e.noColumnBalance&&this.root.push(new ue("w:noColumnBalance",e.noColumnBalance)),e.balanceSingleByteDoubleByteWidth&&this.root.push(new ue("w:balanceSingleByteDoubleByteWidth",e.balanceSingleByteDoubleByteWidth)),e.noExtraLineSpacing&&this.root.push(new ue("w:noExtraLineSpacing",e.noExtraLineSpacing)),e.doNotLeaveBackslashAlone&&this.root.push(new ue("w:doNotLeaveBackslashAlone",e.doNotLeaveBackslashAlone)),e.underlineTrailingSpaces&&this.root.push(new ue("w:ulTrailSpace",e.underlineTrailingSpaces)),e.doNotExpandShiftReturn&&this.root.push(new ue("w:doNotExpandShiftReturn",e.doNotExpandShiftReturn)),e.spacingInWholePoints&&this.root.push(new ue("w:spacingInWholePoints",e.spacingInWholePoints)),e.lineWrapLikeWord6&&this.root.push(new ue("w:lineWrapLikeWord6",e.lineWrapLikeWord6)),e.printBodyTextBeforeHeader&&this.root.push(new ue("w:printBodyTextBeforeHeader",e.printBodyTextBeforeHeader)),e.printColorsBlack&&this.root.push(new ue("w:printColBlack",e.printColorsBlack)),e.spaceWidth&&this.root.push(new ue("w:wpSpaceWidth",e.spaceWidth)),e.showBreaksInFrames&&this.root.push(new ue("w:showBreaksInFrames",e.showBreaksInFrames)),e.subFontBySize&&this.root.push(new ue("w:subFontBySize",e.subFontBySize)),e.suppressBottomSpacing&&this.root.push(new ue("w:suppressBottomSpacing",e.suppressBottomSpacing)),e.suppressTopSpacing&&this.root.push(new ue("w:suppressTopSpacing",e.suppressTopSpacing)),e.suppressSpacingAtTopOfPage&&this.root.push(new ue("w:suppressSpacingAtTopOfPage",e.suppressSpacingAtTopOfPage)),e.suppressTopSpacingWP&&this.root.push(new ue("w:suppressTopSpacingWP",e.suppressTopSpacingWP)),e.suppressSpBfAfterPgBrk&&this.root.push(new ue("w:suppressSpBfAfterPgBrk",e.suppressSpBfAfterPgBrk)),e.swapBordersFacingPages&&this.root.push(new ue("w:swapBordersFacingPages",e.swapBordersFacingPages)),e.convertMailMergeEsc&&this.root.push(new ue("w:convMailMergeEsc",e.convertMailMergeEsc)),e.truncateFontHeightsLikeWP6&&this.root.push(new ue("w:truncateFontHeightsLikeWP6",e.truncateFontHeightsLikeWP6)),e.macWordSmallCaps&&this.root.push(new ue("w:mwSmallCaps",e.macWordSmallCaps)),e.usePrinterMetrics&&this.root.push(new ue("w:usePrinterMetrics",e.usePrinterMetrics)),e.doNotSuppressParagraphBorders&&this.root.push(new ue("w:doNotSuppressParagraphBorders",e.doNotSuppressParagraphBorders)),e.wrapTrailSpaces&&this.root.push(new ue("w:wrapTrailSpaces",e.wrapTrailSpaces)),e.footnoteLayoutLikeWW8&&this.root.push(new ue("w:footnoteLayoutLikeWW8",e.footnoteLayoutLikeWW8)),e.shapeLayoutLikeWW8&&this.root.push(new ue("w:shapeLayoutLikeWW8",e.shapeLayoutLikeWW8)),e.alignTablesRowByRow&&this.root.push(new ue("w:alignTablesRowByRow",e.alignTablesRowByRow)),e.forgetLastTabAlignment&&this.root.push(new ue("w:forgetLastTabAlignment",e.forgetLastTabAlignment)),e.adjustLineHeightInTable&&this.root.push(new ue("w:adjustLineHeightInTable",e.adjustLineHeightInTable)),e.autoSpaceLikeWord95&&this.root.push(new ue("w:autoSpaceLikeWord95",e.autoSpaceLikeWord95)),e.noSpaceRaiseLower&&this.root.push(new ue("w:noSpaceRaiseLower",e.noSpaceRaiseLower)),e.doNotUseHTMLParagraphAutoSpacing&&this.root.push(new ue("w:doNotUseHTMLParagraphAutoSpacing",e.doNotUseHTMLParagraphAutoSpacing)),e.layoutRawTableWidth&&this.root.push(new ue("w:layoutRawTableWidth",e.layoutRawTableWidth)),e.layoutTableRowsApart&&this.root.push(new ue("w:layoutTableRowsApart",e.layoutTableRowsApart)),e.useWord97LineBreakRules&&this.root.push(new ue("w:useWord97LineBreakRules",e.useWord97LineBreakRules)),e.doNotBreakWrappedTables&&this.root.push(new ue("w:doNotBreakWrappedTables",e.doNotBreakWrappedTables)),e.doNotSnapToGridInCell&&this.root.push(new ue("w:doNotSnapToGridInCell",e.doNotSnapToGridInCell)),e.selectFieldWithFirstOrLastCharacter&&this.root.push(new ue("w:selectFldWithFirstOrLastChar",e.selectFieldWithFirstOrLastCharacter)),e.applyBreakingRules&&this.root.push(new ue("w:applyBreakingRules",e.applyBreakingRules)),e.doNotWrapTextWithPunctuation&&this.root.push(new ue("w:doNotWrapTextWithPunct",e.doNotWrapTextWithPunctuation)),e.doNotUseEastAsianBreakRules&&this.root.push(new ue("w:doNotUseEastAsianBreakRules",e.doNotUseEastAsianBreakRules)),e.useWord2002TableStyleRules&&this.root.push(new ue("w:useWord2002TableStyleRules",e.useWord2002TableStyleRules)),e.growAutofit&&this.root.push(new ue("w:growAutofit",e.growAutofit)),e.useFELayout&&this.root.push(new ue("w:useFELayout",e.useFELayout)),e.useNormalStyleForList&&this.root.push(new ue("w:useNormalStyleForList",e.useNormalStyleForList)),e.doNotUseIndentAsNumberingTabStop&&this.root.push(new ue("w:doNotUseIndentAsNumberingTabStop",e.doNotUseIndentAsNumberingTabStop)),e.useAlternateEastAsianLineBreakRules&&this.root.push(new ue("w:useAltKinsokuLineBreakRules",e.useAlternateEastAsianLineBreakRules)),e.allowSpaceOfSameStyleInTable&&this.root.push(new ue("w:allowSpaceOfSameStyleInTable",e.allowSpaceOfSameStyleInTable)),e.doNotSuppressIndentation&&this.root.push(new ue("w:doNotSuppressIndentation",e.doNotSuppressIndentation)),e.doNotAutofitConstrainedTables&&this.root.push(new ue("w:doNotAutofitConstrainedTables",e.doNotAutofitConstrainedTables)),e.autofitToFirstFixedWidthCell&&this.root.push(new ue("w:autofitToFirstFixedWidthCell",e.autofitToFirstFixedWidthCell)),e.underlineTabInNumberingList&&this.root.push(new ue("w:underlineTabInNumList",e.underlineTabInNumberingList)),e.displayHangulFixedWidth&&this.root.push(new ue("w:displayHangulFixedWidth",e.displayHangulFixedWidth)),e.splitPgBreakAndParaMark&&this.root.push(new ue("w:splitPgBreakAndParaMark",e.splitPgBreakAndParaMark)),e.doNotVerticallyAlignCellWithSp&&this.root.push(new ue("w:doNotVertAlignCellWithSp",e.doNotVerticallyAlignCellWithSp)),e.doNotBreakConstrainedForcedTable&&this.root.push(new ue("w:doNotBreakConstrainedForcedTable",e.doNotBreakConstrainedForcedTable)),e.ignoreVerticalAlignmentInTextboxes&&this.root.push(new ue("w:doNotVertAlignInTxbx",e.ignoreVerticalAlignmentInTextboxes)),e.useAnsiKerningPairs&&this.root.push(new ue("w:useAnsiKerningPairs",e.useAnsiKerningPairs)),e.cachedColumnBalance&&this.root.push(new ue("w:cachedColBalance",e.cachedColumnBalance))}}class Mf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{wpc:"xmlns:wpc",mc:"xmlns:mc",o:"xmlns:o",r:"xmlns:r",m:"xmlns:m",v:"xmlns:v",wp14:"xmlns:wp14",wp:"xmlns:wp",w10:"xmlns:w10",w:"xmlns:w",w14:"xmlns:w14",w15:"xmlns:w15",wpg:"xmlns:wpg",wpi:"xmlns:wpi",wne:"xmlns:wne",wps:"xmlns:wps",Ignorable:"mc:Ignorable"})}}class Uf extends ne{constructor(e){var t,a,o,l,d,i,h,x;super("w:settings"),this.root.push(new Mf({wpc:"http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas",mc:"http://schemas.openxmlformats.org/markup-compatibility/2006",o:"urn:schemas-microsoft-com:office:office",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",m:"http://schemas.openxmlformats.org/officeDocument/2006/math",v:"urn:schemas-microsoft-com:vml",wp14:"http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing",wp:"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing",w10:"urn:schemas-microsoft-com:office:word",w:"http://schemas.openxmlformats.org/wordprocessingml/2006/main",w14:"http://schemas.microsoft.com/office/word/2010/wordml",w15:"http://schemas.microsoft.com/office/word/2012/wordml",wpg:"http://schemas.microsoft.com/office/word/2010/wordprocessingGroup",wpi:"http://schemas.microsoft.com/office/word/2010/wordprocessingInk",wne:"http://schemas.microsoft.com/office/word/2006/wordml",wps:"http://schemas.microsoft.com/office/word/2010/wordprocessingShape",Ignorable:"w14 w15 wp14"})),this.root.push(new ue("w:displayBackgroundShape",!0)),e.trackRevisions!==void 0&&this.root.push(new ue("w:trackRevisions",e.trackRevisions)),e.evenAndOddHeaders!==void 0&&this.root.push(new ue("w:evenAndOddHeaders",e.evenAndOddHeaders)),e.updateFields!==void 0&&this.root.push(new ue("w:updateFields",e.updateFields)),e.defaultTabStop!==void 0&&this.root.push(new bt("w:defaultTabStop",e.defaultTabStop)),((t=e.hyphenation)==null?void 0:t.autoHyphenation)!==void 0&&this.root.push(new ue("w:autoHyphenation",e.hyphenation.autoHyphenation)),((a=e.hyphenation)==null?void 0:a.hyphenationZone)!==void 0&&this.root.push(new bt("w:hyphenationZone",e.hyphenation.hyphenationZone)),((o=e.hyphenation)==null?void 0:o.consecutiveHyphenLimit)!==void 0&&this.root.push(new bt("w:consecutiveHyphenLimit",e.hyphenation.consecutiveHyphenLimit)),((l=e.hyphenation)==null?void 0:l.doNotHyphenateCaps)!==void 0&&this.root.push(new ue("w:doNotHyphenateCaps",e.hyphenation.doNotHyphenateCaps)),this.root.push(new Lf(je(me({},(d=e.compatibility)!=null?d:{}),{version:(x=(h=(i=e.compatibility)==null?void 0:i.version)!=null?h:e.compatibilityModeVersion)!=null?x:15})))}}class _o extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{val:"w:val"})}}class Hf extends ne{constructor(e){super("w:name"),this.root.push(new _o({val:e}))}}class jf extends ne{constructor(e){super("w:uiPriority"),this.root.push(new _o({val:Te(e)}))}}class zf extends fe{constructor(){super(...arguments),re(this,"xmlKeys",{type:"w:type",styleId:"w:styleId",default:"w:default",customStyle:"w:customStyle"})}}class Eo extends ne{constructor(e,t){super("w:style"),this.root.push(new zf(e)),t.name&&this.root.push(new Hf(t.name)),t.basedOn&&this.root.push(new Qe("w:basedOn",t.basedOn)),t.next&&this.root.push(new Qe("w:next",t.next)),t.link&&this.root.push(new Qe("w:link",t.link)),t.uiPriority!==void 0&&this.root.push(new jf(t.uiPriority)),t.semiHidden!==void 0&&this.root.push(new ue("w:semiHidden",t.semiHidden)),t.unhideWhenUsed!==void 0&&this.root.push(new ue("w:unhideWhenUsed",t.unhideWhenUsed)),t.quickFormat!==void 0&&this.root.push(new ue("w:qFormat",t.quickFormat))}}class Zt extends Eo{constructor(e){super({type:"paragraph",styleId:e.id},e),re(this,"paragraphProperties"),re(this,"runProperties"),this.paragraphProperties=new ht(e.paragraph),this.runProperties=new et(e.run),this.root.push(this.paragraphProperties),this.root.push(this.runProperties)}}class Yt extends Eo{constructor(e){super({type:"character",styleId:e.id},me({uiPriority:99,unhideWhenUsed:!0},e)),re(this,"runProperties"),this.runProperties=new et(e.run),this.root.push(this.runProperties)}}class Ve extends Zt{constructor(e){super(me({basedOn:"Normal",next:"Normal",quickFormat:!0},e))}}class Wf extends Ve{constructor(e){super(me({id:"Title",name:"Title"},e))}}class qf extends Ve{constructor(e){super(me({id:"Heading1",name:"Heading 1"},e))}}class Gf extends Ve{constructor(e){super(me({id:"Heading2",name:"Heading 2"},e))}}class Kf extends Ve{constructor(e){super(me({id:"Heading3",name:"Heading 3"},e))}}class Vf extends Ve{constructor(e){super(me({id:"Heading4",name:"Heading 4"},e))}}class $f extends Ve{constructor(e){super(me({id:"Heading5",name:"Heading 5"},e))}}class Xf extends Ve{constructor(e){super(me({id:"Heading6",name:"Heading 6"},e))}}class Zf extends Ve{constructor(e){super(me({id:"Strong",name:"Strong"},e))}}class Yf extends Zt{constructor(e){super(me({id:"ListParagraph",name:"List Paragraph",basedOn:"Normal",quickFormat:!0},e))}}class Jf extends Zt{constructor(e){super(me({id:"FootnoteText",name:"footnote text",link:"FootnoteTextChar",basedOn:"Normal",uiPriority:99,semiHidden:!0,unhideWhenUsed:!0,paragraph:{spacing:{after:0,line:240,lineRule:Vn.AUTO}},run:{size:20}},e))}}class Qf extends Yt{constructor(e){super(me({id:"FootnoteReference",name:"footnote reference",basedOn:"DefaultParagraphFont",semiHidden:!0,run:{superScript:!0}},e))}}class ed extends Yt{constructor(e){super(me({id:"FootnoteTextChar",name:"Footnote Text Char",basedOn:"DefaultParagraphFont",link:"FootnoteText",semiHidden:!0,run:{size:20}},e))}}class td extends Yt{constructor(e){super(me({id:"Hyperlink",name:"Hyperlink",basedOn:"DefaultParagraphFont",run:{color:"0563C1",underline:{type:Ja.SINGLE}}},e))}}class Zn extends ne{constructor(e){if(super("w:styles"),e.initialStyles&&this.root.push(e.initialStyles),e.importedStyles)for(const t of e.importedStyles)this.root.push(t);if(e.paragraphStyles)for(const t of e.paragraphStyles)this.root.push(new Zt(t));if(e.characterStyles)for(const t of e.characterStyles)this.root.push(new Yt(t))}}class rd extends ne{constructor(e){super("w:pPrDefault"),this.root.push(new ht(e))}}class nd extends ne{constructor(e){super("w:rPrDefault"),this.root.push(new et(e))}}class id extends ne{constructor(e){super("w:docDefaults"),re(this,"runPropertiesDefaults"),re(this,"paragraphPropertiesDefaults"),this.runPropertiesDefaults=new nd(e.run),this.paragraphPropertiesDefaults=new rd(e.paragraph),this.root.push(this.runPropertiesDefaults),this.root.push(this.paragraphPropertiesDefaults)}}class sd{newInstance(e){const t=Ga.xml2js(e,{compact:!1});let a;for(const d of t.elements||[])d.name==="w:styles"&&(a=d);if(a===void 0)throw new Error("can not find styles element");const o=a.elements||[];return new Zn({initialStyles:new Du(a.attributes),importedStyles:o.map(d=>ai(d))})}}class ya{newInstance(e={}){var t;return{initialStyles:new Xt(["mc","r","w","w14","w15"],"w14 w15"),importedStyles:[new id((t=e.document)!=null?t:{}),new Wf(me({run:{size:56}},e.title)),new qf(me({run:{color:"2E74B5",size:32}},e.heading1)),new Gf(me({run:{color:"2E74B5",size:26}},e.heading2)),new Kf(me({run:{color:"1F4D78",size:24}},e.heading3)),new Vf(me({run:{color:"2E74B5",italics:!0}},e.heading4)),new $f(me({run:{color:"2E74B5"}},e.heading5)),new Xf(me({run:{color:"1F4D78"}},e.heading6)),new Zf(me({run:{bold:!0}},e.strong)),new Yf(e.listParagraph||{}),new td(e.hyperlink||{}),new Qf(e.footnoteReference||{}),new Jf(e.footnoteText||{}),new ed(e.footnoteTextChar||{})]}}}class Id{constructor(e){re(this,"currentRelationshipId",1),re(this,"documentWrapper"),re(this,"headers",[]),re(this,"footers",[]),re(this,"coreProperties"),re(this,"numbering"),re(this,"media"),re(this,"fileRelationships"),re(this,"footnotesWrapper"),re(this,"settings"),re(this,"contentTypes"),re(this,"customProperties"),re(this,"appProperties"),re(this,"styles"),re(this,"comments"),re(this,"fontWrapper");var t,a,o,l,d,i,h,x,A,R,k,y;if(this.coreProperties=new Wh(je(me({},e),{creator:(t=e.creator)!=null?t:"Un-named",revision:(a=e.revision)!=null?a:1,lastModifiedBy:(o=e.lastModifiedBy)!=null?o:"Un-named"})),this.numbering=new Ff(e.numbering?e.numbering:{config:[]}),this.comments=new Fc((l=e.comments)!=null?l:{children:[]}),this.fileRelationships=new tt,this.customProperties=new Xh((d=e.customProperties)!=null?d:[]),this.appProperties=new Mh,this.footnotesWrapper=new df,this.contentTypes=new zh,this.documentWrapper=new po({background:e.background}),this.settings=new Uf({compatibilityModeVersion:e.compatabilityModeVersion,compatibility:e.compatibility,evenAndOddHeaders:!!e.evenAndOddHeaderAndFooters,trackRevisions:(i=e.features)==null?void 0:i.trackRevisions,updateFields:(h=e.features)==null?void 0:h.updateFields,defaultTabStop:e.defaultTabStop,hyphenation:{autoHyphenation:(x=e.hyphenation)==null?void 0:x.autoHyphenation,hyphenationZone:(A=e.hyphenation)==null?void 0:A.hyphenationZone,consecutiveHyphenLimit:(R=e.hyphenation)==null?void 0:R.consecutiveHyphenLimit,doNotHyphenateCaps:(k=e.hyphenation)==null?void 0:k.doNotHyphenateCaps}}),this.media=new gf,e.externalStyles!==void 0){const T=new sd;this.styles=T.newInstance(e.externalStyles)}else if(e.styles){const b=new ya().newInstance(e.styles.default);this.styles=new Zn(me(me({},b),e.styles))}else{const T=new ya;this.styles=new Zn(T.newInstance())}this.addDefaultRelationships();for(const T of e.sections)this.addSection(T);if(e.footnotes)for(const T in e.footnotes)this.footnotesWrapper.View.createFootNote(parseFloat(T),e.footnotes[T].children);this.fontWrapper=new Qh((y=e.fonts)!=null?y:[])}addSection({headers:e={},footers:t={},children:a,properties:o}){this.documentWrapper.View.Body.addSection(je(me({},o),{headerWrapperGroup:{default:e.default?this.createHeader(e.default):void 0,first:e.first?this.createHeader(e.first):void 0,even:e.even?this.createHeader(e.even):void 0},footerWrapperGroup:{default:t.default?this.createFooter(t.default):void 0,first:t.first?this.createFooter(t.first):void 0,even:t.even?this.createFooter(t.even):void 0}}));for(const l of a)this.documentWrapper.View.add(l)}createHeader(e){const t=new wf(this.media,this.currentRelationshipId++);for(const a of e.options.children)t.add(a);return this.addHeaderToDocument(t),t}createFooter(e){const t=new rf(this.media,this.currentRelationshipId++);for(const a of e.options.children)t.add(a);return this.addFooterToDocument(t),t}addHeaderToDocument(e,t=at.DEFAULT){this.headers.push({header:e,type:t}),this.documentWrapper.Relationships.createRelationship(e.View.ReferenceId,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/header",`header${this.headers.length}.xml`),this.contentTypes.addHeader(this.headers.length)}addFooterToDocument(e,t=at.DEFAULT){this.footers.push({footer:e,type:t}),this.documentWrapper.Relationships.createRelationship(e.View.ReferenceId,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer",`footer${this.footers.length}.xml`),this.contentTypes.addFooter(this.footers.length)}addDefaultRelationships(){this.fileRelationships.createRelationship(1,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument","word/document.xml"),this.fileRelationships.createRelationship(2,"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties","docProps/core.xml"),this.fileRelationships.createRelationship(3,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties","docProps/app.xml"),this.fileRelationships.createRelationship(4,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties","docProps/custom.xml"),this.documentWrapper.Relationships.createRelationship(this.currentRelationshipId++,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles","styles.xml"),this.documentWrapper.Relationships.createRelationship(this.currentRelationshipId++,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering","numbering.xml"),this.documentWrapper.Relationships.createRelationship(this.currentRelationshipId++,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes","footnotes.xml"),this.documentWrapper.Relationships.createRelationship(this.currentRelationshipId++,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings","settings.xml"),this.documentWrapper.Relationships.createRelationship(this.currentRelationshipId++,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments","comments.xml")}get Document(){return this.documentWrapper}get Styles(){return this.styles}get CoreProperties(){return this.coreProperties}get Numbering(){return this.numbering}get Media(){return this.media}get FileRelationships(){return this.fileRelationships}get Headers(){return this.headers.map(e=>e.header)}get Footers(){return this.footers.map(e=>e.footer)}get ContentTypes(){return this.contentTypes}get CustomProperties(){return this.customProperties}get AppProperties(){return this.appProperties}get FootNotes(){return this.footnotesWrapper}get Settings(){return this.settings}get Comments(){return this.comments}get FontTable(){return this.fontWrapper}}var ad=ni();function jt(r){throw new Error('Could not dynamically require "'+r+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Hn={exports:{}},va;function od(){return va||(va=1,function(r,e){(function(t){r.exports=t()})(function(){return function t(a,o,l){function d(x,A){if(!o[x]){if(!a[x]){var R=typeof jt=="function"&&jt;if(!A&&R)return R(x,!0);if(i)return i(x,!0);var k=new Error("Cannot find module '"+x+"'");throw k.code="MODULE_NOT_FOUND",k}var y=o[x]={exports:{}};a[x][0].call(y.exports,function(T){var b=a[x][1][T];return d(b||T)},y,y.exports,t,a,o,l)}return o[x].exports}for(var i=typeof jt=="function"&&jt,h=0;h<l.length;h++)d(l[h]);return d}({1:[function(t,a,o){var l=t("./utils"),d=t("./support"),i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";o.encode=function(h){for(var x,A,R,k,y,T,b,I=[],u=0,v=h.length,m=v,c=l.getTypeOf(h)!=="string";u<h.length;)m=v-u,R=c?(x=h[u++],A=u<v?h[u++]:0,u<v?h[u++]:0):(x=h.charCodeAt(u++),A=u<v?h.charCodeAt(u++):0,u<v?h.charCodeAt(u++):0),k=x>>2,y=(3&x)<<4|A>>4,T=1<m?(15&A)<<2|R>>6:64,b=2<m?63&R:64,I.push(i.charAt(k)+i.charAt(y)+i.charAt(T)+i.charAt(b));return I.join("")},o.decode=function(h){var x,A,R,k,y,T,b=0,I=0,u="data:";if(h.substr(0,u.length)===u)throw new Error("Invalid base64 input, it looks like a data url.");var v,m=3*(h=h.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(h.charAt(h.length-1)===i.charAt(64)&&m--,h.charAt(h.length-2)===i.charAt(64)&&m--,m%1!=0)throw new Error("Invalid base64 input, bad content length.");for(v=d.uint8array?new Uint8Array(0|m):new Array(0|m);b<h.length;)x=i.indexOf(h.charAt(b++))<<2|(k=i.indexOf(h.charAt(b++)))>>4,A=(15&k)<<4|(y=i.indexOf(h.charAt(b++)))>>2,R=(3&y)<<6|(T=i.indexOf(h.charAt(b++))),v[I++]=x,y!==64&&(v[I++]=A),T!==64&&(v[I++]=R);return v}},{"./support":30,"./utils":32}],2:[function(t,a,o){var l=t("./external"),d=t("./stream/DataWorker"),i=t("./stream/Crc32Probe"),h=t("./stream/DataLengthProbe");function x(A,R,k,y,T){this.compressedSize=A,this.uncompressedSize=R,this.crc32=k,this.compression=y,this.compressedContent=T}x.prototype={getContentWorker:function(){var A=new d(l.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new h("data_length")),R=this;return A.on("end",function(){if(this.streamInfo.data_length!==R.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),A},getCompressedWorker:function(){return new d(l.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},x.createWorkerFrom=function(A,R,k){return A.pipe(new i).pipe(new h("uncompressedSize")).pipe(R.compressWorker(k)).pipe(new h("compressedSize")).withStreamInfo("compression",R)},a.exports=x},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(t,a,o){var l=t("./stream/GenericWorker");o.STORE={magic:"\0\0",compressWorker:function(){return new l("STORE compression")},uncompressWorker:function(){return new l("STORE decompression")}},o.DEFLATE=t("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(t,a,o){var l=t("./utils"),d=function(){for(var i,h=[],x=0;x<256;x++){i=x;for(var A=0;A<8;A++)i=1&i?3988292384^i>>>1:i>>>1;h[x]=i}return h}();a.exports=function(i,h){return i!==void 0&&i.length?l.getTypeOf(i)!=="string"?function(x,A,R,k){var y=d,T=k+R;x^=-1;for(var b=k;b<T;b++)x=x>>>8^y[255&(x^A[b])];return-1^x}(0|h,i,i.length,0):function(x,A,R,k){var y=d,T=k+R;x^=-1;for(var b=k;b<T;b++)x=x>>>8^y[255&(x^A.charCodeAt(b))];return-1^x}(0|h,i,i.length,0):0}},{"./utils":32}],5:[function(t,a,o){o.base64=!1,o.binary=!1,o.dir=!1,o.createFolders=!0,o.date=null,o.compression=null,o.compressionOptions=null,o.comment=null,o.unixPermissions=null,o.dosPermissions=null},{}],6:[function(t,a,o){var l=null;l=typeof Promise<"u"?Promise:t("lie"),a.exports={Promise:l}},{lie:37}],7:[function(t,a,o){var l=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Uint32Array<"u",d=t("pako"),i=t("./utils"),h=t("./stream/GenericWorker"),x=l?"uint8array":"array";function A(R,k){h.call(this,"FlateWorker/"+R),this._pako=null,this._pakoAction=R,this._pakoOptions=k,this.meta={}}o.magic="\b\0",i.inherits(A,h),A.prototype.processChunk=function(R){this.meta=R.meta,this._pako===null&&this._createPako(),this._pako.push(i.transformTo(x,R.data),!1)},A.prototype.flush=function(){h.prototype.flush.call(this),this._pako===null&&this._createPako(),this._pako.push([],!0)},A.prototype.cleanUp=function(){h.prototype.cleanUp.call(this),this._pako=null},A.prototype._createPako=function(){this._pako=new d[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var R=this;this._pako.onData=function(k){R.push({data:k,meta:R.meta})}},o.compressWorker=function(R){return new A("Deflate",R)},o.uncompressWorker=function(){return new A("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(t,a,o){function l(y,T){var b,I="";for(b=0;b<T;b++)I+=String.fromCharCode(255&y),y>>>=8;return I}function d(y,T,b,I,u,v){var m,c,S=y.file,P=y.compression,B=v!==x.utf8encode,W=i.transformTo("string",v(S.name)),C=i.transformTo("string",x.utf8encode(S.name)),Z=S.comment,le=i.transformTo("string",v(Z)),N=i.transformTo("string",x.utf8encode(Z)),U=C.length!==S.name.length,g=N.length!==Z.length,K="",ee="",z="",se=S.dir,Q=S.date,ce={crc32:0,compressedSize:0,uncompressedSize:0};T&&!b||(ce.crc32=y.crc32,ce.compressedSize=y.compressedSize,ce.uncompressedSize=y.uncompressedSize);var V=0;T&&(V|=8),B||!U&&!g||(V|=2048);var D=0,$=0;se&&(D|=16),u==="UNIX"?($=798,D|=function(te,G){var _=te;return te||(_=G?16893:33204),(65535&_)<<16}(S.unixPermissions,se)):($=20,D|=function(te){return 63&(te||0)}(S.dosPermissions)),m=Q.getUTCHours(),m<<=6,m|=Q.getUTCMinutes(),m<<=5,m|=Q.getUTCSeconds()/2,c=Q.getUTCFullYear()-1980,c<<=4,c|=Q.getUTCMonth()+1,c<<=5,c|=Q.getUTCDate(),U&&(ee=l(1,1)+l(A(W),4)+C,K+="up"+l(ee.length,2)+ee),g&&(z=l(1,1)+l(A(le),4)+N,K+="uc"+l(z.length,2)+z);var X="";return X+=`
\0`,X+=l(V,2),X+=P.magic,X+=l(m,2),X+=l(c,2),X+=l(ce.crc32,4),X+=l(ce.compressedSize,4),X+=l(ce.uncompressedSize,4),X+=l(W.length,2),X+=l(K.length,2),{fileRecord:R.LOCAL_FILE_HEADER+X+W+K,dirRecord:R.CENTRAL_FILE_HEADER+l($,2)+X+l(le.length,2)+"\0\0\0\0"+l(D,4)+l(I,4)+W+K+le}}var i=t("../utils"),h=t("../stream/GenericWorker"),x=t("../utf8"),A=t("../crc32"),R=t("../signature");function k(y,T,b,I){h.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=T,this.zipPlatform=b,this.encodeFileName=I,this.streamFiles=y,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}i.inherits(k,h),k.prototype.push=function(y){var T=y.meta.percent||0,b=this.entriesCount,I=this._sources.length;this.accumulate?this.contentBuffer.push(y):(this.bytesWritten+=y.data.length,h.prototype.push.call(this,{data:y.data,meta:{currentFile:this.currentFile,percent:b?(T+100*(b-I-1))/b:100}}))},k.prototype.openedSource=function(y){this.currentSourceOffset=this.bytesWritten,this.currentFile=y.file.name;var T=this.streamFiles&&!y.file.dir;if(T){var b=d(y,T,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:b.fileRecord,meta:{percent:0}})}else this.accumulate=!0},k.prototype.closedSource=function(y){this.accumulate=!1;var T=this.streamFiles&&!y.file.dir,b=d(y,T,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(b.dirRecord),T)this.push({data:function(I){return R.DATA_DESCRIPTOR+l(I.crc32,4)+l(I.compressedSize,4)+l(I.uncompressedSize,4)}(y),meta:{percent:100}});else for(this.push({data:b.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},k.prototype.flush=function(){for(var y=this.bytesWritten,T=0;T<this.dirRecords.length;T++)this.push({data:this.dirRecords[T],meta:{percent:100}});var b=this.bytesWritten-y,I=function(u,v,m,c,S){var P=i.transformTo("string",S(c));return R.CENTRAL_DIRECTORY_END+"\0\0\0\0"+l(u,2)+l(u,2)+l(v,4)+l(m,4)+l(P.length,2)+P}(this.dirRecords.length,b,y,this.zipComment,this.encodeFileName);this.push({data:I,meta:{percent:100}})},k.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},k.prototype.registerPrevious=function(y){this._sources.push(y);var T=this;return y.on("data",function(b){T.processChunk(b)}),y.on("end",function(){T.closedSource(T.previous.streamInfo),T._sources.length?T.prepareNextSource():T.end()}),y.on("error",function(b){T.error(b)}),this},k.prototype.resume=function(){return!!h.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},k.prototype.error=function(y){var T=this._sources;if(!h.prototype.error.call(this,y))return!1;for(var b=0;b<T.length;b++)try{T[b].error(y)}catch{}return!0},k.prototype.lock=function(){h.prototype.lock.call(this);for(var y=this._sources,T=0;T<y.length;T++)y[T].lock()},a.exports=k},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(t,a,o){var l=t("../compressions"),d=t("./ZipFileWorker");o.generateWorker=function(i,h,x){var A=new d(h.streamFiles,x,h.platform,h.encodeFileName),R=0;try{i.forEach(function(k,y){R++;var T=function(v,m){var c=v||m,S=l[c];if(!S)throw new Error(c+" is not a valid compression method !");return S}(y.options.compression,h.compression),b=y.options.compressionOptions||h.compressionOptions||{},I=y.dir,u=y.date;y._compressWorker(T,b).withStreamInfo("file",{name:k,dir:I,date:u,comment:y.comment||"",unixPermissions:y.unixPermissions,dosPermissions:y.dosPermissions}).pipe(A)}),A.entriesCount=R}catch(k){A.error(k)}return A}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(t,a,o){function l(){if(!(this instanceof l))return new l;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var d=new l;for(var i in this)typeof this[i]!="function"&&(d[i]=this[i]);return d}}(l.prototype=t("./object")).loadAsync=t("./load"),l.support=t("./support"),l.defaults=t("./defaults"),l.version="3.10.1",l.loadAsync=function(d,i){return new l().loadAsync(d,i)},l.external=t("./external"),a.exports=l},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(t,a,o){var l=t("./utils"),d=t("./external"),i=t("./utf8"),h=t("./zipEntries"),x=t("./stream/Crc32Probe"),A=t("./nodejsUtils");function R(k){return new d.Promise(function(y,T){var b=k.decompressed.getContentWorker().pipe(new x);b.on("error",function(I){T(I)}).on("end",function(){b.streamInfo.crc32!==k.decompressed.crc32?T(new Error("Corrupted zip : CRC32 mismatch")):y()}).resume()})}a.exports=function(k,y){var T=this;return y=l.extend(y||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:i.utf8decode}),A.isNode&&A.isStream(k)?d.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):l.prepareContent("the loaded zip file",k,!0,y.optimizedBinaryString,y.base64).then(function(b){var I=new h(y);return I.load(b),I}).then(function(b){var I=[d.Promise.resolve(b)],u=b.files;if(y.checkCRC32)for(var v=0;v<u.length;v++)I.push(R(u[v]));return d.Promise.all(I)}).then(function(b){for(var I=b.shift(),u=I.files,v=0;v<u.length;v++){var m=u[v],c=m.fileNameStr,S=l.resolve(m.fileNameStr);T.file(S,m.decompressed,{binary:!0,optimizedBinaryString:!0,date:m.date,dir:m.dir,comment:m.fileCommentStr.length?m.fileCommentStr:null,unixPermissions:m.unixPermissions,dosPermissions:m.dosPermissions,createFolders:y.createFolders}),m.dir||(T.file(S).unsafeOriginalName=c)}return I.zipComment.length&&(T.comment=I.zipComment),T})}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(t,a,o){var l=t("../utils"),d=t("../stream/GenericWorker");function i(h,x){d.call(this,"Nodejs stream input adapter for "+h),this._upstreamEnded=!1,this._bindStream(x)}l.inherits(i,d),i.prototype._bindStream=function(h){var x=this;(this._stream=h).pause(),h.on("data",function(A){x.push({data:A,meta:{percent:0}})}).on("error",function(A){x.isPaused?this.generatedError=A:x.error(A)}).on("end",function(){x.isPaused?x._upstreamEnded=!0:x.end()})},i.prototype.pause=function(){return!!d.prototype.pause.call(this)&&(this._stream.pause(),!0)},i.prototype.resume=function(){return!!d.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},a.exports=i},{"../stream/GenericWorker":28,"../utils":32}],13:[function(t,a,o){var l=t("readable-stream").Readable;function d(i,h,x){l.call(this,h),this._helper=i;var A=this;i.on("data",function(R,k){A.push(R)||A._helper.pause(),x&&x(k)}).on("error",function(R){A.emit("error",R)}).on("end",function(){A.push(null)})}t("../utils").inherits(d,l),d.prototype._read=function(){this._helper.resume()},a.exports=d},{"../utils":32,"readable-stream":16}],14:[function(t,a,o){a.exports={isNode:typeof Buffer<"u",newBufferFrom:function(l,d){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(l,d);if(typeof l=="number")throw new Error('The "data" argument must not be a number');return new Buffer(l,d)},allocBuffer:function(l){if(Buffer.alloc)return Buffer.alloc(l);var d=new Buffer(l);return d.fill(0),d},isBuffer:function(l){return Buffer.isBuffer(l)},isStream:function(l){return l&&typeof l.on=="function"&&typeof l.pause=="function"&&typeof l.resume=="function"}}},{}],15:[function(t,a,o){function l(S,P,B){var W,C=i.getTypeOf(P),Z=i.extend(B||{},A);Z.date=Z.date||new Date,Z.compression!==null&&(Z.compression=Z.compression.toUpperCase()),typeof Z.unixPermissions=="string"&&(Z.unixPermissions=parseInt(Z.unixPermissions,8)),Z.unixPermissions&&16384&Z.unixPermissions&&(Z.dir=!0),Z.dosPermissions&&16&Z.dosPermissions&&(Z.dir=!0),Z.dir&&(S=u(S)),Z.createFolders&&(W=I(S))&&v.call(this,W,!0);var le=C==="string"&&Z.binary===!1&&Z.base64===!1;B&&B.binary!==void 0||(Z.binary=!le),(P instanceof R&&P.uncompressedSize===0||Z.dir||!P||P.length===0)&&(Z.base64=!1,Z.binary=!0,P="",Z.compression="STORE",C="string");var N=null;N=P instanceof R||P instanceof h?P:T.isNode&&T.isStream(P)?new b(S,P):i.prepareContent(S,P,Z.binary,Z.optimizedBinaryString,Z.base64);var U=new k(S,N,Z);this.files[S]=U}var d=t("./utf8"),i=t("./utils"),h=t("./stream/GenericWorker"),x=t("./stream/StreamHelper"),A=t("./defaults"),R=t("./compressedObject"),k=t("./zipObject"),y=t("./generate"),T=t("./nodejsUtils"),b=t("./nodejs/NodejsStreamInputAdapter"),I=function(S){S.slice(-1)==="/"&&(S=S.substring(0,S.length-1));var P=S.lastIndexOf("/");return 0<P?S.substring(0,P):""},u=function(S){return S.slice(-1)!=="/"&&(S+="/"),S},v=function(S,P){return P=P!==void 0?P:A.createFolders,S=u(S),this.files[S]||l.call(this,S,null,{dir:!0,createFolders:P}),this.files[S]};function m(S){return Object.prototype.toString.call(S)==="[object RegExp]"}var c={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(S){var P,B,W;for(P in this.files)W=this.files[P],(B=P.slice(this.root.length,P.length))&&P.slice(0,this.root.length)===this.root&&S(B,W)},filter:function(S){var P=[];return this.forEach(function(B,W){S(B,W)&&P.push(W)}),P},file:function(S,P,B){if(arguments.length!==1)return S=this.root+S,l.call(this,S,P,B),this;if(m(S)){var W=S;return this.filter(function(Z,le){return!le.dir&&W.test(Z)})}var C=this.files[this.root+S];return C&&!C.dir?C:null},folder:function(S){if(!S)return this;if(m(S))return this.filter(function(C,Z){return Z.dir&&S.test(C)});var P=this.root+S,B=v.call(this,P),W=this.clone();return W.root=B.name,W},remove:function(S){S=this.root+S;var P=this.files[S];if(P||(S.slice(-1)!=="/"&&(S+="/"),P=this.files[S]),P&&!P.dir)delete this.files[S];else for(var B=this.filter(function(C,Z){return Z.name.slice(0,S.length)===S}),W=0;W<B.length;W++)delete this.files[B[W].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(S){var P,B={};try{if((B=i.extend(S||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:d.utf8encode})).type=B.type.toLowerCase(),B.compression=B.compression.toUpperCase(),B.type==="binarystring"&&(B.type="string"),!B.type)throw new Error("No output type specified.");i.checkSupport(B.type),B.platform!=="darwin"&&B.platform!=="freebsd"&&B.platform!=="linux"&&B.platform!=="sunos"||(B.platform="UNIX"),B.platform==="win32"&&(B.platform="DOS");var W=B.comment||this.comment||"";P=y.generateWorker(this,B,W)}catch(C){(P=new h("error")).error(C)}return new x(P,B.type||"string",B.mimeType)},generateAsync:function(S,P){return this.generateInternalStream(S).accumulate(P)},generateNodeStream:function(S,P){return(S=S||{}).type||(S.type="nodebuffer"),this.generateInternalStream(S).toNodejsStream(P)}};a.exports=c},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(t,a,o){a.exports=t("stream")},{stream:void 0}],17:[function(t,a,o){var l=t("./DataReader");function d(i){l.call(this,i);for(var h=0;h<this.data.length;h++)i[h]=255&i[h]}t("../utils").inherits(d,l),d.prototype.byteAt=function(i){return this.data[this.zero+i]},d.prototype.lastIndexOfSignature=function(i){for(var h=i.charCodeAt(0),x=i.charCodeAt(1),A=i.charCodeAt(2),R=i.charCodeAt(3),k=this.length-4;0<=k;--k)if(this.data[k]===h&&this.data[k+1]===x&&this.data[k+2]===A&&this.data[k+3]===R)return k-this.zero;return-1},d.prototype.readAndCheckSignature=function(i){var h=i.charCodeAt(0),x=i.charCodeAt(1),A=i.charCodeAt(2),R=i.charCodeAt(3),k=this.readData(4);return h===k[0]&&x===k[1]&&A===k[2]&&R===k[3]},d.prototype.readData=function(i){if(this.checkOffset(i),i===0)return[];var h=this.data.slice(this.zero+this.index,this.zero+this.index+i);return this.index+=i,h},a.exports=d},{"../utils":32,"./DataReader":18}],18:[function(t,a,o){var l=t("../utils");function d(i){this.data=i,this.length=i.length,this.index=0,this.zero=0}d.prototype={checkOffset:function(i){this.checkIndex(this.index+i)},checkIndex:function(i){if(this.length<this.zero+i||i<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+i+"). Corrupted zip ?")},setIndex:function(i){this.checkIndex(i),this.index=i},skip:function(i){this.setIndex(this.index+i)},byteAt:function(){},readInt:function(i){var h,x=0;for(this.checkOffset(i),h=this.index+i-1;h>=this.index;h--)x=(x<<8)+this.byteAt(h);return this.index+=i,x},readString:function(i){return l.transformTo("string",this.readData(i))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var i=this.readInt(4);return new Date(Date.UTC(1980+(i>>25&127),(i>>21&15)-1,i>>16&31,i>>11&31,i>>5&63,(31&i)<<1))}},a.exports=d},{"../utils":32}],19:[function(t,a,o){var l=t("./Uint8ArrayReader");function d(i){l.call(this,i)}t("../utils").inherits(d,l),d.prototype.readData=function(i){this.checkOffset(i);var h=this.data.slice(this.zero+this.index,this.zero+this.index+i);return this.index+=i,h},a.exports=d},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(t,a,o){var l=t("./DataReader");function d(i){l.call(this,i)}t("../utils").inherits(d,l),d.prototype.byteAt=function(i){return this.data.charCodeAt(this.zero+i)},d.prototype.lastIndexOfSignature=function(i){return this.data.lastIndexOf(i)-this.zero},d.prototype.readAndCheckSignature=function(i){return i===this.readData(4)},d.prototype.readData=function(i){this.checkOffset(i);var h=this.data.slice(this.zero+this.index,this.zero+this.index+i);return this.index+=i,h},a.exports=d},{"../utils":32,"./DataReader":18}],21:[function(t,a,o){var l=t("./ArrayReader");function d(i){l.call(this,i)}t("../utils").inherits(d,l),d.prototype.readData=function(i){if(this.checkOffset(i),i===0)return new Uint8Array(0);var h=this.data.subarray(this.zero+this.index,this.zero+this.index+i);return this.index+=i,h},a.exports=d},{"../utils":32,"./ArrayReader":17}],22:[function(t,a,o){var l=t("../utils"),d=t("../support"),i=t("./ArrayReader"),h=t("./StringReader"),x=t("./NodeBufferReader"),A=t("./Uint8ArrayReader");a.exports=function(R){var k=l.getTypeOf(R);return l.checkSupport(k),k!=="string"||d.uint8array?k==="nodebuffer"?new x(R):d.uint8array?new A(l.transformTo("uint8array",R)):new i(l.transformTo("array",R)):new h(R)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(t,a,o){o.LOCAL_FILE_HEADER="PK",o.CENTRAL_FILE_HEADER="PK",o.CENTRAL_DIRECTORY_END="PK",o.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK\x07",o.ZIP64_CENTRAL_DIRECTORY_END="PK",o.DATA_DESCRIPTOR="PK\x07\b"},{}],24:[function(t,a,o){var l=t("./GenericWorker"),d=t("../utils");function i(h){l.call(this,"ConvertWorker to "+h),this.destType=h}d.inherits(i,l),i.prototype.processChunk=function(h){this.push({data:d.transformTo(this.destType,h.data),meta:h.meta})},a.exports=i},{"../utils":32,"./GenericWorker":28}],25:[function(t,a,o){var l=t("./GenericWorker"),d=t("../crc32");function i(){l.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}t("../utils").inherits(i,l),i.prototype.processChunk=function(h){this.streamInfo.crc32=d(h.data,this.streamInfo.crc32||0),this.push(h)},a.exports=i},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(t,a,o){var l=t("../utils"),d=t("./GenericWorker");function i(h){d.call(this,"DataLengthProbe for "+h),this.propName=h,this.withStreamInfo(h,0)}l.inherits(i,d),i.prototype.processChunk=function(h){if(h){var x=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=x+h.data.length}d.prototype.processChunk.call(this,h)},a.exports=i},{"../utils":32,"./GenericWorker":28}],27:[function(t,a,o){var l=t("../utils"),d=t("./GenericWorker");function i(h){d.call(this,"DataWorker");var x=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,h.then(function(A){x.dataIsReady=!0,x.data=A,x.max=A&&A.length||0,x.type=l.getTypeOf(A),x.isPaused||x._tickAndRepeat()},function(A){x.error(A)})}l.inherits(i,d),i.prototype.cleanUp=function(){d.prototype.cleanUp.call(this),this.data=null},i.prototype.resume=function(){return!!d.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,l.delay(this._tickAndRepeat,[],this)),!0)},i.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(l.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},i.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var h=null,x=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":h=this.data.substring(this.index,x);break;case"uint8array":h=this.data.subarray(this.index,x);break;case"array":case"nodebuffer":h=this.data.slice(this.index,x)}return this.index=x,this.push({data:h,meta:{percent:this.max?this.index/this.max*100:0}})},a.exports=i},{"../utils":32,"./GenericWorker":28}],28:[function(t,a,o){function l(d){this.name=d||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}l.prototype={push:function(d){this.emit("data",d)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(d){this.emit("error",d)}return!0},error:function(d){return!this.isFinished&&(this.isPaused?this.generatedError=d:(this.isFinished=!0,this.emit("error",d),this.previous&&this.previous.error(d),this.cleanUp()),!0)},on:function(d,i){return this._listeners[d].push(i),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(d,i){if(this._listeners[d])for(var h=0;h<this._listeners[d].length;h++)this._listeners[d][h].call(this,i)},pipe:function(d){return d.registerPrevious(this)},registerPrevious:function(d){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=d.streamInfo,this.mergeStreamInfo(),this.previous=d;var i=this;return d.on("data",function(h){i.processChunk(h)}),d.on("end",function(){i.end()}),d.on("error",function(h){i.error(h)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var d=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),d=!0),this.previous&&this.previous.resume(),!d},flush:function(){},processChunk:function(d){this.push(d)},withStreamInfo:function(d,i){return this.extraStreamInfo[d]=i,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var d in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,d)&&(this.streamInfo[d]=this.extraStreamInfo[d])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var d="Worker "+this.name;return this.previous?this.previous+" -> "+d:d}},a.exports=l},{}],29:[function(t,a,o){var l=t("../utils"),d=t("./ConvertWorker"),i=t("./GenericWorker"),h=t("../base64"),x=t("../support"),A=t("../external"),R=null;if(x.nodestream)try{R=t("../nodejs/NodejsStreamOutputAdapter")}catch{}function k(T,b){return new A.Promise(function(I,u){var v=[],m=T._internalType,c=T._outputType,S=T._mimeType;T.on("data",function(P,B){v.push(P),b&&b(B)}).on("error",function(P){v=[],u(P)}).on("end",function(){try{var P=function(B,W,C){switch(B){case"blob":return l.newBlob(l.transformTo("arraybuffer",W),C);case"base64":return h.encode(W);default:return l.transformTo(B,W)}}(c,function(B,W){var C,Z=0,le=null,N=0;for(C=0;C<W.length;C++)N+=W[C].length;switch(B){case"string":return W.join("");case"array":return Array.prototype.concat.apply([],W);case"uint8array":for(le=new Uint8Array(N),C=0;C<W.length;C++)le.set(W[C],Z),Z+=W[C].length;return le;case"nodebuffer":return Buffer.concat(W);default:throw new Error("concat : unsupported type '"+B+"'")}}(m,v),S);I(P)}catch(B){u(B)}v=[]}).resume()})}function y(T,b,I){var u=b;switch(b){case"blob":case"arraybuffer":u="uint8array";break;case"base64":u="string"}try{this._internalType=u,this._outputType=b,this._mimeType=I,l.checkSupport(u),this._worker=T.pipe(new d(u)),T.lock()}catch(v){this._worker=new i("error"),this._worker.error(v)}}y.prototype={accumulate:function(T){return k(this,T)},on:function(T,b){var I=this;return T==="data"?this._worker.on(T,function(u){b.call(I,u.data,u.meta)}):this._worker.on(T,function(){l.delay(b,arguments,I)}),this},resume:function(){return l.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(T){if(l.checkSupport("nodestream"),this._outputType!=="nodebuffer")throw new Error(this._outputType+" is not supported by this method");return new R(this,{objectMode:this._outputType!=="nodebuffer"},T)}},a.exports=y},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(t,a,o){if(o.base64=!0,o.array=!0,o.string=!0,o.arraybuffer=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u",o.nodebuffer=typeof Buffer<"u",o.uint8array=typeof Uint8Array<"u",typeof ArrayBuffer>"u")o.blob=!1;else{var l=new ArrayBuffer(0);try{o.blob=new Blob([l],{type:"application/zip"}).size===0}catch{try{var d=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);d.append(l),o.blob=d.getBlob("application/zip").size===0}catch{o.blob=!1}}}try{o.nodestream=!!t("readable-stream").Readable}catch{o.nodestream=!1}},{"readable-stream":16}],31:[function(t,a,o){for(var l=t("./utils"),d=t("./support"),i=t("./nodejsUtils"),h=t("./stream/GenericWorker"),x=new Array(256),A=0;A<256;A++)x[A]=252<=A?6:248<=A?5:240<=A?4:224<=A?3:192<=A?2:1;x[254]=x[254]=1;function R(){h.call(this,"utf-8 decode"),this.leftOver=null}function k(){h.call(this,"utf-8 encode")}o.utf8encode=function(y){return d.nodebuffer?i.newBufferFrom(y,"utf-8"):function(T){var b,I,u,v,m,c=T.length,S=0;for(v=0;v<c;v++)(64512&(I=T.charCodeAt(v)))==55296&&v+1<c&&(64512&(u=T.charCodeAt(v+1)))==56320&&(I=65536+(I-55296<<10)+(u-56320),v++),S+=I<128?1:I<2048?2:I<65536?3:4;for(b=d.uint8array?new Uint8Array(S):new Array(S),v=m=0;m<S;v++)(64512&(I=T.charCodeAt(v)))==55296&&v+1<c&&(64512&(u=T.charCodeAt(v+1)))==56320&&(I=65536+(I-55296<<10)+(u-56320),v++),I<128?b[m++]=I:(I<2048?b[m++]=192|I>>>6:(I<65536?b[m++]=224|I>>>12:(b[m++]=240|I>>>18,b[m++]=128|I>>>12&63),b[m++]=128|I>>>6&63),b[m++]=128|63&I);return b}(y)},o.utf8decode=function(y){return d.nodebuffer?l.transformTo("nodebuffer",y).toString("utf-8"):function(T){var b,I,u,v,m=T.length,c=new Array(2*m);for(b=I=0;b<m;)if((u=T[b++])<128)c[I++]=u;else if(4<(v=x[u]))c[I++]=65533,b+=v-1;else{for(u&=v===2?31:v===3?15:7;1<v&&b<m;)u=u<<6|63&T[b++],v--;1<v?c[I++]=65533:u<65536?c[I++]=u:(u-=65536,c[I++]=55296|u>>10&1023,c[I++]=56320|1023&u)}return c.length!==I&&(c.subarray?c=c.subarray(0,I):c.length=I),l.applyFromCharCode(c)}(y=l.transformTo(d.uint8array?"uint8array":"array",y))},l.inherits(R,h),R.prototype.processChunk=function(y){var T=l.transformTo(d.uint8array?"uint8array":"array",y.data);if(this.leftOver&&this.leftOver.length){if(d.uint8array){var b=T;(T=new Uint8Array(b.length+this.leftOver.length)).set(this.leftOver,0),T.set(b,this.leftOver.length)}else T=this.leftOver.concat(T);this.leftOver=null}var I=function(v,m){var c;for((m=m||v.length)>v.length&&(m=v.length),c=m-1;0<=c&&(192&v[c])==128;)c--;return c<0||c===0?m:c+x[v[c]]>m?c:m}(T),u=T;I!==T.length&&(d.uint8array?(u=T.subarray(0,I),this.leftOver=T.subarray(I,T.length)):(u=T.slice(0,I),this.leftOver=T.slice(I,T.length))),this.push({data:o.utf8decode(u),meta:y.meta})},R.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:o.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},o.Utf8DecodeWorker=R,l.inherits(k,h),k.prototype.processChunk=function(y){this.push({data:o.utf8encode(y.data),meta:y.meta})},o.Utf8EncodeWorker=k},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(t,a,o){var l=t("./support"),d=t("./base64"),i=t("./nodejsUtils"),h=t("./external");function x(b){return b}function A(b,I){for(var u=0;u<b.length;++u)I[u]=255&b.charCodeAt(u);return I}t("setimmediate"),o.newBlob=function(b,I){o.checkSupport("blob");try{return new Blob([b],{type:I})}catch{try{var u=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return u.append(b),u.getBlob(I)}catch{throw new Error("Bug : can't construct the Blob.")}}};var R={stringifyByChunk:function(b,I,u){var v=[],m=0,c=b.length;if(c<=u)return String.fromCharCode.apply(null,b);for(;m<c;)I==="array"||I==="nodebuffer"?v.push(String.fromCharCode.apply(null,b.slice(m,Math.min(m+u,c)))):v.push(String.fromCharCode.apply(null,b.subarray(m,Math.min(m+u,c)))),m+=u;return v.join("")},stringifyByChar:function(b){for(var I="",u=0;u<b.length;u++)I+=String.fromCharCode(b[u]);return I},applyCanBeUsed:{uint8array:function(){try{return l.uint8array&&String.fromCharCode.apply(null,new Uint8Array(1)).length===1}catch{return!1}}(),nodebuffer:function(){try{return l.nodebuffer&&String.fromCharCode.apply(null,i.allocBuffer(1)).length===1}catch{return!1}}()}};function k(b){var I=65536,u=o.getTypeOf(b),v=!0;if(u==="uint8array"?v=R.applyCanBeUsed.uint8array:u==="nodebuffer"&&(v=R.applyCanBeUsed.nodebuffer),v)for(;1<I;)try{return R.stringifyByChunk(b,u,I)}catch{I=Math.floor(I/2)}return R.stringifyByChar(b)}function y(b,I){for(var u=0;u<b.length;u++)I[u]=b[u];return I}o.applyFromCharCode=k;var T={};T.string={string:x,array:function(b){return A(b,new Array(b.length))},arraybuffer:function(b){return T.string.uint8array(b).buffer},uint8array:function(b){return A(b,new Uint8Array(b.length))},nodebuffer:function(b){return A(b,i.allocBuffer(b.length))}},T.array={string:k,array:x,arraybuffer:function(b){return new Uint8Array(b).buffer},uint8array:function(b){return new Uint8Array(b)},nodebuffer:function(b){return i.newBufferFrom(b)}},T.arraybuffer={string:function(b){return k(new Uint8Array(b))},array:function(b){return y(new Uint8Array(b),new Array(b.byteLength))},arraybuffer:x,uint8array:function(b){return new Uint8Array(b)},nodebuffer:function(b){return i.newBufferFrom(new Uint8Array(b))}},T.uint8array={string:k,array:function(b){return y(b,new Array(b.length))},arraybuffer:function(b){return b.buffer},uint8array:x,nodebuffer:function(b){return i.newBufferFrom(b)}},T.nodebuffer={string:k,array:function(b){return y(b,new Array(b.length))},arraybuffer:function(b){return T.nodebuffer.uint8array(b).buffer},uint8array:function(b){return y(b,new Uint8Array(b.length))},nodebuffer:x},o.transformTo=function(b,I){if(I=I||"",!b)return I;o.checkSupport(b);var u=o.getTypeOf(I);return T[u][b](I)},o.resolve=function(b){for(var I=b.split("/"),u=[],v=0;v<I.length;v++){var m=I[v];m==="."||m===""&&v!==0&&v!==I.length-1||(m===".."?u.pop():u.push(m))}return u.join("/")},o.getTypeOf=function(b){return typeof b=="string"?"string":Object.prototype.toString.call(b)==="[object Array]"?"array":l.nodebuffer&&i.isBuffer(b)?"nodebuffer":l.uint8array&&b instanceof Uint8Array?"uint8array":l.arraybuffer&&b instanceof ArrayBuffer?"arraybuffer":void 0},o.checkSupport=function(b){if(!l[b.toLowerCase()])throw new Error(b+" is not supported by this platform")},o.MAX_VALUE_16BITS=65535,o.MAX_VALUE_32BITS=-1,o.pretty=function(b){var I,u,v="";for(u=0;u<(b||"").length;u++)v+="\\x"+((I=b.charCodeAt(u))<16?"0":"")+I.toString(16).toUpperCase();return v},o.delay=function(b,I,u){setImmediate(function(){b.apply(u||null,I||[])})},o.inherits=function(b,I){function u(){}u.prototype=I.prototype,b.prototype=new u},o.extend=function(){var b,I,u={};for(b=0;b<arguments.length;b++)for(I in arguments[b])Object.prototype.hasOwnProperty.call(arguments[b],I)&&u[I]===void 0&&(u[I]=arguments[b][I]);return u},o.prepareContent=function(b,I,u,v,m){return h.Promise.resolve(I).then(function(c){return l.blob&&(c instanceof Blob||["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(c))!==-1)&&typeof FileReader<"u"?new h.Promise(function(S,P){var B=new FileReader;B.onload=function(W){S(W.target.result)},B.onerror=function(W){P(W.target.error)},B.readAsArrayBuffer(c)}):c}).then(function(c){var S=o.getTypeOf(c);return S?(S==="arraybuffer"?c=o.transformTo("uint8array",c):S==="string"&&(m?c=d.decode(c):u&&v!==!0&&(c=function(P){return A(P,l.uint8array?new Uint8Array(P.length):new Array(P.length))}(c))),c):h.Promise.reject(new Error("Can't read the data of '"+b+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,setimmediate:54}],33:[function(t,a,o){var l=t("./reader/readerFor"),d=t("./utils"),i=t("./signature"),h=t("./zipEntry"),x=t("./support");function A(R){this.files=[],this.loadOptions=R}A.prototype={checkSignature:function(R){if(!this.reader.readAndCheckSignature(R)){this.reader.index-=4;var k=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+d.pretty(k)+", expected "+d.pretty(R)+")")}},isSignature:function(R,k){var y=this.reader.index;this.reader.setIndex(R);var T=this.reader.readString(4)===k;return this.reader.setIndex(y),T},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var R=this.reader.readData(this.zipCommentLength),k=x.uint8array?"uint8array":"array",y=d.transformTo(k,R);this.zipComment=this.loadOptions.decodeFileName(y)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var R,k,y,T=this.zip64EndOfCentralSize-44;0<T;)R=this.reader.readInt(2),k=this.reader.readInt(4),y=this.reader.readData(k),this.zip64ExtensibleData[R]={id:R,length:k,value:y}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var R,k;for(R=0;R<this.files.length;R++)k=this.files[R],this.reader.setIndex(k.localHeaderOffset),this.checkSignature(i.LOCAL_FILE_HEADER),k.readLocalPart(this.reader),k.handleUTF8(),k.processAttributes()},readCentralDir:function(){var R;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(i.CENTRAL_FILE_HEADER);)(R=new h({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(R);if(this.centralDirRecords!==this.files.length&&this.centralDirRecords!==0&&this.files.length===0)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var R=this.reader.lastIndexOfSignature(i.CENTRAL_DIRECTORY_END);if(R<0)throw this.isSignature(0,i.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(R);var k=R;if(this.checkSignature(i.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===d.MAX_VALUE_16BITS||this.diskWithCentralDirStart===d.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===d.MAX_VALUE_16BITS||this.centralDirRecords===d.MAX_VALUE_16BITS||this.centralDirSize===d.MAX_VALUE_32BITS||this.centralDirOffset===d.MAX_VALUE_32BITS){if(this.zip64=!0,(R=this.reader.lastIndexOfSignature(i.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(R),this.checkSignature(i.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,i.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(i.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(i.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var y=this.centralDirOffset+this.centralDirSize;this.zip64&&(y+=20,y+=12+this.zip64EndOfCentralSize);var T=k-y;if(0<T)this.isSignature(k,i.CENTRAL_FILE_HEADER)||(this.reader.zero=T);else if(T<0)throw new Error("Corrupted zip: missing "+Math.abs(T)+" bytes.")},prepareReader:function(R){this.reader=l(R)},load:function(R){this.prepareReader(R),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},a.exports=A},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utils":32,"./zipEntry":34}],34:[function(t,a,o){var l=t("./reader/readerFor"),d=t("./utils"),i=t("./compressedObject"),h=t("./crc32"),x=t("./utf8"),A=t("./compressions"),R=t("./support");function k(y,T){this.options=y,this.loadOptions=T}k.prototype={isEncrypted:function(){return(1&this.bitFlag)==1},useUTF8:function(){return(2048&this.bitFlag)==2048},readLocalPart:function(y){var T,b;if(y.skip(22),this.fileNameLength=y.readInt(2),b=y.readInt(2),this.fileName=y.readData(this.fileNameLength),y.skip(b),this.compressedSize===-1||this.uncompressedSize===-1)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if((T=function(I){for(var u in A)if(Object.prototype.hasOwnProperty.call(A,u)&&A[u].magic===I)return A[u];return null}(this.compressionMethod))===null)throw new Error("Corrupted zip : compression "+d.pretty(this.compressionMethod)+" unknown (inner file : "+d.transformTo("string",this.fileName)+")");this.decompressed=new i(this.compressedSize,this.uncompressedSize,this.crc32,T,y.readData(this.compressedSize))},readCentralPart:function(y){this.versionMadeBy=y.readInt(2),y.skip(2),this.bitFlag=y.readInt(2),this.compressionMethod=y.readString(2),this.date=y.readDate(),this.crc32=y.readInt(4),this.compressedSize=y.readInt(4),this.uncompressedSize=y.readInt(4);var T=y.readInt(2);if(this.extraFieldsLength=y.readInt(2),this.fileCommentLength=y.readInt(2),this.diskNumberStart=y.readInt(2),this.internalFileAttributes=y.readInt(2),this.externalFileAttributes=y.readInt(4),this.localHeaderOffset=y.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");y.skip(T),this.readExtraFields(y),this.parseZIP64ExtraField(y),this.fileComment=y.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var y=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),y==0&&(this.dosPermissions=63&this.externalFileAttributes),y==3&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||this.fileNameStr.slice(-1)!=="/"||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var y=l(this.extraFields[1].value);this.uncompressedSize===d.MAX_VALUE_32BITS&&(this.uncompressedSize=y.readInt(8)),this.compressedSize===d.MAX_VALUE_32BITS&&(this.compressedSize=y.readInt(8)),this.localHeaderOffset===d.MAX_VALUE_32BITS&&(this.localHeaderOffset=y.readInt(8)),this.diskNumberStart===d.MAX_VALUE_32BITS&&(this.diskNumberStart=y.readInt(4))}},readExtraFields:function(y){var T,b,I,u=y.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});y.index+4<u;)T=y.readInt(2),b=y.readInt(2),I=y.readData(b),this.extraFields[T]={id:T,length:b,value:I};y.setIndex(u)},handleUTF8:function(){var y=R.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=x.utf8decode(this.fileName),this.fileCommentStr=x.utf8decode(this.fileComment);else{var T=this.findExtraFieldUnicodePath();if(T!==null)this.fileNameStr=T;else{var b=d.transformTo(y,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(b)}var I=this.findExtraFieldUnicodeComment();if(I!==null)this.fileCommentStr=I;else{var u=d.transformTo(y,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(u)}}},findExtraFieldUnicodePath:function(){var y=this.extraFields[28789];if(y){var T=l(y.value);return T.readInt(1)!==1||h(this.fileName)!==T.readInt(4)?null:x.utf8decode(T.readData(y.length-5))}return null},findExtraFieldUnicodeComment:function(){var y=this.extraFields[25461];if(y){var T=l(y.value);return T.readInt(1)!==1||h(this.fileComment)!==T.readInt(4)?null:x.utf8decode(T.readData(y.length-5))}return null}},a.exports=k},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(t,a,o){function l(T,b,I){this.name=T,this.dir=I.dir,this.date=I.date,this.comment=I.comment,this.unixPermissions=I.unixPermissions,this.dosPermissions=I.dosPermissions,this._data=b,this._dataBinary=I.binary,this.options={compression:I.compression,compressionOptions:I.compressionOptions}}var d=t("./stream/StreamHelper"),i=t("./stream/DataWorker"),h=t("./utf8"),x=t("./compressedObject"),A=t("./stream/GenericWorker");l.prototype={internalStream:function(T){var b=null,I="string";try{if(!T)throw new Error("No output type specified.");var u=(I=T.toLowerCase())==="string"||I==="text";I!=="binarystring"&&I!=="text"||(I="string"),b=this._decompressWorker();var v=!this._dataBinary;v&&!u&&(b=b.pipe(new h.Utf8EncodeWorker)),!v&&u&&(b=b.pipe(new h.Utf8DecodeWorker))}catch(m){(b=new A("error")).error(m)}return new d(b,I,"")},async:function(T,b){return this.internalStream(T).accumulate(b)},nodeStream:function(T,b){return this.internalStream(T||"nodebuffer").toNodejsStream(b)},_compressWorker:function(T,b){if(this._data instanceof x&&this._data.compression.magic===T.magic)return this._data.getCompressedWorker();var I=this._decompressWorker();return this._dataBinary||(I=I.pipe(new h.Utf8EncodeWorker)),x.createWorkerFrom(I,T,b)},_decompressWorker:function(){return this._data instanceof x?this._data.getContentWorker():this._data instanceof A?this._data:new i(this._data)}};for(var R=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],k=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},y=0;y<R.length;y++)l.prototype[R[y]]=k;a.exports=l},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(t,a,o){(function(l){var d,i,h=l.MutationObserver||l.WebKitMutationObserver;if(h){var x=0,A=new h(T),R=l.document.createTextNode("");A.observe(R,{characterData:!0}),d=function(){R.data=x=++x%2}}else if(l.setImmediate||l.MessageChannel===void 0)d="document"in l&&"onreadystatechange"in l.document.createElement("script")?function(){var b=l.document.createElement("script");b.onreadystatechange=function(){T(),b.onreadystatechange=null,b.parentNode.removeChild(b),b=null},l.document.documentElement.appendChild(b)}:function(){setTimeout(T,0)};else{var k=new l.MessageChannel;k.port1.onmessage=T,d=function(){k.port2.postMessage(0)}}var y=[];function T(){var b,I;i=!0;for(var u=y.length;u;){for(I=y,y=[],b=-1;++b<u;)I[b]();u=y.length}i=!1}a.exports=function(b){y.push(b)!==1||i||d()}}).call(this,typeof Fe<"u"?Fe:typeof self<"u"?self:typeof window<"u"?window:{})},{}],37:[function(t,a,o){var l=t("immediate");function d(){}var i={},h=["REJECTED"],x=["FULFILLED"],A=["PENDING"];function R(u){if(typeof u!="function")throw new TypeError("resolver must be a function");this.state=A,this.queue=[],this.outcome=void 0,u!==d&&b(this,u)}function k(u,v,m){this.promise=u,typeof v=="function"&&(this.onFulfilled=v,this.callFulfilled=this.otherCallFulfilled),typeof m=="function"&&(this.onRejected=m,this.callRejected=this.otherCallRejected)}function y(u,v,m){l(function(){var c;try{c=v(m)}catch(S){return i.reject(u,S)}c===u?i.reject(u,new TypeError("Cannot resolve promise with itself")):i.resolve(u,c)})}function T(u){var v=u&&u.then;if(u&&(typeof u=="object"||typeof u=="function")&&typeof v=="function")return function(){v.apply(u,arguments)}}function b(u,v){var m=!1;function c(B){m||(m=!0,i.reject(u,B))}function S(B){m||(m=!0,i.resolve(u,B))}var P=I(function(){v(S,c)});P.status==="error"&&c(P.value)}function I(u,v){var m={};try{m.value=u(v),m.status="success"}catch(c){m.status="error",m.value=c}return m}(a.exports=R).prototype.finally=function(u){if(typeof u!="function")return this;var v=this.constructor;return this.then(function(m){return v.resolve(u()).then(function(){return m})},function(m){return v.resolve(u()).then(function(){throw m})})},R.prototype.catch=function(u){return this.then(null,u)},R.prototype.then=function(u,v){if(typeof u!="function"&&this.state===x||typeof v!="function"&&this.state===h)return this;var m=new this.constructor(d);return this.state!==A?y(m,this.state===x?u:v,this.outcome):this.queue.push(new k(m,u,v)),m},k.prototype.callFulfilled=function(u){i.resolve(this.promise,u)},k.prototype.otherCallFulfilled=function(u){y(this.promise,this.onFulfilled,u)},k.prototype.callRejected=function(u){i.reject(this.promise,u)},k.prototype.otherCallRejected=function(u){y(this.promise,this.onRejected,u)},i.resolve=function(u,v){var m=I(T,v);if(m.status==="error")return i.reject(u,m.value);var c=m.value;if(c)b(u,c);else{u.state=x,u.outcome=v;for(var S=-1,P=u.queue.length;++S<P;)u.queue[S].callFulfilled(v)}return u},i.reject=function(u,v){u.state=h,u.outcome=v;for(var m=-1,c=u.queue.length;++m<c;)u.queue[m].callRejected(v);return u},R.resolve=function(u){return u instanceof this?u:i.resolve(new this(d),u)},R.reject=function(u){var v=new this(d);return i.reject(v,u)},R.all=function(u){var v=this;if(Object.prototype.toString.call(u)!=="[object Array]")return this.reject(new TypeError("must be an array"));var m=u.length,c=!1;if(!m)return this.resolve([]);for(var S=new Array(m),P=0,B=-1,W=new this(d);++B<m;)C(u[B],B);return W;function C(Z,le){v.resolve(Z).then(function(N){S[le]=N,++P!==m||c||(c=!0,i.resolve(W,S))},function(N){c||(c=!0,i.reject(W,N))})}},R.race=function(u){var v=this;if(Object.prototype.toString.call(u)!=="[object Array]")return this.reject(new TypeError("must be an array"));var m=u.length,c=!1;if(!m)return this.resolve([]);for(var S=-1,P=new this(d);++S<m;)B=u[S],v.resolve(B).then(function(W){c||(c=!0,i.resolve(P,W))},function(W){c||(c=!0,i.reject(P,W))});var B;return P}},{immediate:36}],38:[function(t,a,o){var l={};(0,t("./lib/utils/common").assign)(l,t("./lib/deflate"),t("./lib/inflate"),t("./lib/zlib/constants")),a.exports=l},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(t,a,o){var l=t("./zlib/deflate"),d=t("./utils/common"),i=t("./utils/strings"),h=t("./zlib/messages"),x=t("./zlib/zstream"),A=Object.prototype.toString,R=0,k=-1,y=0,T=8;function b(u){if(!(this instanceof b))return new b(u);this.options=d.assign({level:k,method:T,chunkSize:16384,windowBits:15,memLevel:8,strategy:y,to:""},u||{});var v=this.options;v.raw&&0<v.windowBits?v.windowBits=-v.windowBits:v.gzip&&0<v.windowBits&&v.windowBits<16&&(v.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new x,this.strm.avail_out=0;var m=l.deflateInit2(this.strm,v.level,v.method,v.windowBits,v.memLevel,v.strategy);if(m!==R)throw new Error(h[m]);if(v.header&&l.deflateSetHeader(this.strm,v.header),v.dictionary){var c;if(c=typeof v.dictionary=="string"?i.string2buf(v.dictionary):A.call(v.dictionary)==="[object ArrayBuffer]"?new Uint8Array(v.dictionary):v.dictionary,(m=l.deflateSetDictionary(this.strm,c))!==R)throw new Error(h[m]);this._dict_set=!0}}function I(u,v){var m=new b(v);if(m.push(u,!0),m.err)throw m.msg||h[m.err];return m.result}b.prototype.push=function(u,v){var m,c,S=this.strm,P=this.options.chunkSize;if(this.ended)return!1;c=v===~~v?v:v===!0?4:0,typeof u=="string"?S.input=i.string2buf(u):A.call(u)==="[object ArrayBuffer]"?S.input=new Uint8Array(u):S.input=u,S.next_in=0,S.avail_in=S.input.length;do{if(S.avail_out===0&&(S.output=new d.Buf8(P),S.next_out=0,S.avail_out=P),(m=l.deflate(S,c))!==1&&m!==R)return this.onEnd(m),!(this.ended=!0);S.avail_out!==0&&(S.avail_in!==0||c!==4&&c!==2)||(this.options.to==="string"?this.onData(i.buf2binstring(d.shrinkBuf(S.output,S.next_out))):this.onData(d.shrinkBuf(S.output,S.next_out)))}while((0<S.avail_in||S.avail_out===0)&&m!==1);return c===4?(m=l.deflateEnd(this.strm),this.onEnd(m),this.ended=!0,m===R):c!==2||(this.onEnd(R),!(S.avail_out=0))},b.prototype.onData=function(u){this.chunks.push(u)},b.prototype.onEnd=function(u){u===R&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=d.flattenChunks(this.chunks)),this.chunks=[],this.err=u,this.msg=this.strm.msg},o.Deflate=b,o.deflate=I,o.deflateRaw=function(u,v){return(v=v||{}).raw=!0,I(u,v)},o.gzip=function(u,v){return(v=v||{}).gzip=!0,I(u,v)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(t,a,o){var l=t("./zlib/inflate"),d=t("./utils/common"),i=t("./utils/strings"),h=t("./zlib/constants"),x=t("./zlib/messages"),A=t("./zlib/zstream"),R=t("./zlib/gzheader"),k=Object.prototype.toString;function y(b){if(!(this instanceof y))return new y(b);this.options=d.assign({chunkSize:16384,windowBits:0,to:""},b||{});var I=this.options;I.raw&&0<=I.windowBits&&I.windowBits<16&&(I.windowBits=-I.windowBits,I.windowBits===0&&(I.windowBits=-15)),!(0<=I.windowBits&&I.windowBits<16)||b&&b.windowBits||(I.windowBits+=32),15<I.windowBits&&I.windowBits<48&&!(15&I.windowBits)&&(I.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new A,this.strm.avail_out=0;var u=l.inflateInit2(this.strm,I.windowBits);if(u!==h.Z_OK)throw new Error(x[u]);this.header=new R,l.inflateGetHeader(this.strm,this.header)}function T(b,I){var u=new y(I);if(u.push(b,!0),u.err)throw u.msg||x[u.err];return u.result}y.prototype.push=function(b,I){var u,v,m,c,S,P,B=this.strm,W=this.options.chunkSize,C=this.options.dictionary,Z=!1;if(this.ended)return!1;v=I===~~I?I:I===!0?h.Z_FINISH:h.Z_NO_FLUSH,typeof b=="string"?B.input=i.binstring2buf(b):k.call(b)==="[object ArrayBuffer]"?B.input=new Uint8Array(b):B.input=b,B.next_in=0,B.avail_in=B.input.length;do{if(B.avail_out===0&&(B.output=new d.Buf8(W),B.next_out=0,B.avail_out=W),(u=l.inflate(B,h.Z_NO_FLUSH))===h.Z_NEED_DICT&&C&&(P=typeof C=="string"?i.string2buf(C):k.call(C)==="[object ArrayBuffer]"?new Uint8Array(C):C,u=l.inflateSetDictionary(this.strm,P)),u===h.Z_BUF_ERROR&&Z===!0&&(u=h.Z_OK,Z=!1),u!==h.Z_STREAM_END&&u!==h.Z_OK)return this.onEnd(u),!(this.ended=!0);B.next_out&&(B.avail_out!==0&&u!==h.Z_STREAM_END&&(B.avail_in!==0||v!==h.Z_FINISH&&v!==h.Z_SYNC_FLUSH)||(this.options.to==="string"?(m=i.utf8border(B.output,B.next_out),c=B.next_out-m,S=i.buf2string(B.output,m),B.next_out=c,B.avail_out=W-c,c&&d.arraySet(B.output,B.output,m,c,0),this.onData(S)):this.onData(d.shrinkBuf(B.output,B.next_out)))),B.avail_in===0&&B.avail_out===0&&(Z=!0)}while((0<B.avail_in||B.avail_out===0)&&u!==h.Z_STREAM_END);return u===h.Z_STREAM_END&&(v=h.Z_FINISH),v===h.Z_FINISH?(u=l.inflateEnd(this.strm),this.onEnd(u),this.ended=!0,u===h.Z_OK):v!==h.Z_SYNC_FLUSH||(this.onEnd(h.Z_OK),!(B.avail_out=0))},y.prototype.onData=function(b){this.chunks.push(b)},y.prototype.onEnd=function(b){b===h.Z_OK&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=d.flattenChunks(this.chunks)),this.chunks=[],this.err=b,this.msg=this.strm.msg},o.Inflate=y,o.inflate=T,o.inflateRaw=function(b,I){return(I=I||{}).raw=!0,T(b,I)},o.ungzip=T},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(t,a,o){var l=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Int32Array<"u";o.assign=function(h){for(var x=Array.prototype.slice.call(arguments,1);x.length;){var A=x.shift();if(A){if(typeof A!="object")throw new TypeError(A+"must be non-object");for(var R in A)A.hasOwnProperty(R)&&(h[R]=A[R])}}return h},o.shrinkBuf=function(h,x){return h.length===x?h:h.subarray?h.subarray(0,x):(h.length=x,h)};var d={arraySet:function(h,x,A,R,k){if(x.subarray&&h.subarray)h.set(x.subarray(A,A+R),k);else for(var y=0;y<R;y++)h[k+y]=x[A+y]},flattenChunks:function(h){var x,A,R,k,y,T;for(x=R=0,A=h.length;x<A;x++)R+=h[x].length;for(T=new Uint8Array(R),x=k=0,A=h.length;x<A;x++)y=h[x],T.set(y,k),k+=y.length;return T}},i={arraySet:function(h,x,A,R,k){for(var y=0;y<R;y++)h[k+y]=x[A+y]},flattenChunks:function(h){return[].concat.apply([],h)}};o.setTyped=function(h){h?(o.Buf8=Uint8Array,o.Buf16=Uint16Array,o.Buf32=Int32Array,o.assign(o,d)):(o.Buf8=Array,o.Buf16=Array,o.Buf32=Array,o.assign(o,i))},o.setTyped(l)},{}],42:[function(t,a,o){var l=t("./common"),d=!0,i=!0;try{String.fromCharCode.apply(null,[0])}catch{d=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{i=!1}for(var h=new l.Buf8(256),x=0;x<256;x++)h[x]=252<=x?6:248<=x?5:240<=x?4:224<=x?3:192<=x?2:1;function A(R,k){if(k<65537&&(R.subarray&&i||!R.subarray&&d))return String.fromCharCode.apply(null,l.shrinkBuf(R,k));for(var y="",T=0;T<k;T++)y+=String.fromCharCode(R[T]);return y}h[254]=h[254]=1,o.string2buf=function(R){var k,y,T,b,I,u=R.length,v=0;for(b=0;b<u;b++)(64512&(y=R.charCodeAt(b)))==55296&&b+1<u&&(64512&(T=R.charCodeAt(b+1)))==56320&&(y=65536+(y-55296<<10)+(T-56320),b++),v+=y<128?1:y<2048?2:y<65536?3:4;for(k=new l.Buf8(v),b=I=0;I<v;b++)(64512&(y=R.charCodeAt(b)))==55296&&b+1<u&&(64512&(T=R.charCodeAt(b+1)))==56320&&(y=65536+(y-55296<<10)+(T-56320),b++),y<128?k[I++]=y:(y<2048?k[I++]=192|y>>>6:(y<65536?k[I++]=224|y>>>12:(k[I++]=240|y>>>18,k[I++]=128|y>>>12&63),k[I++]=128|y>>>6&63),k[I++]=128|63&y);return k},o.buf2binstring=function(R){return A(R,R.length)},o.binstring2buf=function(R){for(var k=new l.Buf8(R.length),y=0,T=k.length;y<T;y++)k[y]=R.charCodeAt(y);return k},o.buf2string=function(R,k){var y,T,b,I,u=k||R.length,v=new Array(2*u);for(y=T=0;y<u;)if((b=R[y++])<128)v[T++]=b;else if(4<(I=h[b]))v[T++]=65533,y+=I-1;else{for(b&=I===2?31:I===3?15:7;1<I&&y<u;)b=b<<6|63&R[y++],I--;1<I?v[T++]=65533:b<65536?v[T++]=b:(b-=65536,v[T++]=55296|b>>10&1023,v[T++]=56320|1023&b)}return A(v,T)},o.utf8border=function(R,k){var y;for((k=k||R.length)>R.length&&(k=R.length),y=k-1;0<=y&&(192&R[y])==128;)y--;return y<0||y===0?k:y+h[R[y]]>k?y:k}},{"./common":41}],43:[function(t,a,o){a.exports=function(l,d,i,h){for(var x=65535&l|0,A=l>>>16&65535|0,R=0;i!==0;){for(i-=R=2e3<i?2e3:i;A=A+(x=x+d[h++]|0)|0,--R;);x%=65521,A%=65521}return x|A<<16|0}},{}],44:[function(t,a,o){a.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(t,a,o){var l=function(){for(var d,i=[],h=0;h<256;h++){d=h;for(var x=0;x<8;x++)d=1&d?3988292384^d>>>1:d>>>1;i[h]=d}return i}();a.exports=function(d,i,h,x){var A=l,R=x+h;d^=-1;for(var k=x;k<R;k++)d=d>>>8^A[255&(d^i[k])];return-1^d}},{}],46:[function(t,a,o){var l,d=t("../utils/common"),i=t("./trees"),h=t("./adler32"),x=t("./crc32"),A=t("./messages"),R=0,k=4,y=0,T=-2,b=-1,I=4,u=2,v=8,m=9,c=286,S=30,P=19,B=2*c+1,W=15,C=3,Z=258,le=Z+C+1,N=42,U=113,g=1,K=2,ee=3,z=4;function se(f,Y){return f.msg=A[Y],Y}function Q(f){return(f<<1)-(4<f?9:0)}function ce(f){for(var Y=f.length;0<=--Y;)f[Y]=0}function V(f){var Y=f.state,E=Y.pending;E>f.avail_out&&(E=f.avail_out),E!==0&&(d.arraySet(f.output,Y.pending_buf,Y.pending_out,E,f.next_out),f.next_out+=E,Y.pending_out+=E,f.total_out+=E,f.avail_out-=E,Y.pending-=E,Y.pending===0&&(Y.pending_out=0))}function D(f,Y){i._tr_flush_block(f,0<=f.block_start?f.block_start:-1,f.strstart-f.block_start,Y),f.block_start=f.strstart,V(f.strm)}function $(f,Y){f.pending_buf[f.pending++]=Y}function X(f,Y){f.pending_buf[f.pending++]=Y>>>8&255,f.pending_buf[f.pending++]=255&Y}function te(f,Y){var E,n,s=f.max_chain_length,p=f.strstart,L=f.prev_length,q=f.nice_match,H=f.strstart>f.w_size-le?f.strstart-(f.w_size-le):0,ie=f.window,oe=f.w_mask,ae=f.prev,he=f.strstart+Z,de=ie[p+L-1],pe=ie[p+L];f.prev_length>=f.good_match&&(s>>=2),q>f.lookahead&&(q=f.lookahead);do if(ie[(E=Y)+L]===pe&&ie[E+L-1]===de&&ie[E]===ie[p]&&ie[++E]===ie[p+1]){p+=2,E++;do;while(ie[++p]===ie[++E]&&ie[++p]===ie[++E]&&ie[++p]===ie[++E]&&ie[++p]===ie[++E]&&ie[++p]===ie[++E]&&ie[++p]===ie[++E]&&ie[++p]===ie[++E]&&ie[++p]===ie[++E]&&p<he);if(n=Z-(he-p),p=he-Z,L<n){if(f.match_start=Y,q<=(L=n))break;de=ie[p+L-1],pe=ie[p+L]}}while((Y=ae[Y&oe])>H&&--s!=0);return L<=f.lookahead?L:f.lookahead}function G(f){var Y,E,n,s,p,L,q,H,ie,oe,ae=f.w_size;do{if(s=f.window_size-f.lookahead-f.strstart,f.strstart>=ae+(ae-le)){for(d.arraySet(f.window,f.window,ae,ae,0),f.match_start-=ae,f.strstart-=ae,f.block_start-=ae,Y=E=f.hash_size;n=f.head[--Y],f.head[Y]=ae<=n?n-ae:0,--E;);for(Y=E=ae;n=f.prev[--Y],f.prev[Y]=ae<=n?n-ae:0,--E;);s+=ae}if(f.strm.avail_in===0)break;if(L=f.strm,q=f.window,H=f.strstart+f.lookahead,ie=s,oe=void 0,oe=L.avail_in,ie<oe&&(oe=ie),E=oe===0?0:(L.avail_in-=oe,d.arraySet(q,L.input,L.next_in,oe,H),L.state.wrap===1?L.adler=h(L.adler,q,oe,H):L.state.wrap===2&&(L.adler=x(L.adler,q,oe,H)),L.next_in+=oe,L.total_in+=oe,oe),f.lookahead+=E,f.lookahead+f.insert>=C)for(p=f.strstart-f.insert,f.ins_h=f.window[p],f.ins_h=(f.ins_h<<f.hash_shift^f.window[p+1])&f.hash_mask;f.insert&&(f.ins_h=(f.ins_h<<f.hash_shift^f.window[p+C-1])&f.hash_mask,f.prev[p&f.w_mask]=f.head[f.ins_h],f.head[f.ins_h]=p,p++,f.insert--,!(f.lookahead+f.insert<C)););}while(f.lookahead<le&&f.strm.avail_in!==0)}function _(f,Y){for(var E,n;;){if(f.lookahead<le){if(G(f),f.lookahead<le&&Y===R)return g;if(f.lookahead===0)break}if(E=0,f.lookahead>=C&&(f.ins_h=(f.ins_h<<f.hash_shift^f.window[f.strstart+C-1])&f.hash_mask,E=f.prev[f.strstart&f.w_mask]=f.head[f.ins_h],f.head[f.ins_h]=f.strstart),E!==0&&f.strstart-E<=f.w_size-le&&(f.match_length=te(f,E)),f.match_length>=C)if(n=i._tr_tally(f,f.strstart-f.match_start,f.match_length-C),f.lookahead-=f.match_length,f.match_length<=f.max_lazy_match&&f.lookahead>=C){for(f.match_length--;f.strstart++,f.ins_h=(f.ins_h<<f.hash_shift^f.window[f.strstart+C-1])&f.hash_mask,E=f.prev[f.strstart&f.w_mask]=f.head[f.ins_h],f.head[f.ins_h]=f.strstart,--f.match_length!=0;);f.strstart++}else f.strstart+=f.match_length,f.match_length=0,f.ins_h=f.window[f.strstart],f.ins_h=(f.ins_h<<f.hash_shift^f.window[f.strstart+1])&f.hash_mask;else n=i._tr_tally(f,0,f.window[f.strstart]),f.lookahead--,f.strstart++;if(n&&(D(f,!1),f.strm.avail_out===0))return g}return f.insert=f.strstart<C-1?f.strstart:C-1,Y===k?(D(f,!0),f.strm.avail_out===0?ee:z):f.last_lit&&(D(f,!1),f.strm.avail_out===0)?g:K}function w(f,Y){for(var E,n,s;;){if(f.lookahead<le){if(G(f),f.lookahead<le&&Y===R)return g;if(f.lookahead===0)break}if(E=0,f.lookahead>=C&&(f.ins_h=(f.ins_h<<f.hash_shift^f.window[f.strstart+C-1])&f.hash_mask,E=f.prev[f.strstart&f.w_mask]=f.head[f.ins_h],f.head[f.ins_h]=f.strstart),f.prev_length=f.match_length,f.prev_match=f.match_start,f.match_length=C-1,E!==0&&f.prev_length<f.max_lazy_match&&f.strstart-E<=f.w_size-le&&(f.match_length=te(f,E),f.match_length<=5&&(f.strategy===1||f.match_length===C&&4096<f.strstart-f.match_start)&&(f.match_length=C-1)),f.prev_length>=C&&f.match_length<=f.prev_length){for(s=f.strstart+f.lookahead-C,n=i._tr_tally(f,f.strstart-1-f.prev_match,f.prev_length-C),f.lookahead-=f.prev_length-1,f.prev_length-=2;++f.strstart<=s&&(f.ins_h=(f.ins_h<<f.hash_shift^f.window[f.strstart+C-1])&f.hash_mask,E=f.prev[f.strstart&f.w_mask]=f.head[f.ins_h],f.head[f.ins_h]=f.strstart),--f.prev_length!=0;);if(f.match_available=0,f.match_length=C-1,f.strstart++,n&&(D(f,!1),f.strm.avail_out===0))return g}else if(f.match_available){if((n=i._tr_tally(f,0,f.window[f.strstart-1]))&&D(f,!1),f.strstart++,f.lookahead--,f.strm.avail_out===0)return g}else f.match_available=1,f.strstart++,f.lookahead--}return f.match_available&&(n=i._tr_tally(f,0,f.window[f.strstart-1]),f.match_available=0),f.insert=f.strstart<C-1?f.strstart:C-1,Y===k?(D(f,!0),f.strm.avail_out===0?ee:z):f.last_lit&&(D(f,!1),f.strm.avail_out===0)?g:K}function j(f,Y,E,n,s){this.good_length=f,this.max_lazy=Y,this.nice_length=E,this.max_chain=n,this.func=s}function M(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=v,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new d.Buf16(2*B),this.dyn_dtree=new d.Buf16(2*(2*S+1)),this.bl_tree=new d.Buf16(2*(2*P+1)),ce(this.dyn_ltree),ce(this.dyn_dtree),ce(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new d.Buf16(W+1),this.heap=new d.Buf16(2*c+1),ce(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new d.Buf16(2*c+1),ce(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function O(f){var Y;return f&&f.state?(f.total_in=f.total_out=0,f.data_type=u,(Y=f.state).pending=0,Y.pending_out=0,Y.wrap<0&&(Y.wrap=-Y.wrap),Y.status=Y.wrap?N:U,f.adler=Y.wrap===2?0:1,Y.last_flush=R,i._tr_init(Y),y):se(f,T)}function F(f){var Y=O(f);return Y===y&&function(E){E.window_size=2*E.w_size,ce(E.head),E.max_lazy_match=l[E.level].max_lazy,E.good_match=l[E.level].good_length,E.nice_match=l[E.level].nice_length,E.max_chain_length=l[E.level].max_chain,E.strstart=0,E.block_start=0,E.lookahead=0,E.insert=0,E.match_length=E.prev_length=C-1,E.match_available=0,E.ins_h=0}(f.state),Y}function J(f,Y,E,n,s,p){if(!f)return T;var L=1;if(Y===b&&(Y=6),n<0?(L=0,n=-n):15<n&&(L=2,n-=16),s<1||m<s||E!==v||n<8||15<n||Y<0||9<Y||p<0||I<p)return se(f,T);n===8&&(n=9);var q=new M;return(f.state=q).strm=f,q.wrap=L,q.gzhead=null,q.w_bits=n,q.w_size=1<<q.w_bits,q.w_mask=q.w_size-1,q.hash_bits=s+7,q.hash_size=1<<q.hash_bits,q.hash_mask=q.hash_size-1,q.hash_shift=~~((q.hash_bits+C-1)/C),q.window=new d.Buf8(2*q.w_size),q.head=new d.Buf16(q.hash_size),q.prev=new d.Buf16(q.w_size),q.lit_bufsize=1<<s+6,q.pending_buf_size=4*q.lit_bufsize,q.pending_buf=new d.Buf8(q.pending_buf_size),q.d_buf=1*q.lit_bufsize,q.l_buf=3*q.lit_bufsize,q.level=Y,q.strategy=p,q.method=E,F(f)}l=[new j(0,0,0,0,function(f,Y){var E=65535;for(E>f.pending_buf_size-5&&(E=f.pending_buf_size-5);;){if(f.lookahead<=1){if(G(f),f.lookahead===0&&Y===R)return g;if(f.lookahead===0)break}f.strstart+=f.lookahead,f.lookahead=0;var n=f.block_start+E;if((f.strstart===0||f.strstart>=n)&&(f.lookahead=f.strstart-n,f.strstart=n,D(f,!1),f.strm.avail_out===0)||f.strstart-f.block_start>=f.w_size-le&&(D(f,!1),f.strm.avail_out===0))return g}return f.insert=0,Y===k?(D(f,!0),f.strm.avail_out===0?ee:z):(f.strstart>f.block_start&&(D(f,!1),f.strm.avail_out),g)}),new j(4,4,8,4,_),new j(4,5,16,8,_),new j(4,6,32,32,_),new j(4,4,16,16,w),new j(8,16,32,32,w),new j(8,16,128,128,w),new j(8,32,128,256,w),new j(32,128,258,1024,w),new j(32,258,258,4096,w)],o.deflateInit=function(f,Y){return J(f,Y,v,15,8,0)},o.deflateInit2=J,o.deflateReset=F,o.deflateResetKeep=O,o.deflateSetHeader=function(f,Y){return f&&f.state?f.state.wrap!==2?T:(f.state.gzhead=Y,y):T},o.deflate=function(f,Y){var E,n,s,p;if(!f||!f.state||5<Y||Y<0)return f?se(f,T):T;if(n=f.state,!f.output||!f.input&&f.avail_in!==0||n.status===666&&Y!==k)return se(f,f.avail_out===0?-5:T);if(n.strm=f,E=n.last_flush,n.last_flush=Y,n.status===N)if(n.wrap===2)f.adler=0,$(n,31),$(n,139),$(n,8),n.gzhead?($(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),$(n,255&n.gzhead.time),$(n,n.gzhead.time>>8&255),$(n,n.gzhead.time>>16&255),$(n,n.gzhead.time>>24&255),$(n,n.level===9?2:2<=n.strategy||n.level<2?4:0),$(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&($(n,255&n.gzhead.extra.length),$(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(f.adler=x(f.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):($(n,0),$(n,0),$(n,0),$(n,0),$(n,0),$(n,n.level===9?2:2<=n.strategy||n.level<2?4:0),$(n,3),n.status=U);else{var L=v+(n.w_bits-8<<4)<<8;L|=(2<=n.strategy||n.level<2?0:n.level<6?1:n.level===6?2:3)<<6,n.strstart!==0&&(L|=32),L+=31-L%31,n.status=U,X(n,L),n.strstart!==0&&(X(n,f.adler>>>16),X(n,65535&f.adler)),f.adler=1}if(n.status===69)if(n.gzhead.extra){for(s=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>s&&(f.adler=x(f.adler,n.pending_buf,n.pending-s,s)),V(f),s=n.pending,n.pending!==n.pending_buf_size));)$(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>s&&(f.adler=x(f.adler,n.pending_buf,n.pending-s,s)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(n.status===73)if(n.gzhead.name){s=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>s&&(f.adler=x(f.adler,n.pending_buf,n.pending-s,s)),V(f),s=n.pending,n.pending===n.pending_buf_size)){p=1;break}p=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,$(n,p)}while(p!==0);n.gzhead.hcrc&&n.pending>s&&(f.adler=x(f.adler,n.pending_buf,n.pending-s,s)),p===0&&(n.gzindex=0,n.status=91)}else n.status=91;if(n.status===91)if(n.gzhead.comment){s=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>s&&(f.adler=x(f.adler,n.pending_buf,n.pending-s,s)),V(f),s=n.pending,n.pending===n.pending_buf_size)){p=1;break}p=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,$(n,p)}while(p!==0);n.gzhead.hcrc&&n.pending>s&&(f.adler=x(f.adler,n.pending_buf,n.pending-s,s)),p===0&&(n.status=103)}else n.status=103;if(n.status===103&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&V(f),n.pending+2<=n.pending_buf_size&&($(n,255&f.adler),$(n,f.adler>>8&255),f.adler=0,n.status=U)):n.status=U),n.pending!==0){if(V(f),f.avail_out===0)return n.last_flush=-1,y}else if(f.avail_in===0&&Q(Y)<=Q(E)&&Y!==k)return se(f,-5);if(n.status===666&&f.avail_in!==0)return se(f,-5);if(f.avail_in!==0||n.lookahead!==0||Y!==R&&n.status!==666){var q=n.strategy===2?function(H,ie){for(var oe;;){if(H.lookahead===0&&(G(H),H.lookahead===0)){if(ie===R)return g;break}if(H.match_length=0,oe=i._tr_tally(H,0,H.window[H.strstart]),H.lookahead--,H.strstart++,oe&&(D(H,!1),H.strm.avail_out===0))return g}return H.insert=0,ie===k?(D(H,!0),H.strm.avail_out===0?ee:z):H.last_lit&&(D(H,!1),H.strm.avail_out===0)?g:K}(n,Y):n.strategy===3?function(H,ie){for(var oe,ae,he,de,pe=H.window;;){if(H.lookahead<=Z){if(G(H),H.lookahead<=Z&&ie===R)return g;if(H.lookahead===0)break}if(H.match_length=0,H.lookahead>=C&&0<H.strstart&&(ae=pe[he=H.strstart-1])===pe[++he]&&ae===pe[++he]&&ae===pe[++he]){de=H.strstart+Z;do;while(ae===pe[++he]&&ae===pe[++he]&&ae===pe[++he]&&ae===pe[++he]&&ae===pe[++he]&&ae===pe[++he]&&ae===pe[++he]&&ae===pe[++he]&&he<de);H.match_length=Z-(de-he),H.match_length>H.lookahead&&(H.match_length=H.lookahead)}if(H.match_length>=C?(oe=i._tr_tally(H,1,H.match_length-C),H.lookahead-=H.match_length,H.strstart+=H.match_length,H.match_length=0):(oe=i._tr_tally(H,0,H.window[H.strstart]),H.lookahead--,H.strstart++),oe&&(D(H,!1),H.strm.avail_out===0))return g}return H.insert=0,ie===k?(D(H,!0),H.strm.avail_out===0?ee:z):H.last_lit&&(D(H,!1),H.strm.avail_out===0)?g:K}(n,Y):l[n.level].func(n,Y);if(q!==ee&&q!==z||(n.status=666),q===g||q===ee)return f.avail_out===0&&(n.last_flush=-1),y;if(q===K&&(Y===1?i._tr_align(n):Y!==5&&(i._tr_stored_block(n,0,0,!1),Y===3&&(ce(n.head),n.lookahead===0&&(n.strstart=0,n.block_start=0,n.insert=0))),V(f),f.avail_out===0))return n.last_flush=-1,y}return Y!==k?y:n.wrap<=0?1:(n.wrap===2?($(n,255&f.adler),$(n,f.adler>>8&255),$(n,f.adler>>16&255),$(n,f.adler>>24&255),$(n,255&f.total_in),$(n,f.total_in>>8&255),$(n,f.total_in>>16&255),$(n,f.total_in>>24&255)):(X(n,f.adler>>>16),X(n,65535&f.adler)),V(f),0<n.wrap&&(n.wrap=-n.wrap),n.pending!==0?y:1)},o.deflateEnd=function(f){var Y;return f&&f.state?(Y=f.state.status)!==N&&Y!==69&&Y!==73&&Y!==91&&Y!==103&&Y!==U&&Y!==666?se(f,T):(f.state=null,Y===U?se(f,-3):y):T},o.deflateSetDictionary=function(f,Y){var E,n,s,p,L,q,H,ie,oe=Y.length;if(!f||!f.state||(p=(E=f.state).wrap)===2||p===1&&E.status!==N||E.lookahead)return T;for(p===1&&(f.adler=h(f.adler,Y,oe,0)),E.wrap=0,oe>=E.w_size&&(p===0&&(ce(E.head),E.strstart=0,E.block_start=0,E.insert=0),ie=new d.Buf8(E.w_size),d.arraySet(ie,Y,oe-E.w_size,E.w_size,0),Y=ie,oe=E.w_size),L=f.avail_in,q=f.next_in,H=f.input,f.avail_in=oe,f.next_in=0,f.input=Y,G(E);E.lookahead>=C;){for(n=E.strstart,s=E.lookahead-(C-1);E.ins_h=(E.ins_h<<E.hash_shift^E.window[n+C-1])&E.hash_mask,E.prev[n&E.w_mask]=E.head[E.ins_h],E.head[E.ins_h]=n,n++,--s;);E.strstart=n,E.lookahead=C-1,G(E)}return E.strstart+=E.lookahead,E.block_start=E.strstart,E.insert=E.lookahead,E.lookahead=0,E.match_length=E.prev_length=C-1,E.match_available=0,f.next_in=q,f.input=H,f.avail_in=L,E.wrap=p,y},o.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(t,a,o){a.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(t,a,o){a.exports=function(l,d){var i,h,x,A,R,k,y,T,b,I,u,v,m,c,S,P,B,W,C,Z,le,N,U,g,K;i=l.state,h=l.next_in,g=l.input,x=h+(l.avail_in-5),A=l.next_out,K=l.output,R=A-(d-l.avail_out),k=A+(l.avail_out-257),y=i.dmax,T=i.wsize,b=i.whave,I=i.wnext,u=i.window,v=i.hold,m=i.bits,c=i.lencode,S=i.distcode,P=(1<<i.lenbits)-1,B=(1<<i.distbits)-1;e:do{m<15&&(v+=g[h++]<<m,m+=8,v+=g[h++]<<m,m+=8),W=c[v&P];t:for(;;){if(v>>>=C=W>>>24,m-=C,(C=W>>>16&255)===0)K[A++]=65535&W;else{if(!(16&C)){if(!(64&C)){W=c[(65535&W)+(v&(1<<C)-1)];continue t}if(32&C){i.mode=12;break e}l.msg="invalid literal/length code",i.mode=30;break e}Z=65535&W,(C&=15)&&(m<C&&(v+=g[h++]<<m,m+=8),Z+=v&(1<<C)-1,v>>>=C,m-=C),m<15&&(v+=g[h++]<<m,m+=8,v+=g[h++]<<m,m+=8),W=S[v&B];r:for(;;){if(v>>>=C=W>>>24,m-=C,!(16&(C=W>>>16&255))){if(!(64&C)){W=S[(65535&W)+(v&(1<<C)-1)];continue r}l.msg="invalid distance code",i.mode=30;break e}if(le=65535&W,m<(C&=15)&&(v+=g[h++]<<m,(m+=8)<C&&(v+=g[h++]<<m,m+=8)),y<(le+=v&(1<<C)-1)){l.msg="invalid distance too far back",i.mode=30;break e}if(v>>>=C,m-=C,(C=A-R)<le){if(b<(C=le-C)&&i.sane){l.msg="invalid distance too far back",i.mode=30;break e}if(U=u,(N=0)===I){if(N+=T-C,C<Z){for(Z-=C;K[A++]=u[N++],--C;);N=A-le,U=K}}else if(I<C){if(N+=T+I-C,(C-=I)<Z){for(Z-=C;K[A++]=u[N++],--C;);if(N=0,I<Z){for(Z-=C=I;K[A++]=u[N++],--C;);N=A-le,U=K}}}else if(N+=I-C,C<Z){for(Z-=C;K[A++]=u[N++],--C;);N=A-le,U=K}for(;2<Z;)K[A++]=U[N++],K[A++]=U[N++],K[A++]=U[N++],Z-=3;Z&&(K[A++]=U[N++],1<Z&&(K[A++]=U[N++]))}else{for(N=A-le;K[A++]=K[N++],K[A++]=K[N++],K[A++]=K[N++],2<(Z-=3););Z&&(K[A++]=K[N++],1<Z&&(K[A++]=K[N++]))}break}}break}}while(h<x&&A<k);h-=Z=m>>3,v&=(1<<(m-=Z<<3))-1,l.next_in=h,l.next_out=A,l.avail_in=h<x?x-h+5:5-(h-x),l.avail_out=A<k?k-A+257:257-(A-k),i.hold=v,i.bits=m}},{}],49:[function(t,a,o){var l=t("../utils/common"),d=t("./adler32"),i=t("./crc32"),h=t("./inffast"),x=t("./inftrees"),A=1,R=2,k=0,y=-2,T=1,b=852,I=592;function u(N){return(N>>>24&255)+(N>>>8&65280)+((65280&N)<<8)+((255&N)<<24)}function v(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new l.Buf16(320),this.work=new l.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function m(N){var U;return N&&N.state?(U=N.state,N.total_in=N.total_out=U.total=0,N.msg="",U.wrap&&(N.adler=1&U.wrap),U.mode=T,U.last=0,U.havedict=0,U.dmax=32768,U.head=null,U.hold=0,U.bits=0,U.lencode=U.lendyn=new l.Buf32(b),U.distcode=U.distdyn=new l.Buf32(I),U.sane=1,U.back=-1,k):y}function c(N){var U;return N&&N.state?((U=N.state).wsize=0,U.whave=0,U.wnext=0,m(N)):y}function S(N,U){var g,K;return N&&N.state?(K=N.state,U<0?(g=0,U=-U):(g=1+(U>>4),U<48&&(U&=15)),U&&(U<8||15<U)?y:(K.window!==null&&K.wbits!==U&&(K.window=null),K.wrap=g,K.wbits=U,c(N))):y}function P(N,U){var g,K;return N?(K=new v,(N.state=K).window=null,(g=S(N,U))!==k&&(N.state=null),g):y}var B,W,C=!0;function Z(N){if(C){var U;for(B=new l.Buf32(512),W=new l.Buf32(32),U=0;U<144;)N.lens[U++]=8;for(;U<256;)N.lens[U++]=9;for(;U<280;)N.lens[U++]=7;for(;U<288;)N.lens[U++]=8;for(x(A,N.lens,0,288,B,0,N.work,{bits:9}),U=0;U<32;)N.lens[U++]=5;x(R,N.lens,0,32,W,0,N.work,{bits:5}),C=!1}N.lencode=B,N.lenbits=9,N.distcode=W,N.distbits=5}function le(N,U,g,K){var ee,z=N.state;return z.window===null&&(z.wsize=1<<z.wbits,z.wnext=0,z.whave=0,z.window=new l.Buf8(z.wsize)),K>=z.wsize?(l.arraySet(z.window,U,g-z.wsize,z.wsize,0),z.wnext=0,z.whave=z.wsize):(K<(ee=z.wsize-z.wnext)&&(ee=K),l.arraySet(z.window,U,g-K,ee,z.wnext),(K-=ee)?(l.arraySet(z.window,U,g-K,K,0),z.wnext=K,z.whave=z.wsize):(z.wnext+=ee,z.wnext===z.wsize&&(z.wnext=0),z.whave<z.wsize&&(z.whave+=ee))),0}o.inflateReset=c,o.inflateReset2=S,o.inflateResetKeep=m,o.inflateInit=function(N){return P(N,15)},o.inflateInit2=P,o.inflate=function(N,U){var g,K,ee,z,se,Q,ce,V,D,$,X,te,G,_,w,j,M,O,F,J,f,Y,E,n,s=0,p=new l.Buf8(4),L=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!N||!N.state||!N.output||!N.input&&N.avail_in!==0)return y;(g=N.state).mode===12&&(g.mode=13),se=N.next_out,ee=N.output,ce=N.avail_out,z=N.next_in,K=N.input,Q=N.avail_in,V=g.hold,D=g.bits,$=Q,X=ce,Y=k;e:for(;;)switch(g.mode){case T:if(g.wrap===0){g.mode=13;break}for(;D<16;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(2&g.wrap&&V===35615){p[g.check=0]=255&V,p[1]=V>>>8&255,g.check=i(g.check,p,2,0),D=V=0,g.mode=2;break}if(g.flags=0,g.head&&(g.head.done=!1),!(1&g.wrap)||(((255&V)<<8)+(V>>8))%31){N.msg="incorrect header check",g.mode=30;break}if((15&V)!=8){N.msg="unknown compression method",g.mode=30;break}if(D-=4,f=8+(15&(V>>>=4)),g.wbits===0)g.wbits=f;else if(f>g.wbits){N.msg="invalid window size",g.mode=30;break}g.dmax=1<<f,N.adler=g.check=1,g.mode=512&V?10:12,D=V=0;break;case 2:for(;D<16;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(g.flags=V,(255&g.flags)!=8){N.msg="unknown compression method",g.mode=30;break}if(57344&g.flags){N.msg="unknown header flags set",g.mode=30;break}g.head&&(g.head.text=V>>8&1),512&g.flags&&(p[0]=255&V,p[1]=V>>>8&255,g.check=i(g.check,p,2,0)),D=V=0,g.mode=3;case 3:for(;D<32;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}g.head&&(g.head.time=V),512&g.flags&&(p[0]=255&V,p[1]=V>>>8&255,p[2]=V>>>16&255,p[3]=V>>>24&255,g.check=i(g.check,p,4,0)),D=V=0,g.mode=4;case 4:for(;D<16;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}g.head&&(g.head.xflags=255&V,g.head.os=V>>8),512&g.flags&&(p[0]=255&V,p[1]=V>>>8&255,g.check=i(g.check,p,2,0)),D=V=0,g.mode=5;case 5:if(1024&g.flags){for(;D<16;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}g.length=V,g.head&&(g.head.extra_len=V),512&g.flags&&(p[0]=255&V,p[1]=V>>>8&255,g.check=i(g.check,p,2,0)),D=V=0}else g.head&&(g.head.extra=null);g.mode=6;case 6:if(1024&g.flags&&(Q<(te=g.length)&&(te=Q),te&&(g.head&&(f=g.head.extra_len-g.length,g.head.extra||(g.head.extra=new Array(g.head.extra_len)),l.arraySet(g.head.extra,K,z,te,f)),512&g.flags&&(g.check=i(g.check,K,te,z)),Q-=te,z+=te,g.length-=te),g.length))break e;g.length=0,g.mode=7;case 7:if(2048&g.flags){if(Q===0)break e;for(te=0;f=K[z+te++],g.head&&f&&g.length<65536&&(g.head.name+=String.fromCharCode(f)),f&&te<Q;);if(512&g.flags&&(g.check=i(g.check,K,te,z)),Q-=te,z+=te,f)break e}else g.head&&(g.head.name=null);g.length=0,g.mode=8;case 8:if(4096&g.flags){if(Q===0)break e;for(te=0;f=K[z+te++],g.head&&f&&g.length<65536&&(g.head.comment+=String.fromCharCode(f)),f&&te<Q;);if(512&g.flags&&(g.check=i(g.check,K,te,z)),Q-=te,z+=te,f)break e}else g.head&&(g.head.comment=null);g.mode=9;case 9:if(512&g.flags){for(;D<16;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(V!==(65535&g.check)){N.msg="header crc mismatch",g.mode=30;break}D=V=0}g.head&&(g.head.hcrc=g.flags>>9&1,g.head.done=!0),N.adler=g.check=0,g.mode=12;break;case 10:for(;D<32;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}N.adler=g.check=u(V),D=V=0,g.mode=11;case 11:if(g.havedict===0)return N.next_out=se,N.avail_out=ce,N.next_in=z,N.avail_in=Q,g.hold=V,g.bits=D,2;N.adler=g.check=1,g.mode=12;case 12:if(U===5||U===6)break e;case 13:if(g.last){V>>>=7&D,D-=7&D,g.mode=27;break}for(;D<3;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}switch(g.last=1&V,D-=1,3&(V>>>=1)){case 0:g.mode=14;break;case 1:if(Z(g),g.mode=20,U!==6)break;V>>>=2,D-=2;break e;case 2:g.mode=17;break;case 3:N.msg="invalid block type",g.mode=30}V>>>=2,D-=2;break;case 14:for(V>>>=7&D,D-=7&D;D<32;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if((65535&V)!=(V>>>16^65535)){N.msg="invalid stored block lengths",g.mode=30;break}if(g.length=65535&V,D=V=0,g.mode=15,U===6)break e;case 15:g.mode=16;case 16:if(te=g.length){if(Q<te&&(te=Q),ce<te&&(te=ce),te===0)break e;l.arraySet(ee,K,z,te,se),Q-=te,z+=te,ce-=te,se+=te,g.length-=te;break}g.mode=12;break;case 17:for(;D<14;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(g.nlen=257+(31&V),V>>>=5,D-=5,g.ndist=1+(31&V),V>>>=5,D-=5,g.ncode=4+(15&V),V>>>=4,D-=4,286<g.nlen||30<g.ndist){N.msg="too many length or distance symbols",g.mode=30;break}g.have=0,g.mode=18;case 18:for(;g.have<g.ncode;){for(;D<3;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}g.lens[L[g.have++]]=7&V,V>>>=3,D-=3}for(;g.have<19;)g.lens[L[g.have++]]=0;if(g.lencode=g.lendyn,g.lenbits=7,E={bits:g.lenbits},Y=x(0,g.lens,0,19,g.lencode,0,g.work,E),g.lenbits=E.bits,Y){N.msg="invalid code lengths set",g.mode=30;break}g.have=0,g.mode=19;case 19:for(;g.have<g.nlen+g.ndist;){for(;j=(s=g.lencode[V&(1<<g.lenbits)-1])>>>16&255,M=65535&s,!((w=s>>>24)<=D);){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(M<16)V>>>=w,D-=w,g.lens[g.have++]=M;else{if(M===16){for(n=w+2;D<n;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(V>>>=w,D-=w,g.have===0){N.msg="invalid bit length repeat",g.mode=30;break}f=g.lens[g.have-1],te=3+(3&V),V>>>=2,D-=2}else if(M===17){for(n=w+3;D<n;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}D-=w,f=0,te=3+(7&(V>>>=w)),V>>>=3,D-=3}else{for(n=w+7;D<n;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}D-=w,f=0,te=11+(127&(V>>>=w)),V>>>=7,D-=7}if(g.have+te>g.nlen+g.ndist){N.msg="invalid bit length repeat",g.mode=30;break}for(;te--;)g.lens[g.have++]=f}}if(g.mode===30)break;if(g.lens[256]===0){N.msg="invalid code -- missing end-of-block",g.mode=30;break}if(g.lenbits=9,E={bits:g.lenbits},Y=x(A,g.lens,0,g.nlen,g.lencode,0,g.work,E),g.lenbits=E.bits,Y){N.msg="invalid literal/lengths set",g.mode=30;break}if(g.distbits=6,g.distcode=g.distdyn,E={bits:g.distbits},Y=x(R,g.lens,g.nlen,g.ndist,g.distcode,0,g.work,E),g.distbits=E.bits,Y){N.msg="invalid distances set",g.mode=30;break}if(g.mode=20,U===6)break e;case 20:g.mode=21;case 21:if(6<=Q&&258<=ce){N.next_out=se,N.avail_out=ce,N.next_in=z,N.avail_in=Q,g.hold=V,g.bits=D,h(N,X),se=N.next_out,ee=N.output,ce=N.avail_out,z=N.next_in,K=N.input,Q=N.avail_in,V=g.hold,D=g.bits,g.mode===12&&(g.back=-1);break}for(g.back=0;j=(s=g.lencode[V&(1<<g.lenbits)-1])>>>16&255,M=65535&s,!((w=s>>>24)<=D);){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(j&&!(240&j)){for(O=w,F=j,J=M;j=(s=g.lencode[J+((V&(1<<O+F)-1)>>O)])>>>16&255,M=65535&s,!(O+(w=s>>>24)<=D);){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}V>>>=O,D-=O,g.back+=O}if(V>>>=w,D-=w,g.back+=w,g.length=M,j===0){g.mode=26;break}if(32&j){g.back=-1,g.mode=12;break}if(64&j){N.msg="invalid literal/length code",g.mode=30;break}g.extra=15&j,g.mode=22;case 22:if(g.extra){for(n=g.extra;D<n;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}g.length+=V&(1<<g.extra)-1,V>>>=g.extra,D-=g.extra,g.back+=g.extra}g.was=g.length,g.mode=23;case 23:for(;j=(s=g.distcode[V&(1<<g.distbits)-1])>>>16&255,M=65535&s,!((w=s>>>24)<=D);){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(!(240&j)){for(O=w,F=j,J=M;j=(s=g.distcode[J+((V&(1<<O+F)-1)>>O)])>>>16&255,M=65535&s,!(O+(w=s>>>24)<=D);){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}V>>>=O,D-=O,g.back+=O}if(V>>>=w,D-=w,g.back+=w,64&j){N.msg="invalid distance code",g.mode=30;break}g.offset=M,g.extra=15&j,g.mode=24;case 24:if(g.extra){for(n=g.extra;D<n;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}g.offset+=V&(1<<g.extra)-1,V>>>=g.extra,D-=g.extra,g.back+=g.extra}if(g.offset>g.dmax){N.msg="invalid distance too far back",g.mode=30;break}g.mode=25;case 25:if(ce===0)break e;if(te=X-ce,g.offset>te){if((te=g.offset-te)>g.whave&&g.sane){N.msg="invalid distance too far back",g.mode=30;break}G=te>g.wnext?(te-=g.wnext,g.wsize-te):g.wnext-te,te>g.length&&(te=g.length),_=g.window}else _=ee,G=se-g.offset,te=g.length;for(ce<te&&(te=ce),ce-=te,g.length-=te;ee[se++]=_[G++],--te;);g.length===0&&(g.mode=21);break;case 26:if(ce===0)break e;ee[se++]=g.length,ce--,g.mode=21;break;case 27:if(g.wrap){for(;D<32;){if(Q===0)break e;Q--,V|=K[z++]<<D,D+=8}if(X-=ce,N.total_out+=X,g.total+=X,X&&(N.adler=g.check=g.flags?i(g.check,ee,X,se-X):d(g.check,ee,X,se-X)),X=ce,(g.flags?V:u(V))!==g.check){N.msg="incorrect data check",g.mode=30;break}D=V=0}g.mode=28;case 28:if(g.wrap&&g.flags){for(;D<32;){if(Q===0)break e;Q--,V+=K[z++]<<D,D+=8}if(V!==(4294967295&g.total)){N.msg="incorrect length check",g.mode=30;break}D=V=0}g.mode=29;case 29:Y=1;break e;case 30:Y=-3;break e;case 31:return-4;case 32:default:return y}return N.next_out=se,N.avail_out=ce,N.next_in=z,N.avail_in=Q,g.hold=V,g.bits=D,(g.wsize||X!==N.avail_out&&g.mode<30&&(g.mode<27||U!==4))&&le(N,N.output,N.next_out,X-N.avail_out)?(g.mode=31,-4):($-=N.avail_in,X-=N.avail_out,N.total_in+=$,N.total_out+=X,g.total+=X,g.wrap&&X&&(N.adler=g.check=g.flags?i(g.check,ee,X,N.next_out-X):d(g.check,ee,X,N.next_out-X)),N.data_type=g.bits+(g.last?64:0)+(g.mode===12?128:0)+(g.mode===20||g.mode===15?256:0),($==0&&X===0||U===4)&&Y===k&&(Y=-5),Y)},o.inflateEnd=function(N){if(!N||!N.state)return y;var U=N.state;return U.window&&(U.window=null),N.state=null,k},o.inflateGetHeader=function(N,U){var g;return N&&N.state&&2&(g=N.state).wrap?((g.head=U).done=!1,k):y},o.inflateSetDictionary=function(N,U){var g,K=U.length;return N&&N.state?(g=N.state).wrap!==0&&g.mode!==11?y:g.mode===11&&d(1,U,K,0)!==g.check?-3:le(N,U,K,K)?(g.mode=31,-4):(g.havedict=1,k):y},o.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(t,a,o){var l=t("../utils/common"),d=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],i=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],h=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],x=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];a.exports=function(A,R,k,y,T,b,I,u){var v,m,c,S,P,B,W,C,Z,le=u.bits,N=0,U=0,g=0,K=0,ee=0,z=0,se=0,Q=0,ce=0,V=0,D=null,$=0,X=new l.Buf16(16),te=new l.Buf16(16),G=null,_=0;for(N=0;N<=15;N++)X[N]=0;for(U=0;U<y;U++)X[R[k+U]]++;for(ee=le,K=15;1<=K&&X[K]===0;K--);if(K<ee&&(ee=K),K===0)return T[b++]=20971520,T[b++]=20971520,u.bits=1,0;for(g=1;g<K&&X[g]===0;g++);for(ee<g&&(ee=g),N=Q=1;N<=15;N++)if(Q<<=1,(Q-=X[N])<0)return-1;if(0<Q&&(A===0||K!==1))return-1;for(te[1]=0,N=1;N<15;N++)te[N+1]=te[N]+X[N];for(U=0;U<y;U++)R[k+U]!==0&&(I[te[R[k+U]]++]=U);if(B=A===0?(D=G=I,19):A===1?(D=d,$-=257,G=i,_-=257,256):(D=h,G=x,-1),N=g,P=b,se=U=V=0,c=-1,S=(ce=1<<(z=ee))-1,A===1&&852<ce||A===2&&592<ce)return 1;for(;;){for(W=N-se,Z=I[U]<B?(C=0,I[U]):I[U]>B?(C=G[_+I[U]],D[$+I[U]]):(C=96,0),v=1<<N-se,g=m=1<<z;T[P+(V>>se)+(m-=v)]=W<<24|C<<16|Z|0,m!==0;);for(v=1<<N-1;V&v;)v>>=1;if(v!==0?(V&=v-1,V+=v):V=0,U++,--X[N]==0){if(N===K)break;N=R[k+I[U]]}if(ee<N&&(V&S)!==c){for(se===0&&(se=ee),P+=g,Q=1<<(z=N-se);z+se<K&&!((Q-=X[z+se])<=0);)z++,Q<<=1;if(ce+=1<<z,A===1&&852<ce||A===2&&592<ce)return 1;T[c=V&S]=ee<<24|z<<16|P-b|0}}return V!==0&&(T[P+V]=N-se<<24|64<<16|0),u.bits=ee,0}},{"../utils/common":41}],51:[function(t,a,o){a.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(t,a,o){var l=t("../utils/common"),d=0,i=1;function h(s){for(var p=s.length;0<=--p;)s[p]=0}var x=0,A=29,R=256,k=R+1+A,y=30,T=19,b=2*k+1,I=15,u=16,v=7,m=256,c=16,S=17,P=18,B=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],W=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],C=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],Z=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],le=new Array(2*(k+2));h(le);var N=new Array(2*y);h(N);var U=new Array(512);h(U);var g=new Array(256);h(g);var K=new Array(A);h(K);var ee,z,se,Q=new Array(y);function ce(s,p,L,q,H){this.static_tree=s,this.extra_bits=p,this.extra_base=L,this.elems=q,this.max_length=H,this.has_stree=s&&s.length}function V(s,p){this.dyn_tree=s,this.max_code=0,this.stat_desc=p}function D(s){return s<256?U[s]:U[256+(s>>>7)]}function $(s,p){s.pending_buf[s.pending++]=255&p,s.pending_buf[s.pending++]=p>>>8&255}function X(s,p,L){s.bi_valid>u-L?(s.bi_buf|=p<<s.bi_valid&65535,$(s,s.bi_buf),s.bi_buf=p>>u-s.bi_valid,s.bi_valid+=L-u):(s.bi_buf|=p<<s.bi_valid&65535,s.bi_valid+=L)}function te(s,p,L){X(s,L[2*p],L[2*p+1])}function G(s,p){for(var L=0;L|=1&s,s>>>=1,L<<=1,0<--p;);return L>>>1}function _(s,p,L){var q,H,ie=new Array(I+1),oe=0;for(q=1;q<=I;q++)ie[q]=oe=oe+L[q-1]<<1;for(H=0;H<=p;H++){var ae=s[2*H+1];ae!==0&&(s[2*H]=G(ie[ae]++,ae))}}function w(s){var p;for(p=0;p<k;p++)s.dyn_ltree[2*p]=0;for(p=0;p<y;p++)s.dyn_dtree[2*p]=0;for(p=0;p<T;p++)s.bl_tree[2*p]=0;s.dyn_ltree[2*m]=1,s.opt_len=s.static_len=0,s.last_lit=s.matches=0}function j(s){8<s.bi_valid?$(s,s.bi_buf):0<s.bi_valid&&(s.pending_buf[s.pending++]=s.bi_buf),s.bi_buf=0,s.bi_valid=0}function M(s,p,L,q){var H=2*p,ie=2*L;return s[H]<s[ie]||s[H]===s[ie]&&q[p]<=q[L]}function O(s,p,L){for(var q=s.heap[L],H=L<<1;H<=s.heap_len&&(H<s.heap_len&&M(p,s.heap[H+1],s.heap[H],s.depth)&&H++,!M(p,q,s.heap[H],s.depth));)s.heap[L]=s.heap[H],L=H,H<<=1;s.heap[L]=q}function F(s,p,L){var q,H,ie,oe,ae=0;if(s.last_lit!==0)for(;q=s.pending_buf[s.d_buf+2*ae]<<8|s.pending_buf[s.d_buf+2*ae+1],H=s.pending_buf[s.l_buf+ae],ae++,q===0?te(s,H,p):(te(s,(ie=g[H])+R+1,p),(oe=B[ie])!==0&&X(s,H-=K[ie],oe),te(s,ie=D(--q),L),(oe=W[ie])!==0&&X(s,q-=Q[ie],oe)),ae<s.last_lit;);te(s,m,p)}function J(s,p){var L,q,H,ie=p.dyn_tree,oe=p.stat_desc.static_tree,ae=p.stat_desc.has_stree,he=p.stat_desc.elems,de=-1;for(s.heap_len=0,s.heap_max=b,L=0;L<he;L++)ie[2*L]!==0?(s.heap[++s.heap_len]=de=L,s.depth[L]=0):ie[2*L+1]=0;for(;s.heap_len<2;)ie[2*(H=s.heap[++s.heap_len]=de<2?++de:0)]=1,s.depth[H]=0,s.opt_len--,ae&&(s.static_len-=oe[2*H+1]);for(p.max_code=de,L=s.heap_len>>1;1<=L;L--)O(s,ie,L);for(H=he;L=s.heap[1],s.heap[1]=s.heap[s.heap_len--],O(s,ie,1),q=s.heap[1],s.heap[--s.heap_max]=L,s.heap[--s.heap_max]=q,ie[2*H]=ie[2*L]+ie[2*q],s.depth[H]=(s.depth[L]>=s.depth[q]?s.depth[L]:s.depth[q])+1,ie[2*L+1]=ie[2*q+1]=H,s.heap[1]=H++,O(s,ie,1),2<=s.heap_len;);s.heap[--s.heap_max]=s.heap[1],function(pe,Se){var Ge,Ie,dt,Ee,kt,Jt,Be=Se.dyn_tree,fi=Se.max_code,Ao=Se.stat_desc.static_tree,To=Se.stat_desc.has_stree,So=Se.stat_desc.extra_bits,di=Se.stat_desc.extra_base,pt=Se.stat_desc.max_length,It=0;for(Ee=0;Ee<=I;Ee++)pe.bl_count[Ee]=0;for(Be[2*pe.heap[pe.heap_max]+1]=0,Ge=pe.heap_max+1;Ge<b;Ge++)pt<(Ee=Be[2*Be[2*(Ie=pe.heap[Ge])+1]+1]+1)&&(Ee=pt,It++),Be[2*Ie+1]=Ee,fi<Ie||(pe.bl_count[Ee]++,kt=0,di<=Ie&&(kt=So[Ie-di]),Jt=Be[2*Ie],pe.opt_len+=Jt*(Ee+kt),To&&(pe.static_len+=Jt*(Ao[2*Ie+1]+kt)));if(It!==0){do{for(Ee=pt-1;pe.bl_count[Ee]===0;)Ee--;pe.bl_count[Ee]--,pe.bl_count[Ee+1]+=2,pe.bl_count[pt]--,It-=2}while(0<It);for(Ee=pt;Ee!==0;Ee--)for(Ie=pe.bl_count[Ee];Ie!==0;)fi<(dt=pe.heap[--Ge])||(Be[2*dt+1]!==Ee&&(pe.opt_len+=(Ee-Be[2*dt+1])*Be[2*dt],Be[2*dt+1]=Ee),Ie--)}}(s,p),_(ie,de,s.bl_count)}function f(s,p,L){var q,H,ie=-1,oe=p[1],ae=0,he=7,de=4;for(oe===0&&(he=138,de=3),p[2*(L+1)+1]=65535,q=0;q<=L;q++)H=oe,oe=p[2*(q+1)+1],++ae<he&&H===oe||(ae<de?s.bl_tree[2*H]+=ae:H!==0?(H!==ie&&s.bl_tree[2*H]++,s.bl_tree[2*c]++):ae<=10?s.bl_tree[2*S]++:s.bl_tree[2*P]++,ie=H,de=(ae=0)===oe?(he=138,3):H===oe?(he=6,3):(he=7,4))}function Y(s,p,L){var q,H,ie=-1,oe=p[1],ae=0,he=7,de=4;for(oe===0&&(he=138,de=3),q=0;q<=L;q++)if(H=oe,oe=p[2*(q+1)+1],!(++ae<he&&H===oe)){if(ae<de)for(;te(s,H,s.bl_tree),--ae!=0;);else H!==0?(H!==ie&&(te(s,H,s.bl_tree),ae--),te(s,c,s.bl_tree),X(s,ae-3,2)):ae<=10?(te(s,S,s.bl_tree),X(s,ae-3,3)):(te(s,P,s.bl_tree),X(s,ae-11,7));ie=H,de=(ae=0)===oe?(he=138,3):H===oe?(he=6,3):(he=7,4)}}h(Q);var E=!1;function n(s,p,L,q){X(s,(x<<1)+(q?1:0),3),function(H,ie,oe,ae){j(H),$(H,oe),$(H,~oe),l.arraySet(H.pending_buf,H.window,ie,oe,H.pending),H.pending+=oe}(s,p,L)}o._tr_init=function(s){E||(function(){var p,L,q,H,ie,oe=new Array(I+1);for(H=q=0;H<A-1;H++)for(K[H]=q,p=0;p<1<<B[H];p++)g[q++]=H;for(g[q-1]=H,H=ie=0;H<16;H++)for(Q[H]=ie,p=0;p<1<<W[H];p++)U[ie++]=H;for(ie>>=7;H<y;H++)for(Q[H]=ie<<7,p=0;p<1<<W[H]-7;p++)U[256+ie++]=H;for(L=0;L<=I;L++)oe[L]=0;for(p=0;p<=143;)le[2*p+1]=8,p++,oe[8]++;for(;p<=255;)le[2*p+1]=9,p++,oe[9]++;for(;p<=279;)le[2*p+1]=7,p++,oe[7]++;for(;p<=287;)le[2*p+1]=8,p++,oe[8]++;for(_(le,k+1,oe),p=0;p<y;p++)N[2*p+1]=5,N[2*p]=G(p,5);ee=new ce(le,B,R+1,k,I),z=new ce(N,W,0,y,I),se=new ce(new Array(0),C,0,T,v)}(),E=!0),s.l_desc=new V(s.dyn_ltree,ee),s.d_desc=new V(s.dyn_dtree,z),s.bl_desc=new V(s.bl_tree,se),s.bi_buf=0,s.bi_valid=0,w(s)},o._tr_stored_block=n,o._tr_flush_block=function(s,p,L,q){var H,ie,oe=0;0<s.level?(s.strm.data_type===2&&(s.strm.data_type=function(ae){var he,de=4093624447;for(he=0;he<=31;he++,de>>>=1)if(1&de&&ae.dyn_ltree[2*he]!==0)return d;if(ae.dyn_ltree[18]!==0||ae.dyn_ltree[20]!==0||ae.dyn_ltree[26]!==0)return i;for(he=32;he<R;he++)if(ae.dyn_ltree[2*he]!==0)return i;return d}(s)),J(s,s.l_desc),J(s,s.d_desc),oe=function(ae){var he;for(f(ae,ae.dyn_ltree,ae.l_desc.max_code),f(ae,ae.dyn_dtree,ae.d_desc.max_code),J(ae,ae.bl_desc),he=T-1;3<=he&&ae.bl_tree[2*Z[he]+1]===0;he--);return ae.opt_len+=3*(he+1)+5+5+4,he}(s),H=s.opt_len+3+7>>>3,(ie=s.static_len+3+7>>>3)<=H&&(H=ie)):H=ie=L+5,L+4<=H&&p!==-1?n(s,p,L,q):s.strategy===4||ie===H?(X(s,2+(q?1:0),3),F(s,le,N)):(X(s,4+(q?1:0),3),function(ae,he,de,pe){var Se;for(X(ae,he-257,5),X(ae,de-1,5),X(ae,pe-4,4),Se=0;Se<pe;Se++)X(ae,ae.bl_tree[2*Z[Se]+1],3);Y(ae,ae.dyn_ltree,he-1),Y(ae,ae.dyn_dtree,de-1)}(s,s.l_desc.max_code+1,s.d_desc.max_code+1,oe+1),F(s,s.dyn_ltree,s.dyn_dtree)),w(s),q&&j(s)},o._tr_tally=function(s,p,L){return s.pending_buf[s.d_buf+2*s.last_lit]=p>>>8&255,s.pending_buf[s.d_buf+2*s.last_lit+1]=255&p,s.pending_buf[s.l_buf+s.last_lit]=255&L,s.last_lit++,p===0?s.dyn_ltree[2*L]++:(s.matches++,p--,s.dyn_ltree[2*(g[L]+R+1)]++,s.dyn_dtree[2*D(p)]++),s.last_lit===s.lit_bufsize-1},o._tr_align=function(s){X(s,2,3),te(s,m,le),function(p){p.bi_valid===16?($(p,p.bi_buf),p.bi_buf=0,p.bi_valid=0):8<=p.bi_valid&&(p.pending_buf[p.pending++]=255&p.bi_buf,p.bi_buf>>=8,p.bi_valid-=8)}(s)}},{"../utils/common":41}],53:[function(t,a,o){a.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(t,a,o){(function(l){(function(d,i){if(!d.setImmediate){var h,x,A,R,k=1,y={},T=!1,b=d.document,I=Object.getPrototypeOf&&Object.getPrototypeOf(d);I=I&&I.setTimeout?I:d,h={}.toString.call(d.process)==="[object process]"?function(c){we.nextTick(function(){v(c)})}:function(){if(d.postMessage&&!d.importScripts){var c=!0,S=d.onmessage;return d.onmessage=function(){c=!1},d.postMessage("","*"),d.onmessage=S,c}}()?(R="setImmediate$"+Math.random()+"$",d.addEventListener?d.addEventListener("message",m,!1):d.attachEvent("onmessage",m),function(c){d.postMessage(R+c,"*")}):d.MessageChannel?((A=new MessageChannel).port1.onmessage=function(c){v(c.data)},function(c){A.port2.postMessage(c)}):b&&"onreadystatechange"in b.createElement("script")?(x=b.documentElement,function(c){var S=b.createElement("script");S.onreadystatechange=function(){v(c),S.onreadystatechange=null,x.removeChild(S),S=null},x.appendChild(S)}):function(c){setTimeout(v,0,c)},I.setImmediate=function(c){typeof c!="function"&&(c=new Function(""+c));for(var S=new Array(arguments.length-1),P=0;P<S.length;P++)S[P]=arguments[P+1];var B={callback:c,args:S};return y[k]=B,h(k),k++},I.clearImmediate=u}function u(c){delete y[c]}function v(c){if(T)setTimeout(v,0,c);else{var S=y[c];if(S){T=!0;try{(function(P){var B=P.callback,W=P.args;switch(W.length){case 0:B();break;case 1:B(W[0]);break;case 2:B(W[0],W[1]);break;case 3:B(W[0],W[1],W[2]);break;default:B.apply(i,W)}})(S)}finally{u(c),T=!1}}}}function m(c){c.source===d&&typeof c.data=="string"&&c.data.indexOf(R)===0&&v(+c.data.slice(R.length))}})(typeof self>"u"?l===void 0?this:l:self)}).call(this,typeof Fe<"u"?Fe:typeof self<"u"?self:typeof window<"u"?window:{})},{}]},{},[10])(10)})}(Hn)),Hn.exports}var ud=od();const ld=Yn(ud);var yt={exports:{}},jn,ba;function cd(){if(ba)return jn;ba=1;var r={"&":"&amp;",'"':"&quot;","'":"&apos;","<":"&lt;",">":"&gt;"};function e(t){return t&&t.replace?t.replace(/([&"<>'])/g,function(a,o){return r[o]}):t}return jn=e,jn}var _a;function hd(){if(_a)return yt.exports;_a=1;var r=cd(),e=ni().Stream,t="    ";function a(x,A){typeof A!="object"&&(A={indent:A});var R=A.stream?new e:null,k="",y=!1,T=A.indent?A.indent===!0?t:A.indent:"",b=!0;function I(S){b?we.nextTick(S):S()}function u(S,P){if(P!==void 0&&(k+=P),S&&!y&&(R=R||new e,y=!0),S&&y){var B=k;I(function(){R.emit("data",B)}),k=""}}function v(S,P){i(u,d(S,T,T?1:0),P)}function m(){if(R){var S=k;I(function(){R.emit("data",S),R.emit("end"),R.readable=!1,R.emit("close")})}}function c(S){var P=S.encoding||"UTF-8",B={version:"1.0",encoding:P};S.standalone&&(B.standalone=S.standalone),v({"?xml":{_attr:B}}),k=k.replace("/>","?>")}return I(function(){b=!1}),A.declaration&&c(A.declaration),x&&x.forEach?x.forEach(function(S,P){var B;P+1===x.length&&(B=m),v(S,B)}):v(x,m),R?(R.readable=!0,R):k}function o(){var x=Array.prototype.slice.call(arguments),A={_elem:d(x)};return A.push=function(R){if(!this.append)throw new Error("not assigned to a parent!");var k=this,y=this._elem.indent;i(this.append,d(R,y,this._elem.icount+(y?1:0)),function(){k.append(!0)})},A.close=function(R){R!==void 0&&this.push(R),this.end&&this.end()},A}function l(x,A){return new Array(A||0).join(x||"")}function d(x,A,R){R=R||0;var k=l(A,R),y,T=x,b=!1;if(typeof x=="object"){var I=Object.keys(x);if(y=I[0],T=x[y],T&&T._elem)return T._elem.name=y,T._elem.icount=R,T._elem.indent=A,T._elem.indents=k,T._elem.interrupt=T,T._elem}var u=[],v=[],m;function c(S){var P=Object.keys(S);P.forEach(function(B){u.push(h(B,S[B]))})}switch(typeof T){case"object":if(T===null)break;T._attr&&c(T._attr),T._cdata&&v.push(("<![CDATA["+T._cdata).replace(/\]\]>/g,"]]]]><![CDATA[>")+"]]>"),T.forEach&&(m=!1,v.push(""),T.forEach(function(S){if(typeof S=="object"){var P=Object.keys(S)[0];P=="_attr"?c(S._attr):v.push(d(S,A,R+1))}else v.pop(),m=!0,v.push(r(S))}),m||v.push(""));break;default:v.push(r(T))}return{name:y,interrupt:b,attributes:u,content:v,icount:R,indents:k,indent:A}}function i(x,A,R){if(typeof A!="object")return x(!1,A);var k=A.interrupt?1:A.content.length;function y(){for(;A.content.length;){var b=A.content.shift();if(b!==void 0){if(T(b))return;i(x,b)}}x(!1,(k>1?A.indents:"")+(A.name?"</"+A.name+">":"")+(A.indent&&!R?`
`:"")),R&&R()}function T(b){return b.interrupt?(b.interrupt.append=x,b.interrupt.end=y,b.interrupt=!1,x(!0),!0):!1}if(x(!1,A.indents+(A.name?"<"+A.name:"")+(A.attributes.length?" "+A.attributes.join(" "):"")+(k?A.name?">":"":A.name?"/>":"")+(A.indent&&k>1?`
`:"")),!k)return x(!1,A.indent?`
`:"");T(A)||y()}function h(x,A){return x+'="'+r(A)+'"'}return yt.exports=a,yt.exports.element=yt.exports.Element=o,yt.exports}var fd=hd();const be=Yn(fd),vt=0,zn=32,dd=32,pd=(r,e)=>{const t=e.replace(/-/g,"");if(t.length!==dd)throw new Error(`Error: Cannot extract GUID from font filename: ${e}`);const o=t.replace(/(..)/g,"$1 ").trim().split(" ").map(h=>parseInt(h,16));o.reverse();const d=r.slice(vt,zn).map((h,x)=>h^o[x%o.length]),i=new Uint8Array(vt+d.length+Math.max(0,r.length-zn));return i.set(r.slice(0,vt)),i.set(d,vt),i.set(r.slice(zn),vt+d.length),i};class md{format(e,t={stack:[]}){const a=e.prepForXml(t);if(a)return a;throw Error("XMLComponent did not format correctly")}}class wd{replace(e,t,a){let o=e;return t.forEach((l,d)=>{o=o.replace(new RegExp(`{${l.fileName}}`,"g"),(a+d).toString())}),o}getMediaData(e,t){return t.Array.filter(a=>e.search(`{${a.fileName}}`)>0)}}class gd{replace(e,t){let a=e;for(const o of t)a=a.replace(new RegExp(`{${o.reference}-${o.instance}}`,"g"),o.numId.toString());return a}}class yd{constructor(){re(this,"formatter"),re(this,"imageReplacer"),re(this,"numberingReplacer"),this.formatter=new md,this.imageReplacer=new wd,this.numberingReplacer=new gd}compile(e,t,a=[]){const o=new ld,l=this.xmlifyFile(e,t),d=new Map(Object.entries(l));for(const[,i]of d)if(Array.isArray(i))for(const h of i)o.file(h.path,h.data);else o.file(i.path,i.data);for(const i of a)o.file(i.path,i.data);for(const i of e.Media.Array)i.type!=="svg"?o.file(`word/media/${i.fileName}`,i.data):(o.file(`word/media/${i.fileName}`,i.data),o.file(`word/media/${i.fallback.fileName}`,i.fallback.data));for(const{data:i,name:h,fontKey:x}of e.FontTable.fontOptionsWithKey){const[A]=h.split(".");o.file(`word/fonts/${A}.odttf`,pd(i,x))}return o}xmlifyFile(e,t){const a=e.Document.Relationships.RelationshipCount+1,o=be(this.formatter.format(e.Document.View,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}}),l=e.Comments.Relationships.RelationshipCount+1,d=be(this.formatter.format(e.Comments,{viewWrapper:{View:e.Comments,Relationships:e.Comments.Relationships},file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}}),i=this.imageReplacer.getMediaData(o,e.Media),h=this.imageReplacer.getMediaData(d,e.Media);return{Relationships:{data:(()=>(i.forEach((x,A)=>{e.Document.Relationships.createRelationship(a+A,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",`media/${x.fileName}`)}),e.Document.Relationships.createRelationship(e.Document.Relationships.RelationshipCount+1,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable","fontTable.xml"),be(this.formatter.format(e.Document.Relationships,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}})))(),path:"word/_rels/document.xml.rels"},Document:{data:(()=>{const x=this.imageReplacer.replace(o,i,a);return this.numberingReplacer.replace(x,e.Numbering.ConcreteNumbering)})(),path:"word/document.xml"},Styles:{data:(()=>{const x=be(this.formatter.format(e.Styles,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}});return this.numberingReplacer.replace(x,e.Numbering.ConcreteNumbering)})(),path:"word/styles.xml"},Properties:{data:be(this.formatter.format(e.CoreProperties,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}}),path:"docProps/core.xml"},Numbering:{data:be(this.formatter.format(e.Numbering,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}}),path:"word/numbering.xml"},FileRelationships:{data:be(this.formatter.format(e.FileRelationships,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}),path:"_rels/.rels"},HeaderRelationships:e.Headers.map((x,A)=>{const R=be(this.formatter.format(x.View,{viewWrapper:x,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}});return this.imageReplacer.getMediaData(R,e.Media).forEach((y,T)=>{x.Relationships.createRelationship(T,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",`media/${y.fileName}`)}),{data:be(this.formatter.format(x.Relationships,{viewWrapper:x,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}),path:`word/_rels/header${A+1}.xml.rels`}}),FooterRelationships:e.Footers.map((x,A)=>{const R=be(this.formatter.format(x.View,{viewWrapper:x,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}});return this.imageReplacer.getMediaData(R,e.Media).forEach((y,T)=>{x.Relationships.createRelationship(T,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",`media/${y.fileName}`)}),{data:be(this.formatter.format(x.Relationships,{viewWrapper:x,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}),path:`word/_rels/footer${A+1}.xml.rels`}}),Headers:e.Headers.map((x,A)=>{const R=be(this.formatter.format(x.View,{viewWrapper:x,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}),k=this.imageReplacer.getMediaData(R,e.Media),y=this.imageReplacer.replace(R,k,0);return{data:this.numberingReplacer.replace(y,e.Numbering.ConcreteNumbering),path:`word/header${A+1}.xml`}}),Footers:e.Footers.map((x,A)=>{const R=be(this.formatter.format(x.View,{viewWrapper:x,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}),k=this.imageReplacer.getMediaData(R,e.Media),y=this.imageReplacer.replace(R,k,0);return{data:this.numberingReplacer.replace(y,e.Numbering.ConcreteNumbering),path:`word/footer${A+1}.xml`}}),ContentTypes:{data:be(this.formatter.format(e.ContentTypes,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}),path:"[Content_Types].xml"},CustomProperties:{data:be(this.formatter.format(e.CustomProperties,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}}),path:"docProps/custom.xml"},AppProperties:{data:be(this.formatter.format(e.AppProperties,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}}),path:"docProps/app.xml"},FootNotes:{data:be(this.formatter.format(e.FootNotes.View,{viewWrapper:e.FootNotes,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}),path:"word/footnotes.xml"},FootNotesRelationships:{data:be(this.formatter.format(e.FootNotes.Relationships,{viewWrapper:e.FootNotes,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}),path:"word/_rels/footnotes.xml.rels"},Settings:{data:be(this.formatter.format(e.Settings,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}}),path:"word/settings.xml"},Comments:{data:(()=>{const x=this.imageReplacer.replace(d,h,l);return this.numberingReplacer.replace(x,e.Numbering.ConcreteNumbering)})(),path:"word/comments.xml"},CommentsRelationships:{data:(()=>(h.forEach((x,A)=>{e.Comments.Relationships.createRelationship(l+A,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",`media/${x.fileName}`)}),be(this.formatter.format(e.Comments.Relationships,{viewWrapper:{View:e.Comments,Relationships:e.Comments.Relationships},file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}})))(),path:"word/_rels/comments.xml.rels"},FontTable:{data:be(this.formatter.format(e.FontTable.View,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{standalone:"yes",encoding:"UTF-8"}}),path:"word/fontTable.xml"},FontTableRelationships:{data:(()=>be(this.formatter.format(e.FontTable.Relationships,{viewWrapper:e.Document,file:e,stack:[]}),{indent:t,declaration:{encoding:"UTF-8"}}))(),path:"word/_rels/fontTable.xml.rels"}}}}const vd={NONE:"",WITH_2_BLANKS:"  ",WITH_4_BLANKS:"    ",WITH_TAB:"	"},Ea=r=>r===!0?vd.WITH_2_BLANKS:r===!1?void 0:r,xo=class it{static pack(e,t,a){return Oo(this,arguments,function*(o,l,d,i=[]){return this.compiler.compile(o,Ea(d),i).generateAsync({type:l,mimeType:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",compression:"DEFLATE"})})}static toString(e,t,a=[]){return it.pack(e,"string",t,a)}static toBuffer(e,t,a=[]){return it.pack(e,"nodebuffer",t,a)}static toBase64String(e,t,a=[]){return it.pack(e,"base64",t,a)}static toBlob(e,t,a=[]){return it.pack(e,"blob",t,a)}static toArrayBuffer(e,t,a=[]){return it.pack(e,"arraybuffer",t,a)}static toStream(e,t,a=[]){const o=new ad.Stream;return this.compiler.compile(e,Ea(t),a).generateAsync({type:"nodebuffer",mimeType:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",compression:"DEFLATE"}).then(d=>{o.emit("data",d),o.emit("end")}),o}};re(xo,"compiler",new yd);let Cd=xo;export{Ne as A,Id as F,xd as H,Ed as I,Me as L,Cd as P,Ks as T,_t as a,Td as b,Sd as c,yo as d};
