import{gY as L,gZ as l,g_ as y,g$ as O,x as p,B as E,q as h,r as v,a5 as x,A as C,aZ as A,ak as P,b6 as f,h0 as _,h1 as w,h2 as R,o as b,y as k}from"./index-9381ab2b.js";import{_ as D}from"./_plugin-vue_export-helper-c27b6911.js";function I(e){const r=L.getOrCreateItemByURL(e),{status:n,error:t}=r;return n===l.LOADED?Promise.resolve():n===l.ERROR?Promise.reject(t):n===l.LOADING?new Promise((o,c)=>{const{el:s}=r,a=()=>{s.removeEventListener("load",a),o()},i=d=>{s.removeEventListener("error",i),c(d.error)};s.addEventListener("load",a),s.addEventListener("error",i)}):(r.status=l.LOADING,new Promise((o,c)=>{const s=document.createElement("link");s.rel="stylesheet",s.href=e;const a=()=>{s.removeEventListener("load",a),r.status=l.LOADED,r.el=null,o()},i=d=>{s.removeEventListener("error",i);const u=d.error||new Error(`Load css failed. href=${e}`);r.status=l.ERROR,r.error=u,r.el=null,c(t)};s.addEventListener("load",a),s.addEventListener("error",i),r.el=s,document.head.appendChild(s)}))}function $(e){return Promise.all(e.map(r=>I(r))).then(()=>Promise.resolve()).catch(r=>Promise.reject(r))}function j(e){const n=/legao-comp\/(.*)\/[\d.]+\/web.js$/.exec(e),t=window;if(n&&n.length>0){const o=n[1];t.g_config=t.g_config||{},t.g_config.appKey=o}}function S(e,r,n){e.status=l.LOADING,j(r);const{umd:t,crossOrigin:o}=n;return new Promise((c,s)=>{const a=document.createElement("script");if(a.src=r,a.async=!1,a.crossOrigin=o,t){e.reject=s;const u=a.src;O(u,e)}const i=()=>{a.removeEventListener("load",i),e.status=l.LOADED,e.el=null,c(e.exportThing)},d=u=>{a.removeEventListener("error",d);const m=u.error||new Error(`Load javascript failed. src=${r}`);e.status=l.ERROR,e.error=m,e.el=null,s(m)};a.addEventListener("load",i),a.addEventListener("error",d),e.el=a,document.body.appendChild(a)})}function N(e,r){const n=y.getOrCreateItemByURL(e),{status:t,exportThing:o,error:c}=n;if(t===l.LOADED)return Promise.resolve(o);if(t===l.ERROR)return Promise.reject(c);if(t===l.LOADING){const{el:s}=n;return new Promise((a,i)=>{const d=()=>{s.removeEventListener("load",d),a(n.exportThing)},u=m=>{s.removeEventListener("error",u),i(m.error)};s.addEventListener("load",d),s.addEventListener("error",u)})}return S(n,e,r)}const B={umd:!0,crossOrigin:"anonymous"};function F(e,r){const n={...B,...r};let t=Promise.resolve();const o=e.length-1,{umd:c}=n;return e.forEach((s,a)=>{const i=c&&a===o;t=t.then(()=>N(s,{...n,umd:i}))}),t}async function z(e){const{js:r,css:n,umd:t,crossOrigin:o}=e;return await $(n),F(r,{umd:typeof t>"u"?!0:t,crossOrigin:typeof o>"u"?"anonymous":o})}const T={name:"ErrorComponent"},G={style:{height:"15px",padding:"5px","text-align":"center","line-height":"15px"}};function U(e,r,n,t,o,c){return p(),E("div",G," 个性化组件加载出错了 ")}const g=D(T,[["render",U]]),V={style:{height:"15px",padding:"5px","text-align":"center","background-color":"#F0F2F5","line-height":"15px"},"element-loading-text":"个性化组件加载中"},J={name:"LoadingComponent"},K=h({...J,setup(e){const r=v(!0);return(n,t)=>x((p(),E("div",V,[P(" 个性化组件加载中 ")])),[[C(A),r.value]])}}),Z=e=>{const r=v(f(_({loader:()=>w(()=>import("./DefaultComponent-ac024368.js"),["assets/DefaultComponent-ac024368.js","assets/_plugin-vue_export-helper-c27b6911.js","assets/index-9381ab2b.js","assets/index-58f11664.css"]),delay:200,errorComponent:g,timeout:5e3}))),n=async o=>{const c={js:[o.umd||""],css:o.css?[o.css]:[],umd:!1};return o.css&&c.css.push(o.css),z(c)},t=async()=>{e.ruleInfo&&e.ruleInfo.umd&&(r.value=f(_({loader:async()=>(await n(e.ruleInfo),window[e.ruleInfo.libraryName][e.ruleInfo.componentName]),loadingComponent:K,errorComponent:g,timeout:1e4})))};return R(o=>{console.log(o)}),b(async()=>{await t()}),{personalizationComponent:r}},q={name:"Personalization"},H=h({...q,props:{ruleInfo:{},type:{default:"umd"}},setup(e){const r=e,{personalizationComponent:n}=Z(r);return(t,o)=>(p(),k(C(n)))}});export{H as _};
