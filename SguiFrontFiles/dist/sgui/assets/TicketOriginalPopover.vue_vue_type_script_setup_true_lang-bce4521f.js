import{fe as W,aT as D,aW as h,ab as Q,aX as K,r as A,w as C,ac as V,aY as M,bg as $,eI as L,eJ as j,q as J,x as k,y as U,z as _,B as v,G as S,P as g,Q as m,A as a,eM as q,E as Y,D as z,J as O,ak as G,b3 as X,al as Z}from"./index-9381ab2b.js";import{y as ee}from"./TicketOriginalItem-013d7973.js";import{E as te}from"./index-847d31f7.js";import{E as se}from"./index-9683911d.js";var ie=(e=>(e.SK_QUERY="SKQuery",e.NFD_QUERY="queryNetPrice",e.FD_QUERY="netFare",e.FSD_QUERY="InternationalPublishedRatesQuery",e))(ie||{}),oe=(e=>(e.AD="adult",e.CH="child",e.IN="baby",e))(oe||{}),P=(e=>(e.SEARCH="SEARCH",e.TAB_TO_SEARCH="TAB_TO_SEARCH",e))(P||{});const re=W("fastQuery",{state:()=>({historyAirline:{domesticHistory:[],internationalHistory:[]},queryHistory:new Map,targetInfo:{},showManualRefund:!1,rtktDetailInfoWindows:{active:"",list:[]},isOpened:!1}),getters:{getHistoryAirline(e){return e.historyAirline},getRtktDetailInfoWindows:e=>e.rtktDetailInfoWindows},actions:{setHistoryAirLine(e){this.historyAirline=e,localStorage.setItem("agentHistoryAirline",JSON.stringify(e))},setIsOpened(e){this.isOpened=e},setQueryHistory(e,i,o){var n,f;const r=D(i),c=h(r);if((n=this.queryHistory.get(e))!=null&&n.activeTagKey){o||(this.queryHistory.get(e).activeTagKey=c);const p=((f=this.queryHistory.get(e))==null?void 0:f.list.findIndex(T=>h(T)===c))??-1;if(p>-1){this.queryHistory.get(e).list.splice(p,1,r);return}this.queryHistory.get(e).list.push(r);return}const l={activeTagKey:c,list:[r]};this.queryHistory.set(e,l)},setQueryHistoryBySk(e,i){var l,n;const o=D(i.queryForm),r=h(o);if((l=this.queryHistory.get(e))!=null&&l.activeTagKey){if(this.queryHistory.get(e).activeTagKey=r,i.queryType===P.TAB_TO_SEARCH)return;const f=((n=this.queryHistory.get(e))==null?void 0:n.list.findIndex(p=>h(p)===r))??-1;f>-1&&this.queryHistory.get(e).list.splice(f,1),this.queryHistory.get(e).list.unshift(o);return}const c={activeTagKey:r,list:[o]};this.queryHistory.set(e,c)},delQueryHistory(e,i){var r,c,l;const o=((r=this.queryHistory.get(e))==null?void 0:r.list.findIndex(n=>h(n)===h(i)))??-1;if(o>-1){if(((c=this.queryHistory.get(e))==null?void 0:c.activeTagKey)===h(i)&&this.queryHistory.get(e).list.length>1){const n=o+1<(((l=this.queryHistory.get(e))==null?void 0:l.list.length)??0)?o+1:0;this.queryHistory.get(e).activeTagKey=h(this.queryHistory.get(e).list[n])}this.queryHistory.get(e).list.splice(o,1),this.queryHistory.get(e).list.length||(this.queryHistory.get(e).activeTagKey="")}},setFastQueryTargetInfo(e){this.targetInfo={queryFlag:!0,targetInfo:e}},closeFastQuery(){this.targetInfo={queryFlag:!1,targetInfo:{}},this.setIsOpened(!1)},setShowManualRefund(e){this.showManualRefund=e},setActiveRtktDetailInfoWindows(e){this.rtktDetailInfoWindows.active=e},setRtktDetailInfoWindowsList(e){const i=e.id;this.setActiveRtktDetailInfoWindows(i),this.rtktDetailInfoWindows.list.push({...e,id:i,isShow:!0})},delRtktDetailInfoWindowsList(e){this.rtktDetailInfoWindows.list=this.rtktDetailInfoWindows.list.filter(i=>i.id!==e)}}}),Ie=re,ne=(e,i)=>{var y,N;const{t:o}=Q(),{copy:r,isSupported:c}=K({legacy:!0}),l=A((y=e.tktInfo)==null?void 0:y.showTktPopover),n=A(),f=(N=navigator==null?void 0:navigator.userAgent)==null?void 0:N.toLowerCase(),p=C(()=>f==null?void 0:f.includes("electron/")),T=C(()=>{var t;return typeof((t=window.electronAPI)==null?void 0:t.openTicketOriginalWindow)=="function"}),w=C(()=>R(e.ticketNumber)),I=C(()=>e.secondFactor),x=()=>{if(!T.value)return;const t=[{ticketNo:w.value??"",secondFactor:I.value??{}}];window.electronAPI.openTicketOriginalWindow(JSON.stringify(t))},R=t=>{const d=D(t),u=d[3]!=="-"?`${d.slice(0,3)}-${d.slice(3)}`:d;return u.length>14?u.slice(0,14):d},b=()=>{l.value=!1,i("close-popover",e.tktIndex)},F=()=>{const t=e.formTicket;if(t.secondFactorValue){if(t.secondFactorType==="PNR"&&!$.test(t.secondFactorValue))return!1;if(t.secondFactorType==="name"&&!L.test(t.secondFactorValue))return!1;if(t.secondFactorType==="certificate"&&t.secondFactorCode==="NI"&&!j.test(t.secondFactorValue))return!1}else return t.secondFactorType==="PNR"||t.secondFactorType==="name",!1;return!0},H=async()=>{if(e.isOldFare&&(i("fare-vaild"),!F()))return b(),!1;setTimeout(()=>{var t,d,u;(u=(d=(t=n.value)==null?void 0:t.popperRef)==null?void 0:d.popperInstanceRef)==null||u.forceUpdate()},100)},s=t=>{c&&(r(t),M({message:o("app.batchRefund.copySuccess"),type:"success"}))};return V(()=>{var t;return(t=e.tktInfo)==null?void 0:t.showTktPopover},()=>{var t;l.value=(t=e.tktInfo)==null?void 0:t.showTktPopover},{immediate:!0,deep:!0}),{ticketNo:w,secondFactor:I,showTktRef:n,showTktPopover:l,closePopover:b,openPopover:H,copyInfo:s,isClient:p,isRequiredClientVersion:T,openTicketOriginalWindow:x}},ae={key:0},le={class:"font-bold text-base"},ce={key:1,class:"text-brand-2 text-base font-bold leading-normal cursor-pointer"},ue={key:2,class:"text-brand-2 text-xs font-bold leading-tight cursor-pointer w-[125px]"},fe={key:3,class:"text-brand-2 text-xs font-normal leading-tight cursor-pointer","data-gid":"02080114"},de={class:"flex flex-col"},ye={class:"flex justify-between items-center"},pe={class:"relative h-[20px] flex items-center justify-center"},he={class:"text-gray-1 font-bold text-[16px] leading-snug"},ke=g("i",{class:"iconfont icon-windowed mr-[2px]"},null,-1),ge={key:0,class:"w-full max-h-[600px] overflow-x-hidden overflow-y-auto mt-2.5"},be=J({__name:"TicketOriginalPopover",props:{tktInfo:{},isInternational:{type:Boolean},tktIndex:{},ticketNumber:{},secondFactor:{},outClass:{default:""},refundClassType:{},level:{default:1},queryType:{},conjunctionTicketNos:{},ticketNumberColorClass:{},prefix:{},title:{type:Boolean},isCdsTicket:{type:Boolean},formTicket:{},isOldFare:{type:Boolean,default:!1}},emits:["close-popover"],setup(e,{emit:i}){const o=A(!1),r=()=>{o.value=!0},c=()=>{o.value=!1},l=e,n=i,{ticketNo:f,secondFactor:p,showTktRef:T,showTktPopover:w,closePopover:I,openPopover:x,copyInfo:R,isClient:b,isRequiredClientVersion:F,openTicketOriginalWindow:H}=ne(l,n);return(s,y)=>{const N=Z,t=te,d=se;return k(),U(d,{ref_key:"showTktRef",ref:T,visible:a(w),"onUpdate:visible":y[2]||(y[2]=u=>X(w)?w.value=u:null),placement:"right",teleported:!0,"popper-class":`tkt-crs-popper ${s.outClass}tkt-crs-popper${s.tktIndex} max-h-[calc(100vh_-_20px)]`,"popper-options":{modifiers:[{name:"preventOverflow",options:{padding:10,rootBoundary:"viewport"}}]},width:578,trigger:"click",onBeforeLeave:a(I),onBeforeEnter:a(x),onShow:r,onHide:c},{reference:_(()=>{var u;return[(u=s.tktInfo)!=null&&u.etNumber?(k(),v("span",ae,[S(N,{link:"",type:"primary"},{default:_(()=>[g("span",le,m(a(q)(s.tktInfo.etNumber)),1)]),_:1}),g("em",{class:"iconfont icon-copy text-brand-2 relative top-[2px] cursor-pointer",onClick:y[0]||(y[0]=Y(E=>{var B;return a(R)(((B=s.tktInfo)==null?void 0:B.etNumber)??"")},["stop"]))})])):s.refundClassType==="0"?(k(),v("span",ce,m(a(q)(s.ticketNumber??"")),1)):s.refundClassType==="1"?(k(),v("span",ue,m(a(q)(s.ticketNumber??"")),1)):(k(),v("span",fe,[s.prefix?(k(),v("span",{key:0,class:z(["prefix",s.ticketNumberColorClass])},m(s.prefix),3)):O("",!0),G(" "+m(s.title&&s.ticketNumber?s.$t("app.fareQuery.freightate.details"):a(q)(s.ticketNumber??"")),1)]))]}),default:_(()=>[g("div",de,[g("div",ye,[g("div",pe,[g("div",he,m(s.$t("app.original.ticket"))+" [DETR]",1)]),a(b)&&a(F)?(k(),v("div",{key:0,class:"text-brand-2 flex flex-inline text-xs font-normal leading-tight cursor-pointer",onClick:y[1]||(y[1]=(...u)=>a(H)&&a(H)(...u))},[ke,g("div",null,m(s.$t("app.fastQuery.windowing")),1)])):O("",!0)]),S(t,{"border-style":"dashed"})]),o.value?(k(),v("div",ge,[S(ee,{"ticket-info":{ticketNo:a(f)??"",secondFactor:a(p)??{}},"is-query":!0},null,8,["ticket-info"])])):O("",!0)]),_:1},8,["visible","popper-class","onBeforeLeave","onBeforeEnter"])}}});export{ie as F,P as I,oe as P,be as _,Ie as u};
