import{L as z,M as b,q as j,v as I,w as P,id as Fe,ie as ce,ed as Re,ig as pe,a1 as fe,dO as ve,dP as ee,x as g,B as E,D as m,A as e,P as N,C as q,F as L,Q as W,J as k,y as D,z as _,b9 as Le,H as A,_ as H,K as me,m as x,U as ye,ej as ge,e8 as Q,eC as S,N as _e,eG as V,r as he,ai as Te,aj as Ue,b1 as be,E as K,G as B,ih as De,hw as Oe,ii as Be,g5 as Ne,O as je,s as Y,ij as te,aT as se,ik as qe,il as Ae,ac as ae,en as Me,e9 as Ie,W as Ke,d1 as ze,dD as oe,X as ne}from"./index-9381ab2b.js";import{i as <PERSON>}from"./isEqual-a619023a.js";const We=z({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:o=>o>=0&&o<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:{type:Boolean,default:!1},duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:b(String),default:"round"},textInside:{type:Boolean,default:!1},width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:b([String,Array,Function]),default:""},striped:Boolean,stripedFlow:Boolean,format:{type:b(Function),default:o=>`${o}%`}}),Ve=["aria-valuenow"],Xe={viewBox:"0 0 100 100"},Ge=["d","stroke","stroke-linecap","stroke-width"],Je=["d","stroke","opacity","stroke-linecap","stroke-width"],Qe={key:0},Ye=j({name:"ElProgress"}),Ze=j({...Ye,props:We,setup(o){const t=o,s={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},u=I("progress"),f=P(()=>({width:`${t.percentage}%`,animationDuration:`${t.duration}s`,backgroundColor:F(t.percentage)})),c=P(()=>(t.strokeWidth/t.width*100).toFixed(1)),y=P(()=>["circle","dashboard"].includes(t.type)?Number.parseInt(`${50-Number.parseFloat(c.value)/2}`,10):0),C=P(()=>{const r=y.value,R=t.type==="dashboard";return`
          M 50 50
          m 0 ${R?"":"-"}${r}
          a ${r} ${r} 0 1 1 0 ${R?"-":""}${r*2}
          a ${r} ${r} 0 1 1 0 ${R?"":"-"}${r*2}
          `}),h=P(()=>2*Math.PI*y.value),$=P(()=>t.type==="dashboard"?.75:1),w=P(()=>`${-1*h.value*(1-$.value)/2}px`),v=P(()=>({strokeDasharray:`${h.value*$.value}px, ${h.value}px`,strokeDashoffset:w.value})),l=P(()=>({strokeDasharray:`${h.value*$.value*(t.percentage/100)}px, ${h.value}px`,strokeDashoffset:w.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"})),a=P(()=>{let r;return t.color?r=F(t.percentage):r=s[t.status]||s.default,r}),d=P(()=>t.status==="warning"?Fe:t.type==="line"?t.status==="success"?ce:Re:t.status==="success"?pe:fe),n=P(()=>t.type==="line"?12+t.strokeWidth*.4:t.width*.111111+2),p=P(()=>t.format(t.percentage));function i(r){const R=100/r.length;return r.map((T,U)=>ee(T)?{color:T,percentage:(U+1)*R}:T).sort((T,U)=>T.percentage-U.percentage)}const F=r=>{var R;const{color:O}=t;if(ve(O))return O(r);if(ee(O))return O;{const T=i(O);for(const U of T)if(U.percentage>r)return U.color;return(R=T[T.length-1])==null?void 0:R.color}};return(r,R)=>(g(),E("div",{class:m([e(u).b(),e(u).m(r.type),e(u).is(r.status),{[e(u).m("without-text")]:!r.showText,[e(u).m("text-inside")]:r.textInside}]),role:"progressbar","aria-valuenow":r.percentage,"aria-valuemin":"0","aria-valuemax":"100"},[r.type==="line"?(g(),E("div",{key:0,class:m(e(u).b("bar"))},[N("div",{class:m(e(u).be("bar","outer")),style:q({height:`${r.strokeWidth}px`})},[N("div",{class:m([e(u).be("bar","inner"),{[e(u).bem("bar","inner","indeterminate")]:r.indeterminate},{[e(u).bem("bar","inner","striped")]:r.striped},{[e(u).bem("bar","inner","striped-flow")]:r.stripedFlow}]),style:q(e(f))},[(r.showText||r.$slots.default)&&r.textInside?(g(),E("div",{key:0,class:m(e(u).be("bar","innerText"))},[L(r.$slots,"default",{percentage:r.percentage},()=>[N("span",null,W(e(p)),1)])],2)):k("v-if",!0)],6)],6)],2)):(g(),E("div",{key:1,class:m(e(u).b("circle")),style:q({height:`${r.width}px`,width:`${r.width}px`})},[(g(),E("svg",Xe,[N("path",{class:m(e(u).be("circle","track")),d:e(C),stroke:`var(${e(u).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-linecap":r.strokeLinecap,"stroke-width":e(c),fill:"none",style:q(e(v))},null,14,Ge),N("path",{class:m(e(u).be("circle","path")),d:e(C),stroke:e(a),fill:"none",opacity:r.percentage?1:0,"stroke-linecap":r.strokeLinecap,"stroke-width":e(c),style:q(e(l))},null,14,Je)]))],6)),(r.showText||r.$slots.default)&&!r.textInside?(g(),E("div",{key:2,class:m(e(u).e("text")),style:q({fontSize:`${e(n)}px`})},[L(r.$slots,"default",{percentage:r.percentage},()=>[r.status?(g(),D(e(A),{key:1},{default:_(()=>[(g(),D(Le(e(d))))]),_:1})):(g(),E("span",Qe,W(e(p)),1))])],6)):k("v-if",!0)],10,Ve))}});var xe=H(Ze,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/progress/src/progress.vue"]]);const et=me(xe),ke=Symbol("uploadContextKey"),tt="ElUpload";class st extends Error{constructor(t,s,u,f){super(t),this.name="UploadAjaxError",this.status=s,this.method=u,this.url=f}}function re(o,t,s){let u;return s.response?u=`${s.response.error||s.response}`:s.responseText?u=`${s.responseText}`:u=`fail to ${t.method} ${o} ${s.status}`,new st(u,s.status,t.method,o)}function at(o){const t=o.responseText||o.response;if(!t)return t;try{return JSON.parse(t)}catch{return t}}const ot=o=>{typeof XMLHttpRequest>"u"&&x(tt,"XMLHttpRequest is undefined");const t=new XMLHttpRequest,s=o.action;t.upload&&t.upload.addEventListener("progress",c=>{const y=c;y.percent=c.total>0?c.loaded/c.total*100:0,o.onProgress(y)});const u=new FormData;if(o.data)for(const[c,y]of Object.entries(o.data))ye(y)&&y.length?u.append(c,...y):u.append(c,y);u.append(o.filename,o.file,o.file.name),t.addEventListener("error",()=>{o.onError(re(s,o,t))}),t.addEventListener("load",()=>{if(t.status<200||t.status>=300)return o.onError(re(s,o,t));o.onSuccess(at(t))}),t.open(o.method,s,!0),o.withCredentials&&"withCredentials"in t&&(t.withCredentials=!0);const f=o.headers||{};if(f instanceof Headers)f.forEach((c,y)=>t.setRequestHeader(y,c));else for(const[c,y]of Object.entries(f))ge(y)||t.setRequestHeader(c,String(y));return t.send(u),t},$e=["text","picture","picture-card"];let nt=1;const Z=()=>Date.now()+nt++,we=z({action:{type:String,default:"#"},headers:{type:b(Object)},method:{type:String,default:"post"},data:{type:b([Object,Function,Promise]),default:()=>Q({})},multiple:{type:Boolean,default:!1},name:{type:String,default:"file"},drag:{type:Boolean,default:!1},withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:{type:String,default:""},fileList:{type:b(Array),default:()=>Q([])},autoUpload:{type:Boolean,default:!0},listType:{type:String,values:$e,default:"text"},httpRequest:{type:b(Function),default:ot},disabled:Boolean,limit:Number}),rt=z({...we,beforeUpload:{type:b(Function),default:S},beforeRemove:{type:b(Function)},onRemove:{type:b(Function),default:S},onChange:{type:b(Function),default:S},onPreview:{type:b(Function),default:S},onSuccess:{type:b(Function),default:S},onProgress:{type:b(Function),default:S},onError:{type:b(Function),default:S},onExceed:{type:b(Function),default:S}}),lt=z({files:{type:b(Array),default:()=>Q([])},disabled:{type:Boolean,default:!1},handlePreview:{type:b(Function),default:S},listType:{type:String,values:$e,default:"text"}}),it={remove:o=>!!o},ut=["onKeydown"],dt=["src"],ct=["onClick"],pt=["title"],ft=["onClick"],vt=["onClick"],mt=j({name:"ElUploadList"}),yt=j({...mt,props:lt,emits:it,setup(o,{emit:t}){const s=o,{t:u}=_e(),f=I("upload"),c=I("icon"),y=I("list"),C=V(),h=he(!1),$=P(()=>[f.b("list"),f.bm("list",s.listType),f.is("disabled",s.disabled)]),w=v=>{t("remove",v)};return(v,l)=>(g(),D(Ne,{tag:"ul",class:m(e($)),name:e(y).b()},{default:_(()=>[(g(!0),E(Te,null,Ue(v.files,a=>(g(),E("li",{key:a.uid||a.name,class:m([e(f).be("list","item"),e(f).is(a.status),{focusing:h.value}]),tabindex:"0",onKeydown:be(d=>!e(C)&&w(a),["delete"]),onFocus:l[0]||(l[0]=d=>h.value=!0),onBlur:l[1]||(l[1]=d=>h.value=!1),onClick:l[2]||(l[2]=d=>h.value=!1)},[L(v.$slots,"default",{file:a},()=>[v.listType==="picture"||a.status!=="uploading"&&v.listType==="picture-card"?(g(),E("img",{key:0,class:m(e(f).be("list","item-thumbnail")),src:a.url,alt:""},null,10,dt)):k("v-if",!0),a.status==="uploading"||v.listType!=="picture-card"?(g(),E("div",{key:1,class:m(e(f).be("list","item-info"))},[N("a",{class:m(e(f).be("list","item-name")),onClick:K(d=>v.handlePreview(a),["prevent"])},[B(e(A),{class:m(e(c).m("document"))},{default:_(()=>[B(e(De))]),_:1},8,["class"]),N("span",{class:m(e(f).be("list","item-file-name")),title:a.name},W(a.name),11,pt)],10,ct),a.status==="uploading"?(g(),D(e(et),{key:0,type:v.listType==="picture-card"?"circle":"line","stroke-width":v.listType==="picture-card"?6:2,percentage:Number(a.percentage),style:q(v.listType==="picture-card"?"":"margin-top: 0.5rem")},null,8,["type","stroke-width","percentage","style"])):k("v-if",!0)],2)):k("v-if",!0),N("label",{class:m(e(f).be("list","item-status-label"))},[v.listType==="text"?(g(),D(e(A),{key:0,class:m([e(c).m("upload-success"),e(c).m("circle-check")])},{default:_(()=>[B(e(ce))]),_:1},8,["class"])):["picture-card","picture"].includes(v.listType)?(g(),D(e(A),{key:1,class:m([e(c).m("upload-success"),e(c).m("check")])},{default:_(()=>[B(e(pe))]),_:1},8,["class"])):k("v-if",!0)],2),e(C)?k("v-if",!0):(g(),D(e(A),{key:2,class:m(e(c).m("close")),onClick:d=>w(a)},{default:_(()=>[B(e(fe))]),_:2},1032,["class","onClick"])),k(" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn"),k(" This is a bug which needs to be fixed "),k(" TODO: Fix the incorrect navigation interaction "),e(C)?k("v-if",!0):(g(),E("i",{key:3,class:m(e(c).m("close-tip"))},W(e(u)("el.upload.deleteTip")),3)),v.listType==="picture-card"?(g(),E("span",{key:4,class:m(e(f).be("list","item-actions"))},[N("span",{class:m(e(f).be("list","item-preview")),onClick:d=>v.handlePreview(a)},[B(e(A),{class:m(e(c).m("zoom-in"))},{default:_(()=>[B(e(Oe))]),_:1},8,["class"])],10,ft),e(C)?k("v-if",!0):(g(),E("span",{key:0,class:m(e(f).be("list","item-delete")),onClick:d=>w(a)},[B(e(A),{class:m(e(c).m("delete"))},{default:_(()=>[B(e(Be))]),_:1},8,["class"])],10,vt))],2)):k("v-if",!0)])],42,ut))),128)),L(v.$slots,"append")]),_:3},8,["class","name"]))}});var le=H(yt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/upload/src/upload-list.vue"]]);const gt=z({disabled:{type:Boolean,default:!1}}),ht={file:o=>ye(o)},bt=["onDrop","onDragover"],Ee="ElUploadDrag",kt=j({name:Ee}),$t=j({...kt,props:gt,emits:ht,setup(o,{emit:t}){const s=je(ke);s||x(Ee,"usage: <el-upload><el-upload-dragger /></el-upload>");const u=I("upload"),f=he(!1),c=V(),y=h=>{if(c.value)return;f.value=!1,h.stopPropagation();const $=Array.from(h.dataTransfer.files),w=s.accept.value;if(!w){t("file",$);return}const v=$.filter(l=>{const{type:a,name:d}=l,n=d.includes(".")?`.${d.split(".").pop()}`:"",p=a.replace(/\/.*$/,"");return w.split(",").map(i=>i.trim()).filter(i=>i).some(i=>i.startsWith(".")?n===i:/\/\*$/.test(i)?p===i.replace(/\/\*$/,""):/^[^/]+\/[^/]+$/.test(i)?a===i:!1)});t("file",v)},C=()=>{c.value||(f.value=!0)};return(h,$)=>(g(),E("div",{class:m([e(u).b("dragger"),e(u).is("dragover",f.value)]),onDrop:K(y,["prevent"]),onDragover:K(C,["prevent"]),onDragleave:$[0]||($[0]=K(w=>f.value=!1,["prevent"]))},[L(h.$slots,"default")],42,bt))}});var wt=H($t,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/upload/src/upload-dragger.vue"]]);const Et=z({...we,beforeUpload:{type:b(Function),default:S},onRemove:{type:b(Function),default:S},onStart:{type:b(Function),default:S},onSuccess:{type:b(Function),default:S},onProgress:{type:b(Function),default:S},onError:{type:b(Function),default:S},onExceed:{type:b(Function),default:S}}),Ct=["onKeydown"],Pt=["name","multiple","accept"],St=j({name:"ElUploadContent",inheritAttrs:!1}),Ft=j({...St,props:Et,setup(o,{expose:t}){const s=o,u=I("upload"),f=V(),c=Y({}),y=Y(),C=n=>{if(n.length===0)return;const{autoUpload:p,limit:i,fileList:F,multiple:r,onStart:R,onExceed:O}=s;if(i&&F.length+n.length>i){O(n,F);return}r||(n=n.slice(0,1));for(const T of n){const U=T;U.uid=Z(),R(U),p&&h(U)}},h=async n=>{if(y.value.value="",!s.beforeUpload)return w(n);let p,i={};try{const r=s.data,R=s.beforeUpload(n);i=te(s.data)?se(s.data):s.data,p=await R,te(s.data)&&He(r,i)&&(i=se(s.data))}catch{p=!1}if(p===!1){s.onRemove(n);return}let F=n;p instanceof Blob&&(p instanceof File?F=p:F=new File([p],n.name,{type:n.type})),w(Object.assign(F,{uid:n.uid}),i)},$=async(n,p)=>ve(n)?n(p):n,w=async(n,p)=>{const{headers:i,data:F,method:r,withCredentials:R,name:O,action:T,onProgress:U,onSuccess:Ce,onError:Pe,httpRequest:Se}=s;try{p=await $(p??F,n)}catch{s.onRemove(n);return}const{uid:X}=n,G={headers:i||{},withCredentials:R,file:n,data:p,method:r,filename:O,action:T,onProgress:M=>{U(M,n)},onSuccess:M=>{Ce(M,n),delete c.value[X]},onError:M=>{Pe(M,n),delete c.value[X]}},J=Se(G);c.value[X]=J,J instanceof Promise&&J.then(G.onSuccess,G.onError)},v=n=>{const p=n.target.files;p&&C(Array.from(p))},l=()=>{f.value||(y.value.value="",y.value.click())},a=()=>{l()};return t({abort:n=>{qe(c.value).filter(n?([i])=>String(n.uid)===i:()=>!0).forEach(([i,F])=>{F instanceof XMLHttpRequest&&F.abort(),delete c.value[i]})},upload:h}),(n,p)=>(g(),E("div",{class:m([e(u).b(),e(u).m(n.listType),e(u).is("drag",n.drag)]),tabindex:"0",onClick:l,onKeydown:be(K(a,["self"]),["enter","space"])},[n.drag?(g(),D(wt,{key:0,disabled:e(f),onFile:C},{default:_(()=>[L(n.$slots,"default")]),_:3},8,["disabled"])):L(n.$slots,"default",{key:1}),N("input",{ref_key:"inputRef",ref:y,class:m(e(u).e("input")),name:n.name,multiple:n.multiple,accept:n.accept,type:"file",onChange:v,onClick:p[0]||(p[0]=K(()=>{},["stop"]))},null,42,Pt)],42,Ct))}});var ie=H(Ft,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/upload/src/upload-content.vue"]]);const ue="ElUpload",de=o=>{var t;(t=o.url)!=null&&t.startsWith("blob:")&&URL.revokeObjectURL(o.url)},Rt=(o,t)=>{const s=Ae(o,"fileList",void 0,{passive:!0}),u=l=>s.value.find(a=>a.uid===l.uid);function f(l){var a;(a=t.value)==null||a.abort(l)}function c(l=["ready","uploading","success","fail"]){s.value=s.value.filter(a=>!l.includes(a.status))}const y=(l,a)=>{const d=u(a);d&&(console.error(l),d.status="fail",s.value.splice(s.value.indexOf(d),1),o.onError(l,d,s.value),o.onChange(d,s.value))},C=(l,a)=>{const d=u(a);d&&(o.onProgress(l,d,s.value),d.status="uploading",d.percentage=Math.round(l.percent))},h=(l,a)=>{const d=u(a);d&&(d.status="success",d.response=l,o.onSuccess(l,d,s.value),o.onChange(d,s.value))},$=l=>{ge(l.uid)&&(l.uid=Z());const a={name:l.name,percentage:0,status:"ready",size:l.size,raw:l,uid:l.uid};if(o.listType==="picture-card"||o.listType==="picture")try{a.url=URL.createObjectURL(l)}catch(d){Me(ue,d.message),o.onError(d,a,s.value)}s.value=[...s.value,a],o.onChange(a,s.value)},w=async l=>{const a=l instanceof File?u(l):l;a||x(ue,"file to be removed not found");const d=n=>{f(n);const p=s.value;p.splice(p.indexOf(n),1),o.onRemove(n,p),de(n)};o.beforeRemove?await o.beforeRemove(a,s.value)!==!1&&d(a):d(a)};function v(){s.value.filter(({status:l})=>l==="ready").forEach(({raw:l})=>{var a;return l&&((a=t.value)==null?void 0:a.upload(l))})}return ae(()=>o.listType,l=>{l!=="picture-card"&&l!=="picture"||(s.value=s.value.map(a=>{const{raw:d,url:n}=a;if(!n&&d)try{a.url=URL.createObjectURL(d)}catch(p){o.onError(p,a,s.value)}return a}))}),ae(s,l=>{for(const a of l)a.uid||(a.uid=Z()),a.status||(a.status="success")},{immediate:!0,deep:!0}),{uploadFiles:s,abort:f,clearFiles:c,handleError:y,handleProgress:C,handleStart:$,handleSuccess:h,handleRemove:w,submit:v,revokeFileObjectURL:de}},Lt=j({name:"ElUpload"}),_t=j({...Lt,props:rt,setup(o,{expose:t}){const s=o,u=V(),f=Y(),{abort:c,submit:y,clearFiles:C,uploadFiles:h,handleStart:$,handleError:w,handleRemove:v,handleSuccess:l,handleProgress:a,revokeFileObjectURL:d}=Rt(s,f),n=P(()=>s.listType==="picture-card"),p=P(()=>({...s,fileList:h.value,onStart:$,onProgress:a,onSuccess:l,onError:w,onRemove:v}));return Ie(()=>{h.value.forEach(d)}),Ke(ke,{accept:ze(s,"accept")}),t({abort:c,submit:y,clearFiles:C,handleStart:$,handleRemove:v}),(i,F)=>(g(),E("div",null,[e(n)&&i.showFileList?(g(),D(le,{key:0,disabled:e(u),"list-type":i.listType,files:e(h),"handle-preview":i.onPreview,onRemove:e(v)},oe({append:_(()=>[B(ie,ne({ref_key:"uploadRef",ref:f},e(p)),{default:_(()=>[i.$slots.trigger?L(i.$slots,"trigger",{key:0}):k("v-if",!0),!i.$slots.trigger&&i.$slots.default?L(i.$slots,"default",{key:1}):k("v-if",!0)]),_:3},16)]),_:2},[i.$slots.file?{name:"default",fn:_(({file:r})=>[L(i.$slots,"file",{file:r})])}:void 0]),1032,["disabled","list-type","files","handle-preview","onRemove"])):k("v-if",!0),!e(n)||e(n)&&!i.showFileList?(g(),D(ie,ne({key:1,ref_key:"uploadRef",ref:f},e(p)),{default:_(()=>[i.$slots.trigger?L(i.$slots,"trigger",{key:0}):k("v-if",!0),!i.$slots.trigger&&i.$slots.default?L(i.$slots,"default",{key:1}):k("v-if",!0)]),_:3},16)):k("v-if",!0),i.$slots.trigger?L(i.$slots,"default",{key:2}):k("v-if",!0),L(i.$slots,"tip"),!e(n)&&i.showFileList?(g(),D(le,{key:3,disabled:e(u),"list-type":i.listType,files:e(h),"handle-preview":i.onPreview,onRemove:e(v)},oe({_:2},[i.$slots.file?{name:"default",fn:_(({file:r})=>[L(i.$slots,"file",{file:r})])}:void 0]),1032,["disabled","list-type","files","handle-preview","onRemove"])):k("v-if",!0)]))}});var Tt=H(_t,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/upload/src/upload.vue"]]);const Ot=me(Tt);export{Ot as E};
