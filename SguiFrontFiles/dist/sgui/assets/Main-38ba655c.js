import{L as j,M as F,q as w,v as G,x as c,B as u,D as y,A as r,F as A,ak as S,Q as d,J as g,P as a,C as q,_ as J,K,bU as Q,bV as X,r as p,ax as D,a9 as E,o as M,b5 as I,ae as R,a5 as T,G as f,z as $,ai as B,aj as P,H as U,cc as L,am as Y,an as Z,y as b,w as V,a4 as k}from"./index-9381ab2b.js";import{A as O,f as C,d as x}from"./config-b573cde3.js";/* empty css              */import{V as H}from"./index.min-ecc0c17f.js";import{_ as z}from"./_plugin-vue_export-helper-c27b6911.js";import{E as ee}from"./index-7b8ec8cc.js";import{N as te}from"./NoticeModal-16eafe29.js";import"./refs-d6b4edba.js";import"./isUndefined-aa0326a0.js";import"./noticeApi-c8da9bb2.js";const se=j({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:F([String,Object,Array]),default:""},bodyClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),oe=w({name:"ElCard"}),ae=w({...oe,props:se,setup(e){const s=G("card");return(t,_)=>(c(),u("div",{class:y([r(s).b(),r(s).is(`${t.shadow}-shadow`)])},[t.$slots.header||t.header?(c(),u("div",{key:0,class:y(r(s).e("header"))},[A(t.$slots,"header",{},()=>[S(d(t.header),1)])],2)):g("v-if",!0),a("div",{class:y([r(s).e("body"),t.bodyClass]),style:q(t.bodyStyle)},[A(t.$slots,"default")],6),t.$slots.footer||t.footer?(c(),u("div",{key:1,class:y(r(s).e("footer"))},[A(t.$slots,"footer",{},()=>[S(d(t.footer),1)])],2)):g("v-if",!0)],2))}});var ne=J(ae,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/card/src/card.vue"]]);const le=K(ne),W=()=>Q.post(`${X}/message/listNotReadMessagesInCondition`),ie=()=>{const e=p([]),s=p(!0),t=p([]),_=D(),h=E(),n=async()=>{const l=await W();e.value=l.data;const m=e.value.length<30?e.value.length:30;setTimeout(()=>{var N;for(let v=0;v<m;v+=1)H.preview(document.getElementsByClassName("vditor")[v],I.decode((N=e.value[v])==null?void 0:N.content),{mode:"light",anchor:0,theme:{current:"light",path:`${C}/dist/css/content-theme`},cdn:C})},500)},i=l=>{_.push({name:"NotificationManage",params:{keyWord:l.title,startDate:l.createDate,endDate:l.createDate,msgStatus:"0"}})},o=()=>{e.value=[]};return M(async()=>{(await h.getters.roleResource).includes(O)&&n()}),{dialogTableVisible:s,unReadList:e,getUnReadList:n,readColection:t,routerToNotice:i,closeAnnounce:o}},ce=ie,re=e=>(Y("data-v-0c474faf"),e=e(),Z(),e),de={key:0,class:"announcement"},ue={class:"header"},_e={class:"list"},me=["onClick"],pe={class:"title"},he={class:"time"},ve=re(()=>a("div",{class:"content"},[a("div",{class:"vditor"})],-1)),fe=w({__name:"Announcement",setup(e){const{unReadList:s,routerToNotice:t}=ce();return(_,h)=>{const n=R("permission");return r(s).length?T((c(),u("div",de,[f(r(le),{class:"box-card"},{header:$(()=>[a("div",ue,[a("div",null,d(_.$t("app.anouncement.title")),1)])]),default:$(()=>[a("div",_e,[(c(!0),u(B,null,P(r(s).slice(0,30),i=>(c(),u("div",{key:i.id,class:"item",onClick:o=>r(t)(i)},[a("div",pe,d(i.title),1),a("div",he,[f(r(U),null,{default:$(()=>[f(r(L))]),_:1}),a("div",null,d(_.$t("app.anouncement.time"))+" "+d(i.createDate),1)]),ve],8,me))),128))])]),_:1})])),[[n,"home-announcement-unreadMessages"]]):g("",!0)}}});const ge=z(fe,[["__scopeId","data-v-0c474faf"]]),ye={class:"customWidth"},$e={class:"first-title"},we={class:"list"},Ce=["onClick"],Ne={class:"header"},Ae=a("div",{class:"border"},null,-1),ke={class:"title"},be={class:"time"},Me=a("div",{class:"content"},[a("div",{class:"modalVditor"})],-1),Se=a("p",{class:"line"},null,-1),Ve=w({__name:"Modal",setup(e){const s=p([]),t=p(!0),_=D(),h=n=>{_.push({name:"NotificationManage",params:{keyWord:n.title,startDate:n.createDate,endDate:n.createDate,msgStatus:"0"}})};return M(async()=>{const n=await W();s.value=n.data;const i=s.value.length<10?s.value.length:10;setTimeout(()=>{if(document.getElementsByClassName("modalVditor").length)for(let o=0;o<i;o+=1)H.preview(document.getElementsByClassName("modalVditor")[o],I.decode(s.value[o].content),{mode:"light",anchor:0,theme:{current:"light",path:`${C}/dist/css/content-theme`},cdn:C})},500)}),(n,i)=>{const o=R("permission");return T((c(),u("div",null,[s.value.length?(c(),b(r(ee),{key:0,modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=l=>t.value=l),"close-on-click-modal":!1,class:"custom"},{default:$(()=>[a("div",ye,[a("p",$e,d(n.$t("app.anouncement.title")),1),a("div",we,[(c(!0),u(B,null,P(s.value.slice(0,10),l=>(c(),u("div",{key:l.id,class:"text item",onClick:m=>h(l)},[a("div",Ne,[Ae,a("div",ke,d(l.title),1)]),a("div",be,[f(r(U),null,{default:$(()=>[f(r(L))]),_:1}),a("div",null,d(n.$t("app.anouncement.time"))+" "+d(l.createDate),1)]),Me,Se],8,Ce))),128))])])]),_:1},8,["modelValue"])):g("",!0)])),[[o,"home-announcement-unreadMessages"]])}}});const De=w({name:"Main",components:{Modal:Ve,Announcement:ge,NoticeModal:te},setup(){const e=p(!1),s=p(!1),t=E(),_=V(()=>{var o;return((o=t.state.user)==null?void 0:o.crsSystem)??!1}),h=V(()=>{var o;return((o=t.state.user)==null?void 0:o.defaultOfficeInternational)??!1}),n=p(!1),i=o=>{n.value=o};return M(async()=>{if(window.firstInApp)e.value=!1,n.value=!1;else{window.firstInApp=!0;const o=await t.getters.roleResource;s.value=o.includes(O);let l;const m=await t.getters.routes;m&&m.length>0?l=m==null?void 0:m.map(v=>v.url):l=await t.getters.roleMenu,l.includes(x)&&(n.value=!0),e.value=!0}}),{showAnnounce:e,hasPermission:s,showAgentNotice:n,isCrs:_,changeVisible:i,isIntlCrs:h}}});const Ee={class:"footer"},Ie={key:0,class:"text-gray-4 text-xs font-normal ading-tight"},Re={key:1,class:"text-gray-4 text-xs font-normal ading-tight"};function Te(e,s,t,_,h,n){const i=k("Announcement"),o=k("Modal"),l=k("NoticeModal");return c(),u("div",{class:y([e.isCrs?"main-new":"main",e.isIntlCrs?"main-new-intl":""])},[f(i),e.showAnnounce&&e.hasPermission?(c(),b(o,{key:0})):g("",!0),e.showAgentNotice?(c(),b(l,{key:1,"notice-visible":e.showAgentNotice,onChangeVisible:e.changeVisible},null,8,["notice-visible","onChangeVisible"])):g("",!0),a("footer",Ee,[e.isIntlCrs?(c(),u("div",Ie,d(e.$t("app.login.companyNameIntl")),1)):(c(),u("div",Re,d(e.$t("app.login.companyName")),1))])],2)}const Ge=z(De,[["render",Te],["__scopeId","data-v-51b10822"]]);export{Ge as default};
