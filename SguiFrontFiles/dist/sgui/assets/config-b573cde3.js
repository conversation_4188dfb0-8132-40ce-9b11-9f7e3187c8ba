import{c5 as a}from"./index-9381ab2b.js";const p=[{label:"正常",value:"1"},{label:"注销",value:"0"}],s=[{label:"URL 资源",value:"url"}],o=[{label:"ICS9级",value:9}],n="setting-msgNotification-query",u="setting-helpCenter-query",b="setting-faultReport-query",g="home-announcement-unreadMessages",T="/sgui/protocol",R="/sgui/vditor",S="/sgui",P="/sgui/help",v=[{value:"userName",label:"用户名"},{value:"airline",label:"航司"},{value:"role",label:"角色"}],i=[{value:"component",label:"组件"},{value:"configuration",label:"配置"},{value:"serviceCallback",label:"服务回调"}],E=[{value:"tripSMS",label:"发送行程短信"}],N={personalCenter:"personalCenter-changePassword",refundInfoAig:"report-agentreport-refund"},c="/auxiliaryFunctions/systemNotice",d="auxiliaryFunctions-imitateEterm-execute",C=new Map([["errBirthDate","app.intlPassengerForm.validate.errBirthDate"],["errHKCard","app.intlPassengerForm.validate.errHKCard"],["niBirthDateDiff","app.intlPassengerForm.validate.niBirthDatediff"],["errNIDate","app.intlPassengerForm.validate.errNIDate"],["notCHD","app.intlPassengerForm.validate.notCHD"],["notunaccompaniedCHD","app.intlPassengerForm.validate.notunaccompaniedCHD"],["ppDate","app.intlPassengerForm.validate.ppDate"],["niNumberTip","app.intlPassengerForm.validate.niNumberTip"],["documentTypeTip","app.intlPassengerForm.validate.documentTypeTip"],["uuNumberTip","app.intlPassengerForm.validate.uuNumberTip"],["internationalDocumentValue","app.intlPassengerForm.validate.passportStyleError"],["permitForForeignersTip","app.intlPassengerForm.validate.permitForForeignersTip"]]);a.global.t("app.fastQuery.assistedSearch.all"),a.global.t("app.fastQuery.assistedSearch.public"),a.global.t("app.fastQuery.assistedSearch.private");const f=["ADP","ANG","AUD","BDT","BIF","BRR","BYR","CNY","CYP","DLC","EGP","FIM","GEL","GNF","HKD","HUF","IQD","JMD","KHR","KYD","LRD","LYD","MMK","MTL","MYR","NIO","OMR","PGK","PTE","RUR","SDD","SIT","SSP","SZL","TOP","AED","AOA","AWG","BEF","BMD","BSD","BZD","COP","CZK","DOP","ERN","FJD","GHC","GRD","HNL","IDR","IQR","JOD","KMF","KZT","LSL","MAD","MNT","MUR","MZN","NLG","PAB","PHP","PYG","RWF","SDG","SKK","STD","THB","TRL","AZN","BGL","BND","BTN","CAD","CRC","DEM","DZD","ESP","FKP","GHS","GTQ","HRD","IEP","IRR","JPY","KPW","LAK","LTL","MDL","MOP","MVR","NAD","NOK","PEK","PKR","QAR","SAR","SEK","SLL","SUR","TJS","TRY","AFA","AON","BAM","BGN","BOB","BWP","CHF","CUP","DJF","ECS","ETB","FRF","GIP","GWP","HRK","ILS","ISK","KES","KRW","LBP","LUF","MGA","MRO","MWK","NGN","NPR","PEN","PLN","RON","SBD","SGD","SOS","SVC","TMM","ALL","ARS","BBD","BHD","BRL","BYN","CLP","CVE","DKK","EEK","EUR","GBP","GMD","GYD","HTG","INR","ITL","KGS","KWD","LKR","LVL","MKD","USD","MRU","MXN","NIC","NZD","PES","PLZ","RUB","SCR","SHP","SRG","SYP","TND","AMD","ATS"],y=["ADP","AED","AFA","ALL","AMD","ANG","AOA","AON","ARS","ATS","AUD","AWG","AZN","BAM","BBD","BDT","BEF","BGL","BGN","BHD","BIF","BMD","BND","BOB","BRL","BRR","BSD","BTN","BWP","BYN","BYR","BZD","CAD","CHF","CLP","CNY","COP","CRC","CUP","CVE","CYP","CZK","DEM","DJF","DKK","DLC","DOP","DZD","ECS","EEK","EGP","ERN","ESP","ETB","EUR","FIM","FJD","FKP","FRF","GBP","GEL","GHC","GHS","GIP","GMD","GNF","GRD","GTQ","GWP","GYD","HKD","HNL","HRD","HRK","HTG","HUF","IDR","IEP","ILS","INR","IQD","IQR","IRR","ISK","ITL","JMD","JOD","JPY","KES","KGS","KHR","KMF","KPW","KRW","KWD","KYD","KZT","LAK","LBP","LKR","LRD","LSL","LTL","LUF","LVL","LYD","MAD","MDL","MGA","MKD","MMK","MNT","MOP","MRO","MRU","MTL","MUR","MVR","MWK","MXN","MYR","MZN","NAD","NGN","NIC","NIO","NLG","NOK","NPR","NZD","OMR","PAB","PEK","PEN","PES","PGK","PHP","PKR","PLN","PLZ","PTE","PYG","QAR","RON","RUB","RUR","RWF","SAR","SBD","SCR","SDD","SDG","SEK","SGD","SHP","SIT","SKK","SLL","SOS","SRG","SSP","STD","SUR","SVC","SYP","SZL","THB","TJS","TMM","TND","TOP","TRL","TRY","USD"];a.global.t("app.avSearch.CABIN_F"),a.global.t("app.avSearch.CABIN_B"),a.global.t("app.avSearch.CABIN_W");const L=[{label:"",options:[{value:"ALLF",label:"ALLF"}]},{label:a.global.t("app.fare.advanced.QTB"),options:[{value:"MULB",label:a.global.t("app.fareQuery.freightate.mubl")},{value:"ALLB",label:a.global.t("app.fareQuery.freightate.allb")},{value:"F",label:a.global.t("app.fareQuery.freightate.ffrst")},{value:"B",label:a.global.t("app.fareQuery.freightate.bintr")},{value:"PREC",label:a.global.t("app.fareQuery.freightate.prec")},{value:"W",label:a.global.t("app.fareQuery.freightate.w")}]},{label:a.global.t("app.fare.advanced.fareType"),options:[{value:"PUBL",label:a.global.t("app.fare.advanced.PUBL")},{value:"NEGO",label:a.global.t("app.fare.advanced.NEGO")},{value:"PRVT",label:a.global.t("app.fare.advanced.PRVT")},{value:"NORM",label:a.global.t("app.fare.advanced.NORM")},{value:"SPCL",label:a.global.t("app.fare.advanced.SPCL")},{value:"CRCL",label:a.global.t("app.fare.advanced.CRCL")},{value:"RTWF",label:a.global.t("app.fare.advanced.RTWF")},{value:"GRPF",label:a.global.t("app.fare.advanced.GRPF")},{value:"CTRW",label:a.global.t("app.fare.advanced.CTRW")},{value:"TOUR",label:a.global.t("app.fare.advanced.I")}]},{label:a.global.t("app.fare.advanced.refund"),options:[{value:"CHGP",label:a.global.t("app.fare.advanced.CHGP")},{value:"REFP",label:a.global.t("app.fare.advanced.REFP")},{value:"CHGN",label:a.global.t("app.fare.advanced.CHGN")},{value:"REFN",label:a.global.t("app.fare.advanced.REFN")},{value:"RNCP",label:a.global.t("app.fare.advanced.RNCP")},{value:"RPCN",label:a.global.t("app.fare.advanced.RPCN")},{value:"RPCP",label:a.global.t("app.fare.advanced.RPCP")},{value:"NOPE",label:a.global.t("app.fare.advanced.NOPE")}]},{label:a.global.t("app.fare.advanced.cabin"),options:[{value:"FRST",label:a.global.t("app.fare.advanced.FRST")},{value:"INTR",label:a.global.t("app.fare.advanced.INTR")},{value:"ECON",label:a.global.t("app.fare.advanced.ECON")},{value:"PREC",label:a.global.t("app.fareQuery.freightate.prec")}]}],D=[{label:"",options:[{value:"ALLB",label:a.global.t("app.fareQuery.freightate.allb")},{value:"ALLF",label:"ALLF"}]},{label:a.global.t("app.fare.advanced.QTB"),options:[{value:"MULB",label:a.global.t("app.fareQuery.freightate.mubl")},{value:"ALLB",label:a.global.t("app.fareQuery.freightate.allb")},{value:"F",label:a.global.t("app.fareQuery.freightate.ffrst")},{value:"B",label:a.global.t("app.fareQuery.freightate.bintr")},{value:"PREC",label:a.global.t("app.fareQuery.freightate.prec")},{value:"W",label:a.global.t("app.fareQuery.freightate.w")}]},{label:a.global.t("app.fare.advanced.fareType"),options:[{value:"PUBL",label:a.global.t("app.fare.advanced.PUBL")},{value:"NEGO",label:a.global.t("app.fare.advanced.NEGO")},{value:"PRVT",label:a.global.t("app.fare.advanced.PRVT")},{value:"NORM",label:a.global.t("app.fare.advanced.NORM")},{value:"SPCL",label:a.global.t("app.fare.advanced.SPCL")},{value:"CRCL",label:a.global.t("app.fare.advanced.CRCL")},{value:"RTWF",label:a.global.t("app.fare.advanced.RTWF")},{value:"GRPF",label:a.global.t("app.fare.advanced.GRPF")},{value:"CTRW",label:a.global.t("app.fare.advanced.CTRW")},{value:"TOUR",label:a.global.t("app.fare.advanced.I")}]},{label:a.global.t("app.fare.advanced.refund"),options:[{value:"CHGP",label:a.global.t("app.fare.advanced.CHGP")},{value:"REFP",label:a.global.t("app.fare.advanced.REFP")},{value:"CHGN",label:a.global.t("app.fare.advanced.CHGN")},{value:"REFN",label:a.global.t("app.fare.advanced.REFN")},{value:"RNCP",label:a.global.t("app.fare.advanced.RNCP")},{value:"RPCN",label:a.global.t("app.fare.advanced.RPCN")},{value:"RPCP",label:a.global.t("app.fare.advanced.RPCP")},{value:"NOPE",label:a.global.t("app.fare.advanced.NOPE")}]},{label:a.global.t("app.fare.advanced.cabin"),options:[{value:"FRST",label:a.global.t("app.fare.advanced.FRST")},{value:"INTR",label:a.global.t("app.fare.advanced.INTR")},{value:"ECON",label:a.global.t("app.fare.advanced.ECON")},{value:"PREC",label:a.global.t("app.fare.advanced.PREC")}]}],A=[{type:"A",min:1,max:15,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"P",min:1,max:15,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"IP",min:1,max:15,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"G",min:1,max:15,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"F1",min:1,max:18,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"F2",min:1,max:18,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"F",min:1,max:18,errorTip:"app.intlPassengerForm.validate.ForeignErrorTips"},{type:"C",min:1,max:15,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"M",min:1,max:15,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"I",min:1,max:15,errorTip:"app.intlPassengerForm.validate.certMaxLength"},{type:"IN",min:0,max:15,errorTip:"app.intlPassengerForm.validate.certMinMaxLength"}],F=[{label:"TP-UATP",value:"TP"},{label:"AX-AMERICAN EXPRESS",value:"AX"},{label:"CA/MC-MASTER",value:"MC"},{label:"CU-CHINA UNIONPAY",value:"CU"},{label:"DC-DINERS CLUB",value:"DC"},{label:"DS-DISCOVER",value:"DS"},{label:"JB/JC-JCB",value:"JB/JC"},{label:"KR-KOREA CREDIT CARD",value:"KR"},{label:"TK/TX-LOST & STOLEN TICKET",value:"TK/TX"},{label:"UP-结算商务卡",value:"UP"},{label:"VI-VISA",value:"VI"},{label:"AP-LUFTHANSA AIRPLUS",value:"AP"}];var l=(e=>(e.UPDATE="PNR修改",e.DELETE="PNR删除",e.CREATE="PNR创建",e))(l||{});const I="empty",M={ct:"app.editContact.ctInfo",cts:"app.editContact.ctInfos",contactorEmail:"app.editContact.contactEmile",contact:"app.editContact.contactor",issueLimitCrs:"app.editLimit.issuanceDeadline",others:"SSR OTHS",remarkOsis:"OSI",remarks:"RMK",ckins:"SSR CKIN",clids:"SSR CLID",groupNum:"app.intlPassengerForm.teamSize",ssrContents:"SSR",ssrAdtk:"SSR ADTK"},t=[{value:"ADT",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_adt")},{value:"CNN",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_cnn")},{value:"UNN",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_unn")},{value:"INF",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_inf")},{value:"INS",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_ins")},{value:"C06",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_c06")},{value:"EMI",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_emi")},{value:"EMN",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_emn")},{value:"ENF",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_enf")},{value:"ENS",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_ens")},{value:"CMA",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_cma")},{value:"CMP",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_cmp")},{value:"ITX",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_itx")},{value:"SEA",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_sea")},{value:"STU",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_stu")},{value:"YTH",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_yth")}],m=[...t,{value:"LBR",label:a.global.t("app.fastQuery.headerQuery.passengerType.type_lbr")}],h=["OPEN FOR USE","AIRPORT CNTL"],O={"AIRPORT CNTL":{name:"机场控制",color:"text-yellow-1",shortStatus:"A"},"CHECKED IN":{name:"已值机",color:"text-gray-2",shortStatus:"C"},"LIFT/BOARDED":{name:"航班离港",color:"text-gray-2",shortStatus:"B"},EXCHANGED:{name:"已换开",color:"text-yellow-1",shortStatus:"E"},"FLOWN/USED":{name:"已使用",color:"text-red-1",shortStatus:"F"},"FIM EXCH":{name:"FIM换开",color:"text-yellow-1",shortStatus:"G"},"IRR OPER":{name:"非正常",shortStatus:"I"},BOARDED:{name:"已登机",shortStatus:"L"},"CPN NOTE":{name:"等待通知",shortStatus:"N"},"OPEN FOR USE":{name:"可使用",color:"text-green-1",shortStatus:"O"},PRINTED:{name:"此状态未使用",shortStatus:"P"},REFUNDED:{name:"已退票",color:"text-red-1",shortStatus:"R"},SUSPENDED:{name:"已挂起",color:"text-red-1",shortStatus:"S"},"PAPER TICKET":{name:"换开纸票，旧票状态",color:"text-yellow-1",shortStatus:"T"},VOID:{name:"已作废",color:"text-gray-2",shortStatus:"V"},"PRINT EXCH":{name:"换开纸票，旧票状态",color:"text-yellow-1",shortStatus:"X"},"AIRP CNTL":{name:"换开纸票，旧票状态",color:"text-yellow-1",shortStatus:"A"},"USED/FLOWN":{name:"已使用",color:"text-gray-2",shortStatus:"F"},OPEN:{name:"可使用",color:"text-green-1",shortStatus:"O"},REFU:{name:"已退票",color:"text-red-1",shortStatus:"R"},EXCH:{name:"已换开",color:"text-yellow-1",shortStatus:"E"},FLOW:{name:"已使用",color:"text-gray-2",shortStatus:"F"},SUSP:{name:"已挂起",color:"text-red-1",shortStatus:"S"},CHEK:{name:"已值机",color:"text-gray-2",shortStatus:"C"},PEXC:{name:"换开纸票，旧票状态",color:"text-yellow-1",shortStatus:"X"},PAPR:{name:"换开纸票，旧票状态",color:"text-yellow-1",shortStatus:"T"},AIRC:{name:"机场控制",color:"text-yellow-1",shortStatus:"A"},LIFT:{name:"航班离港",color:"text-gray-2",shortStatus:"B"}},B=[{value:"ADT",label:a.global.t("app.fareQuery.freightate.passengersSelet.ADT")},{value:"EMI",label:a.global.t("app.fareQuery.freightate.passengersSelet.EMI")},{value:"CMA",label:a.global.t("app.fareQuery.freightate.passengersSelet.CMA")},{value:"CMP",label:a.global.t("app.fareQuery.freightate.passengersSelet.CMP")},{value:"ITX",label:a.global.t("app.fareQuery.freightate.passengersSelet.ITX")},{value:"SEA",label:a.global.t("app.fareQuery.freightate.passengersSelet.SEA")},{value:"STU",label:a.global.t("app.fareQuery.freightate.passengersSelet.STU")},{value:"YTH",label:a.global.t("app.fareQuery.freightate.passengersSelet.YTH")},{value:"LBR",label:a.global.t("app.fareQuery.freightate.passengersSelet.LBR")}],Q=[{value:"CNN",label:a.global.t("app.fareQuery.freightate.passengersSelet.CNN")},{value:"UNN",label:a.global.t("app.fareQuery.freightate.passengersSelet.UNN")},{value:"EMN",label:a.global.t("app.fareQuery.freightate.passengersSelet.EMN")}],_=[{value:"INF",label:a.global.t("app.fareQuery.freightate.passengersSelet.INF")},{value:"INS",label:a.global.t("app.fareQuery.freightate.passengersSelet.INS")},{value:"ENF",label:a.global.t("app.fareQuery.freightate.passengersSelet.ENF")},{value:"ENS",label:a.global.t("app.fareQuery.freightate.passengersSelet.ENS")}],G=[{value:"ADT",label:a.global.t("app.fareQuery.freightate.passengersSelet.ADT")},{value:"JC",label:a.global.t("app.fareQuery.freightate.passengersSelet.JC")},{value:"GM",label:a.global.t("app.fareQuery.freightate.passengersSelet.GM")},{value:"CHD",label:a.global.t("app.fareQuery.freightate.passengersSelet.CHD")},{value:"UM",label:a.global.t("app.fareQuery.freightate.passengersSelet.UM")},{value:"INF",label:a.global.t("app.fareQuery.freightate.passengersSelet.INF")}],U=["PUBL","PRVT","FRST","INTR","PREC","ECON"],H="USER_ATTENTION_",K="frequent-traveler-select";export{g as A,h as C,G as D,O as E,U as F,u as H,d as I,n as N,p as O,t as P,s as R,o as S,P as T,H as U,A as V,D as X,m as a,y as b,f as c,c as d,b as e,R as f,N as g,i as h,v as i,E as j,S as k,M as l,l as m,K as n,I as o,L as p,B as q,Q as r,_ as s,F as t,C as u,T as v};
