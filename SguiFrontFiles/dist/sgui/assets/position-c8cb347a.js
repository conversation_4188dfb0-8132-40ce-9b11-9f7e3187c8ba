import{eb as s}from"./index-9381ab2b.js";const r=(n,e)=>{if(!s||!n||!e)return!1;const t=n.getBoundingClientRect();let o;return e instanceof Element?o=e.getBoundingClientRect():o={top:0,right:window.innerWidth,bottom:window.innerHeight,left:0},t.top<o.bottom&&t.bottom>o.top&&t.right>o.left&&t.left<o.right},i=n=>{let e=0,t=n;for(;t;)e+=t.offsetTop,t=t.offsetParent;return e},l=(n,e)=>Math.abs(i(n)-i(e));export{l as g,r as i};
