const s=/^[a-zA-Z\d]{14,20}$/,a=/^[a-zA-Z\d]{3}$/,E=/^[0-9a-zA-Z]{2}$/,A=/^[a-zA-Z]{2,3}$/,_=/^(IC|WV).*/,N=/^(IC).*/,t=/^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2}))|([0]))$/,c=/^(\d+(.(\d)+)?)?$/,T=/^[a-zA-Z0-9\d]{1,22}$/,n=/^[0-9A-Za-z]{1,20}$/,o=/^(IC|WV)$/,R=/^(([1-9][0-9]*)|([0]\.([1-9]|[0-9][1-9]))|([1-9][0-9]*\.\d{1,2}))$/,I=/^(([1-9][0-9]*)|(0\.([0-9]|[0-9][0-9]))|(0)|([1-9][0-9]*\.\d{1,2}))$/,C=/^[0-9a-zA-Z]{2}$/,$=/^[0-9]{12}$/,d=/^\d{1,4}$/,M=/^[a-zA-Z]{4}$/,O=/^[0-9]{10}$/,S=/^\d{5}$|^\d{10}$/,U=/^\d{10}$/,D=/^\d{5}$/,Z=/^[1-9]{1}\d*$/,z=/^[0-9]*$/,L=/^[1-9][0-9]{0,2}$/,F=/^[a-zA-Z\d]{15,18}$/,e=/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/,P=/^(([1-9]\d*)|0)(\.\d+)?$/,f=/^[0-9]+$/,u=/^[1-9][0-9]{0,4}|-1$/,G=/^\d{4}$/,p=/^[0-9]{6}$/,B=/^[A-Za-z]{3}$/,m=/^([a-zA-Z]{1}[0-9]{1})|^([0-9]{1}[a-zA-Z]{1})|^([A-Za-z]{2})$/,i=/[/ ][pP][1-9][0-9]*(\/[pP]?[1-9][0-9]*)*$/,r=/#\d{4,8}/,H=/\d{1,2}[A-z]{3}\d/,X=/\d{8}(?= P|\/P)/,l=/[A-Z]{3}/,K=/^[\p{Script=Han}a-zA-Z0-9()·]*$/u,Y=/\p{Script=Han}/u,J=/^\s*[a-zA-Z·]+[a-zA-Z\s·]*\/\s*[a-zA-Z·]+[a-zA-Z.\s·]*(\s*\((UM\d+)\)|\s*\((\d{1,2}(JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)\d{2})\))?$/,V=/(\s*\((UM\d+)\)|\s*\((\d{1,2}(JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)\d{2})\))$/,b=/(^\s*[a-zA-Z]+[a-zA-Z\s]*\/?\s*[a-zA-Z]+[a-zA-Z.\s]*$)/,g=/^[A-Za-z\d]{3,7}$/,W=/^[A-Za-z]{1}$/,j=/^[A-Za-z\d]{2}$|^\*[A-Za-z\d]{2}$/,k=/^\d{2}[A-Za-z]{3}$/,x=/^\d{2}[A-Za-z]{3}\d{2}$/,y=/^\d{1,2}[A-Za-z]{3}(\d{2})?$/,h=/^[A-Za-z]{6}$/,Q=/[\u4e00-\u9fa5]+/,q=/^(NN|LL)?\d{1,4}$/,v=/^([\p{Script=Han}\u2E80-\uFE4F]+(\s*[a-zA-Z]*))|([\p{Script=Han}\u2E80-\uFE4F]+([a-zA-Z]*))$/u,w=/(^\s*[0-9a-zA-Z]+[0-9a-zA-Z\s]*\/?\s*[0-9a-zA-Z]*[0-9a-zA-Z/()\s]*$)/,ss=/(^\s*[0-9a-zA-Z]+[0-9a-zA-Z\s]*\/?\s*[0-9a-zA-Z]*[0-9a-zA-Z\s]*$)/,as=/^[0-9A-Za-z(~!@#$%^&*]{0,20}$/,Es=/^[0-9A-Za-z]{0,2}$/,As=/^\d{3}$/,_s=/^\d{10}(-\d{2})?$/,Ns=/^\d{1}$/,ts=/^[A-Za-z]+$/,cs=/^\d{4}$/,Ts=/^\d{2}[A-Za-z]{3}\d{2}/,ns=/([01]\d|2[0-3])[0-5]\d/,os=/^\d{6}$/,Rs=/^([01]\d|2[0-3])[0-5]\d$/,Is=/^(?!.*(?:script|embed|style|img|image|object|frame|iframe|frameset|meta|xml|link|applet|onload|alert|SCRIPT|EMBED|STYLE|IMG|IMAGE|OBJECT|FRAME|IFRAME|FRAMESET|META|XML|LINK|APPLET|ONLOAD|ALERT)).*$/,Cs=/^(?!.*(?:script|embed|style|img|image|object|frame|iframe|frameset|meta|xml|link|applet|onload|alert|SCRIPT|EMBED|STYLE|IMG|IMAGE|OBJECT|FRAME|IFRAME|FRAMESET|META|XML|LINK|APPLET|ONLOAD|ALERT)).*$/,$s=/^(?:\d{3}[-\s]?\d{9}|\d{9})$/,ds=/^[a-zA-Z]+$/,Ms=/[\u3002\uff1b\uff0c\uff1a\u201c\u201d\uff08\uff09\u3001\uff1f\u300a\u300b\uff01\u3010\u3011\uffe5]+/,Os=/\(UM\d+\)/,Ss=/^[CF]ID[\d]+$/,Us=/^[a-zA-Z]+\/[a-zA-Z]+$/,Ds=/^[0.]*$/;export{G as $,E as A,Ms as B,A as C,f as D,D as E,Ss as F,m as G,L as H,Y as I,l as J,H as K,X as L,V as M,J as N,r as O,n as P,i as Q,$s as R,cs as S,K as T,Os as U,F as V,e as W,u as X,p as Y,B as Z,P as _,a,Ds as a0,g as a1,W as a2,k as a3,x as a4,h as a5,q as a6,ns as a7,y as a8,Rs as a9,j as aa,Es as ab,Ts as ac,as as ad,Us as ae,O as af,S as ag,Z as ah,z as ai,Is as aj,Cs as ak,t as b,I as c,C as d,ss as e,o as f,_ as g,s as h,T as i,N as j,v as k,d as l,R as m,c as n,$ as o,As as p,_s as q,Ns as r,ts as s,w as t,U as u,b as v,ds as w,Q as x,M as y,os as z};
