import{h9 as Ui}from"./index-9381ab2b.js";var Wt={exports:{}};(function(h,e){(function(){var r,o=0xdeadbeefcafe,u=(o&16777215)==15715070;function n(t,i,s){t!=null&&(typeof t=="number"?this.fromNumber(t,i,s):i==null&&typeof t!="string"?this.fromString(t,256):this.fromString(t,i))}function c(){return new n(null)}function x(t,i,s,f,l,a){for(;--a>=0;){var p=i*this[t++]+s[f]+l;l=Math.floor(p/67108864),s[f++]=p&67108863}return l}function d(t,i,s,f,l,a){for(var p=i&32767,g=i>>15;--a>=0;){var F=this[t]&32767,A=this[t++]>>15,O=g*F+A*p;F=p*F+((O&32767)<<15)+s[f]+(l&1073741823),l=(F>>>30)+(O>>>15)+g*A+(l>>>30),s[f++]=F&1073741823}return l}function m(t,i,s,f,l,a){for(var p=i&16383,g=i>>14;--a>=0;){var F=this[t]&16383,A=this[t++]>>14,O=g*F+A*p;F=p*F+((O&16383)<<14)+s[f]+l,l=(F>>28)+(O>>14)+g*A,s[f++]=F&268435455}return l}var y=typeof navigator<"u";y&&u&&navigator.appName=="Microsoft Internet Explorer"?(n.prototype.am=d,r=30):y&&u&&navigator.appName!="Netscape"?(n.prototype.am=x,r=26):(n.prototype.am=m,r=28),n.prototype.DB=r,n.prototype.DM=(1<<r)-1,n.prototype.DV=1<<r;var b=52;n.prototype.FV=Math.pow(2,b),n.prototype.F1=b-r,n.prototype.F2=2*r-b;var T="0123456789abcdefghijklmnopqrstuvwxyz",B=new Array,D,E;for(D="0".charCodeAt(0),E=0;E<=9;++E)B[D++]=E;for(D="a".charCodeAt(0),E=10;E<36;++E)B[D++]=E;for(D="A".charCodeAt(0),E=10;E<36;++E)B[D++]=E;function G(t){return T.charAt(t)}function z(t,i){var s=B[t.charCodeAt(i)];return s??-1}function v(t){for(var i=this.t-1;i>=0;--i)t[i]=this[i];t.t=this.t,t.s=this.s}function $(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0}function _(t){var i=c();return i.fromInt(t),i}function Ft(t,i){var s;if(i==16)s=4;else if(i==8)s=3;else if(i==256)s=8;else if(i==2)s=1;else if(i==32)s=5;else if(i==4)s=2;else{this.fromRadix(t,i);return}this.t=0,this.s=0;for(var f=t.length,l=!1,a=0;--f>=0;){var p=s==8?t[f]&255:z(t,f);if(p<0){t.charAt(f)=="-"&&(l=!0);continue}l=!1,a==0?this[this.t++]=p:a+s>this.DB?(this[this.t-1]|=(p&(1<<this.DB-a)-1)<<a,this[this.t++]=p>>this.DB-a):this[this.t-1]|=p<<a,a+=s,a>=this.DB&&(a-=this.DB)}s==8&&t[0]&128&&(this.s=-1,a>0&&(this[this.t-1]|=(1<<this.DB-a)-1<<a)),this.clamp(),l&&n.ZERO.subTo(this,this)}function wt(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t}function re(t){if(this.s<0)return"-"+this.negate().toString(t);var i;if(t==16)i=4;else if(t==8)i=3;else if(t==2)i=1;else if(t==32)i=5;else if(t==4)i=2;else return this.toRadix(t);var s=(1<<i)-1,f,l=!1,a="",p=this.t,g=this.DB-p*this.DB%i;if(p-- >0)for(g<this.DB&&(f=this[p]>>g)>0&&(l=!0,a=G(f));p>=0;)g<i?(f=(this[p]&(1<<g)-1)<<i-g,f|=this[--p]>>(g+=this.DB-i)):(f=this[p]>>(g-=i)&s,g<=0&&(g+=this.DB,--p)),f>0&&(l=!0),l&&(a+=G(f));return l?a:"0"}function ne(){var t=c();return n.ZERO.subTo(this,t),t}function oe(){return this.s<0?this.negate():this}function he(t){var i=this.s-t.s;if(i!=0)return i;var s=this.t;if(i=s-t.t,i!=0)return this.s<0?-i:i;for(;--s>=0;)if((i=this[s]-t[s])!=0)return i;return 0}function xt(t){var i=1,s;return(s=t>>>16)!=0&&(t=s,i+=16),(s=t>>8)!=0&&(t=s,i+=8),(s=t>>4)!=0&&(t=s,i+=4),(s=t>>2)!=0&&(t=s,i+=2),(s=t>>1)!=0&&(t=s,i+=1),i}function fe(){return this.t<=0?0:this.DB*(this.t-1)+xt(this[this.t-1]^this.s&this.DM)}function ue(t,i){var s;for(s=this.t-1;s>=0;--s)i[s+t]=this[s];for(s=t-1;s>=0;--s)i[s]=0;i.t=this.t+t,i.s=this.s}function ce(t,i){for(var s=t;s<this.t;++s)i[s-t]=this[s];i.t=Math.max(this.t-t,0),i.s=this.s}function le(t,i){var s=t%this.DB,f=this.DB-s,l=(1<<f)-1,a=Math.floor(t/this.DB),p=this.s<<s&this.DM,g;for(g=this.t-1;g>=0;--g)i[g+a+1]=this[g]>>f|p,p=(this[g]&l)<<s;for(g=a-1;g>=0;--g)i[g]=0;i[a]=p,i.t=this.t+a+1,i.s=this.s,i.clamp()}function ae(t,i){i.s=this.s;var s=Math.floor(t/this.DB);if(s>=this.t){i.t=0;return}var f=t%this.DB,l=this.DB-f,a=(1<<f)-1;i[0]=this[s]>>f;for(var p=s+1;p<this.t;++p)i[p-s-1]|=(this[p]&a)<<l,i[p-s]=this[p]>>f;f>0&&(i[this.t-s-1]|=(this.s&a)<<l),i.t=this.t-s,i.clamp()}function xe(t,i){for(var s=0,f=0,l=Math.min(t.t,this.t);s<l;)f+=this[s]-t[s],i[s++]=f&this.DM,f>>=this.DB;if(t.t<this.t){for(f-=t.s;s<this.t;)f+=this[s],i[s++]=f&this.DM,f>>=this.DB;f+=this.s}else{for(f+=this.s;s<t.t;)f-=t[s],i[s++]=f&this.DM,f>>=this.DB;f-=t.s}i.s=f<0?-1:0,f<-1?i[s++]=this.DV+f:f>0&&(i[s++]=f),i.t=s,i.clamp()}function pe(t,i){var s=this.abs(),f=t.abs(),l=s.t;for(i.t=l+f.t;--l>=0;)i[l]=0;for(l=0;l<f.t;++l)i[l+s.t]=s.am(0,f[l],i,l,0,s.t);i.s=0,i.clamp(),this.s!=t.s&&n.ZERO.subTo(i,i)}function ge(t){for(var i=this.abs(),s=t.t=2*i.t;--s>=0;)t[s]=0;for(s=0;s<i.t-1;++s){var f=i.am(s,i[s],t,2*s,0,1);(t[s+i.t]+=i.am(s+1,2*i[s],t,2*s+1,f,i.t-s-1))>=i.DV&&(t[s+i.t]-=i.DV,t[s+i.t+1]=1)}t.t>0&&(t[t.t-1]+=i.am(s,i[s],t,2*s,0,1)),t.s=0,t.clamp()}function de(t,i,s){var f=t.abs();if(!(f.t<=0)){var l=this.abs();if(l.t<f.t){i!=null&&i.fromInt(0),s!=null&&this.copyTo(s);return}s==null&&(s=c());var a=c(),p=this.s,g=t.s,F=this.DB-xt(f[f.t-1]);F>0?(f.lShiftTo(F,a),l.lShiftTo(F,s)):(f.copyTo(a),l.copyTo(s));var A=a.t,O=a[A-1];if(O!=0){var C=O*(1<<this.F1)+(A>1?a[A-2]>>this.F2:0),j=this.FV/C,gt=(1<<this.F1)/C,N=1<<this.F2,H=s.t,dt=H-A,Z=i??c();for(a.dlShiftTo(dt,Z),s.compareTo(Z)>=0&&(s[s.t++]=1,s.subTo(Z,s)),n.ONE.dlShiftTo(A,Z),Z.subTo(a,a);a.t<A;)a[a.t++]=0;for(;--dt>=0;){var It=s[--H]==O?this.DM:Math.floor(s[H]*j+(s[H-1]+N)*gt);if((s[H]+=a.am(0,It,s,dt,0,A))<It)for(a.dlShiftTo(dt,Z),s.subTo(Z,s);s[H]<--It;)s.subTo(Z,s)}i!=null&&(s.drShiftTo(A,i),p!=g&&n.ZERO.subTo(i,i)),s.t=A,s.clamp(),F>0&&s.rShiftTo(F,s),p<0&&n.ZERO.subTo(s,s)}}}function ye(t){var i=c();return this.abs().divRemTo(t,null,i),this.s<0&&i.compareTo(n.ZERO)>0&&t.subTo(i,i),i}function J(t){this.m=t}function me(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t}function be(t){return t}function ve(t){t.divRemTo(this.m,null,t)}function Te(t,i,s){t.multiplyTo(i,s),this.reduce(s)}function Fe(t,i){t.squareTo(i),this.reduce(i)}J.prototype.convert=me,J.prototype.revert=be,J.prototype.reduce=ve,J.prototype.mulTo=Te,J.prototype.sqrTo=Fe;function we(){if(this.t<1)return 0;var t=this[0];if(!(t&1))return 0;var i=t&3;return i=i*(2-(t&15)*i)&15,i=i*(2-(t&255)*i)&255,i=i*(2-((t&65535)*i&65535))&65535,i=i*(2-t*i%this.DV)%this.DV,i>0?this.DV-i:-i}function K(t){this.m=t,this.mp=t.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function Be(t){var i=c();return t.abs().dlShiftTo(this.m.t,i),i.divRemTo(this.m,null,i),t.s<0&&i.compareTo(n.ZERO)>0&&this.m.subTo(i,i),i}function Se(t){var i=c();return t.copyTo(i),this.reduce(i),i}function De(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var i=0;i<this.m.t;++i){var s=t[i]&32767,f=s*this.mpl+((s*this.mph+(t[i]>>15)*this.mpl&this.um)<<15)&t.DM;for(s=i+this.m.t,t[s]+=this.m.am(0,f,t,i,0,this.m.t);t[s]>=t.DV;)t[s]-=t.DV,t[++s]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)}function Ie(t,i){t.squareTo(i),this.reduce(i)}function Ae(t,i,s){t.multiplyTo(i,s),this.reduce(s)}K.prototype.convert=Be,K.prototype.revert=Se,K.prototype.reduce=De,K.prototype.mulTo=Ae,K.prototype.sqrTo=Ie;function Ee(){return(this.t>0?this[0]&1:this.s)==0}function Re(t,i){if(t>4294967295||t<1)return n.ONE;var s=c(),f=c(),l=i.convert(this),a=xt(t)-1;for(l.copyTo(s);--a>=0;)if(i.sqrTo(s,f),(t&1<<a)>0)i.mulTo(f,l,s);else{var p=s;s=f,f=p}return i.revert(s)}function qe(t,i){var s;return t<256||i.isEven()?s=new J(i):s=new K(i),this.exp(t,s)}n.prototype.copyTo=v,n.prototype.fromInt=$,n.prototype.fromString=Ft,n.prototype.clamp=wt,n.prototype.dlShiftTo=ue,n.prototype.drShiftTo=ce,n.prototype.lShiftTo=le,n.prototype.rShiftTo=ae,n.prototype.subTo=xe,n.prototype.multiplyTo=pe,n.prototype.squareTo=ge,n.prototype.divRemTo=de,n.prototype.invDigit=we,n.prototype.isEven=Ee,n.prototype.exp=Re,n.prototype.toString=re,n.prototype.negate=ne,n.prototype.abs=oe,n.prototype.compareTo=he,n.prototype.bitLength=fe,n.prototype.mod=ye,n.prototype.modPowInt=qe,n.ZERO=_(0),n.ONE=_(1);function Me(){var t=c();return this.copyTo(t),t}function Ce(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function Oe(){return this.t==0?this.s:this[0]<<24>>24}function ke(){return this.t==0?this.s:this[0]<<16>>16}function Le(t){return Math.floor(Math.LN2*this.DB/Math.log(t))}function Pe(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function Ve(t){if(t==null&&(t=10),this.signum()==0||t<2||t>36)return"0";var i=this.chunkSize(t),s=Math.pow(t,i),f=_(s),l=c(),a=c(),p="";for(this.divRemTo(f,l,a);l.signum()>0;)p=(s+a.intValue()).toString(t).substr(1)+p,l.divRemTo(f,l,a);return a.intValue().toString(t)+p}function Ne(t,i){this.fromInt(0),i==null&&(i=10);for(var s=this.chunkSize(i),f=Math.pow(i,s),l=!1,a=0,p=0,g=0;g<t.length;++g){var F=z(t,g);if(F<0){t.charAt(g)=="-"&&this.signum()==0&&(l=!0);continue}p=i*p+F,++a>=s&&(this.dMultiply(f),this.dAddOffset(p,0),a=0,p=0)}a>0&&(this.dMultiply(Math.pow(i,a)),this.dAddOffset(p,0)),l&&n.ZERO.subTo(this,this)}function He(t,i,s){if(typeof i=="number")if(t<2)this.fromInt(1);else for(this.fromNumber(t,s),this.testBit(t-1)||this.bitwiseTo(n.ONE.shiftLeft(t-1),Bt,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(i);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(n.ONE.shiftLeft(t-1),this);else{var f=new Array,l=t&7;f.length=(t>>3)+1,i.nextBytes(f),l>0?f[0]&=(1<<l)-1:f[0]=0,this.fromString(f,256)}}function _e(){var t=this.t,i=new Array;i[0]=this.s;var s=this.DB-t*this.DB%8,f,l=0;if(t-- >0)for(s<this.DB&&(f=this[t]>>s)!=(this.s&this.DM)>>s&&(i[l++]=f|this.s<<this.DB-s);t>=0;)s<8?(f=(this[t]&(1<<s)-1)<<8-s,f|=this[--t]>>(s+=this.DB-8)):(f=this[t]>>(s-=8)&255,s<=0&&(s+=this.DB,--t)),f&128&&(f|=-256),l==0&&(this.s&128)!=(f&128)&&++l,(l>0||f!=this.s)&&(i[l++]=f);return i}function Ue(t){return this.compareTo(t)==0}function ze(t){return this.compareTo(t)<0?this:t}function $e(t){return this.compareTo(t)>0?this:t}function je(t,i,s){var f,l,a=Math.min(t.t,this.t);for(f=0;f<a;++f)s[f]=i(this[f],t[f]);if(t.t<this.t){for(l=t.s&this.DM,f=a;f<this.t;++f)s[f]=i(this[f],l);s.t=this.t}else{for(l=this.s&this.DM,f=a;f<t.t;++f)s[f]=i(l,t[f]);s.t=t.t}s.s=i(this.s,t.s),s.clamp()}function Ge(t,i){return t&i}function Ze(t){var i=c();return this.bitwiseTo(t,Ge,i),i}function Bt(t,i){return t|i}function We(t){var i=c();return this.bitwiseTo(t,Bt,i),i}function Ot(t,i){return t^i}function Xe(t){var i=c();return this.bitwiseTo(t,Ot,i),i}function kt(t,i){return t&~i}function Ye(t){var i=c();return this.bitwiseTo(t,kt,i),i}function Qe(){for(var t=c(),i=0;i<this.t;++i)t[i]=this.DM&~this[i];return t.t=this.t,t.s=~this.s,t}function Je(t){var i=c();return t<0?this.rShiftTo(-t,i):this.lShiftTo(t,i),i}function Ke(t){var i=c();return t<0?this.lShiftTo(-t,i):this.rShiftTo(t,i),i}function ti(t){if(t==0)return-1;var i=0;return t&65535||(t>>=16,i+=16),t&255||(t>>=8,i+=8),t&15||(t>>=4,i+=4),t&3||(t>>=2,i+=2),t&1||++i,i}function ei(){for(var t=0;t<this.t;++t)if(this[t]!=0)return t*this.DB+ti(this[t]);return this.s<0?this.t*this.DB:-1}function ii(t){for(var i=0;t!=0;)t&=t-1,++i;return i}function si(){for(var t=0,i=this.s&this.DM,s=0;s<this.t;++s)t+=ii(this[s]^i);return t}function ri(t){var i=Math.floor(t/this.DB);return i>=this.t?this.s!=0:(this[i]&1<<t%this.DB)!=0}function ni(t,i){var s=n.ONE.shiftLeft(t);return this.bitwiseTo(s,i,s),s}function oi(t){return this.changeBit(t,Bt)}function hi(t){return this.changeBit(t,kt)}function fi(t){return this.changeBit(t,Ot)}function ui(t,i){for(var s=0,f=0,l=Math.min(t.t,this.t);s<l;)f+=this[s]+t[s],i[s++]=f&this.DM,f>>=this.DB;if(t.t<this.t){for(f+=t.s;s<this.t;)f+=this[s],i[s++]=f&this.DM,f>>=this.DB;f+=this.s}else{for(f+=this.s;s<t.t;)f+=t[s],i[s++]=f&this.DM,f>>=this.DB;f+=t.s}i.s=f<0?-1:0,f>0?i[s++]=f:f<-1&&(i[s++]=this.DV+f),i.t=s,i.clamp()}function ci(t){var i=c();return this.addTo(t,i),i}function li(t){var i=c();return this.subTo(t,i),i}function ai(t){var i=c();return this.multiplyTo(t,i),i}function xi(){var t=c();return this.squareTo(t),t}function pi(t){var i=c();return this.divRemTo(t,i,null),i}function gi(t){var i=c();return this.divRemTo(t,null,i),i}function di(t){var i=c(),s=c();return this.divRemTo(t,i,s),new Array(i,s)}function yi(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()}function mi(t,i){if(t!=0){for(;this.t<=i;)this[this.t++]=0;for(this[i]+=t;this[i]>=this.DV;)this[i]-=this.DV,++i>=this.t&&(this[this.t++]=0),++this[i]}}function ut(){}function Lt(t){return t}function bi(t,i,s){t.multiplyTo(i,s)}function vi(t,i){t.squareTo(i)}ut.prototype.convert=Lt,ut.prototype.revert=Lt,ut.prototype.mulTo=bi,ut.prototype.sqrTo=vi;function Ti(t){return this.exp(t,new ut)}function Fi(t,i,s){var f=Math.min(this.t+t.t,i);for(s.s=0,s.t=f;f>0;)s[--f]=0;var l;for(l=s.t-this.t;f<l;++f)s[f+this.t]=this.am(0,t[f],s,f,0,this.t);for(l=Math.min(t.t,i);f<l;++f)this.am(0,t[f],s,f,0,i-f);s.clamp()}function wi(t,i,s){--i;var f=s.t=this.t+t.t-i;for(s.s=0;--f>=0;)s[f]=0;for(f=Math.max(i-this.t,0);f<t.t;++f)s[this.t+f-i]=this.am(i-f,t[f],s,0,0,this.t+f-i);s.clamp(),s.drShiftTo(1,s)}function tt(t){this.r2=c(),this.q3=c(),n.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}function Bi(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var i=c();return t.copyTo(i),this.reduce(i),i}function Si(t){return t}function Di(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)}function Ii(t,i){t.squareTo(i),this.reduce(i)}function Ai(t,i,s){t.multiplyTo(i,s),this.reduce(s)}tt.prototype.convert=Bi,tt.prototype.revert=Si,tt.prototype.reduce=Di,tt.prototype.mulTo=Ai,tt.prototype.sqrTo=Ii;function Ei(t,i){var s=t.bitLength(),f,l=_(1),a;if(s<=0)return l;s<18?f=1:s<48?f=3:s<144?f=4:s<768?f=5:f=6,s<8?a=new J(i):i.isEven()?a=new tt(i):a=new K(i);var p=new Array,g=3,F=f-1,A=(1<<f)-1;if(p[1]=a.convert(this),f>1){var O=c();for(a.sqrTo(p[1],O);g<=A;)p[g]=c(),a.mulTo(O,p[g-2],p[g]),g+=2}var C=t.t-1,j,gt=!0,N=c(),H;for(s=xt(t[C])-1;C>=0;){for(s>=F?j=t[C]>>s-F&A:(j=(t[C]&(1<<s+1)-1)<<F-s,C>0&&(j|=t[C-1]>>this.DB+s-F)),g=f;!(j&1);)j>>=1,--g;if((s-=g)<0&&(s+=this.DB,--C),gt)p[j].copyTo(l),gt=!1;else{for(;g>1;)a.sqrTo(l,N),a.sqrTo(N,l),g-=2;g>0?a.sqrTo(l,N):(H=l,l=N,N=H),a.mulTo(N,p[j],l)}for(;C>=0&&!(t[C]&1<<s);)a.sqrTo(l,N),H=l,l=N,N=H,--s<0&&(s=this.DB-1,--C)}return a.revert(l)}function Ri(t){var i=this.s<0?this.negate():this.clone(),s=t.s<0?t.negate():t.clone();if(i.compareTo(s)<0){var f=i;i=s,s=f}var l=i.getLowestSetBit(),a=s.getLowestSetBit();if(a<0)return i;for(l<a&&(a=l),a>0&&(i.rShiftTo(a,i),s.rShiftTo(a,s));i.signum()>0;)(l=i.getLowestSetBit())>0&&i.rShiftTo(l,i),(l=s.getLowestSetBit())>0&&s.rShiftTo(l,s),i.compareTo(s)>=0?(i.subTo(s,i),i.rShiftTo(1,i)):(s.subTo(i,s),s.rShiftTo(1,s));return a>0&&s.lShiftTo(a,s),s}function qi(t){if(t<=0)return 0;var i=this.DV%t,s=this.s<0?t-1:0;if(this.t>0)if(i==0)s=this[0]%t;else for(var f=this.t-1;f>=0;--f)s=(i*s+this[f])%t;return s}function Mi(t){var i=t.isEven();if(this.isEven()&&i||t.signum()==0)return n.ZERO;for(var s=t.clone(),f=this.clone(),l=_(1),a=_(0),p=_(0),g=_(1);s.signum()!=0;){for(;s.isEven();)s.rShiftTo(1,s),i?((!l.isEven()||!a.isEven())&&(l.addTo(this,l),a.subTo(t,a)),l.rShiftTo(1,l)):a.isEven()||a.subTo(t,a),a.rShiftTo(1,a);for(;f.isEven();)f.rShiftTo(1,f),i?((!p.isEven()||!g.isEven())&&(p.addTo(this,p),g.subTo(t,g)),p.rShiftTo(1,p)):g.isEven()||g.subTo(t,g),g.rShiftTo(1,g);s.compareTo(f)>=0?(s.subTo(f,s),i&&l.subTo(p,l),a.subTo(g,a)):(f.subTo(s,f),i&&p.subTo(l,p),g.subTo(a,g))}if(f.compareTo(n.ONE)!=0)return n.ZERO;if(g.compareTo(t)>=0)return g.subtract(t);if(g.signum()<0)g.addTo(t,g);else return g;return g.signum()<0?g.add(t):g}var R=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Ci=(1<<26)/R[R.length-1];function Oi(t){var i,s=this.abs();if(s.t==1&&s[0]<=R[R.length-1]){for(i=0;i<R.length;++i)if(s[0]==R[i])return!0;return!1}if(s.isEven())return!1;for(i=1;i<R.length;){for(var f=R[i],l=i+1;l<R.length&&f<Ci;)f*=R[l++];for(f=s.modInt(f);i<l;)if(f%R[i++]==0)return!1}return s.millerRabin(t)}function ki(t){var i=this.subtract(n.ONE),s=i.getLowestSetBit();if(s<=0)return!1;var f=i.shiftRight(s);t=t+1>>1,t>R.length&&(t=R.length);for(var l=c(),a=0;a<t;++a){l.fromInt(R[Math.floor(Math.random()*R.length)]);var p=l.modPow(f,this);if(p.compareTo(n.ONE)!=0&&p.compareTo(i)!=0){for(var g=1;g++<s&&p.compareTo(i)!=0;)if(p=p.modPowInt(2,this),p.compareTo(n.ONE)==0)return!1;if(p.compareTo(i)!=0)return!1}}return!0}n.prototype.chunkSize=Le,n.prototype.toRadix=Ve,n.prototype.fromRadix=Ne,n.prototype.fromNumber=He,n.prototype.bitwiseTo=je,n.prototype.changeBit=ni,n.prototype.addTo=ui,n.prototype.dMultiply=yi,n.prototype.dAddOffset=mi,n.prototype.multiplyLowerTo=Fi,n.prototype.multiplyUpperTo=wi,n.prototype.modInt=qi,n.prototype.millerRabin=ki,n.prototype.clone=Me,n.prototype.intValue=Ce,n.prototype.byteValue=Oe,n.prototype.shortValue=ke,n.prototype.signum=Pe,n.prototype.toByteArray=_e,n.prototype.equals=Ue,n.prototype.min=ze,n.prototype.max=$e,n.prototype.and=Ze,n.prototype.or=We,n.prototype.xor=Xe,n.prototype.andNot=Ye,n.prototype.not=Qe,n.prototype.shiftLeft=Je,n.prototype.shiftRight=Ke,n.prototype.getLowestSetBit=ei,n.prototype.bitCount=si,n.prototype.testBit=ri,n.prototype.setBit=oi,n.prototype.clearBit=hi,n.prototype.flipBit=fi,n.prototype.add=ci,n.prototype.subtract=li,n.prototype.multiply=ai,n.prototype.divide=pi,n.prototype.remainder=gi,n.prototype.divideAndRemainder=di,n.prototype.modPow=Ei,n.prototype.modInverse=Mi,n.prototype.pow=Ti,n.prototype.gcd=Ri,n.prototype.isProbablePrime=Oi,n.prototype.square=xi,n.prototype.Barrett=tt;var pt,M,I;function Li(t){M[I++]^=t&255,M[I++]^=t>>8&255,M[I++]^=t>>16&255,M[I++]^=t>>24&255,I>=Dt&&(I-=Dt)}function Pt(){Li(new Date().getTime())}if(M==null){M=new Array,I=0;var V;if(typeof window<"u"&&window.crypto){if(window.crypto.getRandomValues){var Vt=new Uint8Array(32);for(window.crypto.getRandomValues(Vt),V=0;V<32;++V)M[I++]=Vt[V]}else if(navigator.appName=="Netscape"&&navigator.appVersion<"5"){var Nt=window.crypto.random(32);for(V=0;V<Nt.length;++V)M[I++]=Nt.charCodeAt(V)&255}}for(;I<Dt;)V=Math.floor(65536*Math.random()),M[I++]=V>>>8,M[I++]=V&255;I=0,Pt()}function Pi(){if(pt==null){for(Pt(),pt=_i(),pt.init(M),I=0;I<M.length;++I)M[I]=0;I=0}return pt.next()}function Vi(t){var i;for(i=0;i<t.length;++i)t[i]=Pi()}function Ht(){}Ht.prototype.nextBytes=Vi;function St(){this.i=0,this.j=0,this.S=new Array}function Ni(t){var i,s,f;for(i=0;i<256;++i)this.S[i]=i;for(s=0,i=0;i<256;++i)s=s+this.S[i]+t[i%t.length]&255,f=this.S[i],this.S[i]=this.S[s],this.S[s]=f;this.i=0,this.j=0}function Hi(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]}St.prototype.init=Ni,St.prototype.next=Hi;function _i(){return new St}var Dt=256;h.exports={default:n,BigInteger:n,SecureRandom:Ht}}).call(Ui)})(Wt);var ft=Wt.exports;const{BigInteger:ht}=ft;function zi(h){let e=h.toString(16);if(e.substr(0,1)!=="-")e.length%2===1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{let o=e.substr(1).length;o%2===1?o+=1:e.match(/^[0-7]/)||(o+=2);let u="";for(let x=0;x<o;x++)u+="f";e=new ht(u,16).xor(h).add(ht.ONE).toString(16).replace(/^-/,"")}return e}class Xt{constructor(){this.isModified=!0,this.hTLV=null,this.hT="00",this.hL="00",this.hV=""}getLengthHexFromValue(){const e=this.hV.length/2;let r=e.toString(16);return r.length%2===1&&(r="0"+r),e<128?r:(128+r.length/2).toString(16)+r}getEncodedHex(){return(this.hTLV==null||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV}getFreshValueHex(){return""}}class _t extends Xt{constructor(e){super(),this.hT="02",e&&e.bigint&&(this.hTLV=null,this.isModified=!0,this.hV=zi(e.bigint))}getFreshValueHex(){return this.hV}}class $i extends Xt{constructor(e){super(),this.hT="30",this.asn1Array=[],e&&e.array&&(this.asn1Array=e.array)}getFreshValueHex(){let e="";for(let r=0;r<this.asn1Array.length;r++){const o=this.asn1Array[r];e+=o.getEncodedHex()}return this.hV=e,this.hV}}function Yt(h,e){if(h.substring(e+2,e+3)!=="8")return 1;const r=parseInt(h.substring(e+3,e+4),10);return r===0?-1:r>0&&r<10?r+1:-2}function ji(h,e){const r=Yt(h,e);return r<1?"":h.substring(e+2,e+2+r*2)}function Rt(h,e){const r=ji(h,e);if(r==="")return-1;let o;return parseInt(r.substring(0,1),10)<8?o=new ht(r,16):o=new ht(r.substring(2),16),o.intValue()}function qt(h,e){const r=Yt(h,e);return r<0?r:e+(r+1)*2}function Ut(h,e){const r=qt(h,e),o=Rt(h,e);return h.substring(r,r+o*2)}function Gi(h,e){const r=qt(h,e),o=Rt(h,e);return r+o*2}function Zi(h,e){const r=[],o=qt(h,e);r.push(o);const u=Rt(h,e);let n=o,c=0;for(;;){const x=Gi(h,n);if(x==null||x-o>=u*2||c>=200)break;r.push(x),n=x,c++}return r}var Wi={encodeDer(h,e){const r=new _t({bigint:h}),o=new _t({bigint:e});return new $i({array:[r,o]}).getEncodedHex()},decodeDer(h){const e=Zi(h,0),r=e[0],o=e[1],u=Ut(h,r),n=Ut(h,o),c=new ht(u,16),x=new ht(n,16);return{r:c,s:x}}};const{BigInteger:U}=ft,zt=new U("3");class Y{constructor(e,r){this.x=r,this.q=e}equals(e){return e===this?!0:this.q.equals(e.q)&&this.x.equals(e.x)}toBigInteger(){return this.x}negate(){return new Y(this.q,this.x.negate().mod(this.q))}add(e){return new Y(this.q,this.x.add(e.toBigInteger()).mod(this.q))}subtract(e){return new Y(this.q,this.x.subtract(e.toBigInteger()).mod(this.q))}multiply(e){return new Y(this.q,this.x.multiply(e.toBigInteger()).mod(this.q))}divide(e){return new Y(this.q,this.x.multiply(e.toBigInteger().modInverse(this.q)).mod(this.q))}square(){return new Y(this.q,this.x.square().mod(this.q))}}class it{constructor(e,r,o,u){this.curve=e,this.x=r,this.y=o,this.z=u??U.ONE,this.zinv=null}getX(){return this.zinv===null&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))}getY(){return this.zinv===null&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))}equals(e){return e===this?!0:this.isInfinity()?e.isInfinity():e.isInfinity()?this.isInfinity():e.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(e.z)).mod(this.curve.q).equals(U.ZERO)?e.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(e.z)).mod(this.curve.q).equals(U.ZERO):!1}isInfinity(){return this.x===null&&this.y===null?!0:this.z.equals(U.ZERO)&&!this.y.toBigInteger().equals(U.ZERO)}negate(){return new it(this.curve,this.x,this.y.negate(),this.z)}add(e){if(this.isInfinity())return e;if(e.isInfinity())return this;const r=this.x.toBigInteger(),o=this.y.toBigInteger(),u=this.z,n=e.x.toBigInteger(),c=e.y.toBigInteger(),x=e.z,d=this.curve.q,m=r.multiply(x).mod(d),y=n.multiply(u).mod(d),b=m.subtract(y),T=o.multiply(x).mod(d),B=c.multiply(u).mod(d),D=T.subtract(B);if(U.ZERO.equals(b))return U.ZERO.equals(D)?this.twice():this.curve.infinity;const E=m.add(y),G=u.multiply(x).mod(d),z=b.square().mod(d),v=b.multiply(z).mod(d),$=G.multiply(D.square()).subtract(E.multiply(z)).mod(d),_=b.multiply($).mod(d),Ft=D.multiply(z.multiply(m).subtract($)).subtract(T.multiply(v)).mod(d),wt=v.multiply(G).mod(d);return new it(this.curve,this.curve.fromBigInteger(_),this.curve.fromBigInteger(Ft),wt)}twice(){if(this.isInfinity())return this;if(!this.y.toBigInteger().signum())return this.curve.infinity;const e=this.x.toBigInteger(),r=this.y.toBigInteger(),o=this.z,u=this.curve.q,n=this.curve.a.toBigInteger(),c=e.square().multiply(zt).add(n.multiply(o.square())).mod(u),x=r.shiftLeft(1).multiply(o).mod(u),d=r.square().mod(u),m=d.multiply(e).multiply(o).mod(u),y=x.square().mod(u),b=c.square().subtract(m.shiftLeft(3)).mod(u),T=x.multiply(b).mod(u),B=c.multiply(m.shiftLeft(2).subtract(b)).subtract(y.shiftLeft(1).multiply(d)).mod(u),D=x.multiply(y).mod(u);return new it(this.curve,this.curve.fromBigInteger(T),this.curve.fromBigInteger(B),D)}multiply(e){if(this.isInfinity())return this;if(!e.signum())return this.curve.infinity;const r=e.multiply(zt),o=this.negate();let u=this;for(let n=r.bitLength()-2;n>0;n--){u=u.twice();const c=r.testBit(n),x=e.testBit(n);c!==x&&(u=u.add(c?this:o))}return u}}let Xi=class{constructor(e,r,o){this.q=e,this.a=this.fromBigInteger(r),this.b=this.fromBigInteger(o),this.infinity=new it(this,null,null)}equals(e){return e===this?!0:this.q.equals(e.q)&&this.a.equals(e.a)&&this.b.equals(e.b)}fromBigInteger(e){return new Y(this.q,e)}decodePointHex(e){switch(parseInt(e.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:return null;case 4:case 6:case 7:const r=(e.length-2)/2,o=e.substr(2,r),u=e.substr(r+2,r);return new it(this,this.fromBigInteger(new U(o,16)),this.fromBigInteger(new U(u,16)));default:return null}}};var Yi={ECPointFp:it,ECCurveFp:Xi};const{BigInteger:et,SecureRandom:Qi}=ft,{ECCurveFp:Ji}=Yi,Ki=new Qi,{curve:ts,G:es,n:$t}=Qt();function is(){return ts}function Qt(){const h=new et("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16),e=new et("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16),r=new et("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16),o=new Ji(h,e,r),u="32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7",n="BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0",c=o.decodePointHex("04"+u+n),x=new et("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16);return{curve:o,G:c,n:x}}function ss(){const h=new et($t.bitLength(),Ki).mod($t.subtract(et.ONE)).add(et.ONE),e=lt(h.toString(16),64),r=es.multiply(h),o=lt(r.getX().toBigInteger().toString(16),64),u=lt(r.getY().toBigInteger().toString(16),64),n="04"+o+u;return{privateKey:e,publicKey:n}}function rs(h){h=unescape(encodeURIComponent(h));const e=h.length,r=[];for(let u=0;u<e;u++)r[u>>>2]|=(h.charCodeAt(u)&255)<<24-u%4*8;const o=[];for(let u=0;u<e;u++){const n=r[u>>>2]>>>24-u%4*8&255;o.push((n>>>4).toString(16)),o.push((n&15).toString(16))}return o.join("")}function ns(h){return Array.prototype.map.call(new Uint8Array(h),e=>("00"+e.toString(16)).slice(-2)).join("")}function lt(h,e){return h.length>=e?h:new Array(e-h.length+1).join("0")+h}function os(h){const e=[];let r=0;for(let u=0;u<h.length*2;u+=2)e[u>>>3]|=parseInt(h[r],10)<<24-u%8*4,r++;const o=[];for(let u=0;u<h.length;u++){const n=e[u>>>2]>>>24-u%4*8&255;o.push((n>>>4).toString(16)),o.push((n&15).toString(16))}return o.join("")}function hs(h){const e=[];let r=0;for(let o=0;o<h.length*2;o+=2)e[o>>>3]|=parseInt(h[r],10)<<24-o%8*4,r++;try{const o=[];for(let u=0;u<h.length;u++){const n=e[u>>>2]>>>24-u%4*8&255;o.push(String.fromCharCode(n))}return decodeURIComponent(escape(o.join("")))}catch{throw new Error("Malformed UTF-8 data")}}function fs(h){const e=[];let r=h.length;r%2!==0&&(h=lt(h,r+1)),r=h.length;for(let o=0;o<r;o+=2)e.push(parseInt(h.substr(o,2),16));return e}var Mt={getGlobalCurve:is,generateEcparam:Qt,generateKeyPairHex:ss,parseUtf8StringToHex:rs,parseArrayBufferToHex:ns,leftPad:lt,arrayToHex:os,arrayToUtf8:hs,hexToArray:fs};const{BigInteger:yt}=ft,k=Mt,st=function(h,e,r,o,u){for(let n=0;n<u;n++)r[o+n]=h[e+n]},S={minValue:-2147483648,maxValue:2147483647,parse(h){if(h<this.minValue){const r=Number(-h).toString(2),o=r.substr(r.length-31,31);let u="";for(let c=0;c<o.length;c++){const x=o.substr(c,1);u+=x==="0"?"1":"0"}return parseInt(u,2)+1}else if(h>this.maxValue){const r=Number(h).toString(2),o=r.substr(r.length-31,31);let u="";for(let c=0;c<o.length;c++){const x=o.substr(c,1);u+=x==="0"?"1":"0"}return-(parseInt(u,2)+1)}else return h},parseByte(h){if(h<0){const r=Number(-h).toString(2),o=r.substr(r.length-8,8);let u="";for(let c=0;c<o.length;c++){const x=o.substr(c,1);u+=x==="0"?"1":"0"}return(parseInt(u,2)+1)%256}else if(h>255){const r=Number(h).toString(2);return parseInt(r.substr(r.length-8,8),2)}else return h}};let us=class{constructor(...e){this.xBuf=[],this.xBufOff=0,this.byteCount=0,this.DIGEST_LENGTH=32,this.v0=[1937774191,1226093241,388252375,3666478592,2842636476,372324522,3817729613,2969243214],this.v0=[1937774191,1226093241,388252375,-628488704,-1452330820,372324522,-477237683,-1325724082],this.v=new Array(8),this.v_=new Array(8),this.X0=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.X=new Array(68),this.xOff=0,this.T_00_15=2043430169,this.T_16_63=2055708042,e.length>0?this.initDigest(e[0]):this.init()}init(){this.xBuf=new Array(4),this.reset()}initDigest(e){this.xBuf=[].concat(e.xBuf),this.xBufOff=e.xBufOff,this.byteCount=e.byteCount,st(e.X,0,this.X,0,e.X.length),this.xOff=e.xOff,st(e.v,0,this.v,0,e.v.length)}getDigestSize(){return this.DIGEST_LENGTH}reset(){this.byteCount=0,this.xBufOff=0;const e=Object.keys(this.xBuf);for(let r=0,o=e.length;r<o;r++)this.xBuf[e[r]]=null;st(this.v0,0,this.v,0,this.v0.length),this.xOff=0,st(this.X0,0,this.X,0,this.X0.length)}processBlock(){let e;const r=this.X,o=new Array(64);for(e=16;e<68;e++)r[e]=this.p1(r[e-16]^r[e-9]^this.rotate(r[e-3],15))^this.rotate(r[e-13],7)^r[e-6];for(e=0;e<64;e++)o[e]=r[e]^r[e+4];const u=this.v,n=this.v_;st(u,0,n,0,this.v0.length);let c,x,d,m,y;for(e=0;e<16;e++)y=this.rotate(n[0],12),c=S.parse(S.parse(y+n[4])+this.rotate(this.T_00_15,e)),c=this.rotate(c,7),x=c^y,d=S.parse(S.parse(this.ff_00_15(n[0],n[1],n[2])+n[3])+x)+o[e],m=S.parse(S.parse(this.gg_00_15(n[4],n[5],n[6])+n[7])+c)+r[e],n[3]=n[2],n[2]=this.rotate(n[1],9),n[1]=n[0],n[0]=d,n[7]=n[6],n[6]=this.rotate(n[5],19),n[5]=n[4],n[4]=this.p0(m);for(e=16;e<64;e++)y=this.rotate(n[0],12),c=S.parse(S.parse(y+n[4])+this.rotate(this.T_16_63,e)),c=this.rotate(c,7),x=c^y,d=S.parse(S.parse(this.ff_16_63(n[0],n[1],n[2])+n[3])+x)+o[e],m=S.parse(S.parse(this.gg_16_63(n[4],n[5],n[6])+n[7])+c)+r[e],n[3]=n[2],n[2]=this.rotate(n[1],9),n[1]=n[0],n[0]=d,n[7]=n[6],n[6]=this.rotate(n[5],19),n[5]=n[4],n[4]=this.p0(m);for(e=0;e<8;e++)u[e]^=S.parse(n[e]);this.xOff=0,st(this.X0,0,this.X,0,this.X0.length)}processWord(e,r){let o=e[r]<<24;o|=(e[++r]&255)<<16,o|=(e[++r]&255)<<8,o|=e[++r]&255,this.X[this.xOff]=o,++this.xOff===16&&this.processBlock()}processLength(e){this.xOff>14&&this.processBlock(),this.X[14]=this.urShiftLong(e,32),this.X[15]=e&4294967295}intToBigEndian(e,r,o){r[o]=S.parseByte(this.urShift(e,24))&255,r[++o]=S.parseByte(this.urShift(e,16))&255,r[++o]=S.parseByte(this.urShift(e,8))&255,r[++o]=S.parseByte(e)&255}doFinal(e,r){this.finish();for(let o=0;o<8;o++)this.intToBigEndian(this.v[o],e,r+o*4);return this.reset(),this.DIGEST_LENGTH}update(e){this.xBuf[this.xBufOff++]=e,this.xBufOff===this.xBuf.length&&(this.processWord(this.xBuf,0),this.xBufOff=0),this.byteCount++}blockUpdate(e,r,o){for(;this.xBufOff!==0&&o>0;)this.update(e[r]),r++,o--;for(;o>this.xBuf.length;)this.processWord(e,r),r+=this.xBuf.length,o-=this.xBuf.length,this.byteCount+=this.xBuf.length;for(;o>0;)this.update(e[r]),r++,o--}finish(){const e=this.byteCount<<3;for(this.update(128);this.xBufOff!==0;)this.update(0);this.processLength(e),this.processBlock()}rotate(e,r){return e<<r|this.urShift(e,32-r)}p0(e){return e^this.rotate(e,9)^this.rotate(e,17)}p1(e){return e^this.rotate(e,15)^this.rotate(e,23)}ff_00_15(e,r,o){return e^r^o}ff_16_63(e,r,o){return e&r|e&o|r&o}gg_00_15(e,r,o){return e^r^o}gg_16_63(e,r,o){return e&r|~e&o}urShift(e,r){return(e>S.maxValue||e<S.minValue)&&(e=S.parse(e)),e>>>r}urShiftLong(e,r){let o;const u=new yt;if(u.fromInt(e),u.signum()>=0)o=u.shiftRight(r).intValue();else{const n=new yt;n.fromInt(2);const c=~r;let x="";if(c<0){const d=64+c;for(let T=0;T<d;T++)x+="0";const m=new yt;m.fromInt(e>>r);const y=new yt("10"+x,2);x=y.toRadix(10),o=y.add(m).toRadix(10)}else x=n.shiftLeft(~r).intValue(),o=(e>>r)+x}return o}getZ(e,r,o){let u=0;if(o){if(typeof o!="string")throw new Error(`sm2: Type of userId Must be String! Receive Type: ${typeof o}`);if(o.length>=8192)throw new Error(`sm2: The Length of userId Must Less Than 8192! Length: ${o.length}`);o=k.parseUtf8StringToHex(o),u=o.length*4}if(this.update(u>>8&255),this.update(u&255),o){const T=k.hexToArray(o);this.blockUpdate(T,0,T.length)}const n=k.hexToArray(k.leftPad(e.curve.a.toBigInteger().toRadix(16),64)),c=k.hexToArray(k.leftPad(e.curve.b.toBigInteger().toRadix(16),64)),x=k.hexToArray(k.leftPad(e.getX().toBigInteger().toRadix(16),64)),d=k.hexToArray(k.leftPad(e.getY().toBigInteger().toRadix(16),64)),m=k.hexToArray(r.substr(0,64)),y=k.hexToArray(r.substr(64,64));this.blockUpdate(n,0,n.length),this.blockUpdate(c,0,c.length),this.blockUpdate(x,0,x.length),this.blockUpdate(d,0,d.length),this.blockUpdate(m,0,m.length),this.blockUpdate(y,0,y.length);const b=new Array(this.getDigestSize());return this.doFinal(b,0),b}};var Jt=us;const{BigInteger:cs}=ft,At=Jt,W=Mt;let ls=class{constructor(){this.ct=1,this.p2=null,this.sm3keybase=null,this.sm3c3=null,this.key=new Array(32),this.keyOff=0}reset(){this.sm3keybase=new At,this.sm3c3=new At;const e=W.hexToArray(W.leftPad(this.p2.getX().toBigInteger().toRadix(16),64)),r=W.hexToArray(W.leftPad(this.p2.getY().toBigInteger().toRadix(16),64));this.sm3keybase.blockUpdate(e,0,e.length),this.sm3c3.blockUpdate(e,0,e.length),this.sm3keybase.blockUpdate(r,0,r.length),this.ct=1,this.nextKey()}nextKey(){const e=new At(this.sm3keybase);e.update(this.ct>>24&255),e.update(this.ct>>16&255),e.update(this.ct>>8&255),e.update(this.ct&255),e.doFinal(this.key,0),this.keyOff=0,this.ct++}initEncipher(e){const r=W.generateKeyPairHex(),o=new cs(r.privateKey,16);let u=r.publicKey;return this.p2=e.multiply(o),this.reset(),u.length>128&&(u=u.substr(u.length-128)),u}encryptBlock(e){this.sm3c3.blockUpdate(e,0,e.length);for(let r=0;r<e.length;r++)this.keyOff===this.key.length&&this.nextKey(),e[r]^=this.key[this.keyOff++]&255}initDecipher(e,r){this.p2=r.multiply(e),this.reset()}decryptBlock(e){for(let r=0;r<e.length;r++)this.keyOff===this.key.length&&this.nextKey(),e[r]^=this.key[this.keyOff++]&255;this.sm3c3.blockUpdate(e,0,e.length)}doFinal(e){const r=W.hexToArray(W.leftPad(this.p2.getY().toBigInteger().toRadix(16),64));this.sm3c3.blockUpdate(r,0,r.length),this.sm3c3.doFinal(e,0),this.reset()}createPoint(e,r){const o="04"+e+r;return W.getGlobalCurve().decodePointHex(o)}};var as=ls;const{BigInteger:P}=ft,{encodeDer:xs,decodeDer:ps}=Wi,jt=Jt,Kt=as,w=Mt,{G:Ct,curve:te,n:nt}=w.generateEcparam(),ee=0;function gs(h,e,r=1){const o=new Kt;h=w.hexToArray(w.parseUtf8StringToHex(h)),e.length>128&&(e=e.substr(e.length-128));const u=e.substr(0,64),n=e.substr(64);e=o.createPoint(u,n);const c=o.initEncipher(e);o.encryptBlock(h);const x=w.arrayToHex(h);let d=new Array(32);return o.doFinal(d),d=w.arrayToHex(d),r===ee?c+x+d:c+d+x}function ds(h,e,r=1){const o=new Kt;e=new P(e,16);const u=h.substr(0,64),n=h.substr(0+u.length,64),c=u.length+n.length;let x=h.substr(c,64),d=h.substr(c+64);r===ee&&(x=h.substr(h.length-64),d=h.substr(c,h.length-c-64));const m=w.hexToArray(d),y=o.createPoint(u,n);o.initDecipher(e,y),o.decryptBlock(m);const b=new Array(32);return o.doFinal(b),w.arrayToHex(b)===x?w.arrayToUtf8(m):""}function ys(h,e,{pointPool:r,der:o,hash:u,publicKey:n,userId:c}={}){let x=typeof h=="string"?w.parseUtf8StringToHex(h):w.parseArrayBufferToHex(h);u&&(n=n||bs(e),x=ie(x,n,c));const d=new P(e,16),m=new P(x,16);let y=null,b=null,T=null;do{do{let B;r&&r.length?B=r.pop():B=se(),y=B.k,b=m.add(B.x1).mod(nt)}while(b.equals(P.ZERO)||b.add(y).equals(nt));T=d.add(P.ONE).modInverse(nt).multiply(y.subtract(b.multiply(d))).mod(nt)}while(T.equals(P.ZERO));return o?xs(b,T):w.leftPad(b.toString(16),64)+w.leftPad(T.toString(16),64)}function ms(h,e,r,{der:o,hash:u,userId:n}={}){let c=typeof h=="string"?w.parseUtf8StringToHex(h):w.parseArrayBufferToHex(h);u&&(c=ie(c,r,n));let x,d;if(o){const D=ps(e);x=D.r,d=D.s}else x=new P(e.substring(0,64),16),d=new P(e.substring(64),16);const m=te.decodePointHex(r),y=new P(c,16),b=x.add(d).mod(nt);if(b.equals(P.ZERO))return!1;const T=Ct.multiply(d).add(m.multiply(b)),B=y.add(T.getX().toBigInteger()).mod(nt);return x.equals(B)}function ie(h,e,r="1234567812345678"){const o=new jt,u=new jt().getZ(Ct,e.substr(2,128),r),n=w.hexToArray(w.arrayToHex(u).toString()),c=h,x=w.hexToArray(c),d=new Array(o.getDigestSize());return o.blockUpdate(n,0,n.length),o.blockUpdate(x,0,x.length),o.doFinal(d,0),w.arrayToHex(d).toString()}function bs(h){const e=Ct.multiply(new P(h,16)),r=w.leftPad(e.getX().toBigInteger().toString(16),64),o=w.leftPad(e.getY().toBigInteger().toString(16),64);return"04"+r+o}function se(){const h=w.generateKeyPairHex(),e=te.decodePointHex(h.publicKey);return h.k=new P(h.privateKey,16),h.x1=e.getX().toBigInteger(),h}var vs={generateKeyPairHex:w.generateKeyPairHex,doEncrypt:gs,doDecrypt:ds,doSignature:ys,doVerifySignature:ms,getPoint:se};function Ts(h){return h.map(e=>(e=e.toString(16),e.length===1?"0"+e:e)).join("")}function Fs(h){const e=[];for(let r=0,o=h.length;r<o;r++){const u=h.codePointAt(r);if(u<=127)e.push(u);else if(u<=2047)e.push(192|u>>>6),e.push(128|u&63);else if(u<=55295||u>=57344&&u<=65535)e.push(224|u>>>12),e.push(128|u>>>6&63),e.push(128|u&63);else if(u>=65536&&u<=1114111)r++,e.push(240|u>>>18&28),e.push(128|u>>>12&63),e.push(128|u>>>6&63),e.push(128|u&63);else throw e.push(u),new Error("input is not supported")}return e}function L(h,e){const r=[],o=~~(e/8),u=e%8;for(let n=0,c=h.length;n<c;n++)r[n]=(h[(n+o)%c]<<u&255)+(h[(n+o+1)%c]>>>8-u&255);return r}function q(h,e){const r=[];for(let o=h.length-1;o>=0;o--)r[o]=(h[o]^e[o])&255;return r}function at(h,e){const r=[];for(let o=h.length-1;o>=0;o--)r[o]=h[o]&e[o]&255;return r}function Et(h,e){const r=[];for(let o=h.length-1;o>=0;o--)r[o]=(h[o]|e[o])&255;return r}function X(h,e){const r=[];let o=0;for(let u=h.length-1;u>=0;u--){const n=h[u]+e[u]+o;n>255?(o=1,r[u]=n&255):(o=0,r[u]=n&255)}return r}function ws(h){const e=[];for(let r=h.length-1;r>=0;r--)e[r]=~h[r]&255;return e}function Bs(h){return q(q(h,L(h,9)),L(h,17))}function Ss(h){return q(q(h,L(h,15)),L(h,23))}function Ds(h,e,r,o){return o>=0&&o<=15?q(q(h,e),r):Et(Et(at(h,e),at(h,r)),at(e,r))}function Is(h,e,r,o){return o>=0&&o<=15?q(q(h,e),r):Et(at(h,e),at(ws(h),r))}function As(h,e){const r=[],o=[];for(let v=0;v<16;v++){const $=v*4;r.push(e.slice($,$+4))}for(let v=16;v<68;v++)r.push(q(q(Ss(q(q(r[v-16],r[v-9]),L(r[v-3],15))),L(r[v-13],7)),r[v-6]));for(let v=0;v<64;v++)o.push(q(r[v],r[v+4]));const u=[121,204,69,25],n=[122,135,157,138];let c=h.slice(0,4),x=h.slice(4,8),d=h.slice(8,12),m=h.slice(12,16),y=h.slice(16,20),b=h.slice(20,24),T=h.slice(24,28),B=h.slice(28,32),D,E,G,z;for(let v=0;v<64;v++){const $=v>=0&&v<=15?u:n;D=L(X(X(L(c,12),y),L($,v)),7),E=q(D,L(c,12)),G=X(X(X(Ds(c,x,d,v),m),E),o[v]),z=X(X(X(Is(y,b,T,v),B),D),r[v]),m=d,d=L(x,9),x=c,c=G,B=T,T=L(b,19),b=y,y=Bs(z)}return q([].concat(c,x,d,m,y,b,T,B),h)}var Es=function(h){const e=typeof h=="string"?Fs(h):Array.prototype.slice.call(h);let r=e.length*8,o=r%512;o=o>=448?512-o%448-1:448-o-1;const u=new Array((o-7)/8);for(let m=0,y=u.length;m<y;m++)u[m]=0;const n=[];r=r.toString(2);for(let m=7;m>=0;m--)if(r.length>8){const y=r.length-8;n[m]=parseInt(r.substr(y),2),r=r.substr(0,y)}else r.length>0?(n[m]=parseInt(r,2),r=""):n[m]=0;const c=[].concat(e,[128],u,n),x=c.length/64;let d=[115,128,22,111,73,20,178,185,23,36,66,215,218,138,6,0,169,111,48,188,22,49,56,170,227,141,238,77,176,251,14,78];for(let m=0;m<x;m++){const y=64*m,b=c.slice(y,y+64);d=As(d,b)}return Ts(d)};const ct=0,Rs=32,rt=16,mt=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],bt=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function Gt(h){const e=[];for(let r=0,o=h.length;r<o;r+=2)e.push(parseInt(h.substr(r,2),16));return e}function qs(h){return h.map(e=>(e=e.toString(16),e.length===1?"0"+e:e)).join("")}function Ms(h){const e=[];for(let r=0,o=h.length;r<o;r++){const u=h.codePointAt(r);if(u<=127)e.push(u);else if(u<=2047)e.push(192|u>>>6),e.push(128|u&63);else if(u<=55295||u>=57344&&u<=65535)e.push(224|u>>>12),e.push(128|u>>>6&63),e.push(128|u&63);else if(u>=65536&&u<=1114111)r++,e.push(240|u>>>18&28),e.push(128|u>>>12&63),e.push(128|u>>>6&63),e.push(128|u&63);else throw e.push(u),new Error("input is not supported")}return e}function Cs(h){const e=[];for(let r=0,o=h.length;r<o;r++)h[r]>=240&&h[r]<=247?(e.push(String.fromCodePoint(((h[r]&7)<<18)+((h[r+1]&63)<<12)+((h[r+2]&63)<<6)+(h[r+3]&63))),r+=3):h[r]>=224&&h[r]<=239?(e.push(String.fromCodePoint(((h[r]&15)<<12)+((h[r+1]&63)<<6)+(h[r+2]&63))),r+=2):h[r]>=192&&h[r]<=223?(e.push(String.fromCodePoint(((h[r]&31)<<6)+(h[r+1]&63))),r++):e.push(String.fromCodePoint(h[r]));return e.join("")}function ot(h,e){return h<<e|h>>>32-e}function Q(h){return(mt[h>>>24&255]&255)<<24|(mt[h>>>16&255]&255)<<16|(mt[h>>>8&255]&255)<<8|mt[h&255]&255}function vt(h){return h^ot(h,2)^ot(h,10)^ot(h,18)^ot(h,24)}function Tt(h){return h^ot(h,13)^ot(h,23)}function Os(h,e,r){const o=new Array(4),u=new Array(4);for(let n=0;n<4;n++)u[0]=h[0+4*n]&255,u[1]=h[1+4*n]&255,u[2]=h[2+4*n]&255,u[3]=h[3+4*n]&255,o[n]=u[0]<<24|u[1]<<16|u[2]<<8|u[3];for(let n=0,c;n<32;n+=4)c=o[1]^o[2]^o[3]^r[n+0],o[0]^=vt(Q(c)),c=o[2]^o[3]^o[0]^r[n+1],o[1]^=vt(Q(c)),c=o[3]^o[0]^o[1]^r[n+2],o[2]^=vt(Q(c)),c=o[0]^o[1]^o[2]^r[n+3],o[3]^=vt(Q(c));for(let n=0;n<16;n+=4)e[n]=o[3-n/4]>>>24&255,e[n+1]=o[3-n/4]>>>16&255,e[n+2]=o[3-n/4]>>>8&255,e[n+3]=o[3-n/4]&255}function ks(h,e,r){const o=new Array(4),u=new Array(4);for(let n=0;n<4;n++)u[0]=h[0+4*n]&255,u[1]=h[1+4*n]&255,u[2]=h[2+4*n]&255,u[3]=h[3+4*n]&255,o[n]=u[0]<<24|u[1]<<16|u[2]<<8|u[3];o[0]^=2746333894,o[1]^=1453994832,o[2]^=1736282519,o[3]^=2993693404;for(let n=0,c;n<32;n+=4)c=o[1]^o[2]^o[3]^bt[n+0],e[n+0]=o[0]^=Tt(Q(c)),c=o[2]^o[3]^o[0]^bt[n+1],e[n+1]=o[1]^=Tt(Q(c)),c=o[3]^o[0]^o[1]^bt[n+2],e[n+2]=o[2]^=Tt(Q(c)),c=o[0]^o[1]^o[2]^bt[n+3],e[n+3]=o[3]^=Tt(Q(c));if(r===ct)for(let n=0,c;n<16;n++)c=e[n],e[n]=e[31-n],e[31-n]=c}function Zt(h,e,r,{padding:o="pkcs#5",mode:u,output:n="string"}={}){if(typeof e=="string"&&(e=Gt(e)),e.length!==128/8)throw new Error("key is invalid");if(typeof h=="string"?r!==ct?h=Ms(h):h=Gt(h):h=[...h],o==="pkcs#5"&&r!==ct){const y=rt-h.length%rt;for(let b=0;b<y;b++)h.push(y)}const c=new Array(Rs);ks(e,c,r);const x=[];let d=h.length,m=0;for(;d>=rt;){const y=h.slice(m,m+16),b=new Array(16);Os(y,b,c);for(let T=0;T<rt;T++)x[m+T]=b[T];d-=rt,m+=rt}if(o==="pkcs#5"&&r===ct){const y=x[x.length-1];x.splice(x.length-y,y)}return n!=="array"?r!==ct?qs(x):Cs(x):x}var Ls={encrypt(h,e,r){return Zt(h,e,1,r)},decrypt(h,e,r){return Zt(h,e,0,r)}},Ps={sm2:vs,sm3:Es,sm4:Ls};const Us=h=>{const e="04CE524881F3EA91B340646199A426D5B70F254606923B7E96E7CBE4CC24514CCA54406ECCE471E73C150C6C9C2741C857DABA453DDC9AFEF7C1AD28530562A779";return Ps.sm2.doEncrypt(h,e,1)};export{Us as g};
