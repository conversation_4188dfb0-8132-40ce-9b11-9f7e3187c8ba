import{dZ as Fo,gF as Wa,gG as Lo,f1 as wo,dY as No,gH as Io,gI as Eo,gJ as <PERSON>,L as Oo,gK as Qo,gL as Za,q as Be,Y as Ro,v as <PERSON>,r as P,w as Re,x as u,y as Ce,z as x,a5 as yt,P as o,D as ve,A as t,H as je,b9 as Vo,J as de,B as D,F as ft,ak as fe,Q as m,ai as be,G as b,a6 as ha,T as Bo,_ as Uo,gM as Yo,K as Ho,dO as qo,m as jo,aG as St,gN as Go,gO as Xa,aT as Xe,f6 as lt,ao as Ye,a_ as et,dq as eo,c2 as Zt,b0 as va,d4 as Rt,ac as Ke,aS as st,o as mt,fn as Ko,aj as we,C as ua,b3 as tt,ab as ct,aF as zo,am as Lt,an as wt,ah as Nt,E as Mt,b1 as aa,c5 as s,gP as Jo,aC as It,aD as Yt,aI as to,af as Vt,ag as Jt,fe as Wo,dD as Zo,X as Xo,R as ue,au as ao,a9 as Xt,a$ as Ia,s as nt,aH as gt,aP as oo,gQ as Ea,gR as Pa,aq as Wt,f9 as Ue,dG as en,ar as Et,gS as tn,aO as $t,aU as Aa,aV as an,al as ra,b7 as Oa,b8 as Qa,aX as on,aY as Da,bO as nn,aZ as sa,fr as rn,av as sn,eV as no,gT as pa,ax as ln,a8 as cn,fF as dn,fs as ga,c3 as un,g8 as pn,ex as io,fb as gn,bo as fn,gU as mn,gV as hn,fd as Ra,at as ro,ew as so,gW as qe,gX as vn,d3 as $a,fB as yn,bV as Ht}from"./index-9381ab2b.js";import{U as xt,m as bn}from"./config-b573cde3.js";import{E as _n}from"./index-d6bcff40.js";import{E as Cn}from"./index-2b7c36a5.js";import{_ as ze}from"./_plugin-vue_export-helper-c27b6911.js";import{E as xa}from"./index-847d31f7.js";import{E as vt}from"./index-c19c3f80.js";import{_ as An}from"./theme-light_empty-0081a108.js";import{g as Ma,C as Sa,c as ea,a as Dn,b as $n}from"./CustomDatePicker-0425400e.js";import{u as la}from"./TicketOriginalPopover.vue_vue_type_script_setup_true_lang-bce4521f.js";import{E as oa,a as lo}from"./index-57a4abc9.js";import{c as xn}from"./_createAggregator-eae1d96b.js";import{C as co}from"./index-a4ffe93f.js";import{E as Dt}from"./index-9683911d.js";import{e as Sn}from"./index-6bc0a2f0.js";import{f as Tn,E as na,a as uo}from"./index-a7943392.js";import{g as Ta,a as po}from"./time-c3069dc1.js";import{aa as kn,a5 as Fn,a8 as Ln,ab as Va,ac as wn}from"./regular-crs-0d781ceb.js";import{E as qt,a as jt}from"./index-951011fc.js";import{E as Nn,a as In,b as En}from"./index-d6fb0de3.js";import{t as ya}from"./throttle-9e041729.js";import{E as go}from"./index-1d08351c.js";import{E as fo}from"./index-34c19038.js";import{E as Pn}from"./index-16cc567f.js";import{u as On}from"./usePersonalization-2a6caa7c.js";import{_ as Qn}from"./Personalization.vue_vue_type_script_setup_true_lang-5fcfa236.js";import{E as Rn}from"./index-dcebdc35.js";import{u as Mn}from"./useTemporaryOrderUtils-89399995.js";import{g as Vn}from"./position-c8cb347a.js";import{E as Bn}from"./index-e22833ad.js";function Un(e,a,i){var r=-1,d=e.length;a<0&&(a=-a>d?0:d+a),i=i>d?d:i,i<0&&(i+=d),d=a>i?0:i-a>>>0,a>>>=0;for(var n=Array(d);++r<d;)n[r]=e[r+a];return n}function Yn(e,a){return a.length<2?e:Fo(e,Un(a,0,-1))}function Hn(e,a){return a=Wa(a,e),e=Yn(e,a),e==null||delete e[Lo(wo(a))]}function qn(e){return Sn(e)?void 0:e}var jn=1,Gn=2,Kn=4,zn=Tn(function(e,a){var i={};if(e==null)return i;var r=!1;a=No(a,function(n){return n=Wa(n,e),r||(r=n.length>1),n}),Io(e,Eo(e),i),r&&(i=Po(i,jn|Gn|Kn,qn));for(var d=a.length;d--;)Hn(i,a[d]);return i});const Jn=zn;var Wn=xn(function(e,a,i){e[i?0:1].push(a)},function(){return[[],[]]});const Pt=Wn,Zn=["light","dark"],Xn=Oo({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:Qo(Za),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:Zn,default:"light"}}),ei={close:e=>e instanceof MouseEvent},ti=Be({name:"ElAlert"}),ai=Be({...ti,props:Xn,emits:ei,setup(e,{emit:a}){const i=e,{Close:r}=Yo,d=Ro(),n=Mo("alert"),l=P(!0),$=Re(()=>Za[i.type]),p=Re(()=>[n.e("icon"),{[n.is("big")]:!!i.description||!!d.default}]),C=Re(()=>({[n.is("bold")]:i.description||d.default})),_=f=>{l.value=!1,a("close",f)};return(f,S)=>(u(),Ce(Bo,{name:t(n).b("fade"),persisted:""},{default:x(()=>[yt(o("div",{class:ve([t(n).b(),t(n).m(f.type),t(n).is("center",f.center),t(n).is(f.effect)]),role:"alert"},[f.showIcon&&t($)?(u(),Ce(t(je),{key:0,class:ve(t(p))},{default:x(()=>[(u(),Ce(Vo(t($))))]),_:1},8,["class"])):de("v-if",!0),o("div",{class:ve(t(n).e("content"))},[f.title||f.$slots.title?(u(),D("span",{key:0,class:ve([t(n).e("title"),t(C)])},[ft(f.$slots,"title",{},()=>[fe(m(f.title),1)])],2)):de("v-if",!0),f.$slots.default||f.description?(u(),D("p",{key:1,class:ve(t(n).e("description"))},[ft(f.$slots,"default",{},()=>[fe(m(f.description),1)])],2)):de("v-if",!0),f.closable?(u(),D(be,{key:2},[f.closeText?(u(),D("div",{key:0,class:ve([t(n).e("close-btn"),t(n).is("customed")]),onClick:_},m(f.closeText),3)):(u(),Ce(t(je),{key:1,class:ve(t(n).e("close-btn")),onClick:_},{default:x(()=>[b(t(r))]),_:1},8,["class"]))],64)):de("v-if",!0)],2)],2),[[ha,l.value]])]),_:3},8,["name"]))}});var oi=Uo(ai,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/alert/src/alert.vue"]]);const ni=Ho(oi),pt="ElInfiniteScroll",ii=50,ri=200,si=0,li={delay:{type:Number,default:ri},distance:{type:Number,default:si},disabled:{type:Boolean,default:!1},immediate:{type:Boolean,default:!0}},ka=(e,a)=>Object.entries(li).reduce((i,[r,d])=>{var n,l;const{type:$,default:p}=d,C=e.getAttribute(`infinite-scroll-${r}`);let _=(l=(n=a[C])!=null?n:C)!=null?l:p;return _=_==="false"?!1:_,_=$(_),i[r]=Number.isNaN(_)?p:_,i},{}),mo=e=>{const{observer:a}=e[pt];a&&(a.disconnect(),delete e[pt].observer)},ci=(e,a)=>{const{container:i,containerEl:r,instance:d,observer:n,lastScrollTop:l}=e[pt],{disabled:$,distance:p}=ka(e,d),{clientHeight:C,scrollHeight:_,scrollTop:f}=r,S=f-l;if(e[pt].lastScrollTop=f,n||$||S<0)return;let I=!1;if(i===e)I=_-(C+f)<=p;else{const{clientTop:L,scrollHeight:E}=e,g=Vn(e,r);I=f+C>=g+L+E-p}I&&a.call(d)};function fa(e,a){const{containerEl:i,instance:r}=e[pt],{disabled:d}=ka(e,r);d||i.clientHeight===0||(i.scrollHeight<=i.clientHeight?a.call(r):mo(e))}const di={async mounted(e,a){const{instance:i,value:r}=a;qo(r)||jo(pt,"'v-infinite-scroll' binding value must be a function"),await St();const{delay:d,immediate:n}=ka(e,i),l=Go(e,!0),$=l===window?document.documentElement:l,p=ya(ci.bind(null,e,r),d);if(l){if(e[pt]={instance:i,container:l,containerEl:$,delay:d,cb:r,onScroll:p,lastScrollTop:$.scrollTop},n){const C=new MutationObserver(ya(fa.bind(null,e,r),ii));e[pt].observer=C,C.observe(e,{childList:!0,subtree:!0}),fa(e,r)}l.addEventListener("scroll",p)}},unmounted(e){const{container:a,onScroll:i}=e[pt];a==null||a.removeEventListener("scroll",i),mo(e)},async updated(e){if(!e[pt])await St();else{const{containerEl:a,cb:i,observer:r}=e[pt];a.clientHeight&&r&&fa(e,i)}}},ba=di;ba.install=e=>{e.directive("InfiniteScroll",ba)};const ui=ba,pi=["ContactHeaderInfo","TableList","TabBars"],gi=["TagBar","Menu"],ho=e=>{const a=localStorage.getItem(xt);if(a){try{const r=JSON.parse(a)??[],d=r.find(n=>n.id===e.id);if(d){const{localData:n,updateTime:l}=e;d.localData=n,d.updateTime=l}else r.push(e);localStorage.setItem(xt,JSON.stringify(r))}catch{console.error("setLocalAttentionData-error"),localStorage.setItem(xt,JSON.stringify([e]))}return}localStorage.setItem(xt,JSON.stringify([e]))},fi=e=>{const a=localStorage.getItem(xt);if(a)try{return(JSON.parse(a)??[]).find(n=>n.id===e)||null}catch{return console.error("getLocalAttentionData-error"),null}return null},mi=e=>{const a=localStorage.getItem(xt);if(a)try{const d=(JSON.parse(a)??[]).filter(n=>n.id!==e);localStorage.setItem(xt,JSON.stringify(d))}catch{console.error("deleteLocalAttentionData-error")}},Gt=async()=>{const{agent:e}=await Xa.getters.user;return`${xt}${e}`},Bt=async(e,a)=>{try{const r={id:await Gt(),localData:JSON.stringify(e),updateTime:a};await ho(r)}catch{console.error("setUserAttention-error")}},hi=async()=>{const e=await Gt();return Xe(await fi(e))},Tt=async()=>{const e=await hi();return e?{localData:JSON.parse(e.localData),updateTime:e.updateTime}:{localData:{orderList:[],orderDetails:[]},updateTime:new Date().getTime()}},Yg=async()=>{const e=await Gt();await mi(e)},vi=async()=>{const{localData:e}=await Tt(),{orderList:a=[]}=e;return a},yi=async()=>(await vi()).some(a=>{var i;return((i=a.messages)==null?void 0:i.length)>0}),Fa=async()=>{const e=await Gt();pi.forEach(a=>{lt(`${e}_${a}`,"CHANGE")})},vo=async()=>{const e=await Gt(),a=await yi();gi.forEach(i=>{lt(`${e}_NOTIFY_${i}`,a)})},Hg=async(e,a)=>{const{localData:i}=await Tt();i.orderList=e??[],await Bt(i,a),await Fa(),await vo()},qg=async(e,a)=>{const{localData:i,updateTime:r}=await Tt();i.orderList??(i.orderList=[]),i.orderList.findIndex(n=>n.pnrNo===e)===-1&&(i.orderList.push({pnrNo:e,time:a,messages:[]}),await Bt(i,r),await Fa())},jg=async e=>{const{localData:a,updateTime:i}=await Tt();a.orderList??(a.orderList=[]);const r=a.orderList.findIndex(d=>d.pnrNo===e);r>-1&&(a.orderList[r].messages=[],await Bt(a,i),await vo())},Gg=async e=>{const{localData:a,updateTime:i}=await Tt();a.orderList??(a.orderList=[]);const r=a.orderList.findIndex(d=>d.pnrNo===e);r>-1&&(a.orderList.splice(r,1),await Bt(a,i),await Fa())},Kg=async e=>{const{localData:a,updateTime:i}=await Tt();a.orderDetails??(a.orderDetails=[]);const r=a.orderDetails.findIndex(d=>d.pnrNo===e.pnrNo);r===-1?(a.orderDetails.push(e),await Bt(a,i)):(a.orderDetails[r]=e,await Bt(a,i))},zg=async(e,a)=>{const i=await Gt();ho({id:i,localData:e,updateTime:a})},Jg=async()=>{const{localData:e}=await Tt(),{orderDetails:a=[]}=e;return a},Wg=async()=>{const{localData:e}=await Tt(),{orderList:a=[],orderDetails:i=[]}=e;return a.filter(r=>i.findIndex(d=>d.pnrNo===r.pnrNo)===-1).map(r=>r.pnrNo)},yo=(e,a)=>Ye(`${et}/flight/queryFlights`,{headers:{gid:a}}).post(e).json(),bi=(e,a)=>Ye(`${et}/flight/queryFlights`,{headers:{gid:a}},{ignoreError:!0}).post(e).json(),_i=(e,a)=>Ye(`${et}/flight/queryMultiInfos`,{headers:{gid:a}}).post(e).json(),Ci=e=>Ye(`${eo}/commonConfiguration/terminalSelectInfo`,{headers:{gid:e}}).get().json(),Ai=e=>Ye(`${eo}/commonConfiguration/querySeamlessOrDa`,{headers:{gid:e}}).get().json(),Zg=(e,a)=>Ye(`${et}/apiAvSearch/airRetrievePor`,{headers:{gid:e}},{onFetchError:a}).post().json(),Xg=(e,a)=>Ye(`${et}/apiAvSearch/airPreOccupy`,{headers:{gid:a}}).post(e).json(),ef=(e,a,i)=>Ye(`${et}/apiAvSearch/airCancelPor`,{headers:{gid:a}},{onFetchError:i}).post(e).json(),tf=e=>Ye(`${et}/apiAvSearch/airIgnorePor`,{headers:{gid:e}}).post().json(),af=(e,a)=>Ye(`${et}/apiAvSearch/airIgnorePor`,{headers:{gid:e}},{onFetchError:a}).post().json(),of=(e,a)=>Ye(`${et}/flight/segment/deleteSegment`,{headers:{gid:a}}).post(e).json(),nf=(e,a)=>Ye(`${et}/flight/segment/modifyStatus`,{headers:{gid:a}}).post(e).json(),rf=(e,a)=>Ye(`${et}/flight/segment/updateSegmentOrder`,{headers:{gid:a}}).post(e).json(),sf=(e,a)=>Ye(`${et}/flight/segment/selectSegment`,{headers:{gid:a}}).post(e).json(),bo=e=>Ye(`${et}/flight/segment/addSegment`).post(e).json(),lf=(e,a)=>Ye(`${et}/flight/segment/unmark`,{headers:{gid:a}}).post(e).json(),Di=(e,a)=>Ye(`${et}/flight/quota`,{headers:{gid:a}}).post(e).json(),cf=(e,a)=>Ye(`${et}/flight/segment/qtbRebook`,{headers:{gid:a}}).post(e).json();var ta=(e=>(e.A="A",e.B="B",e.H="H",e))(ta||{});const $i=(e,a)=>{const i=la(),r="国内",d="国际",n=P(""),l=P(i.historyAirline),$=P({}),p=P({}),C=P([]),_=P([]),f=P(),S=P(!1),I=P(!1),L=P(!1),E=P(Zt()),g=va({}),T=va({}),Y=P([]);let H=!1;const h=P(""),v=P(),c=P(),V=(X,oe)=>{const M=[];if(oe[0]){const U=oe[0].airportCode;oe[0].airportCode=`${X}#${U}`}for(let U=0;U<oe.length;)M.push(oe.slice(U,U+=4));return M},y=X=>{const oe=[];return(X??[]).forEach(M=>{const U=[];M.alphabeticalOrderValue.forEach(ne=>{const Se={};Se.type=ne.alphabet,Se.airPort=V(ne.alphabet,ne.alphabetValue),U.push(Se)});const W={};W.group=M.alphabeticalOrder,W.groupValue=U,oe.push(W)}),oe},q=X=>{const oe=[ta.A,ta.B,ta.H];Y.value=X.map(M=>{const U={...M,type:M.firstLevel===r,airportEnName:M.airportEnName||M.airportCode,airportFullName:"",airPortFormatName:"",cityName:"",cityEnName:M.cityEnName||M.cityCode,searchName:[...M.nameArray,...M.namePYArray].toString(),searchCity:[...M.cityArray,...M.cityPYArray].toString()};return E.value==="en"?(U.cityName=U.cityEnName,U.airportFullName=U.airportEnName===U.airportCode?U.airportCode:`${U.airportEnName}${U.airportCode}`,U.airPortFormatName=U.airportEnName):(U.cityName=U.secondLevel,U.airportFullName=`${U.airportCnName}${U.airportCode}`,U.airPortFormatName=U.airportCnName),U}).sort((M,U)=>{const W=M.firstLevel===r,ne=U.firstLevel===r;if(W&&!ne)return-1;if(!W&&ne)return 1;const Se=M.codeType,ke=U.codeType,Pe=oe.indexOf(Se)===-1?oe.length:oe.indexOf(Se),Ae=oe.indexOf(ke)===-1?oe.length:oe.indexOf(ke);return Pe-Ae})},O=()=>{var oe;const X={};X.data=JSON.parse((oe=v.value)==null?void 0:oe.localData),g.aggregate=y(X.data.domestic),T.aggregate=y(X.data.international)},k=()=>{var M;const X={};X.data=JSON.parse((M=c.value)==null?void 0:M.localData);const oe=Xe(X.data);q(oe)},B=X=>{if(X==="local"){const oe=setInterval(async()=>{v.value=await st("diLocalData"),v.value&&(O(),clearInterval(oe))},5e3)}else{const oe=setInterval(async()=>{c.value=await st("searchLocalData"),c.value&&(k(),clearInterval(oe))},5e3)}},ee=async()=>{if(v.value=await st("diLocalData"),!v.value){B("local");return}O()},le=async()=>{if(c.value=await st("searchLocalData"),!c.value){B("all");return}k()},K=(X,oe)=>((X??[]).some(U=>{const W=E.value==="en"?U.airportEnName===oe.airportEnName:U.airportCnName===oe.airportCnName;return U.airportCode===oe.airportCode&&W})||((X??[]).length===8&&X.shift(),(X??[]).push(oe)),X),J=(X,oe,M,U)=>{var ne,Se;const W={airportCode:X,airportCnName:oe,airportEnName:M,airPortFullName:"",airPortFormatName:""};U?l.value.domesticHistory=K((ne=l.value)==null?void 0:ne.domesticHistory,W):l.value.internationalHistory=K((Se=l.value)==null?void 0:Se.internationalHistory,W),i.setHistoryAirLine(l.value)},Z=(X,oe,M,U,W)=>{n.value=X,a("update:modelValue",X),a("update:name",E.value==="en"?M:oe),a("change",E.value==="en"?M:oe,X,e.segIndex),S.value=!1,f.value=W??""},Q=Rt(async X=>{X.length!==0?(S.value=!1,I.value=!1,L.value=!0,await St(),H=!1,h.value=X,Y.value.some(M=>`${M.airportCnName}${M.airportCode}`===X||`${M.cityName}${M.cityCode}`===X)&&(L.value=!1),S.value=!0):L.value=!1},200),F=Re({get:()=>(n.value=e.modelValue.toLocaleUpperCase(),e.modelValue===e.name?e.modelValue:E.value==="en"&&e.name?`${e.modelValue} ${e.name??""}`:`${e.modelValue}${e.name??""}`),set:X=>{n.value=X.toLocaleUpperCase(),a("update:modelValue",X.toLocaleUpperCase()),a("update:name",""),a("change",X,e.segIndex),Q(X)}}),N=()=>{S.value=!0,I.value=!0,L.value=!1,me()},w=P({}),A=(X,oe)=>!X||(Ko.test(X)?X.length<3:X.length<2)?!1:oe.some(U=>U.airportCode===X||U.cityCode===X||U.airportCnName===X||U.cityName===X?(f.value=U.cityCode===X||U.cityName.includes(X)?"city":"airport",w.value=U,!0):!1),ae=(X,oe,M,U,W)=>{H=!0,J(X,oe,M,U),Z(X,oe,M,U,W)},ge=(X,oe,M,U,W)=>{H=!0,J(X,oe,M,U),Z(X,oe,M,U,W)},he=()=>{if(H)return;const X=e.modelValue.toUpperCase(),oe=A(X,Y.value);let M="",U=X,W="";oe&&(f.value==="airport"?(M=w.value.airportCnName,W=w.value.airportEnName,U=w.value.airportCode):(M=w.value.cityName,W=w.value.cityEnName,U=w.value.cityCode),J(U,M,W,w.value.type)),n.value=U,a("update:name",E.value==="en"?W:M),a("update:modelValue",U),a("change",U,e.segIndex)},pe=X=>{X.keyCode===9&&he()},te=()=>{S.value&&(S.value=!1),L.value=!1,I.value=!1},xe=async()=>{var oe,M;const X=await st("HOT_CITIES");if(X){const U=JSON.parse(X==null?void 0:X.localData);$.value=((oe=U.filter(W=>W.field3===d))==null?void 0:oe[0])??{},p.value=((M=U.filter(W=>W.field3===r))==null?void 0:M[0])??{}}},me=async()=>{const X=await st("COMMON_CITIES");if(!X)return;const oe=JSON.parse(X==null?void 0:X.localData);C.value=oe.domesticCommonCities??[],_.value=oe.internationalCommonCities??[]},Ee=()=>{a("triggerAirBlur")};return Ke(()=>{var X;return(X=Xa.state.user)==null?void 0:X.switchAirline},async()=>{v.value=await st("diLocalData"),O()}),Ke(()=>e.isAshingTransitTerminal,()=>{e.isAshingTransitTerminal&&(I.value=!1)}),mt(async()=>{await ee(),await le(),await Promise.all([xe(),me()])}),{visible:S,hotCitiesOfDomestic:p,hotCitiesOfInter:$,tabShow:I,showInputModel:F,searchShow:L,domesticData:g,internationalData:T,searchAllData:Y,inputValue:h,inputClick:N,keyChange:Q,getPortValue:ae,getSearchValue:ge,clickOutside:te,popoverHide:he,updateCityCnName:pe,airBlurClick:Ee,locale:E,threeCharacterCodeInput:n,comCitiesDomestic:C,comCitiesInter:_}},xi=$i,Si=(e,a)=>{const i=la(),r=P(""),d=P(""),n=P([]),l=P([]),$=Re(()=>{const V=i.historyAirline,y=e.difference?V.domesticHistory:V.internationalHistory;return Xe(y).map(O=>{var k;return{...O,airPortFullName:e.locale==="en"?O.airportEnName||O.airportCode:O.airportCnName||O.airportCode,airPortFormatName:S(O),tooltipDisabled:e.locale==="en"?((k=O.airportEnName)==null?void 0:k.length)<14:O.airportCnName.length<7}})}),p=async V=>{d.value=V.airportCode;const y=V.airportCode,q=y.includes("#")?y.slice(2,y.length):y;a("sendPortValue",q,V.airportCnName,V.airportEnName,e.difference)},C=async V=>{d.value=V.code;const y=V.code,q=y.includes("#")?y.slice(2,y.length):y;a("sendPortValue",q,V.cnName,V.enName,e.difference)},_=V=>{d.value=V.code,a("sendPortValue",V.code,V.city,V.city,e.difference,"city")},f=V=>V.includes("#")?V.slice(2,V.length):V,S=V=>e.locale==="en"?V.airportEnName:V.airportCnName,I=V=>V[0].airportCode.includes("#"),L=P(32),E=P(0),g=P(0),T=P(10),Y=P(0),H=P([]),h=V=>{E.value=V.target.scrollTop,g.value=Math.floor(V.target.scrollTop/L.value),T.value=g.value+10},v=async V=>{if(V.paneName==="hotHistory")return;E.value=0,g.value=0,T.value=10;const y=n.value.filter(O=>O.group===V.paneName),q=[];y[0].groupValue.forEach(O=>{O.airPort.forEach(k=>{q.push(k)})}),H.value=q,Y.value=H.value.length};Ke(()=>e.modelValue,()=>{d.value=e.modelValue.slice(0,3)},{immediate:!0});const c=Re(()=>H.value.slice(g.value,T.value));return mt(async()=>{n.value=e.resData.aggregate,r.value="hotHistory"}),{tabActive:r,isChecked:d,paneData:n,history:$,getRealCode:f,checkContent:p,transformText:S,includeMark:I,submitHotCities:_,itemHeight:L,scrollTop:E,curryDataLength:Y,scroll:h,tabClick:v,visibleList:c,scrollRef:l,checkComCity:C}},Ti=Si,ki={class:"main flex flex-row flex-wrap w-[537px] pt-3"},Fi={key:0,class:"more flex w-full"},Li={class:"h-[22px] leading-[22px] text-red-1 whitespace-nowrap text-xs pr-5"},wi={class:"map flex flex-row flex-wrap"},Ni=["onClick"],Ii={class:"airport-info-box"},Ei={class:"airport-code-box"},Pi={class:"airport-code-text"},Oi={class:"airport-name-text focus:outline-none"},Qi={key:1,class:"more flex w-full"},Ri={class:"h-[22px] leading-[22px] text-red-1 whitespace-nowrap text-xs pr-5"},Mi={class:"map flex flex-row flex-wrap"},Vi=["onClick"],Bi={class:"airport-info-box"},Ui={class:"airport-code-box"},Yi={class:"airport-code-text"},Hi={class:"focus:outline-none"},qi={class:"more flex w-full"},ji={key:0,class:"h-[22px] leading-[22px] text-xs text-red-1 pr-5 whitespace-nowrap"},Gi={class:"map flex flex-row flex-wrap"},Ki=["onClick"],zi={class:"airport-info-box"},Ji={class:"airport-code-box"},Wi={class:"airport-code-text"},Zi={class:"airport-name-text"},Xi={key:0,class:"h-[22px] leading-[22px] text-xs text-red-1 w-2.5 pr-6"},er={key:1,class:"h-[22px] mr-6 leading-4"},tr=["onClick"],ar={class:"airport-info-box"},or={class:"airport-code-box"},nr={class:"airport-code-text"},ir={class:"airport-name-text"},rr=Be({__name:"AgentPort",props:{resData:{},difference:{type:Boolean},hotCities:{},comCities:{},locale:{},modelValue:{}},emits:["sendPortValue"],setup(e,{emit:a}){const i=e,r=a,d=Zt(),{tabActive:n,isChecked:l,paneData:$,history:p,checkContent:C,transformText:_,includeMark:f,submitHotCities:S,getRealCode:I,itemHeight:L,scrollTop:E,curryDataLength:g,scroll:T,tabClick:Y,visibleList:H,scrollRef:h,checkComCity:v}=Ti(i,r);return(c,V)=>(u(),Ce(t(lo),{modelValue:t(n),"onUpdate:modelValue":V[1]||(V[1]=y=>tt(n)?n.value=y:null),type:"border-card",class:"portTabs w-[536px] px-5 box-border",onTabClick:t(Y)},{default:x(()=>[b(t(oa),{label:c.$t("app.fastQuery.headerQuery.hotHistory"),name:"hotHistory",lazy:!0},{default:x(()=>{var y,q;return[o("div",ki,[c.comCities.length>0?(u(),D("div",Fi,[o("span",Li,m(c.$t("app.fastQuery.headerQuery.common")),1),o("div",wi,[(u(!0),D(be,null,we(c.comCities,O=>(u(),D("div",{key:O.code,class:ve(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(l)===O.code}]),onClick:k=>t(v)(O)},[b(t(vt),{effect:"dark",content:t(d)==="en"?O.enName:O.cnName,placement:"top"},{default:x(()=>[o("div",Ii,[o("div",Ei,[o("span",Pi,m(t(I)(O.code)),1)]),o("div",Oi,m(t(d)==="en"?O.enName:O.cnName),1)])]),_:2},1032,["content"])],10,Ni))),128))])])):de("",!0),(y=c.hotCities)!=null&&y.field4?(u(),D("div",Qi,[o("span",Ri,m(c.$t("app.fastQuery.headerQuery.hot")),1),o("div",Mi,[(u(!0),D(be,null,we(JSON.parse(c.hotCities.field4.toString()),O=>(u(),D("div",{key:O.code,class:ve(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(l)===O.code}]),onClick:k=>t(S)(O)},[b(t(vt),{effect:"dark",content:O.city,placement:"top"},{default:x(()=>[o("div",Bi,[o("div",Ui,[o("span",Yi,m(t(I)(O.code)),1)]),o("div",Hi,m(O.city),1)])]),_:2},1032,["content"])],10,Vi))),128))])])):de("",!0),o("div",qi,[((q=t(p))==null?void 0:q.length)!==0?(u(),D("span",ji,m(c.$t("app.fastQuery.headerQuery.history")),1)):de("",!0),o("div",Gi,[(u(!0),D(be,null,we(t(p),O=>(u(),D("div",{key:O.airportCode,class:ve(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(l)===O.airportCode}]),onClick:k=>t(C)(O)},[b(t(vt),{effect:"dark",content:O.airPortFormatName,placement:"top"},{default:x(()=>[o("div",zi,[o("div",Ji,[o("span",Wi,m(t(I)(O.airportCode)),1)]),o("div",Zi,m(O.airPortFormatName),1)])]),_:2},1032,["content"])],10,Ki))),128))])])])]}),_:1},8,["label"]),(u(!0),D(be,null,we(t($),(y,q)=>(u(),Ce(t(oa),{key:y.group,label:y.group,name:y.group,lazy:!0},{default:x(()=>[o("div",{ref_for:!0,ref:O=>{O&&(t(h)[q]=O)},class:"scroll overflow-auto w-full h-[350px]",onScroll:V[0]||(V[0]=(...O)=>t(T)&&t(T)(...O))},[o("div",{class:"overflow-hidden",style:ua({height:(t(g)+1)*t(L)+"px"})},[o("ul",{class:"list-none m-0",style:ua({"margin-top":`${t(E)}px`})},[(u(!0),D(be,null,we(t(H),(O,k)=>{var B;return u(),D("li",{key:k,class:"flex flex-nowrap",style:ua({height:t(L)+"px","margin-top":t(f)(O)?"10px":"0px"})},[t(f)(O)?(u(),D("span",Xi,m((B=O[0])==null?void 0:B.airportCode.slice(0,1)),1)):(u(),D("span",er)),(u(!0),D(be,null,we(O,ee=>(u(),D("div",{key:ee.airportCode,class:ve(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(l)===ee.airportCode}]),onClick:le=>t(C)(ee)},[b(t(vt),{effect:"dark",content:t(_)(ee),placement:"top"},{default:x(()=>[o("div",ar,[o("div",or,[o("span",nr,m(t(I)(ee.airportCode)),1)]),o("div",ir,m(t(_)(ee)),1)])]),_:2},1032,["content"])],10,tr))),128))],4)}),128))],4)],4)],544)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","onTabClick"]))}});const Ba=ze(rr,[["__scopeId","data-v-12f64a04"]]),sr=(e,a)=>{const{t:i}=ct(),r=Re(()=>e.searchData),d=P([]),n=P([]),l=P(),$=P(""),p=P(0),C=P(),_=P(),f=350,S=3,I=24,L=f/I+S,E=P(),g=k=>{const B={};return k.forEach(ee=>{const{firstLevel:le,secondLevel:K,cityCode:J,cityName:Z,country:Q}=ee,F=le+K;B[F]||(B[F]={firstLevel:le,secondLevel:Z,secondLevelFullName:Z===J?J:`${Z}${J}`,country:Q,cityCode:J,list:[]}),B[F].list.push(ee)}),Object.values(B)},T=(k,B,ee,le)=>{const K=e.locale==="en"?(k==null?void 0:k.split(" "))??"":B,J=K==null?void 0:K.map(Z=>Z[0]).join("").toUpperCase().startsWith(ee);return le?J:(k==null?void 0:k.toUpperCase().startsWith(ee))||J},Y=(k,B)=>{if(!B)return[];const ee=B.toUpperCase(),[le,K]=Pt(k,pe=>pe.airportCode.toUpperCase().startsWith(ee)),[J,Z]=Pt(K,pe=>pe.cityCode.toUpperCase().startsWith(ee)),[Q,F]=Pt(Z,pe=>T(pe.cityName,pe.cityPYArray,ee,!0)),[N,w]=Pt(F,pe=>pe.cityName.toUpperCase().startsWith(ee)),[A,ae]=Pt(w,pe=>T(pe.airPortFormatName,pe.namePYArray,ee,!0)),[ge,he]=Pt(ae,pe=>pe.airPortFormatName.toUpperCase().startsWith(ee));return[...new Set([...le,...J,...Q,...N,...A,...ge,...he])]},H=k=>{const B=new Set,ee=new Set,le=new Set,K=k.toUpperCase(),J=r.value;for(let Q=J.length-1;Q>=0;Q--){const F=J[Q].airportCode,N=J[Q].cityCode;if(F.startsWith(K)||e.type!=="1"&&N.startsWith(K))B.add(J[Q]);else{const w=T(J[Q].airPortFormatName,J[Q].namePYArray,K),A=k.length>3&&(J[Q].airportCnName+J[Q].airportCode).includes(k);w||A?ee.add(J[Q]):T(J[Q].cityName,J[Q].cityPYArray,K)&&le.add(J[Q])}}return g(Y([...B,...ee,...le],k))},h=k=>{const B=[],ee=[],le=[];return k.forEach(K=>K.firstLevel==="国际"?ee.push(K):le.push(K)),k.length&&(k[0].firstLevel==="国际"?(ee.length&&B.push({code:"I",list:ee}),le.length&&B.push({code:"D",list:le})):(le.length&&B.push({code:"D",list:le}),ee.length&&B.push({code:"I",list:ee}))),B},v=k=>new Promise(ee=>{const le=h(H(k));ee(le)}),c=k=>{l.value=k.airportCode;const B=k.firstLevel==="国内";a("sendSearchValue",k.airportCode,k.airportCnName,k.airportEnName,B,"airport",k.airportEnName)},V=k=>{l.value=k.cityCode;const B=k.firstLevel==="国内";a("sendSearchValue",k.cityCode,k.secondLevel,k.secondLevel,B,"city")},y=(k,B)=>k.length+B.length>9?`${k.substr(0,6)}...${B}`:k+B,q=k=>{const le=Math.floor((k||0)/I)+L;let K=0,J=-1;const Z=n.value.findIndex((F,N)=>(J=F.list.findIndex((w,A)=>{const ae=K+w.list.length;return le<ae&&!(le<K)||N===n.value.length-1&&A===F.list.length-1&&!(ae>le)?!0:(K=ae,!1)}),J>-1)),Q=[];if(Z===-1){d.value=Q;return}n.value.slice(0,Z+1).forEach((F,N)=>{if(N>Z)return;if(N<Z){Q.push(F);return}const w=F.list.slice(0,J+1);Q.push({...F,list:w})}),(Q??[]).forEach(F=>{((F==null?void 0:F.list)??[]).forEach(N=>{((N==null?void 0:N.list)??[]).sort((w,A)=>{const ae=["A","B","H"],ge=ae.indexOf(w.codeType),he=ae.indexOf(A.codeType);return ge===he?0:ge-he})})}),d.value=Q},O=k=>{C.value||(C.value=!0,requestAnimationFrame(()=>{C.value=!1}),q(k.target.scrollTop))};return zo(async()=>{$.value=i("app.fastQuery.headerQuery.searching"),E.value=!0,n.value=await v(e.input),await q(),E.value=!1,d.value.length<=0&&($.value=i("app.fastQuery.headerQuery.noAirportsFound"))}),{allData:r,currySearchRes:d,isChecked:l,text:$,checkMemory:c,transformText:y,containerRef:_,handleScroll:O,totalHeight:p,searching:E,checkCity:V}},lr=sr,cr=e=>(Lt("data-v-70c79d4c"),e=e(),wt(),e),dr={class:"type-text-box"},ur={class:"text-name"},pr=cr(()=>o("div",{class:"name-line"},null,-1)),gr={class:"flex cursor-pointer"},fr=["textContent"],mr={key:0,class:"list"},hr={class:"airport-info-box"},vr={class:"airport-code-box"},yr={class:"airport-code-text"},br={class:"airport-name-text"},_r={key:1,class:"list"},Cr={class:"airport-info-box hover:bg-brand-7 hover:text-[var(--bkc-el-color-primary)]"},Ar={class:"airport-code-box"},Dr={class:"airport-code-text"},$r=["onClick"],xr={key:2,class:"list"},Sr=["onClick"],Tr={class:"airport-info-box hover:bg-brand-7 hover:text-[var(--bkc-el-color-primary)]"},kr={class:"airport-code-box"},Fr={class:"airport-code-text"},Lr={class:"w-[248px] shadow bg-gray-0 rounded-sm border border-gray-6 text-gray-6"},wr={class:"h-[22px] leading-[22px] inline-block my-2 pl-2 w-[240px] text-xs bg-gray-7 text-gray-2"},Nr=Be({__name:"AgentSearch",props:{type:{},input:{},searchData:{},locale:{}},emits:["sendSearchValue"],setup(e,{emit:a}){const i=e,r=a,{currySearchRes:d,isChecked:n,text:l,checkMemory:$,handleScroll:p,containerRef:C,searching:_,checkCity:f}=lr(i,r);return(S,I)=>(u(),D(be,null,[yt(o("div",{class:ve(["agent-search",S.locale==="en"?"en-agent-search":""]),onScroll:I[0]||(I[0]=(...L)=>t(p)&&t(p)(...L))},[o("div",null,[o("div",{ref_key:"containerRef",ref:C},[(u(!0),D(be,null,we(t(d),(L,E)=>(u(),D("div",{key:E,class:"panel"},[o("div",dr,[o("div",ur,m(L.code==="I"?S.$t("app.avSearch.Intl"):S.$t("app.avSearch.Dom")),1),pr]),(u(!0),D(be,null,we(L.list,(g,T)=>(u(),D("div",{key:T},[o("div",gr,[o("span",{class:"country-code-text",textContent:m(g.country)},null,8,fr),S.type===0?(u(),D("div",mr,[b(t(vt),{effect:"dark",content:g.secondLevel,placement:"top"},{default:x(()=>[o("div",hr,[o("div",vr,[o("div",yr,m(g.cityCode.includes("#")?g.cityCode.slice(2,g.cityCode.length):g.cityCode),1)]),o("div",br,m(g.secondLevel),1)])]),_:2},1032,["content"])])):(u(),D("div",_r,[b(t(vt),{effect:"dark",content:g.secondLevel,placement:"top"},{default:x(()=>[o("div",Cr,[o("div",Ar,[o("div",Dr,m(g.cityCode.includes("#")?g.cityCode.slice(2,g.cityCode.length):g.cityCode),1)]),o("div",{class:ve(["airport-name-text",t(n)===g.cityCode?"bg-brand-7 text-[var(--bkc-el-color-primary)]":""]),onClick:Y=>t(f)(g)},m(g.secondLevel),11,$r)])]),_:2},1032,["content"])])),S.type!==1?(u(),D("div",xr,[(u(!0),D(be,null,we(g.list,(Y,H)=>(u(),D("div",{key:H,class:ve(["content text-ellipsis",{"bg-brand-7 text-[var(--bkc-el-color-primary)]":t(n)===Y.airportCode}]),onClick:h=>t($)(Y)},[b(t(vt),{effect:"dark",content:Y.airPortFormatName,placement:"top"},{default:x(()=>[o("div",Tr,[o("div",kr,[o("div",Fr,m(Y.airportCode.includes("#")?Y.cityCode.slice(2,Y.airportCode.length):Y.airportCode),1)]),o("div",{class:ve(["airport-name-text",t(n)===Y.airportCode?"bg-brand-7 text-[var(--bkc-el-color-primary)]":""])},m(Y.airPortFormatName),3)])]),_:2},1032,["content"])],10,Sr))),128))])):de("",!0)])]))),128))]))),128))],512)])],34),[[ha,!t(_)&&t(d).length>0]]),yt(o("div",Lr,[o("span",wr,m(t(l)),1)],512),[[ha,t(_)||!(t(d).length>0)]])],64))}});const _o=ze(Nr,[["__scopeId","data-v-70c79d4c"]]),Ir={key:0,class:"svg-icon iconfont icon-nav-booking"},Er={key:1,class:"svg-icon iconfont icon-booking-to"},Pr={key:0},Or=Be({__name:"AgentAirportContainer",props:{position:{},prefixTitle:{},type:{},modelValue:{},name:{},segIndex:{},isAshingTransitTerminal:{type:Boolean},isAgentCity:{}},emits:["update:modelValue","update:name","change","triggerAirBlur"],setup(e,{emit:a}){const i=e,r=a,{tabShow:d,searchShow:n,domesticData:l,internationalData:$,searchAllData:p,inputValue:C,locale:_,inputClick:f,getPortValue:S,getSearchValue:I,showInputModel:L,clickOutside:E,visible:g,hotCitiesOfDomestic:T,hotCitiesOfInter:Y,popoverHide:H,airBlurClick:h,threeCharacterCodeInput:v,comCitiesDomestic:c,comCitiesInter:V}=xi(i,r);return(y,q)=>(u(),Ce(t(Dt),{visible:t(g),"onUpdate:visible":q[3]||(q[3]=O=>tt(g)?g.value=O:null),trigger:"click",placement:y.position,"popper-class":"airportClass","show-arrow":!1,onHide:t(H)},{reference:x(()=>[b(t(Nt),{modelValue:t(L),"onUpdate:modelValue":q[0]||(q[0]=O=>tt(L)?L.value=O:null),disabled:y.isAshingTransitTerminal,placeholder:y.prefixTitle,clearable:"",class:"middlePosition",onClick:Mt(t(f),["stop"]),onKeydown:aa(t(E),["tab"]),onKeyup:aa(t(f),["tab"]),onBlur:t(h)},{prefix:x(()=>[y.type!==""?(u(),D(be,{key:0},[y.type==="takeOff"?(u(),D("em",Ir)):de("",!0),y.type==="arrive"?(u(),D("em",Er)):de("",!0)],64)):ft(y.$slots,"default",{key:1},void 0,!0)]),_:3},8,["modelValue","disabled","placeholder","onClick","onKeydown","onKeyup","onBlur"])]),default:x(()=>[t(d)||t(n)?(u(),D("div",Pr,[t(d)?(u(),Ce(t(lo),{key:0,type:"card",class:"diTabs"},{default:x(()=>[b(t(oa),{label:y.$t("app.fastQuery.headerQuery.domestic")},{default:x(()=>[b(Ba,{modelValue:t(v),"onUpdate:modelValue":q[1]||(q[1]=O=>tt(v)?v.value=O:null),"res-data":t(l),"hot-cities":t(T),"com-cities":t(c),difference:!0,locale:t(_),onSendPortValue:t(S)},null,8,["modelValue","res-data","hot-cities","com-cities","locale","onSendPortValue"])]),_:1},8,["label"]),b(t(oa),{label:y.$t("app.fastQuery.headerQuery.International")},{default:x(()=>[b(Ba,{modelValue:t(v),"onUpdate:modelValue":q[2]||(q[2]=O=>tt(v)?v.value=O:null),"res-data":t($),"hot-cities":t(Y),"com-cities":t(V),difference:!1,locale:t(_),onSendPortValue:t(S)},null,8,["modelValue","res-data","hot-cities","com-cities","locale","onSendPortValue"])]),_:1},8,["label"])]),_:1})):de("",!0),t(n)?yt((u(),Ce(_o,{key:1,type:y.isAgentCity,"search-data":t(p),input:t(C),locale:t(_),onSendSearchValue:t(I)},null,8,["type","search-data","input","locale","onSendSearchValue"])),[[t(co),t(E)]]):de("",!0)])):de("",!0)]),_:3},8,["visible","placement","onHide"]))}});const Ut=ze(Or,[["__scopeId","data-v-4b22c934"]]),Qr={class:"transit-terminal-airport-box custom-focus-tip-input"},Rr=o("div",{class:"hidden bkc-el-form-item__error"},"A/B/..",-1),Mr={class:"airport-box"},Vr=Be({__name:"TransitTerminalAirport",props:{modelValue:{default:""},isAgentCity:{}},emits:["update:modelValue"],setup(e,{emit:a}){const i=e,r=a,d=P(Zt()),n=P(!1),l=P(),$=P(!1),p=P([]),C=P(""),_=Re({get:()=>i.modelValue.toUpperCase(),set:h=>{n.value=!!h,L(h)}}),f=()=>{L(_.value)},S=h=>{r("update:modelValue",h),$.value=!h},I=h=>{var ee,le,K;const v=(ee=l.value.input)!=null&&ee.selectionStart?l.value.input.selectionStart-1:-1,c=h.split(""),V=h.slice(0,v+1),y=h.slice(v+1,h.length);let q=-1,O=-1;if((le=c[v])!=null&&le.includes("/")&&(q=v+1),(K=c[v+1])!=null&&K.includes("/")&&(O=v+1),q===-1){const J=V.lastIndexOf("/");J>-1&&(q=J+1)}if(O===-1){const J=y.indexOf("/");J>-1&&(O=J+V.length)}const k=q===-1?0:q,B=O===-1?h.length:O;return{start:k,end:B}},L=h=>{if(S(h),!h)return;const v=h==null?void 0:h.toUpperCase(),{start:c,end:V}=I(h);C.value=v.slice(c,V),n.value=!!C.value},E=h=>{p.value=h.map(v=>{const c={...v,type:v.firstLevel==="国内",airportEnName:v.airportEnName||v.airportCode,airportFullName:"",airPortFormatName:"",cityName:"",cityEnName:v.cityEnName||v.cityCode,searchName:[...v.nameArray,...v.namePYArray].toString(),searchCity:[...v.cityArray,...v.cityPYArray].toString()};return d.value==="en"?(c.cityName=c.cityEnName,c.airportFullName=c.airportEnName===c.airportCode?c.airportCode:`${c.airportEnName}${c.airportCode}`,c.airPortFormatName=c.airportEnName):(c.cityName=c.secondLevel,c.airportFullName=`${c.airportCnName}${c.airportCode}`,c.airPortFormatName=c.airportCnName),c}).sort((v,c)=>+(v.firstLevel==="国内")-+(c.firstLevel==="国内"))},g=async()=>{if(p.value.length)return;const h=await st("searchLocalData");if(h&&(h!=null&&h.localData)){const v=JSON.parse(h==null?void 0:h.localData);E(v)}},T=()=>{n.value=!1},Y=h=>{const{start:v,end:c}=I(_.value),V=_.value.slice(0,v),y=_.value.slice(c);S(`${V}${h}${y}`),T()},H=async h=>{[37,38,39,40].includes(h.keyCode)&&(await St(),L(_.value))};return mt(()=>{g()}),(h,v)=>{const c=Nt,V=Dt;return u(),D("div",Qr,[b(V,{visible:n.value,persistent:!1,trigger:"click","popper-class":"transit-terminal-airport-popover","show-arrow":!1,width:"248"},{reference:x(()=>[o("div",{class:ve(["input-focus-tip",$.value?"input-focus":""])},[b(c,{ref_key:"inputRef",ref:l,modelValue:_.value,"onUpdate:modelValue":v[0]||(v[0]=y=>_.value=y),placeholder:h.$t("app.avSearch.transitTerminal"),onKeyup:H,onBlur:v[1]||(v[1]=y=>$.value=!1),onClick:Mt(f,["stop"])},null,8,["modelValue","placeholder"]),Rr],2)]),default:x(()=>[yt((u(),D("div",Mr,[b(_o,{locale:d.value,type:h.isAgentCity,"search-data":p.value,input:C.value,onSendSearchValue:Y},null,8,["locale","type","search-data","input"])])),[[t(co),T]])]),_:1},8,["visible"])])}}});const ma=[{code:"Y8",icon:"icon-y8-c",logo:"#icon-y8-c",name:s.global.t("app.airlineList.JinpengAirlines")},{code:"TW",icon:"icon-tw-c",logo:"#icon-tw-c",name:s.global.t("app.airlineList.DulwichAir")},{code:"7C",icon:"icon-7c-c",logo:"#icon-7c-c",name:s.global.t("app.airlineList.JejuAir")},{code:"EK",icon:"icon-ek-c",logo:"#icon-ek-c",name:s.global.t("app.airlineList.Emirates")},{code:"EN",icon:"icon-en-c",logo:"#icon-en-c",name:s.global.t("app.airlineList.AirDolomites")},{code:"ET",icon:"icon-et-c",logo:"#icon-et-c",name:s.global.t("app.airlineList.EthiopianAirlines")},{code:"EU",icon:"icon-eu-c",logo:"#icon-eu-c",name:s.global.t("app.airlineList.ChengduAirlines")},{code:"EY",icon:"icon-ey-c",logo:"#icon-ey-c",name:s.global.t("app.airlineList.EtihadAirways")},{code:"F7",icon:"icon-f7-c",logo:"#icon-f7-c",name:s.global.t("app.airlineList.AirFrye")},{code:"F9",icon:"icon-f9-c",logo:"#icon-f9-c",name:s.global.t("app.airlineList.FrontierAirlines")},{code:"FB",icon:"icon-fb-c",logo:"#icon-fb-c",name:s.global.t("app.airlineList.BulgarianAirlines")},{code:"FE",icon:"icon-fe-c",logo:"#icon-fe-c",name:s.global.t("app.airlineList.FarEastAirlines")},{code:"FI",icon:"icon-fi-c",logo:"#icon-fi-c",name:s.global.t("app.airlineList.Icelandair")},{code:"FJ",icon:"icon-fj-c",logo:"#icon-fj-c",name:s.global.t("app.airlineList.PacificAirlines")},{code:"FM",icon:"icon-fm-c",logo:"#icon-fm-c",name:s.global.t("app.airlineList.ShanghaiAirlines")},{code:"FQ",icon:"icon-fq-c",logo:"#icon-fq-c",name:s.global.t("app.airlineList.BrindabellaAir")},{code:"FV",icon:"icon-fv-c",logo:"#icon-fv-c",name:s.global.t("app.airlineList.RussianNationalAviation")},{code:"G3",icon:"icon-g3-c",logo:"#icon-g3-c",name:s.global.t("app.airlineList.GowerTransportAir")},{code:"G5",icon:"icon-g5-c",logo:"#icon-g5-c",name:s.global.t("app.airlineList.HuaAirlines")},{code:"GA",icon:"icon-ga-c",logo:"#icon-ga-c",name:s.global.t("app.airlineList.GarudaIndonesia")},{code:"GE",icon:"icon-ge-c",logo:"#icon-ge-c",name:s.global.t("app.airlineList.TransasiaAirways")},{code:"GF",icon:"icon-gf-c",logo:"#icon-gf-c",name:s.global.t("app.airlineList.GulfAir")},{code:"GJ",icon:"icon-gj-c",logo:"#icon-gj-c",name:s.global.t("app.airlineList.YoloAir")},{code:"GR",icon:"icon-gr-c",logo:"#icon-gr-c",name:s.global.t("app.airlineList.AirOrigny")},{code:"GS",icon:"icon-gs-c",logo:"#icon-gs-c",name:s.global.t("app.airlineList.TianjinAirlines")},{code:"HA",icon:"icon-ha-c",logo:"#icon-ha-c",name:s.global.t("app.airlineList.HawaiianAirlines")},{code:"HF",icon:"icon-hf-c",logo:"#icon-hf-c",name:s.global.t("app.airlineList.LloydAir")},{code:"HM",icon:"icon-hm-c",logo:"#icon-hm-c",name:s.global.t("app.airlineList.AirSeychelles")},{code:"HO",icon:"icon-ho-c",logo:"#icon-ho-c",name:s.global.t("app.airlineList.JuneyaoAirlines")},{code:"HU",icon:"icon-hu-c",logo:"#icon-hu-c",name:s.global.t("app.airlineList.HainanAirlines")},{code:"HX",icon:"icon-hx-c",logo:"#icon-hx-c",name:s.global.t("app.airlineList.HongKongAirlines")},{code:"HY",icon:"icon-hy-c",logo:"#icon-hy-c",name:s.global.t("app.airlineList.UzbekistanAirways")},{code:"HZ",icon:"icon-hz-c",logo:"#icon-hz-c",name:s.global.t("app.airlineList.SakhalinAir")},{code:"IB",icon:"icon-ib-c",logo:"#icon-ib-c",name:s.global.t("app.airlineList.Spainair")},{code:"IC",icon:"icon-ic-c",logo:"#icon-ic-c",name:s.global.t("app.airlineList.AirIndia")},{code:"IE",icon:"icon-ie-c",logo:"#icon-ie-c",name:s.global.t("app.airlineList.SolomonAirlines")},{code:"IG",icon:"icon-ig-c",logo:"#icon-ig-c",name:s.global.t("app.airlineList.AirMeridian")},{code:"IR",icon:"icon-ir-c",logo:"#icon-ir-c",name:s.global.t("app.airlineList.IranAir")},{code:"IT",icon:"icon-it-c",logo:"#icon-it-c",name:s.global.t("app.airlineList.KingfisherAirlines")},{code:"IY",icon:"icon-iy-c",logo:"#icon-iy-c",name:s.global.t("app.airlineList.YemeniaAirways")},{code:"IZ",icon:"icon-iz-c",logo:"#icon-iz-c",name:s.global.t("app.airlineList.Ehang")},{code:"J2",icon:"icon-j2-c",logo:"#icon-j2-c",name:s.global.t("app.airlineList.AzerbaijanAirlines")},{code:"JD",icon:"icon-jd-c",logo:"#icon-jd-c",name:s.global.t("app.airlineList.CapitalAirlines")},{code:"JH",icon:"icon-jh-c",logo:"#icon-jh-c",name:s.global.t("app.airlineList.NortheastAirlines")},{code:"JJ",icon:"icon-jj-c",logo:"#icon-jj-c",name:s.global.t("app.airlineList.SouthAmericanAirlines")},{code:"JK",icon:"icon-jk-c",logo:"#icon-jk-c",name:s.global.t("app.airlineList.Spanair")},{code:"JL",icon:"icon-jl-c",logo:"#icon-jl-c",name:s.global.t("app.airlineList.JapanAirlines")},{code:"JP",icon:"icon-jp-c",logo:"#icon-jp-c",name:s.global.t("app.airlineList.AdriaticAir")},{code:"JQ",icon:"icon-jq-c",logo:"#icon-jq-c",name:s.global.t("app.airlineList.JetstarAirways")},{code:"JR",icon:"icon-jr-c",logo:"#icon-jr-c",name:s.global.t("app.airlineList.HappyAir")},{code:"JS",icon:"icon-js-c",logo:"#icon-js-c",name:s.global.t("app.airlineList.Headingforthesea")},{code:"JU",icon:"icon-ju-c",logo:"#icon-ju-c",name:s.global.t("app.airlineList.YugoslavAirlines")},{code:"K6",icon:"icon-k6-c",logo:"#icon-k6-c",name:s.global.t("app.airlineList.AngkorAir")},{code:"KA",icon:"icon-ka-c",logo:"#icon-ka-c",name:s.global.t("app.airlineList.Dragonair")},{code:"KC",icon:"icon-kc-c",logo:"#icon-kc-c",name:s.global.t("app.airlineList.KazakhAirlines")},{code:"KE",icon:"icon-ke-c",logo:"#icon-ke-c",name:s.global.t("app.airlineList.KoreanAir")},{code:"KF",icon:"icon-kf-c",logo:"#icon-kf-c",name:s.global.t("app.airlineList.PortniaBlueFirstAir")},{code:"KK",icon:"icon-kk-c",logo:"#icon-kk-c",name:s.global.t("app.airlineList.AtlasAir")},{code:"KL",icon:"icon-kl-c",logo:"#icon-kl-c",name:s.global.t("app.airlineList.KLM")},{code:"KM",icon:"icon-km-c",logo:"#icon-km-c",name:s.global.t("app.airlineList.AirMalta")},{code:"KN",icon:"icon-kn-c",logo:"#icon-kn-c",name:s.global.t("app.airlineList.ChinaUnitedAirlines")},{code:"KP",icon:"icon-kp-c",logo:"#icon-kp-c",name:s.global.t("app.airlineList.CapeAir")},{code:"KQ",icon:"icon-kq-c",logo:"#icon-kq-c",name:s.global.t("app.airlineList.KenyaAirways")},{code:"KS",icon:"icon-ks-c",logo:"#icon-ks-c",name:s.global.t("app.airlineList.PencilAviation")},{code:"KU",icon:"icon-ku-c",logo:"#icon-ku-c",name:s.global.t("app.airlineList.KuwaitAirways")},{code:"KY",icon:"icon-ky-c",logo:"#icon-ky-c",name:s.global.t("app.airlineList.KunmingAirlines")},{code:"LA",icon:"icon-la-c",logo:"#icon-la-c",name:s.global.t("app.airlineList.AirChile")},{code:"LG",icon:"icon-lg-c",logo:"#icon-lg-c",name:s.global.t("app.airlineList.LuxembourgAir")},{code:"LH",icon:"icon-lh-c",logo:"#icon-lh-c",name:s.global.t("app.airlineList.Lufthansa")},{code:"LI",icon:"icon-li-c",logo:"#icon-li-c",name:s.global.t("app.airlineList.LeewardAviation")},{code:"LN",icon:"icon-ln-c",logo:"#icon-ln-c",name:s.global.t("app.airlineList.LibyanAirlines")},{code:"LO",icon:"icon-lo-c",logo:"#icon-lo-c",name:s.global.t("app.airlineList.WaveNavigation")},{code:"LP",icon:"icon-lp-c",logo:"#icon-lp-c",name:s.global.t("app.airlineList.PeruvianAirlines")},{code:"LR",icon:"icon-lr-c",logo:"#icon-lr-c",name:s.global.t("app.airlineList.LascaAir")},{code:"9W",icon:"icon-9w-c",logo:"#icon-9w-c",name:s.global.t("app.airlineList.JetAirways")},{code:"A3",icon:"icon-a3-c",logo:"#icon-a3-c",name:s.global.t("app.airlineList.AegeanAir")},{code:"A5",icon:"icon-a5-c",logo:"#icon-a5-c",name:s.global.t("app.airlineList.LinelAir")},{code:"A9",icon:"icon-a9-c",logo:"#icon-a9-c",name:s.global.t("app.airlineList.AirGeorgia")},{code:"AA",icon:"icon-aa-c",logo:"#icon-aa-c",name:s.global.t("app.airlineList.AmericanAirlines")},{code:"AB",icon:"icon-ab-c",logo:"#icon-ab-c",name:s.global.t("app.airlineList.AirBerlin")},{code:"AC",icon:"icon-ac-c",logo:"#icon-ac-c",name:s.global.t("app.airlineList.AirCanada")},{code:"AD",icon:"icon-ad-c",logo:"#icon-ad-c",name:s.global.t("app.airlineList.IndonesianParadiseAirlines")},{code:"AE",icon:"icon-ae-c",logo:"#icon-ae-c",name:s.global.t("app.airlineList.CefcAirlines")},{code:"AF",icon:"icon-af-c",logo:"#icon-af-c",name:s.global.t("app.airlineList.AirFrance")},{code:"AH",icon:"icon-ah-c",logo:"#icon-ah-c",name:s.global.t("app.airlineList.AirAlgerie")},{code:"AI",icon:"icon-ai-c",logo:"#icon-ai-c",name:s.global.t("app.airlineList.IndiaAir")},{code:"AM",icon:"icon-am-c",logo:"#icon-am-c",name:s.global.t("app.airlineList.AirMexico")},{code:"AP",icon:"icon-ap-c",logo:"#icon-ap-c",name:s.global.t("app.airlineList.AirOne")},{code:"AR",icon:"icon-ar-c",logo:"#icon-ar-c",name:s.global.t("app.airlineList.AerolineasArgentinas")},{code:"AS",icon:"icon-as-c",logo:"#icon-as-c",name:s.global.t("app.airlineList.AlaskaAirlines")},{code:"AT",icon:"icon-at-c",logo:"#icon-at-c",name:s.global.t("app.airlineList.AirMaroc")},{code:"AV",icon:"icon-av-c",logo:"#icon-av-c",name:s.global.t("app.airlineList.ColumbiaAirlines")},{code:"AW",icon:"icon-aw-c",logo:"#icon-aw-c",name:s.global.t("app.airlineList.DecantaraAir")},{code:"AY",icon:"icon-ay-c",logo:"#icon-ay-c",name:s.global.t("app.airlineList.Finnair")},{code:"AZ",icon:"icon-az-c",logo:"#icon-az-c",name:s.global.t("app.airlineList.Alitalia")},{code:"B2",icon:"icon-b2-c",logo:"#icon-b2-c",name:s.global.t("app.airlineList.BelarusianAirlines")},{code:"B6",icon:"icon-b6-c",logo:"#icon-b6-c",name:s.global.t("app.airlineList.JetblueAirways")},{code:"B7",icon:"icon-b7-c",logo:"#icon-b7-c",name:s.global.t("app.airlineList.LirongAir")},{code:"BA",icon:"icon-ba-c",logo:"#icon-ba-c",name:s.global.t("app.airlineList.BritishAirways")},{code:"BD",icon:"icon-bd-c",logo:"#icon-bd-c",name:s.global.t("app.airlineList.MidlandAirlines")},{code:"BE",icon:"icon-be-c",logo:"#icon-be-c",name:s.global.t("app.airlineList.FlybyAir")},{code:"BG",icon:"icon-bg-c",logo:"#icon-bg-c",name:s.global.t("app.airlineList.BanglaAirlines")},{code:"BI",icon:"icon-bi-c",logo:"#icon-bi-c",name:s.global.t("app.airlineList.BruneiAirlines")},{code:"BK",icon:"icon-bk-c",logo:"#icon-bk-c",name:s.global.t("app.airlineList.OkAirways")},{code:"BL",icon:"icon-bl-c",logo:"#icon-bl-c",name:s.global.t("app.airlineList.PacificAirlines")},{code:"BM",icon:"icon-bm-c",logo:"#icon-bm-c",name:s.global.t("app.airlineList.PrayuthAirIndonesia")},{code:"BP",icon:"icon-bp-c",logo:"#icon-bp-c",name:s.global.t("app.airlineList.AirBotswana")},{code:"BR",icon:"icon-br-c",logo:"#icon-br-c",name:s.global.t("app.airlineList.EvaAir")},{code:"BT",icon:"icon-bt-c",logo:"#icon-bt-c",name:s.global.t("app.airlineList.AirBaltic")},{code:"BU",icon:"icon-bu-c",logo:"#icon-bu-c",name:s.global.t("app.airlineList.BrassensAir")},{code:"BV",icon:"icon-bv-c",logo:"#icon-bv-c",name:s.global.t("app.airlineList.BluePanoramaAviation")},{code:"BW",icon:"icon-bw-c",logo:"#icon-bw-c",name:s.global.t("app.airlineList.Tedosi")},{code:"BX",icon:"icon-bx-c",logo:"#icon-bx-c",name:s.global.t("app.airlineList.OceanfrontAir")},{code:"CA",icon:"icon-ca-c",logo:"#icon-ca-c",name:s.global.t("app.airlineList.AirChina")},{code:"CI",icon:"icon-ci-c",logo:"#icon-ci-c",name:s.global.t("app.airlineList.ChinaAirlines")},{code:"CM",icon:"icon-cm-c",logo:"#icon-cm-c",name:s.global.t("app.airlineList.PanamaAirlift")},{code:"CN",icon:"icon-cn-c",logo:"#icon-cn-c",name:s.global.t("app.airlineList.GrandChinaAirlines")},{code:"CU",icon:"icon-cu-c",logo:"#icon-cu-c",name:s.global.t("app.airlineList.AirCubana")},{code:"CX",icon:"icon-cx-c",logo:"#icon-cx-c",name:s.global.t("app.airlineList.CathayPacific")},{code:"CY",icon:"icon-cy-c",logo:"#icon-cy-c",name:s.global.t("app.airlineList.CyprusAirways")},{code:"CZ",icon:"icon-cz-c",logo:"#icon-cz-c",name:s.global.t("app.airlineList.ChinaSouthernAirlines")},{code:"DC",icon:"icon-dc-c",logo:"#icon-dc-c",name:s.global.t("app.airlineList.GoldenAir")},{code:"DE",icon:"icon-de-c",logo:"#icon-de-c",name:s.global.t("app.airlineList.CondorAir")},{code:"DJ",icon:"icon-dj-c",logo:"#icon-dj-c",name:s.global.t("app.airlineList.VirginAirlines")},{code:"DL",icon:"icon-dl-c",logo:"#icon-dl-c",name:s.global.t("app.airlineList.DeltaAirLines")},{code:"DN",icon:"icon-dn-c",logo:"#icon-dn-c",name:s.global.t("app.airlineList.AirDeccan")},{code:"DR",icon:"icon-dr-c",logo:"#icon-dr-c",name:s.global.t("app.airlineList.LinkAir")},{code:"DT",icon:"icon-dt-c",logo:"#icon-dt-c",name:s.global.t("app.airlineList.AirAngola")},{code:"DY",icon:"icon-dy-c",logo:"#icon-dy-c",name:s.global.t("app.airlineList.NorwegianAir")},{code:"DZ",icon:"icon-dz-c",logo:"#icon-dz-c",name:s.global.t("app.airlineList.DonghaiAirlines")},{code:"E3",icon:"icon-e3-c",logo:"#icon-e3-c",name:s.global.t("app.airlineList.DomodedovoAir")},{code:"EF",icon:"icon-ef-c",logo:"#icon-ef-c",name:s.global.t("app.airlineList.FarEastAir")},{code:"EI",icon:"icon-ei-c",logo:"#icon-ei-c",name:s.global.t("app.airlineList.AerLingus")},{code:"2P",icon:"icon-2p-c",logo:"#icon-2p-c",name:s.global.t("app.airlineList.AirPhilippine")},{code:"3K",icon:"icon-3k-c",logo:"#icon-3k-c",name:s.global.t("app.airlineList.JetstarAirways")},{code:"4D",icon:"icon-4d-c",logo:"#icon-4d-c",name:s.global.t("app.airlineList.SinaiAir")},{code:"4U",icon:"icon-4u-c",logo:"#icon-4u-c",name:s.global.t("app.airlineList.Germanwings")},{code:"5L",icon:"icon-5l-c",logo:"#icon-5l-c",name:s.global.t("app.airlineList.AlsoAir")},{code:"7B",icon:"icon-7b-c",logo:"#icon-7b-c",name:s.global.t("app.airlineList.KrasnoyarskAir")},{code:"8C",icon:"icon-8c-c",logo:"#icon-8c-c",name:s.global.t("app.airlineList.EastStarAirlines")},{code:"8L",icon:"icon-8l-c",logo:"#icon-8l-c",name:s.global.t("app.airlineList.LuckyAir")},{code:"8M",icon:"icon-8m-c",logo:"#icon-8m-c",name:s.global.t("app.airlineList.MyanmarAirlines")},{code:"8U",icon:"icon-8u-c",logo:"#icon-8u-c",name:s.global.t("app.airlineList.AfrikiaAir")},{code:"9B",icon:"icon-9b-c",logo:"#icon-9b-c",name:s.global.t("app.airlineList.NorwegianStateRailways")},{code:"9K",icon:"icon-9k-c",logo:"#icon-9k-c",name:s.global.t("app.airlineList.CapeCodAir")},{code:"9U",icon:"icon-9u-c",logo:"#icon-9u-c",name:s.global.t("app.airlineList.AirMoldova")},{code:"LX",icon:"icon-lx-c",logo:"#icon-lx-c",name:s.global.t("app.airlineList.SwissAir")},{code:"LY",icon:"icon-ly-c",logo:"#icon-ly-c",name:s.global.t("app.airlineList.Ehang")},{code:"MA",icon:"icon-ma-c",logo:"#icon-ma-c",name:s.global.t("app.airlineList.Malev")},{code:"MD",icon:"icon-md-c",logo:"#icon-md-c",name:s.global.t("app.airlineList.AirMadagascar")},{code:"ME",icon:"icon-me-c",logo:"#icon-me-c",name:s.global.t("app.airlineList.MiddleEastAirlines")},{code:"MF",icon:"icon-mf-c",logo:"#icon-mf-c",name:s.global.t("app.airlineList.XiamenAirlines")},{code:"MH",icon:"icon-mh-c",logo:"#icon-mh-c",name:s.global.t("app.airlineList.MalaysiaAirlines")},{code:"MI",icon:"icon-mi-c",logo:"#icon-mi-c",name:s.global.t("app.airlineList.Silkair")},{code:"UQ",icon:"icon-uq-c",logo:"#icon-uq-c",name:s.global.t("app.airlineList.UrumqiAir")},{code:"MO",icon:"icon-mo-c",logo:"#icon-mo-c",name:s.global.t("app.airlineList.StaticAir")},{code:"MP",icon:"icon-mp-c",logo:"#icon-mp-c",name:s.global.t("app.airlineList.MartinAir")},{code:"MJ",icon:"icon-mj-c",logo:"#icon-mj-c",name:s.global.t("app.airlineList.ARGAviation")},{code:"MK",icon:"icon-mk-c",logo:"#icon-mk-c",name:s.global.t("app.airlineList.AirMauritius")},{code:"MR",icon:"icon-mr-c",logo:"#icon-mr-c",name:s.global.t("app.airlineList.AirMauritania")},{code:"MS",icon:"icon-ms-c",logo:"#icon-ms-c",name:s.global.t("app.airlineList.Egyptair")},{code:"MU",icon:"icon-mu-c",logo:"#icon-mu-c",name:s.global.t("app.airlineList.ChinaEasternAirlines")},{code:"MX",icon:"icon-mx-c",logo:"#icon-mx-c",name:s.global.t("app.airlineList.Aeromexico")},{code:"NE",icon:"icon-ne-c",logo:"#icon-ne-c",name:s.global.t("app.airlineList.SkyEurope")},{code:"NF",icon:"icon-nf-c",logo:"#icon-nf-c",name:s.global.t("app.airlineList.AirVanuatu")},{code:"NH",icon:"icon-nh-c",logo:"#icon-nh-c",name:s.global.t("app.airlineList.AllNipponAirways")},{code:"NS",icon:"icon-ns-c",logo:"#icon-ns-c",name:s.global.t("app.airlineList.HebeiAirlines")},{code:"NX",icon:"icon-nx-c",logo:"#icon-nx-c",name:s.global.t("app.airlineList.AirMacau")},{code:"NZ",icon:"icon-nz-c",logo:"#icon-nz-c",name:s.global.t("app.airlineList.AirNewZealand")},{code:"OA",icon:"icon-oa-c",logo:"#icon-oa-c",name:s.global.t("app.airlineList.OlympicAir")},{code:"OD",icon:"icon-od-c",logo:"#icon-od-c",name:s.global.t("app.airlineList.AirNatalco")},{code:"OK",icon:"icon-ok-c",logo:"#icon-ok-c",name:s.global.t("app.airlineList.CzechAirlines")},{code:"OL",icon:"icon-ol-c",logo:"#icon-ol-c",name:s.global.t("app.airlineList.OLTAviation")},{code:"OM",icon:"icon-om-c",logo:"#icon-om-c",name:s.global.t("app.airlineList.MongolianAirlines")},{code:"OQ",icon:"icon-oq-c",logo:"#icon-oq-c",name:s.global.t("app.airlineList.ChongqingAirlines")},{code:"OS",icon:"icon-os-c",logo:"#icon-os-c",name:s.global.t("app.airlineList.OlympicAirlines")},{code:"OT",icon:"icon-ot-c",logo:"#icon-ot-c",name:s.global.t("app.airlineList.PelicanAir")},{code:"OU",icon:"icon-ou-c",logo:"#icon-ou-c",name:s.global.t("app.airlineList.CroatianAirlines")},{code:"OV",icon:"icon-ov-c",logo:"#icon-ov-c",name:s.global.t("app.airlineList.EstonianAir")},{code:"OZ",icon:"icon-oz-c",logo:"#icon-oz-c",name:s.global.t("app.airlineList.AsianaAirlines")},{code:"PG",icon:"icon-pg-c",logo:"#icon-pg-c",name:s.global.t("app.airlineList.BangkokAirways")},{code:"PK",icon:"icon-pk-c",logo:"#icon-pk-c",name:s.global.t("app.airlineList.PakistanAirlines")},{code:"PN",icon:"icon-pn-c",logo:"#icon-pn-c",name:s.global.t("app.airlineList.WesternAir")},{code:"PR",icon:"icon-pr-c",logo:"#icon-pr-c",name:s.global.t("app.airlineList.PhilippineAirlines")},{code:"PS",icon:"icon-ps-c",logo:"#icon-ps-c",name:s.global.t("app.airlineList.UkrainianAirlines")},{code:"PU",icon:"icon-pu-c",logo:"#icon-pu-c",name:s.global.t("app.airlineList.AirUruguay")},{code:"PW",icon:"icon-pw-c",logo:"#icon-pw-c",name:s.global.t("app.airlineList.PrecisionAviation")},{code:"PX",icon:"icon-px-c",logo:"#icon-px-c",name:s.global.t("app.airlineList.AirNewGuinea")},{code:"PY",icon:"icon-py-c",logo:"#icon-py-c",name:s.global.t("app.airlineList.SurinameAir")},{code:"PZ",icon:"icon-pz-c",logo:"#icon-pz-c",name:s.global.t("app.airlineList.AirParaguay")},{code:"QF",icon:"icon-qf-c",logo:"#icon-qf-c",name:s.global.t("app.airlineList.QantasAirways")},{code:"QR",icon:"icon-qr-c",logo:"#icon-qr-c",name:s.global.t("app.airlineList.QatarAirlines")},{code:"QV",icon:"icon-qv-c",logo:"#icon-qv-c",name:s.global.t("app.airlineList.LaoAir")},{code:"RA",icon:"icon-ra-c",logo:"#icon-ra-c",name:s.global.t("app.airlineList.NepalAirlines")},{code:"RC",icon:"icon-rc-c",logo:"#icon-rc-c",name:s.global.t("app.airlineList.FaroeIslandsAir")},{code:"RG",icon:"icon-rg-c",logo:"#icon-rg-c",name:s.global.t("app.airlineList.BrazilianAirlines")},{code:"RJ",icon:"icon-rj-c",logo:"#icon-rj-c",name:s.global.t("app.airlineList.JordanAviation")},{code:"RO",icon:"icon-ro-c",logo:"#icon-ro-c",name:s.global.t("app.airlineList.RomanianAirlines")},{code:"RQ",icon:"icon-rq-c",logo:"#icon-rq-c",name:s.global.t("app.airlineList.CAMAir")},{code:"S2",icon:"icon-s2-c",logo:"#icon-s2-c",name:s.global.t("app.airlineList.SaharaAir")},{code:"S7",icon:"icon-s7-c",logo:"#icon-s7-c",name:s.global.t("app.airlineList.SiberianAirlines")},{code:"SA",icon:"icon-sa-c",logo:"#icon-sa-c",name:s.global.t("app.airlineList.SouthAfricanAirways")},{code:"SB",icon:"icon-sb-c",logo:"#icon-sb-c",name:s.global.t("app.airlineList.AirCaledonia")},{code:"SC",icon:"icon-sc-c",logo:"#icon-sc-c",name:s.global.t("app.airlineList.ShandongAirlines")},{code:"SK",icon:"icon-sk-c",logo:"#icon-sk-c",name:s.global.t("app.airlineList.ScandinavianAirlines")},{code:"SN",icon:"icon-sn-c",logo:"#icon-sn-c",name:s.global.t("app.airlineList.BrusselsAirlines")},{code:"SQ",icon:"icon-sq-c",logo:"#icon-sq-c",name:s.global.t("app.airlineList.SingaporeAirlines")},{code:"SU",icon:"icon-su-c",logo:"#icon-su-c",name:s.global.t("app.airlineList.Aeroflot")},{code:"SV",icon:"icon-sv-c",logo:"#icon-sv-c",name:s.global.t("app.airlineList.SaudiAirlines")},{code:"SW",icon:"icon-sw-c",logo:"#icon-sw-c",name:s.global.t("app.airlineList.AirNamibia")},{code:"T5",icon:"icon-t5-c",logo:"#icon-t5-c",name:s.global.t("app.airlineList.TurkmenistanAir")},{code:"TA",icon:"icon-ta-c",logo:"#icon-ta-c",name:s.global.t("app.airlineList.TakaInternationalAirlines")},{code:"TF",icon:"icon-tf-c",logo:"#icon-tf-c",name:s.global.t("app.airlineList.MalmoAir")},{code:"TG",icon:"icon-tg-c",logo:"#icon-tg-c",name:s.global.t("app.airlineList.ThaiAirways")},{code:"TK",icon:"icon-tk-c",logo:"#icon-tk-c",name:s.global.t("app.airlineList.TurkishAirlines")},{code:"TM",icon:"icon-tm-c",logo:"#icon-tm-c",name:s.global.t("app.airlineList.RamAir")},{code:"TN",icon:"icon-tn-c",logo:"#icon-tn-c",name:s.global.t("app.airlineList.AirTahiti")},{code:"TO",icon:"icon-to-c",logo:"#icon-to-c",name:s.global.t("app.airlineList.PresidentialAviation")},{code:"TP",icon:"icon-tp-c",logo:"#icon-tp-c",name:s.global.t("app.airlineList.AirPortugal")},{code:"TU",icon:"icon-tu-c",logo:"#icon-tu-c",name:s.global.t("app.airlineList.TunisAir")},{code:"TV",icon:"icon-tv-c",logo:"#icon-tv-c",name:s.global.t("app.airlineList.TibetAirlines")},{code:"U6",icon:"icon-u6-c",logo:"#icon-u6-c",name:s.global.t("app.airlineList.UralAirlines")},{code:"UA",icon:"icon-ua-c",logo:"#icon-ua-c",name:s.global.t("app.airlineList.UnitedAirlines")},{code:"UL",icon:"icon-ul-c",logo:"#icon-ul-c",name:s.global.t("app.airlineList.SiHang")},{code:"UM",icon:"icon-um-c",logo:"#icon-um-c",name:s.global.t("app.airlineList.AirZimbabwe")},{code:"UN",icon:"icon-un-c",logo:"#icon-un-c",name:s.global.t("app.airlineList.AnnulusAviation")},{code:"UO",icon:"icon-uo-c",logo:"#icon-uo-c",name:s.global.t("app.airlineList.HongKongExpress")},{code:"UP",icon:"icon-up-c",logo:"#icon-up-c",name:s.global.t("app.airlineList.BahamasAirlines")},{code:"US",icon:"icon-us-c",logo:"#icon-us-c",name:s.global.t("app.airlineList.AmericanAir")},{code:"UU",icon:"icon-uu-c",logo:"#icon-uu-c",name:s.global.t("app.airlineList.AuslarAir")},{code:"UX",icon:"icon-ux-c",logo:"#icon-ux-c",name:s.global.t("app.airlineList.SpanishAirEurope")},{code:"V2",icon:"icon-v2-c",logo:"#icon-v2-c",name:s.global.t("app.airlineList.KaratAir")},{code:"VA",icon:"icon-va-c",logo:"#icon-va-c",name:s.global.t("app.airlineList.VirginAustralia")},{code:"VD",icon:"icon-vd-c",logo:"#icon-vd-c",name:s.global.t("app.airlineList.HenanAirlines")},{code:"VF",icon:"icon-vf-c",logo:"#icon-vf-c",name:s.global.t("app.airlineList.HuitravelAir")},{code:"VN",icon:"icon-vn-c",logo:"#icon-vn-c",name:s.global.t("app.airlineList.VietnamAirlines")},{code:"VR",icon:"icon-vr-c",logo:"#icon-vr-c",name:s.global.t("app.airlineList.CapeVerdeAirlines")},{code:"VS",icon:"icon-vs-c",logo:"#icon-vs-c",name:s.global.t("app.airlineList.VirginAtlantic")},{code:"VV",icon:"icon-vv-c",logo:"#icon-vv-c",name:s.global.t("app.airlineList.UkrainianAirlines")},{code:"VW",icon:"icon-vw-c",logo:"#icon-vw-c",name:s.global.t("app.airlineList.GreatLakesAir")},{code:"VX",icon:"icon-vx-c",logo:"#icon-vx-c",name:s.global.t("app.airlineList.WebbardAir")},{code:"VY",icon:"icon-vy-c",logo:"#icon-vy-c",name:s.global.t("app.airlineList.WellingAir")},{code:"WF",icon:"icon-wf-c",logo:"#icon-wf-c",name:s.global.t("app.airlineList.WidroAir")},{code:"WN",icon:"icon-wn-c",logo:"#icon-wn-c",name:s.global.t("app.airlineList.SouthwestAirlines")},{code:"WS",icon:"icon-ws-c",logo:"#icon-ws-c",name:s.global.t("app.airlineList.WesternJetAviation")},{code:"WY",icon:"icon-wy-c",logo:"#icon-wy-c",name:s.global.t("app.airlineList.OmanAir")},{code:"XF",icon:"icon-xf-c",logo:"#icon-xf-c",name:s.global.t("app.airlineList.AirVladivostok")},{code:"XK",icon:"icon-xk-c",logo:"#icon-xk-c",name:s.global.t("app.airlineList.CorsairAirlines")},{code:"YV",icon:"icon-yv-c",logo:"#icon-yv-c",name:s.global.t("app.airlineList.MesaAir")},{code:"ZH",icon:"icon-zh-c",logo:"#icon-zh-c",name:s.global.t("app.airlineList.ShenzhenAirlines")},{code:"ZI",icon:"icon-zi-c",logo:"#icon-zi-c",name:s.global.t("app.airlineList.AigleAzur")},{code:"ZK",icon:"icon-zk-c",logo:"#icon-zk-c",name:s.global.t("app.airlineList.GreatLakesAirlines")},{code:"ZL",icon:"icon-zl-c",logo:"#icon-zl-c",name:s.global.t("app.airlineList.RegionalExpressAirlines")},{code:"3U",icon:"icon-3u-c",logo:"#icon-3u-c",name:s.global.t("app.airlineList.SichuanAirlines")},{code:"QW",icon:"icon-qw-c",logo:"#icon-qw-c",name:s.global.t("app.airlineList.QingdaoAirlines")},{code:"KR",icon:"icon-kr-c",logo:"#icon-kr-c",name:s.global.t("app.airlineList.CambodiaAirways")},{code:"QD",icon:"icon-qd-c",logo:"#icon-qd-c",name:s.global.t("app.airlineList.JCAirlinesCambodia")},{code:"TR",icon:"icon-tr-c",logo:"#icon-tr-c",name:s.global.t("app.airlineList.Tigerair")},{code:"FU",icon:"icon-fu-c",logo:"#icon-fu-c",name:s.global.t("app.airlineList.FuzhouAirlines")},{code:"XW",icon:"icon-xw-c",logo:"#icon-xw-c",name:s.global.t("app.airlineList.CoolBirdAir")},{code:"GY",icon:"icon-gy-c",logo:"#icon-gy-c",name:s.global.t("app.airlineList.GuizhouAirlines")},{code:"9D",icon:"icon-9d-c",logo:"#icon-9d-c",name:s.global.t("app.airlineList.PermAir")},{code:"H9",icon:"icon-h9-c",logo:"#icon-h9-c",name:s.global.t("app.airlineList.HimalayaAirlines")},{code:"9H",icon:"icon-9h-c",logo:"#icon-9h-c",name:s.global.t("app.airlineList.ChanganAirlines")},{code:"GT",icon:"icon-gt-c",logo:"#icon-gt-c",name:s.global.t("app.airlineList.GuilinAir")},{code:"GX",icon:"icon-gx-c",logo:"#icon-gx-c",name:s.global.t("app.airlineList.BeibuGulfAir")},{code:"LT",icon:"icon-lt-c",logo:"#icon-lt-c",name:s.global.t("app.airlineList.LongjiangAirlines")},{code:"A6",icon:"icon-a6-c",logo:"#icon-a6-c",name:s.global.t("app.airlineList.RedEarthAir")},{code:"RY",icon:"icon-ry-c",logo:"#icon-ry-c",name:s.global.t("app.airlineList.JiangxiAirlines")}],Br={class:"rounded bg-brand-7 flex flex-col border-t mt-[10px]"},Ur={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},Yr={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},Hr={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},df=Be({__name:"AirRoute",props:{routingSegList:{},needCloseIcon:{type:Boolean}},setup(e){return(a,i)=>(u(),D("div",Br,[a.needCloseIcon?ft(a.$slots,"default",{key:0}):de("",!0),(u(!0),D(be,null,we(a.routingSegList,(r,d)=>(u(),D("div",{key:d,class:"flex flex-col p-[10px] font-normal text-xs leading-[20px] break-all"},[(u(!0),D(be,null,we(r.routingRestrictions,(n,l)=>(u(),D("div",{key:l,class:"font-normal text-xs leading-[20px] text-gray-1"},[o("span",Ur,m(`*${n}`),1)]))),128)),(u(!0),D(be,null,we(r.routingPaths,(n,l)=>(u(),D("div",{key:l,class:"font-normal text-xs leading-[20px] text-gray-1"},[o("span",Yr,m(`${l+1}*`),1),o("span",Hr,m(`${n}`),1)]))),128))]))),128))]))}}),qr=e=>{const a=e.match(/\.(\d+)$/);return a?a[1].length:0},uf=e=>e&&e!=="-"&&e!=="--"&&e.indexOf("%")===-1&&!e.includes("+")?qr(e)>2?e:Number(e).toFixed(2):e,pf=(e,a,i)=>{let r,d,n,l;try{r=e.toString().split(".")[1].length}catch{r=0}try{d=a.toString().split(".")[1].length}catch{d=0}const $=Math.abs(r-d),p=10**Math.max(r,d);if($>0){const C=10**$;r>d?(n=Number(e.toString().replace(".","")),l=Number(a.toString().replace(".",""))*C):(n=Number(e.toString().replace(".",""))*C,l=Number(a.toString().replace(".","")))}else n=Number(e.toString().replace(".","")),l=Number(a.toString().replace(".",""));return i==="+"?(n+l)/p:(n-l)/p},gf=(e,a)=>Ye(`${Jo}/apiAvSearch/queryRules`,{headers:{gid:a}}).post(e).json(),jr=e=>(Lt("data-v-2f9e66d0"),e=e(),wt(),e),Gr={class:"min-h-[50px]"},Kr={key:0,class:"brand text-xs"},zr=jr(()=>o("div",{class:"brand-line border-b mt-[10px]"},null,-1)),Jr={key:1,className:"text-neutral-800 text-xs font-bold leading-tight"},Wr={key:2,className:"w-[100%] h-[30px] px-1.5 py-[7px] bg-[#f5f9ff] rounded-tl-md justify-start items-center inline-flex mt-[6px]"},Zr={key:3,className:"w-[100%] h-[30px] px-1.5 py-[7px] bg-[#f5f9ff] rounded-tl-md justify-start items-center inline-flex mt-[6px]"},Xr={class:"row-base th ml-[-7px]"},es={key:4,class:"cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex flex-col"},ts={key:6,class:"cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex flex-col"},as=Be({__name:"priceBasis",props:{priceBasis:{},ticketingTime:{},computingTime:{},priceFc:{},fcc:{},needCloseIcon:{type:Boolean}},setup(e){return(a,i)=>{var n,l,$,p;const r=_n,d=Cn;return u(),D("div",Gr,[a.priceBasis?(u(),D("div",Kr,[zr,a.needCloseIcon?ft(a.$slots,"default",{key:0},void 0,!0):(u(),D("div",Jr,m(a.$t("app.fareQuery.freightate.ticketPriceBasis")),1)),a.priceFc?(u(),D("div",Wr,m(a.priceFc),1)):de("",!0),(l=(n=a.priceBasis)==null?void 0:n.segmentFare[0])!=null&&l.rate?(u(),D("div",Zr,[o("span",Xr,m(a.$t("app.fareQuery.freightate.rate")),1),o("span",null,m((p=($=a.priceBasis)==null?void 0:$.segmentFare[0])==null?void 0:p.rate),1)])):de("",!0),b(d,{class:"row-base th mt-[2px]"},{default:x(()=>[b(r,{span:4},{default:x(()=>[fe("PU")]),_:1}),b(r,{span:4},{default:x(()=>[fe("Farebasis")]),_:1}),b(r,{span:4},{default:x(()=>[fe(m(a.$t("app.fareQuery.freightate.currency")),1)]),_:1}),b(r,{span:4},{default:x(()=>[fe("NUC")]),_:1}),b(r,{span:4},{default:x(()=>[fe(m(a.$t("app.fareQuery.freightate.desc")),1)]),_:1}),b(r,{span:4},{default:x(()=>[fe(m(a.$t("app.fareQuery.freightate.globalIndicator")),1)]),_:1})]),_:1}),a.priceBasis.segmentFare&&a.priceBasis.segmentFare.length>0?(u(),D("div",es,[(u(!0),D(be,null,we(a.priceBasis.segmentFare,(C,_)=>(u(),Ce(d,{key:_,class:"row-base"},{default:x(()=>[b(r,{span:4},{default:x(()=>[fe(m(C.pu??"--"),1)]),_:2},1024),b(r,{span:4},{default:x(()=>[fe(m(C.fareBasis??"--"),1)]),_:2},1024),b(r,{span:4},{default:x(()=>[fe(m(C.currency??"--"),1)]),_:2},1024),b(r,{span:4},{default:x(()=>[fe(m(C.nuc??"--"),1)]),_:2},1024),b(r,{span:4},{default:x(()=>[fe(m(C.description??"--"),1)]),_:2},1024),b(r,{span:4},{default:x(()=>[fe(m(C.globalIndicator??"--"),1)]),_:2},1024)]),_:2},1024))),128))])):(u(),Ce(d,{key:5,class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md"},{default:x(()=>[b(r,{span:4},{default:x(()=>[fe("--")]),_:1}),b(r,{span:4},{default:x(()=>[fe("--")]),_:1}),b(r,{span:4},{default:x(()=>[fe("--")]),_:1}),b(r,{span:4},{default:x(()=>[fe("--")]),_:1}),b(r,{span:4},{default:x(()=>[fe("--")]),_:1}),b(r,{span:4},{default:x(()=>[fe("--")]),_:1})]),_:1})),b(d,{class:"row-base mt-[10px] th"},{default:x(()=>[b(r,{span:4},{default:x(()=>[fe("CNY TAX")]),_:1}),b(r,{span:4},{default:x(()=>[fe("LST "+m(a.$t("app.fareQuery.freightate.currency")),1)]),_:1}),b(r,{span:4},{default:x(()=>[fe("CODES TYP")]),_:1}),b(r,{span:8},{default:x(()=>[fe(m(a.$t("app.fareQuery.freightate.desc")),1)]),_:1}),b(r,{span:4},{default:x(()=>[fe("TAX")]),_:1})]),_:1}),a.priceBasis.taxFare&&a.priceBasis.taxFare.length>0?(u(),D("div",ts,[(u(!0),D(be,null,we(a.priceBasis.taxFare,(C,_)=>(u(),Ce(d,{key:_,class:"row-base"},{default:x(()=>[b(r,{span:4},{default:x(()=>[fe(m(C.cnyTax??"--"),1)]),_:2},1024),b(r,{span:4},{default:x(()=>[fe(m(C.lstCurrency??"--"),1)]),_:2},1024),b(r,{span:4},{default:x(()=>[fe(m(C.codesTyp??"--"),1)]),_:2},1024),b(r,{span:8},{default:x(()=>[fe(m(C.description??"--"),1)]),_:2},1024),b(r,{span:4},{default:x(()=>[fe(m(C.tax??"--"),1)]),_:2},1024)]),_:2},1024))),128))])):(u(),Ce(d,{key:7,class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md"},{default:x(()=>[b(r,{span:4},{default:x(()=>[fe("--")]),_:1}),b(r,{span:4},{default:x(()=>[fe("--")]),_:1}),b(r,{span:4},{default:x(()=>[fe("--")]),_:1}),b(r,{span:8},{default:x(()=>[fe("--")]),_:1}),b(r,{span:4},{default:x(()=>[fe("--")]),_:1})]),_:1})),b(d,{class:"row-base mt-[10px] th"},{default:x(()=>[b(r,{span:8},{default:x(()=>[fe(m(a.$t("app.fareQuery.freightate.computingTime")),1)]),_:1}),b(r,{span:8},{default:x(()=>[fe(m(a.$t("app.fareQuery.freightate.ticketingTime")),1)]),_:1}),b(r,{span:4},{default:x(()=>[fe("FCC")]),_:1})]),_:1}),b(d,{class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex"},{default:x(()=>[b(r,{span:8},{default:x(()=>[fe(m(a.computingTime??"--"),1)]),_:1}),b(r,{span:8},{default:x(()=>[fe(m(a.ticketingTime??"--"),1)]),_:1}),b(r,{span:4},{default:x(()=>[fe(m(a.fcc??"--"),1)]),_:1})]),_:1})])):de("",!0)])}}});const ff=ze(as,[["__scopeId","data-v-2f9e66d0"]]),os={class:"cursor-default"},ns={class:"w-full flex justify-between"},is={class:"text-xs text-gray-2 mb-1.5"},rs={class:"text-xs text-gray-2 font-bold mb-1.5"},ss={class:"text-xs rounded bg-brand-7 mt-[5px]"},ls={class:"pr-[40px] text-gray-2"},cs={class:"basis-[100px] text-gray-2"},ds={class:"text-gray-2"},us={key:0,class:"mb-2.5"},ps={class:"flex justify-between items-center"},gs={key:0,class:"w-full overflow-x-hidden text-xs mr-5 bg-brand-7"},fs={class:"overflow-x-auto"},ms={class:"flex p-0 mb-1 bg-[#FFFFFF] text-[#8C8C8C] whitespace-nowrap h-[30px] leading-[30px]"},hs={class:"w-60 px-2"},vs={class:"w-40 px-2"},ys={class:"min-w-[120px] px-2"},bs={class:"w-[60px] px-2"},_s={class:"w-60 px-2 overflow-hidden whitespace-nowrap text-ellipsis bg-brand-7"},Cs={class:"w-40 bg-brand-7"},As={class:"min-w-[125px] px-2"},Ds={class:"w-[60px] px-2"},$s=Be({__name:"Package",props:{baggageAllowance:{},international:{type:Boolean},needCloseIcon:{type:Boolean},doBaggageApi:{type:Boolean}},emits:["closePanel"],setup(e){const a=P(!1),i=p=>p.index?`${p.baggageCurrency}${p.baggageAmout}${p.baggageType==="4"?`/${p.baggageUnit}`:""}`:"-",r=p=>p.baggageType??"-",d=It(),{activeTag:n}=Yt(d),l=Re(()=>d.getOrderInfo),$=Re(()=>{var p;return((p=l.value.get(n.value))==null?void 0:p.type)==="2"});return(p,C)=>{var L,E,g;const _=xa,f=je,S=ni,I=vt;return u(),D("div",os,[b(_),o("div",ns,[o("p",is,m(p.international?p.$t("app.fareQuery.freightate.baggageAllowanceTips"):""),1),p.needCloseIcon?(u(),Ce(f,{key:0,class:"cursor-pointer mr-[10px]",size:"16px",onClick:C[0]||(C[0]=T=>p.$emit("closePanel"))},{default:x(()=>[b(t(to))]),_:1})):de("",!0)]),o("div",{class:ve(["mb-[2px]",p.international&&$.value?"hidden":""])},[o("p",rs,m(p.$t("app.fareQuery.freightate.freeBaggageAllowance")),1),!p.international&&p.doBaggageApi?(u(),Ce(S,{key:0,title:p.$t("app.fareQuery.freightate.domBaggageAllowanceTips"),type:"warning",closable:!1,"show-icon":""},null,8,["title"])):de("",!0),o("div",ss,[(u(!0),D(be,null,we(((L=p.baggageAllowance)==null?void 0:L.freeBaggageSegInfo)??[],(T,Y)=>(u(),D("p",{key:Y,class:"flex px-2 h-[30px] leading-[30px]"},[o("span",ls,m(p.$t("app.fareQuery.freightate.freeBaggageAllowance")),1),o("span",cs,m(`${T.departurePlace}-${T.arrivalPlace}`),1),o("span",ds,m(T.freeBaggageFare),1)]))),128))])],2),p.international?(u(),D("div",us,[o("div",ps,[o("p",{class:"flex items-center text-xs text-gray-1 font-bold mb-1.5",onClick:C[1]||(C[1]=T=>a.value=!a.value)},[o("span",null,m(p.$t("app.fareQuery.freightate.excessBaggageAllowance")),1),a.value?(u(),Ce(f,{key:0,size:16},{default:x(()=>[b(t(Vt))]),_:1})):(u(),Ce(f,{key:1,size:16},{default:x(()=>[b(t(Jt))]),_:1}))])]),a.value?(u(),D("div",gs,[o("div",fs,[o("p",ms,[o("span",hs,m(p.$t("app.fareQuery.freightate.baggageDescription")),1),o("span",vs,m(p.$t("app.fareQuery.freightate.segment")),1),(u(!0),D(be,null,we(((E=p.baggageAllowance)==null?void 0:E.payBaggageTitle)??[],(T,Y)=>(u(),D(be,{key:Y},[o("span",ys,m(T),1),o("span",bs,m(p.$t("app.fareQuery.freightate.baggageType")),1)],64))),128))]),(u(!0),D(be,null,we(((g=p.baggageAllowance)==null?void 0:g.payBaggageInfo)??[],(T,Y)=>(u(),D(be,{key:Y},[(u(!0),D(be,null,we(T.payBaggageSegInfo,(H,h)=>(u(),D("div",{key:h,class:"rounded flex p-0 h-[30px] leading-[30px]"},[b(I,{effect:"dark",placement:"top",content:T.payBaggageStatement},{default:x(()=>[o("span",_s,m(T.payBaggageStatement),1)]),_:2},1032,["content"]),o("span",Cs,m(`${H.departurePlace}-${H.arrivalPlace}`),1),(u(!0),D(be,null,we(H.payBaggageFares,(v,c)=>(u(),D(be,{key:c},[o("span",As,m(i(v)),1),o("span",Ds,m(r(v)),1)],64))),128))]))),128))],64))),128))])])):de("",!0)])):de("",!0)])}}});const mf=ze($s,[["__scopeId","data-v-66ec95ac"]]),xs={class:"min-h-[50px] border-t mt-[10px]"},Ss={key:1,class:"brand text-xs flex flex-col"},Ts={class:"brand-title font-bold text-gray-2 mb-1 mt-0.5"},ks={key:0,class:"brand-info bg-brand-7 rounded-[1px] p-[8px] flex flex-col"},Fs={class:"brand-free brand-one text-green-2 mr-4 font-bold h-5"},Ls={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},ws={class:"min-w-[30px]"},Ns=o("div",{class:"border-b border-dashed border-gray-300 mb-1 mt-1"},null,-1),Is={class:"brand-pay brand-one text-yellow-1 mr-4 font-bold h-5"},Es={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},Ps={class:"min-w-[30px]"},Os={key:0,class:"border-b border-dashed border-gray-300 mb-1 mt-1"},Qs=o("div",{class:"brand-provide brand-one text-gray-1 mr-4 font-bold h-5"},"DISPALY AS NOT OFFERED",-1),Rs={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},Ms={class:"min-w-[30px]"},Vs={key:2,class:"min-h-[50px]"},hf=Be({__name:"Brand",props:{brandInfoData:{},needCloseIcon:{type:Boolean}},setup(e){return(a,i)=>{var r;return u(),D("div",xs,[a.needCloseIcon?ft(a.$slots,"default",{key:0}):de("",!0),(r=a.brandInfoData)!=null&&r.length?(u(),D("div",Ss,[(u(!0),D(be,null,we(a.brandInfoData,(d,n)=>(u(),D(be,{key:n},[o("div",Ts,m(d.brandName)+" "+m(d.tierCode),1),d.equityClassify.length>0?(u(),D("div",ks,[(u(!0),D(be,null,we(d.equityClassify,(l,$)=>(u(),D("div",{key:$,class:"mb-1"},[l.offeredForFree.length>0?(u(),D(be,{key:0},[o("div",Fs,m(a.$t("app.fareQuery.freightate.free")),1),(u(!0),D(be,null,we(l.offeredForFree,(p,C)=>(u(),D(be,{key:C},[p.subCode||p.title?(u(),D("div",Ls,[o("div",ws,m(p.subCode??""),1),fe(m(p.title),1)])):de("",!0)],64))),128)),Ns],64)):de("",!0),l.offeredForCharge.length>0?(u(),D(be,{key:1},[o("div",Is,m(a.$t("app.fareQuery.freightate.charge")),1),(u(!0),D(be,null,we(l.offeredForCharge,(p,C)=>(u(),D(be,{key:C},[p.subCode||p.title?(u(),D("div",Es,[o("div",Ps,m(p.subCode??""),1),fe(m(p.title),1)])):de("",!0)],64))),128)),l.displayAsNotOffered.length>0?(u(),D("div",Os)):de("",!0)],64)):de("",!0),l.displayAsNotOffered.length>0?(u(),D(be,{key:2},[Qs,(u(!0),D(be,null,we(l.displayAsNotOffered,(p,C)=>(u(),D(be,{key:C},[p.subCode||p.title?(u(),D("div",Rs,[o("div",Ms,m(p.subCode??""),1),fe(m(p.title),1)])):de("",!0)],64))),128))],64)):de("",!0)]))),128))])):de("",!0)],64))),128))])):(u(),D("div",Vs,m(a.$t("app.fareQuery.freightate.noData")),1))])}}}),Bs=It(),{activeTag:Us}=Yt(Bs),Ua=()=>({flightDataList:new Map,clickFlightSearchFlag:!1,seizeSeatInfoFlight:[],clickHistoryFlag:{},isInterSegment:!1,refrashFlight:!1,activeFlightIndex:0}),ca=Wo("flight",{state:()=>({flightByDate:{[Us.value]:Ua()},simEtermIsOpen:!1}),actions:{initAgentSell(e){this.flightByDate[e]=Ua()},deleteAgentSell(e){delete this.flightByDate[e]},setActiveFlightIndex(e,a){this.flightByDate[e].activeFlightIndex=a},setFlightDataList(e,a,i,r,d){var l,$;i.currentPage=d||1,i.queryForm=a;const n=new Map([[r,i]]);(l=this.flightByDate[e])==null||l.flightDataList.forEach((p,C)=>{n.has(C)||n.set(C,p)}),($=this.flightByDate[e])==null||$.flightDataList.clear(),this.flightByDate[e].flightDataList=n},delFlightDataList(e,a){this.flightByDate[e].flightDataList.delete(a)},updateFlightDataListKey(e,a,i,r){const d=[...this.flightByDate[e].flightDataList.keys()].indexOf(i),n=this.flightByDate[e].flightDataList.get(i);if(!n)return;n.queryForm=r;const l=[...this.flightByDate[e].flightDataList];l.splice(d,1,[a,n]);const $=new Map(l);this.flightByDate[e].flightDataList=$},delFlightByPnr(e){this.flightByDate=Jn(this.flightByDate,[e])},setHistoryQueryForm(e,a){this.flightByDate[e].clickHistoryFlag.flag=!this.flightByDate[e].clickHistoryFlag.flag,this.flightByDate[e].clickHistoryFlag.queryForm=a},setClickFlightSearchFlag(e,a){this.flightByDate[e]||this.initAgentSell(e),this.flightByDate[e].clickFlightSearchFlag=!!a},delSeizeSeatInfoFlight(e,a){this.flightByDate[e].seizeSeatInfoFlight=this.flightByDate[e].seizeSeatInfoFlight.filter(i=>i.key!==a)},setSeizeSeatInfoFlight(e,a){this.flightByDate[e].seizeSeatInfoFlight.push(a)},setRefrashFlight(e,a){this.flightByDate[e].refrashFlight=a},setSimEtermOpenFlag(e){this.simEtermIsOpen=e}}}),Ys={class:"custom-focus-tip-input"},Hs={class:"hidden bkc-el-form-item__error"},qs=Be({__name:"CustomFocusTipInput",props:{modelValue:{default:""},tipText:{}},emits:["update:modelValue"],setup(e,{emit:a}){const i=e,r=a,d=Re({get:()=>i.modelValue.toUpperCase(),set:$=>{n.value=!$,r("update:modelValue",$.toUpperCase())}}),n=P(!1),l=$=>{if($){n.value=!d.value;return}n.value=$};return($,p)=>{const C=Nt;return u(),D("div",Ys,[o("div",{class:ve(["input-focus-tip",n.value?"input-focus":""])},[b(C,Xo({ref:"inputRef",modelValue:d.value,"onUpdate:modelValue":p[0]||(p[0]=_=>d.value=_)},$.$attrs,{onBlur:p[1]||(p[1]=_=>l(!1)),onFocus:p[2]||(p[2]=_=>l(!0))}),Zo({_:2},[we($.$slots,(_,f)=>({name:f,fn:x(()=>[ft($.$slots,f,{},void 0,!0)])}))]),1040,["modelValue"]),o("div",Hs,m($.tipText),1)],2)])}}});const Ya=ze(qs,[["__scopeId","data-v-6bab0367"]]);var Ot=(e=>(e[e.DIRECT=0]="DIRECT",e[e.STOP=-1]="STOP",e))(Ot||{});const Qt="FAST_QUERY_AV_QUERY",js=()=>ue().format("YYYY-MM-DD"),Gs=e=>ue(e).add(1,"day").format("YYYY-MM-DD"),Ks=e=>ue(e).subtract(1,"day").format("YYYY-MM-DD"),Co=(e,a)=>{var n;let i=[];const r=Array.from(a.keys()),d=r==null?void 0:r.find(l=>{var $,p;return l!==e&&((p=($=a.get(l))==null?void 0:$.specialContent)==null?void 0:p.includes(s.global.t("app.basic.occupy")))});if(d){const l=a.get(d);i=((l==null?void 0:l.flight)??[]).filter($=>$.segments.some(p=>p.segmentType==="2"))}else i=(((n=a.get(e))==null?void 0:n.flight)??[]).filter(l=>l.segments.some($=>$.segmentType==="2"));return i},zs=e=>{var a,i;return!!(e!=null&&e.notchPath)||((i=(a=e==null?void 0:e.segments[0])==null?void 0:a.airlines)==null?void 0:i.flightNo)==="ARNK"},Js=e=>{var a,i;return!!(e!=null&&e.openFlag)||((i=(a=e==null?void 0:e.segments[0])==null?void 0:a.airlines)==null?void 0:i.flightNo)==="OPEN"},Ws=e=>{const a=e==null?void 0:e.match(/[a-zA-Z]+/g);return a?a==null?void 0:a.join(""):""},ia=(e,a)=>{let i=[],r=Xe(a);e&&(r=(r??[]).filter(d=>!Js(d)&&!zs(d)));try{i=r.flatMap(d=>d.segments.map(n=>{var l,$,p,C,_;return{airline:n.airlines.airCode,flightNumber:parseInt(($=((l=n.airlines)==null?void 0:l.flightNo)??"")==null?void 0:$.replace(/[a-zA-Z]/g,"")),flightSuffix:Ws((p=n.airlines)==null?void 0:p.flightNo),cls:n.cabins[0].cabinName,departureDate:n.departureDate?ue(n.departureDate).format("YYYY-MM-DD"):"",origin:n.departureAirportCode,destination:n.arrivalAirportCode,seats:(n==null?void 0:n.tktNum)??0,action:e?n.actionCode??"":n.seatTag??"",departureTime:(C=n.departureTime)==null?void 0:C.replace(/:/g,""),arrivalTime:(_=n.arrivalTime)==null?void 0:_.replace(/:/g,""),married:n.marriedSegmentNumber?parseInt(n.marriedSegmentNumber):0}}))}catch{i=[]}return i},Zs=async(e,a,i,r,d,n,l)=>{var I,L,E;const $={departureDate:ue(a.departureDate).format("YYYY-MM-DD"),queryType:"REAL_TIME_AV",flightNo:e.toUpperCase()},p=(((I=d.get(i))==null?void 0:I.type)??"")==="2",C=Co(i,d),_=((L=r==null?void 0:r.get(i))==null?void 0:L.flight)??[];p&&C.length&&($.preOccupySegmentInfoList=ia(!1,C)),!p&&_.length&&($.preOccupySegmentInfoList=ia(!0,_));const f=await bi($,n),S=((E=f==null?void 0:f.data.value)==null?void 0:E.flightInfoList)??[];if(S.length){const g=T=>{const{departureAirportCode:Y,departureAirportCN:H,arrivalAirportCode:h,arrivalAirportCN:v,departureDate:c,departureTime:V,arrivalDate:y,arrivalTime:q,departureTerminal:O,arrivalTerminal:k,airlines:B,cabins:ee,etInd:le}=T;if(l!=null&&l.length){const J=l.find(Q=>Q.airportCode.toUpperCase()===Y.toUpperCase()),Z=l.find(Q=>Q.airportCode.toUpperCase()===h.toUpperCase());J&&Z&&(a.departureAirportCode.toUpperCase()===J.cityCode.toUpperCase()&&(a.departureAirportCode=J.airportCode),a.arrivalAirportCode.toUpperCase()===Z.cityCode.toUpperCase()&&(a.arrivalAirportCode=Z.airportCode))}const K=`${a.departureAirportCode}${a.arrivalAirportCode}`.toUpperCase()===`${Y}${h}`.toUpperCase();if(K){const J=ee==null?void 0:ee.find(Q=>{var F;return(Q==null?void 0:Q.cabinName.toUpperCase())===((F=a==null?void 0:a.cabin)==null?void 0:F.toUpperCase())});let Z="";J&&(Z=J.state??""),Object.assign(a,{isRealFlightNo:K,departureAirportCode:Y,departureAirportCN:H,arrivalAirportCode:h,arrivalAirportCN:v,departureDate:c,departureTime:V,arrivalDate:y,arrivalTime:q,departureTerminal:O,arrivalTerminal:k,airCN:B.airCN,isShared:B.isShared,state:Z,etInd:le})}return K};return S.some(T=>{if(T.segments.find(H=>g(H)))return!0;if(T.segments.length>1){const H=T.segments[0],h=T.segments[T.segments.length-1],{arrivalAirportCode:v,arrivalDate:c,arrivalTime:V,arrivalTerminal:y}=h;return Object.assign(H,{arrivalAirportCode:v,arrivalDate:c,arrivalTime:V,arrivalTerminal:y}),g(H)}})}return!1},Xs=async(e,a,i,r,d)=>{const n=e.map(async l=>{if(l.isOpen)return l.isRealFlightNo=!0,!0;const $=l.flightNumber.startsWith(l.airlines)?l.flightNumber:`${l.airlines}${l.flightNumber}`;return await Zs($,l,i,r,d,ao("01010215"),a),!!l.isRealFlightNo});await Promise.all(n)},el=e=>(e??[]).map(a=>{const i=[],r=a.segments;return r.some(n=>Number((n==null?void 0:n.stopCity)??0))&&(i.includes(Ot.STOP)||i.push(Ot.STOP),r.length-1===0)?{...a,flyType:i}:(i.includes(r.length-1)||i.push(r.length-1),{...a,flyType:i})}),tl=()=>{const e=Xt();return{cacheHistoryCondition:async r=>{var p;const d=await st("QUERY_HISTORY"),{userName:n}=await e.getters.user;if(d===null){await Ia("QUERY_HISTORY",JSON.stringify([{userName:n,qureyParam:[r]}]));return}const l=JSON.parse(d==null?void 0:d.localData).findIndex(C=>C.userName===n),$=JSON.parse(d==null?void 0:d.localData);if(l<0)$.push({userName:n,qureyParam:[r]});else{const C=JSON.parse(d==null?void 0:d.localData)[l];if((p=C==null?void 0:C.qureyParam)==null?void 0:p.map(f=>JSON.stringify(f)).some(f=>f===JSON.stringify(r)))return;C.qureyParam.length<5||C.qureyParam.shift(),C.qureyParam.push(r),$[l]=C}await Ia("QUERY_HISTORY",JSON.stringify($))},getHistory:async()=>{var l;const r=await st("QUERY_HISTORY"),{userName:d}=await e.getters.user;if(!(r!=null&&r.localData))return null;const n=(l=JSON.parse(r==null?void 0:r.localData))==null?void 0:l.find($=>$.userName===d);return(n==null?void 0:n.qureyParam)??null}}},Ha=()=>({destName:"",originName:"",origin:"",destination:"",departureDate:"",departureDateTime:"",departureDateTimes:"",flightNumber:"",onlyDirectFlight:!1,airlines:"",onlyCompany:"",seamlessOrDa:"",unsharedFlight:!1,carrierFlight:!1,lowestPrice:!1,timeSequence:!1,transitTerminal:"",transitTerminalName:"",selectPassengers:{adult:{num:0,min:0,max:1},children:{num:0,min:0,max:1},baby:{num:0,min:0,max:1},chdOverseasStudent:{num:0,min:0,max:1},overseasStudent:{num:0,min:0,max:1},migrate:{num:0,min:0,max:1},chdMigrate:{num:0,min:0,max:1},seafarer:{num:0,min:0,max:1},labourer:{num:0,min:0,max:1}}}),al=(e,a)=>{const{t:i}=ct(),{cacheHistoryCondition:r,getHistory:d}=tl(),n=P(),l=Xt(),$=Re(()=>l.getters.userPreferences),p=la(),C=It(),{activeTag:_,orderInfo:f}=Yt(C),S=a.avQueryFromFastQuery?Qt:_.value,I=ca(),L=Re(()=>{var j;return((j=f.value.get(_.value))==null?void 0:j.commandsAVCheckSuccess)??!1}),E=Re(()=>{var G;const j=(G=f.value.get(_.value))==null?void 0:G.commands;return JSON.stringify(j?j.get("AV"):"")}),g=P(!1),T=P(""),Y=P(),H=P(),h=P(),v=P(!1),c=P(!0),V=P(!1),y=P(Ha()),q=P(!1),O=["H","E","P","O"].sort(),k=P(!1),B=nt({render(){return gt("em",{class:"iconfont icon-calendar"})}}),ee=P(),le=P([`${i("app.fastQuery.headerQuery.notHistory")}`]),K=(j,G,ce)=>{if(c.value){if(G.length>3||!$t.test(G)){ce(new Error(i("app.fastQuery.headerQuery.formatError")));return}if(y.value.destination&&G===y.value.destination){ce(new Error(i("app.fastQuery.headerQuery.identical")));return}}ce()},J=(j,G,ce)=>{if(c.value&&!G){ce(new Error(i("app.fastQuery.headerQuery.must")));return}ce()},Z=(j,G,ce)=>{if(c.value){if(G.length>3||!$t.test(G)){ce(new Error(i("app.fastQuery.headerQuery.formatError")));return}if(y.value.origin&&G===y.value.origin){ce(new Error(i("app.fastQuery.headerQuery.identical")));return}}ce()},Q=(j,G,ce)=>{if(c.value&&!G){ce(new Error(i("app.fastQuery.headerQuery.must")));return}ce()},F=j=>j.some(G=>{var ce;return G.includes("*")?!G.startsWith("*")||(((ce=G.match(/\*/g))==null?void 0:ce.length)??0)>1:!1}),N=j=>j!=null&&j.some(G=>G.length===1)?!0:j.some((G,ce,z)=>ce<z.length-1&&G.length===2&&z[ce+1].length===2),w=(j,G,ce)=>{var De,_e,Le;const z=new RegExp("^(?!\\/)(?!.*\\/\\/)(?!.*[^/]{4})(?!(?:[^/]{2}\\/){2}|[^/]{2}\\/[^/]{2}(?:\\/|$))[a-zA-Z0-9*]{2,3}(?:\\/[a-zA-Z0-9*]{2,3})*$(?<!\\/)"),se=(De=y.value.transitTerminal??"")==null?void 0:De.split("/").filter(Qe=>Qe!==""),re=(Le=(_e=y.value.transitTerminal??"")==null?void 0:_e.replace(/\*/g,""))==null?void 0:Le.split("/").filter(Qe=>Qe!=="");G?re.some(Qe=>Qe.length===3)?z.test(y.value.transitTerminal??"")?N(re)?ce(new Error(i("app.pnrManagement.validate.formatErr"))):F(se)?ce(new Error(i("app.pnrManagement.validate.formatErr"))):ce():ce(new Error(i("app.pnrManagement.validate.formatErr"))):ce(new Error(i("app.pnrManagement.validate.formatErr"))):ce()},A=(j,G,ce)=>{V.value&&!G&&ce(new Error(i("app.fastQuery.headerQuery.must"))),ce()},ae={origin:[{validator:J,trigger:"change"},{validator:K,trigger:"blur"}],destination:[{validator:Q,trigger:"change"},{validator:Z,trigger:"blur"}],departureDate:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"change"}],airlines:[{validator:A,trigger:"change"},{pattern:oo,message:new Error(i("app.fastQuery.headerQuery.correctAirlineNum")),trigger:"change"}],flightNumber:[{pattern:Ea,message:new Error(i("app.avSearch.validateFltNo")),trigger:"change"}],departureDateTime:[{pattern:Pa,message:new Error(i("app.avSearch.timeFormatTip")),trigger:"change"}],transitTerminal:[{validator:w,trigger:"change"}]},ge=async()=>{const{data:j}=await Ai("08100116");ee.value=j.value},he=()=>{var j,G;y.value.carrierFlight=((j=$.value)==null?void 0:j.unshared)??!1,y.value.onlyDirectFlight=((G=$.value)==null?void 0:G.nonstop)??!1},pe=()=>{const j=y.value.origin;y.value.origin=y.value.destination,y.value.destination=j;const G=y.value.originName;y.value.originName=y.value.destName,y.value.destName=G},te=j=>{const G=Ta();return j.getTime()<G.getTime()},xe=j=>{y.value.seamlessOrDa===j?y.value.seamlessOrDa="":y.value.seamlessOrDa=j},me=async(j,G,ce)=>{var se;const z=a.avQueryFromFastQuery?Qt:_.value;return I.setClickFlightSearchFlag(z,!((se=I.flightByDate[z])!=null&&se.clickFlightSearchFlag)),j&&G&&await r({dept:j,arrivel:G}),{...y.value,origin:j,destination:G,departureDate:ue(ce)?ue(ce).format("YYYY-MM-DD"):""}},Ee=(j,G,ce)=>`${j}${G}${ce}`,X=async()=>{var G,ce;T.value="",g.value=!1;let j=y.value.departureDate;if(q.value){j=((G=y.value.departureDate)==null?void 0:G[0])??"";const z=((ce=y.value.departureDate)==null?void 0:ce[1])??"";T.value=`${Ee(y.value.origin,y.value.destination,j)}/${Ee(y.value.destination,y.value.origin,z)}`;const se=await me(y.value.origin,y.value.destination,j);await Fe();const re=await me(y.value.destination,y.value.origin,z);e("searchRoundClick",se,re)}else{const z=await me(y.value.origin,y.value.destination,j);await Fe(),e("searchClick",z)}e("getRoundTripFlag",T.value),g.value=!0},oe=Rt(()=>{var j;(j=Y.value)==null||j.validate(async G=>{G&&(y.value.flightNumber&&(M(!1),y.value.transitTerminal=""),X())})},300),M=j=>{j&&(y.value.flightNumber="",y.value.departureDate=""),y.value.carrierFlight=!1,y.value.lowestPrice=!1,y.value.timeSequence=!1,y.value.onlyDirectFlight=!1,y.value.airlines="",y.value.origin="",y.value.originName="",y.value.destination="",y.value.destName="",y.value.seamlessOrDa="",y.value.departureDateTime="",q.value=!1},U=j=>{const G=j.filter(ce=>$t.test(ce));return y.value.transitTerminal=G.join("/"),j.filter(ce=>ce&&!$t.test(ce))},W=j=>{if(!j)return j;const G=j.match(/^(\d{1,2})([A-Za-z]{3})$/);if(!G)return j;const ce=G[1],z=G[2].toUpperCase();return ce.padStart(2,"0")+z},ne=j=>{let G=j;return G.length>5?Ma(ue(G).format("YYYY-MM-DD"),!0):(G.length<5&&(G=W(G)),Ma(po(G),!0))},Se=j=>j&&(y.value.lowestPrice=j),ke=j=>{const G=j[0].split("").sort();return j[0].length<=4&&G.every(ce=>O.includes(ce))&&(G.includes("E")&&(y.value.timeSequence=!0),G.includes("O")&&(y.value.carrierFlight=!0),Se(G.includes("P")),j.shift()),j},Pe=j=>{let G=Xe(j);M(!0),G=ke(G),G=U(G),G.forEach(ce=>{if(ce==="D"){y.value.onlyDirectFlight=!0;return}if(kn.test(ce)){ce.startsWith("*")?(y.value.seamlessOrDa="DA",y.value.airlines=ce.slice(1)):(y.value.seamlessOrDa="seamless",y.value.airlines=ce);return}if(Fn.test(ce)){y.value.origin=ce.slice(0,3),y.value.destination=ce.slice(3);return}if(Ln.test(ce)){y.value.departureDate=ne(ce);return}Ea.test(ce)&&(y.value.flightNumber=ce),Pa.test(ce)&&(y.value.departureDateTime=ce)})},Ae=()=>{y.value=Ha(),y.value.carrierFlight=!1,y.value.lowestPrice=!1,y.value.timeSequence=!1,c.value=!0,Y.value.resetFields()},Fe=async()=>{const j=await d();(j??[]).length<1||(le.value=(j??[]).map(G=>`${G.dept}-${G.arrivel}`))},He=j=>{I.simEtermIsOpen||j.key==="Enter"&&(p.isOpened?S===Qt:_.value===S)&&oe()},We=async j=>{const G=j.split("-");G.length<2||(n.value=j,y.value.origin=(G==null?void 0:G[0])??"",y.value.destination=(G==null?void 0:G[1])??"",y.value.destName="",y.value.originName="")};return Wt.on(`${Ue.QUERY_MARRIED_FLIGHT}${_.value}`,async j=>{y.value=j,await St(),oe()}),Ke(()=>y.value.onlyDirectFlight,()=>{y.value.onlyDirectFlight?(y.value.transitTerminal="",k.value=!0):k.value=!1}),Ke(()=>y.value.transitTerminal,()=>{y.value.transitTerminal!==""?(y.value.onlyDirectFlight=!1,v.value=!0):v.value=!1}),Ke(()=>y.value.flightNumber,()=>{y.value.flightNumber?(c.value=!1,Y.value.clearValidate("origin"),Y.value.clearValidate("destination")):c.value=!0}),Ke(()=>y.value.seamlessOrDa,async()=>{y.value.seamlessOrDa?V.value=!0:V.value=!1,await Y.value.validateField("airlines")}),Ke([()=>L.value,()=>E.value],async()=>{var j,G;if(L.value&&(E.value??[]).length){const ce=(G=(j=JSON.parse(E.value)[0])==null?void 0:j.replace(/\s+/g," ").slice(3))==null?void 0:G.split("/").filter(z=>z);await Pe(ce),await oe(),C.setCommandsAvCheckSuccess(_.value,!1)}}),Ke(()=>a.historyQueryForm,async j=>{j&&!q.value&&(y.value=Xe(j))},{immediate:!0,deep:!0}),Ke(()=>$.value,j=>{j&&(y.value.carrierFlight=(j==null?void 0:j.unshared)??!1,y.value.onlyDirectFlight=(j==null?void 0:j.nonstop)??!1)}),mt(async()=>{he(),await ge(),await Fe(),window.addEventListener("keydown",He,!0)}),en(()=>{window.addEventListener("keydown",He,!0)}),Et(()=>{Wt.off(`${Ue.QUERY_MARRIED_FLIGHT}${_.value}`),window.removeEventListener("keydown",He,!0)}),tn(()=>{window.removeEventListener("keydown",He,!0)}),{queryFormRef:Y,FORM_RULES:ae,queryForm:y,datePrefix:B,isAshingTransitTerminal:k,onlyDirectFlightAshing:v,agentAirportOriginRef:H,agentAirportDestinationRef:h,revertFromTo:pe,disabledDate:te,seamlessOrDaClick:xe,search:oe,resetClick:Ae,chooseItems:le,roundTripSwitch:q,selectedItem:n,handleSelectItem:We}},La=e=>(Lt("data-v-bd87614f"),e=e(),wt(),e),ol={class:"flex"},nl={class:"flex mr-[10px] self-stretch"},il=La(()=>o("div",{class:"airlines-item bkc-el-input__wrapper"},"*",-1)),rl=[il],sl={class:"inline-flex"},ll={class:"carrier-only-direct"},cl={class:"h-full pt-[2px] flex flex-col items-start mr-2.5"},dl={class:"lowest-price-box flex items-start"},ul={class:"lowest-price-box flex items-start"},pl={class:"flex-col justify-start items-start gap-5 inline-flex ml-[10px]"},gl={class:"inline-flex items-center justify-center gap-1"},fl=La(()=>o("span",{class:"iconfont icon-time-circle2"},null,-1)),ml={class:"text-xs"},hl={key:0,class:"flex items-center"},vl={class:"text-brand-2 text-xs font-normal leading-tight"},yl={key:1,class:"flex items-center"},bl=La(()=>o("div",{class:"w-[19px]"},null,-1)),_l={class:"text-gray-2 text-xs font-normal leading-tight"},Cl=Be({__name:"HeaderAvQuery",props:{flightSearchFlag:{},cityOrAirport:{},historyQueryForm:{},avQueryFromFastQuery:{type:Boolean}},emits:["searchClick","searchRoundClick","isPnrExtraction","getRoundTripFlag"],setup(e,{expose:a,emit:i}){const r=e,d=i,{queryFormRef:n,queryForm:l,FORM_RULES:$,revertFromTo:p,datePrefix:C,resetClick:_,roundTripSwitch:f,seamlessOrDaClick:S,search:I,onlyDirectFlightAshing:L,agentAirportOriginRef:E,agentAirportDestinationRef:g,chooseItems:T,selectedItem:Y,handleSelectItem:H}=al(d,r);return a({search:I}),(h,v)=>{const c=qt,V=je,y=Nt,q=na,O=ra,k=Nn,B=In,ee=En,le=jt;return u(),D("div",ol,[b(le,{ref_key:"queryFormRef",ref:n,class:"header-av-query-form","label-position":"top",model:t(l),rules:t($)},{default:x(()=>[b(c,{class:ve(["agent-airport-item",h.avQueryFromFastQuery?"fast-query-airport-item":"airport-item"]),prop:"origin"},{default:x(()=>[b(Ut,{ref_key:"agentAirportOriginRef",ref:E,modelValue:t(l).origin,"onUpdate:modelValue":v[0]||(v[0]=K=>t(l).origin=K),name:t(l).originName,"onUpdate:name":v[1]||(v[1]=K=>t(l).originName=K),"is-agent-city":h.cityOrAirport,"prefix-title":h.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1},8,["class"]),o("div",{class:"mt-[6px] ml-[2px] mr-[2px] w-[14px] h-[14px] cursor-pointer",onClick:v[2]||(v[2]=(...K)=>t(p)&&t(p)(...K))},[b(V,{class:"sort-text"},{default:x(()=>[b(t(Aa))]),_:1})]),b(c,{class:ve(["agent-airport-item",h.avQueryFromFastQuery?"fast-query-airport-item":"airport-item"]),prop:"destination"},{default:x(()=>[b(Ut,{ref_key:"agentAirportDestinationRef",ref:g,modelValue:t(l).destination,"onUpdate:modelValue":v[3]||(v[3]=K=>t(l).destination=K),name:t(l).destName,"onUpdate:name":v[4]||(v[4]=K=>t(l).destName=K),"is-agent-city":h.cityOrAirport,"prefix-title":h.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1},8,["class"]),b(c,{class:ve(["departure-date",t(f)?"round-trip-date":""]),prop:"departureDate"},{default:x(()=>[b(Sa,{modelValue:t(l).departureDate,"onUpdate:modelValue":v[5]||(v[5]=K=>t(l).departureDate=K),roundTripSwitch:t(f),"onUpdate:roundTripSwitch":v[6]||(v[6]=K=>tt(f)?f.value=K:null),type:t(f)?"daterange":"date","show-switch":"",clearable:!1,"prefix-icon":t(C),placeholder:h.$t("app.avSearch.date")},null,8,["modelValue","roundTripSwitch","type","prefix-icon","placeholder"])]),_:1},8,["class"]),b(c,{prop:"airlines",class:"airlines-input-item"},{default:x(()=>[o("div",nl,[o("div",{class:ve(["airlines-item-box",t(l).seamlessOrDa==="DA"?"radio-selected":"radio-cancel"]),onClick:v[7]||(v[7]=K=>t(S)("DA"))},rl,2),b(Ya,{modelValue:t(l).airlines,"onUpdate:modelValue":v[8]||(v[8]=K=>t(l).airlines=K),modelModifiers:{trim:!0},class:"airlines-input","tip-text":"CA",placeholder:h.$t("app.avSearch.airline")},null,8,["modelValue","placeholder"])])]),_:1}),b(c,{prop:"flightNumber"},{default:x(()=>[b(Ya,{modelValue:t(l).flightNumber,"onUpdate:modelValue":v[9]||(v[9]=K=>t(l).flightNumber=K),modelModifiers:{trim:!0},class:"flightNumber-input","tip-text":"CA1234",placeholder:h.$t("app.avSearch.flightNumber")},null,8,["modelValue","placeholder"])]),_:1}),b(c,{prop:"departureDateTime"},{default:x(()=>[b(y,{modelValue:t(l).departureDateTime,"onUpdate:modelValue":v[10]||(v[10]=K=>t(l).departureDateTime=K),class:"small-width",placeholder:h.$t("app.avSearch.departureTime"),onInput:v[11]||(v[11]=K=>t(l).departureDateTime=t(l).departureDateTime.toUpperCase().trim())},null,8,["modelValue","placeholder"])]),_:1}),b(c,{class:"transit-terminal input-focus-tip",prop:"transitTerminal"},{default:x(()=>[b(Vr,{modelValue:t(l).transitTerminal,"onUpdate:modelValue":v[12]||(v[12]=K=>t(l).transitTerminal=K),modelModifiers:{trim:!0},"is-agent-city":h.cityOrAirport},null,8,["modelValue","is-agent-city"])]),_:1}),b(c,null,{default:x(()=>[o("div",sl,[o("div",ll,[b(q,{modelValue:t(l).carrierFlight,"onUpdate:modelValue":v[13]||(v[13]=K=>t(l).carrierFlight=K)},{default:x(()=>[fe(m(h.$t("app.avSearch.nonSharedFlights")),1)]),_:1},8,["modelValue"]),b(q,{modelValue:t(l).onlyDirectFlight,"onUpdate:modelValue":v[14]||(v[14]=K=>t(l).onlyDirectFlight=K),disabled:t(L)},{default:x(()=>[fe(m(h.$t("app.avSearch.onlyFight")),1)]),_:1},8,["modelValue","disabled"])])])]),_:1}),b(c,null,{default:x(()=>[o("div",cl,[o("div",dl,[b(q,{modelValue:t(l).lowestPrice,"onUpdate:modelValue":v[15]||(v[15]=K=>t(l).lowestPrice=K)},{default:x(()=>[fe(m(h.$t("app.avSearch.lowestPrice")),1)]),_:1},8,["modelValue"])]),o("div",ul,[b(q,{modelValue:t(l).timeSequence,"onUpdate:modelValue":v[16]||(v[16]=K=>t(l).timeSequence=K)},{default:x(()=>[fe(m(h.$t("app.avSearch.timeSequence")),1)]),_:1},8,["modelValue"])])])]),_:1}),b(c,null,{default:x(()=>[b(O,{type:"primary",onClick:t(I)},{default:x(()=>[fe(m(h.$t("app.avSearch.searchText")),1)]),_:1},8,["onClick"])]),_:1}),b(c,null,{default:x(()=>[b(O,{class:"ml-[8px]",onClick:t(_)},{default:x(()=>[fe(m(h.$t("app.fastQuery.headerQuery.reset")),1)]),_:1},8,["onClick"])]),_:1}),h.avQueryFromFastQuery?de("",!0):(u(),Ce(c,{key:0},{default:x(()=>[o("div",pl,[o("div",gl,[b(ee,{"popper-class":"av-history-popper",onCommand:t(H)},{dropdown:x(()=>[b(B,null,{default:x(()=>[(u(!0),D(be,null,we(t(T),(K,J)=>(u(),Ce(k,{key:J,command:K},{default:x(()=>[t(Y)===K?(u(),D("span",hl,[b(V,{class:"bg-inherit !text-brand-2 !text-[14px]"},{default:x(()=>[b(t(an))]),_:1}),o("span",vl,m(K),1)])):(u(),D("span",yl,[bl,o("span",_l,m(K),1)]))]),_:2},1032,["command"]))),128))]),_:1})]),default:x(()=>[b(O,{link:"",type:"primary",class:"service-book-btn"},{default:x(()=>[fl,o("span",ml,m(h.$t("app.fastQuery.headerQuery.historyQuery")),1)]),_:1})]),_:1},8,["onCommand"])])])]),_:1}))]),_:1},8,["model","rules"])])}}});const Al=ze(Cl,[["__scopeId","data-v-bd87614f"]]),Dl=(e,a)=>{const i=ca(),r=nt([]),{activeTag:d}=It(),n=e.avQueryFromFastQuery?Qt:d,l=Re(()=>{var g;return((g=i.flightByDate[n])==null?void 0:g.activeFlightIndex)??0}),$=Re(()=>{const g=i.flightByDate[n].flightDataList;return r.value=_(g),g}),p=Re(()=>e.roundTripFlag??""),C=(g,T)=>{let Y=g;if(Y.includes(T[1])){const H=Y.filter(h=>h!==T[1]);Y=[T[1],...H]}if(Y.includes(T[0])){const H=Y.filter(h=>h!==T[0]);Y=[T[0],...H]}return Y},_=g=>{var h,v;const T=[],Y=p.value?(v=((h=p.value)==null?void 0:h.split("/"))??[])==null?void 0:v.filter(c=>c.trim()!==""):[];return(p.value?C([...g.keys()],Y):[...g.keys()]).forEach(c=>{var y;const V={};V.departureAirportCode=c.substring(0,3),V.arrivalAirportCode=c.substring(3,6),V.departureDate=c.substring(6),V.firstQueryDate=(y=g.get(c))==null?void 0:y.firstQueryDate,T.push(V)}),T},f=g=>`${g.departureAirportCode??""}${g.arrivalAirportCode??""}${g.departureDate??""}`,S=ya((g,T)=>{l.value!==T&&(i.setActiveFlightIndex(n,T),a("queryData",f(g)))},100,{trailing:!1}),I=g=>{const T=r.value[g],Y=f(T),H=[...$.value.keys()][l.value];i.delFlightDataList(n,Y);const h=[...$.value.keys()];if(h.length>=1)if(l.value===g)i.setActiveFlightIndex(n,0),a("queryData",h[0]);else{const v=h.findIndex(c=>c===H);i.setActiveFlightIndex(n,v)}else a("queryData","");a("closeAVHistory",`${T.departureAirportCode}${T.arrivalAirportCode}${(T==null?void 0:T.firstQueryDate)??""}`)},L=(g,T)=>{const Y=V=>ue(V).isValid()?ue(V).format("YYYY-MM-DD"):"",H=(V,y,q)=>{const O=Y(q);return`${V??""}${y??""}${O}`},h=H(g.departureAirportCode,g.arrivalAirportCode,T),v=r.value.findIndex(V=>h===H(V.departureAirportCode,V.arrivalAirportCode,V.departureDate));if(v>-1){S(r.value[v],v);return}const c=f(g);a("callQueryApi",c,Object.assign({},g,{departureDate:Y(T)}))},E=()=>{var T;const g=(T=i.flightByDate[n])==null?void 0:T.flightDataList;g&&(r.value=_(g),r.value.length&&a("queryData",f(r.value[l.value])))};return Ke(()=>e.searchCompleteKey,()=>{if(!e.searchCompleteKey)return;const g=e.searchCompleteKey.split("+")[0],T=[...$.value.keys()].indexOf(g);i.setActiveFlightIndex(n,T),T<0&&i.setActiveFlightIndex(n,r.value.length)},{immediate:!0}),mt(()=>{E()}),Et(()=>{r.value.length=0}),{flightInfos:r,activeFlightIndex:l,flightClick:S,changeDate:L,clickClose:I}},$l=(e,a)=>{const i=P(a.segFlight.departureDate??""),r=nt({render(){return gt("em",{class:"iconfont icon-calendar"})}}),d=Re(()=>a.activeFlightIndex===a.segIndex?a.segFlight.firstQueryDate===a.segFlight.departureDate?"active-blue-box":"active-red-box":""),n=()=>{e("changeDate",i.value),i.value=a.segFlight.departureDate},l=C=>ue(C).isBefore(ue(new Date),"date"),$=C=>ue(C).isValid()?ue(C).format("MM-DD"):"",p=()=>{e("clickClose",a.segIndex)};return Ke(()=>a.segFlight.departureDate,()=>{i.value=a.segFlight.departureDate}),{clickClose:p,datePrefix:r,changeDate:n,activeStyle:d,formatDate:$,departureDate:i,disabledDate:l}},xl=e=>(Lt("data-v-78242d0d"),e=e(),wt(),e),Sl={class:"flex"},Tl={class:"flex font-bold text-[14px] text-[#676767] code"},kl=xl(()=>o("span",null,"-",-1)),Fl={class:"flex items-center text-[12px] text-[#8C8C8C] font-normal"},Ll={class:"date-text mx-1.5 whitespace-nowrap"},wl=Be({__name:"FlightInfo",props:{activeFlightIndex:{},segIndex:{},segFlight:{}},emits:["clickClose","changeDate"],setup(e,{emit:a}){const i=e,r=a,{clickClose:d,changeDate:n,activeStyle:l,departureDate:$,datePrefix:p,formatDate:C,disabledDate:_}=$l(r,i);return(f,S)=>{const I=go,L=je;return u(),D("div",null,[o("div",{class:ve(["flex justify-between items-center mr-[10px] pl-[10px] pr-[10px] h-[32px] border border-[#D9D9D9] rounded cursor-pointer",t(l)])},[o("div",Sl,[o("div",Tl,[o("div",null,m(f.segFlight.departureAirportCode??""),1),kl,o("div",null,m(f.segFlight.arrivalAirportCode??""),1)]),o("div",Fl,[o("div",Ll,m(t(C)(f.segFlight.departureDate)),1),o("div",{class:"mr-[2px] h-[15px] date-icon flex items-center justify-center",onClick:S[1]||(S[1]=Mt(()=>{},["stop"]))},[b(I,{modelValue:t($),"onUpdate:modelValue":S[0]||(S[0]=E=>tt($)?$.value=E:null),"prefix-icon":t(p),type:"date",clearable:!1,disabled:f.activeFlightIndex!==f.segIndex,editable:!1,"disabled-date":t(_),format:"YYYY-MM-DD",onChange:t(n)},null,8,["modelValue","prefix-icon","disabled","disabled-date","onChange"])])])]),o("div",{class:"flex items-center",onClick:S[2]||(S[2]=Mt((...E)=>t(d)&&t(d)(...E),["stop"]))},[b(L,null,{default:x(()=>[b(t(to),{class:"text-[#676767]"})]),_:1})])],2)])}}});const Nl=ze(wl,[["__scopeId","data-v-78242d0d"]]),Il={key:0},El={class:"flex"},Pl=Be({__name:"AvHistory",props:{roundTripFlag:{},searchCompleteKey:{},queryForm:{},avQueryFromFastQuery:{type:Boolean}},emits:["queryData","callQueryApi","closeAVHistory"],setup(e,{emit:a}){const i=e,r=a,{flightInfos:d,flightClick:n,activeFlightIndex:l,clickClose:$,changeDate:p}=Dl(i,r);return(C,_)=>{const f=fo;return t(d).length>0?(u(),D("div",Il,[b(f,null,{default:x(()=>[o("div",El,[(u(!0),D(be,null,we(t(d),(S,I)=>(u(),Ce(Nl,{key:I,"seg-flight":S,"seg-index":I,"active-flight-index":t(l),onClick:L=>t(n)(S,I),onChangeDate:L=>t(p)(S,L),onClickClose:t($)},null,8,["seg-flight","seg-index","active-flight-index","onClick","onChangeDate","onClickClose"]))),128))])]),_:1})])):de("",!0)}}}),Ao="confirm",_a=[{startTime:"00:00",endTime:"06:00",label:"time_0"},{startTime:"06:00",endTime:"12:00",label:"time_6"},{startTime:"12:00",endTime:"18:00",label:"time_12"},{startTime:"18:00",endTime:"24:00",label:"time_18"}],qa=(e,a)=>{e.sort((i,r)=>{let d=0,n=0;return i.segments.forEach(l=>{d+=Number(l.flightTime.replaceAll(":",""))}),r.segments.forEach(l=>{n+=Number(l.flightTime.replaceAll(":",""))}),a?d-n:n-d})},Do=(e,a,i)=>{const r=ue(new Date).format("YYYY-MM-DD"),d=`${r} ${e}`,n=`${r} ${a}`,l=`${r} ${i}`;return(ue(d).isSame(ue(l))||ue(l).isAfter(ue(d)))&&ue(l).isBefore(ue(n))},$o=(e,a,i)=>{var r;if((r=e.segments)!=null&&r.length){const d=e.segments[0].departureTime;return Do(a,i,d)}return!1},xo=(e,a,i)=>{var r;if((r=e.segments)!=null&&r.length){const d=e.segments[e.segments.length-1].arrivalTime;return Do(a,i,d)}return!1},Ol=(e,a)=>{let i=[];if(e.filterDepartureTime.length){const r=_a.filter(d=>e.filterDepartureTime.includes(d.label));i=a.filter(d=>r.some(n=>$o(d,n.startTime,n.endTime)))}else i=a;if(e.filterArrivalTime.length){const r=_a.filter(d=>e.filterArrivalTime.includes(d.label));i=i.filter(d=>r.some(n=>xo(d,n.startTime,n.endTime)))}return i},Ql=(e,a)=>{const{t:i}=ct(),r=P(!1),d=nt(ma),n=P(ma),l=P([]),$=P([]),p=P({filterAirlines:""}),C={filterAirlines:[{pattern:Va,message:new Error(i("app.avHistoryFlightCondition.enterAirlineCode")),trigger:"change"}]},_=P([]),f=P(!1),S=P([]),I=P({label:i("app.avHistoryFlightCondition.airport"),screenPopoverData:[]}),L=P({label:i("app.avHistoryFlightCondition.timeDepartureAndArrival"),screenPopoverData:[]}),E=P(!1),g=0,T=1,Y=i("app.avHistoryFlightCondition.departureAirport"),H=i("app.avHistoryFlightCondition.arrivalAirport"),h=i("app.avHistoryFlightCondition.departureTime"),v=i("app.avHistoryFlightCondition.arrivelTime"),c=va({sortType:-1,ascNum:-1});let V={};const y=()=>{var U,W;const M=[];if(((U=e.moreDeparture)==null?void 0:U.length)>1){const ne=e.moreDeparture.map(Se=>({label:Se,value:Se}));M.push({label:Y,codes:ne,checked:[]})}if(((W=e.moreArrival)==null?void 0:W.length)>1){const ne=e.moreArrival.map(Se=>({label:Se,value:Se}));M.push({label:H,codes:ne,checked:[]})}return M},q=M=>{const U=[],W=[];return _a.forEach(ne=>{const Se=M==null?void 0:M.find(Pe=>$o(Pe,ne.startTime,ne.endTime)),ke=M==null?void 0:M.find(Pe=>xo(Pe,ne.startTime,ne.endTime));Se&&U.push(ne.label),ke&&W.push(ne.label)}),{departureFlightTimeCodes:U,arrivalFlightTimeCodes:W}},O=()=>{const M=[],{departureFlightTimeCodes:U,arrivalFlightTimeCodes:W}=q(e.avSearchData),ne=Se=>Se.map(ke=>({label:i(`app.avHistoryFlightCondition.${ke}`),value:ke}));return U.length>1&&M.push({label:h,codes:ne(U),checked:[]}),W.length>1&&M.push({label:v,codes:ne(W),checked:[]}),M},k=()=>ue().format("YYYY-MM-DD"),B=()=>{l.value=V.filterCondition},ee=()=>{_.value=V.transferTimes},le=()=>{var M,U,W,ne,Se,ke,Pe,Ae;V={filterCondition:l.value,sortTypeNum:c.ascNum,filterDeparture:((U=(M=I.value.screenPopoverData)==null?void 0:M.find(Fe=>Fe.label===Y))==null?void 0:U.checked)??[],filterArrival:((ne=(W=I.value.screenPopoverData)==null?void 0:W.find(Fe=>Fe.label===H))==null?void 0:ne.checked)??[],transferTimes:_.value,filterDepartureTime:((ke=(Se=L.value.screenPopoverData)==null?void 0:Se.find(Fe=>Fe.label===h))==null?void 0:ke.checked)??[],filterArrivalTime:((Ae=(Pe=L.value.screenPopoverData)==null?void 0:Pe.find(Fe=>Fe.label===v))==null?void 0:Ae.checked)??[]},pe(),a("sortFlight",V)},K=()=>{r.value=!1,f.value=!1,le()},J=()=>{l.value=[],$.value=[],p.value.filterAirlines="",K()},Z=()=>{_.value=[],K()},Q=M=>{I.value=M,K()},F=M=>{L.value=M,K()},N=M=>ma.filter(U=>M.includes(U.code)),w=()=>{const M=[];(e.avSearchData??[]).forEach(U=>{var W,ne;(W=U.segments)!=null&&W.length&&((ne=U.flyType)==null||ne.forEach(Se=>M.includes(Se)||M.push(Se)))}),S.value=[...M].sort((U,W)=>U===Ot.STOP?1:W===Ot.STOP?-1:U-W).map(U=>({label:U.toString(),value:U.toString()}))},A=M=>M===Ot.STOP.toString()?i("app.avHistoryFlightCondition.transferTimes_stop"):i(`app.avHistoryFlightCondition.transferTimes_${M}`),ae=(M,U)=>{var W;(W=M.flyType)==null||W.forEach(ne=>U.includes(ne)||U.push(ne))},ge=()=>{const M=[],U=[],W=[],ne=[];e.currentFlightList.map(Ae=>{Ae.segments.map(He=>{var We,j,G;M.includes((We=He.airlines)==null?void 0:We.airCode)||(j=He.airlines)!=null&&j.airCode&&M.push((G=He.airlines)==null?void 0:G.airCode)});const Fe=Ae.segments;if(Fe.length){ae(Ae,U);const He=Fe[0],We=Fe[Fe.length-1];W.includes(He.departureAirportCode)||W.push(He.departureAirportCode??""),ne.includes(We.arrivalAirportCode)||ne.push(We.arrivalAirportCode??"")}});const{departureFlightTimeCodes:Se,arrivalFlightTimeCodes:ke}=q(e.currentFlightList);n.value.forEach(Ae=>Ae.disabled=!l.value.includes(Ae.code)&&!M.includes(Ae.code)),S.value.forEach(Ae=>Ae.disabled=!_.value.includes(Ae.value)&&!U.includes(Number(Ae.value)));const Pe=(Ae,Fe)=>{Ae.codes.forEach(He=>He.disabled=!Ae.checked.includes(He.value)&&!Fe.includes(He.value))};L.value.screenPopoverData.forEach(Ae=>{Ae.label===h&&Pe(Ae,Se),Ae.label===v&&Pe(Ae,ke)}),I.value.screenPopoverData.forEach(Ae=>{Ae.label===Y&&Pe(Ae,W),Ae.label===H&&Pe(Ae,ne)})},he=()=>{_.value=[],e.queryFormParams.onlyDirectFlight&&_.value.push("0"),w(),d.value=e.airlines&&e.airlines.length?N(e.airlines):[],n.value=e.airlines&&e.airlines.length?N(e.airlines):[],l.value=[],$.value=[],c.sortType=-1,c.ascNum=-1,I.value.screenPopoverData=y(),L.value.screenPopoverData=O()},pe=()=>{$.value=N(l.value)},te=M=>{const U=e.queryResDateGroupBySessionId.get(`${e.queryFormParams.origin}${e.queryFormParams.destination}${e.queryFormParams.departureDate}`)||e.queryFormParams.departureDate;return!(M==="pre"&&U===k())},xe=M=>{a("changeDate",M)},me=M=>{if(E.value=!0,c.sortType=M,M)switch(c.ascNum){case 2:c.ascNum=3;break;case 3:c.ascNum=-1;break;default:c.ascNum=2;break}else switch(c.ascNum){case 0:c.ascNum=1;break;case 1:c.ascNum=-1;break;default:c.ascNum=0;break}le()},Ee=()=>{var M,U,W,ne,Se,ke,Pe,Ae;V={filterCondition:l.value,sortTypeNum:c.ascNum,filterDeparture:((U=(M=I.value.screenPopoverData)==null?void 0:M.find(Fe=>Fe.label===Y))==null?void 0:U.checked)??[],filterArrival:((ne=(W=I.value.screenPopoverData)==null?void 0:W.find(Fe=>Fe.label===H))==null?void 0:ne.checked)??[],transferTimes:_.value,filterDepartureTime:((ke=(Se=L.value.screenPopoverData)==null?void 0:Se.find(Fe=>Fe.label===h))==null?void 0:ke.checked)??[],filterArrivalTime:((Ae=(Pe=L.value.screenPopoverData)==null?void 0:Pe.find(Fe=>Fe.label===v))==null?void 0:Ae.checked)??[]}},X=M=>{p.value.filterAirlines=M==null?void 0:M.toUpperCase(),Va.test(M)&&(n.value=d.value.filter(U=>U.code.includes(M==null?void 0:M.toUpperCase())),l.value=[])},oe=()=>{p.value.filterAirlines="",n.value=d.value};return mt(()=>{he(),Ee()}),Ke(()=>e.airlines,()=>{he(),Ee()}),Ke(()=>e.updateFlightListFlag,()=>{ge()}),Et(()=>{d.value.length=0,n.value.length=0,l.value.length=0,$.value.length=0}),{isAllowChangeDate:te,changeDate:xe,popoverVisible:r,listSort:c,canChooseAirlines:d,checkedAirlines:l,filterForm:p,FORM_RULES:C,tipAirlinesObj:$,FLY_TIME_SORT:T,DEPARTURE_TIME_SORT:g,openPopover:B,sortClick:me,checkAirlines:pe,reset:J,screeningAirlines:K,isPermitSortByTime:E,confirmAirportDepartureAndArrival:Q,confirmTimeDepartureAndArrival:F,airportDepartureAndArrival:I,timeDepartureAndArrival:L,popoverTransferTimes:f,openPopoverTransferTimes:ee,resetPopoverTransferTimes:Z,canChooseTransferTimes:S,checkedTransferTimes:_,changeFilterAirlines:X,filterCanChooseAirlines:n,getTransferTimes:A,showFilterPopver:oe}},Rl={class:"text-[12px] text-brand-2 cursor-pointer ml-4 whitespace-nowrap"},Ml={class:"popover-airlines"},Vl={class:"airport-title text-[12px] text-gray-1 font-normal mb-[5px]"},Bl=["textContent"],Ul={class:"button-operator"},Yl=Be({__name:"ScreenPopover",props:{screenPopover:{},width:{}},emits:["confirm"],setup(e,{emit:a}){const i=e,r=a,d=P(!1),n=P(i.screenPopover),l=()=>{n.value=Xe(i.screenPopover)},$=()=>{d.value=!1},p=()=>{$(),r("confirm",n.value)},C=()=>{n.value.screenPopoverData.forEach(_=>_.checked=[]),$(),r("confirm",n.value)};return(_,f)=>{const S=je,I=na,L=uo,E=Dt;return u(),Ce(E,{visible:d.value,"onUpdate:visible":f[0]||(f[0]=g=>d.value=g),trigger:"click",placement:"bottom-start","show-arrow":!1,width:_.width,"popper-class":"airport-popper"},{reference:x(()=>[o("div",Rl,[o("span",{onClick:l},m(n.value.label),1),d.value?(u(),Ce(S,{key:1,class:"ml-[4px]"},{default:x(()=>[b(t(Vt))]),_:1})):(u(),Ce(S,{key:0,class:"ml-[4px]",onClick:l},{default:x(()=>[b(t(Jt))]),_:1}))])]),default:x(()=>[o("div",Ml,[(u(!0),D(be,null,we(n.value.screenPopoverData,(g,T)=>(u(),D("div",{key:g.label,class:ve(["popover-airport py-[8px]",T?"border-t border-gray-6":""])},[o("div",Vl,m(g.label),1),b(L,{modelValue:g.checked,"onUpdate:modelValue":Y=>g.checked=Y},{default:x(()=>[(u(!0),D(be,null,we(g.codes,(Y,H)=>(u(),Ce(I,{key:H,label:Y.value,disabled:Y.disabled},{default:x(()=>[o("span",{class:"code-label inline-block w-[22px] text-xs text-gray-2",textContent:m(Y.label)},null,8,Bl)]),_:2},1032,["label","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])],2))),128)),o("div",Ul,[o("div",null,[o("span",{onClick:p},m(_.$t("app.avHistoryFlightCondition.sure")),1),o("span",{onClick:C},m(_.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible","width"])}}});const ja=ze(Yl,[["__scopeId","data-v-283a3918"]]),Hl={class:"mr-[6px] h-[40px] flex items-end"},ql={class:"flex justify-between items-center h-[32px] px-2.5 w-full border-[1px] border-[#EBF2FF] rounded"},jl={class:"flex items-center h-[24px]"},Gl={class:"mr-[14px] text-[12px] font-normal text-[#595959]"},Kl={class:"flex mr-[14px]"},zl={class:"text-[12px] text-brand-2 cursor-pointer mr-4 whitespace-nowrap"},Jl={class:"popover-airport"},Wl={class:"code-label inline-block text-xs font-normal text-gray-2"},Zl={class:"button-operator"},Xl={class:"text-[12px] text-brand-2 cursor-pointer whitespace-nowrap"},ec={key:0},tc={class:"flex justify-center items-center flex-wrap w-[300px] text-[13px]"},ac={class:"popover-airlines"},oc={class:"w-[13px] h-[14px]","aria-hidden":"true"},nc=["xlink:href"],ic=["textContent"],rc=["textContent"],sc={class:"button-operator"},lc={class:"flex items-center h-[24px] whitespace-nowrap"},cc={class:"ml-[4px] flex flex-col sort-box"},dc={class:"ml-[4px] flex flex-col sort-box"},uc=Be({__name:"FlightCondition",props:{avSearchData:{},airlines:{},queryFormParams:{},queryResDateGroupBySessionId:{},moreDeparture:{},moreArrival:{},currentFlightList:{},updateFlightListFlag:{type:Boolean}},emits:["screeningDirect","sortFlight","changeDate","isAllowChangeDate"],setup(e,{emit:a}){const i=e,r=a,{popoverVisible:d,listSort:n,filterCanChooseAirlines:l,checkedAirlines:$,filterForm:p,FORM_RULES:C,tipAirlinesObj:_,FLY_TIME_SORT:f,DEPARTURE_TIME_SORT:S,isPermitSortByTime:I,isAllowChangeDate:L,changeDate:E,sortClick:g,openPopover:T,reset:Y,screeningAirlines:H,airportDepartureAndArrival:h,timeDepartureAndArrival:v,confirmAirportDepartureAndArrival:c,confirmTimeDepartureAndArrival:V,popoverTransferTimes:y,openPopoverTransferTimes:q,resetPopoverTransferTimes:O,canChooseTransferTimes:k,checkedTransferTimes:B,getTransferTimes:ee,changeFilterAirlines:le,showFilterPopver:K}=Ql(i,r);return(J,Z)=>{var ge,he,pe;const Q=uo,F=vt,N=Nt,w=qt,A=jt,ae=Pn;return u(),D("div",Hl,[o("div",ql,[o("div",jl,[o("span",Gl,m(J.$t("app.avHistoryFlightCondition.total",{total:((ge=J.avSearchData)==null?void 0:ge.length)??0})),1),o("div",Kl,[o("span",{class:ve(["mr-[10px] operate",{"disable-to-operate":!t(L)("pre")}]),"data-gid":"01010209",onClick:Z[0]||(Z[0]=te=>t(E)("pre"))},m(J.$t("app.avHistoryFlightCondition.previousDay")),3),o("span",{class:ve(["operate",{"disable-to-operate":!t(L)("next")}]),"data-gid":"01010210",onClick:Z[1]||(Z[1]=te=>t(E)("next"))},m(J.$t("app.avHistoryFlightCondition.nextDay")),3)]),b(t(Dt),{visible:t(y),"onUpdate:visible":Z[6]||(Z[6]=te=>tt(y)?y.value=te:null),trigger:"click",placement:"bottom-start","show-arrow":!1,"popper-class":"airport-popper-av transfer-proper"},{reference:x(()=>[o("div",zl,[o("span",{onClick:Z[2]||(Z[2]=(...te)=>t(q)&&t(q)(...te))},m(J.$t("app.avHistoryFlightCondition.directFlight")),1),t(y)?(u(),Ce(t(je),{key:1,class:"ml-[4px]"},{default:x(()=>[b(t(Vt))]),_:1})):(u(),Ce(t(je),{key:0,class:"ml-[4px]",onClick:t(q)},{default:x(()=>[b(t(Jt))]),_:1},8,["onClick"]))])]),default:x(()=>[o("div",Jl,[b(Q,{modelValue:t(B),"onUpdate:modelValue":Z[3]||(Z[3]=te=>tt(B)?B.value=te:null),class:"flex flex-col"},{default:x(()=>[(u(!0),D(be,null,we(t(k),te=>(u(),Ce(t(na),{key:te.value,label:te.value,disabled:te.disabled},{default:x(()=>[o("span",Wl,m(t(ee)(te.value)),1)]),_:2},1032,["label","disabled"]))),128))]),_:1},8,["modelValue"]),o("div",Zl,[o("div",null,[o("span",{onClick:Z[4]||(Z[4]=(...te)=>t(H)&&t(H)(...te))},m(J.$t("app.avHistoryFlightCondition.sure")),1),o("span",{onClick:Z[5]||(Z[5]=(...te)=>t(O)&&t(O)(...te))},m(J.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible"]),b(t(Dt),{visible:t(d),"onUpdate:visible":Z[13]||(Z[13]=te=>tt(d)?d.value=te:null),trigger:"click",placement:"bottom-start","show-arrow":!1,width:500,onShow:t(K)},{reference:x(()=>[o("div",Xl,[t(_).length>0?(u(),D("span",ec,[b(F,{effect:"dark","popper-class":"airlineTip",placement:"bottom"},{content:x(()=>[o("div",tc,[(u(!0),D(be,null,we(t(_),te=>(u(),D("span",{key:te.code,class:"flex items-center w-[40%] p-[7px]"},[o("span",{class:ve([["iconfont",te.icon],"inline-block mr-[10px]"])},null,2),fe(" "+m(te.code)+"-"+m(te.name),1)]))),128))])]),default:x(()=>[o("span",{onClick:Z[7]||(Z[7]=(...te)=>t(T)&&t(T)(...te))},m(J.$t("app.avHistoryFlightCondition.airlineScreening"))+" "+m(t(_).length??""),1)]),_:1})])):(u(),D("span",{key:1,onClick:Z[8]||(Z[8]=(...te)=>t(T)&&t(T)(...te))},m(J.$t("app.avHistoryFlightCondition.airlineScreening")),1)),t(d)?(u(),Ce(t(je),{key:3,class:"ml-[4px]"},{default:x(()=>[b(t(Vt))]),_:1})):(u(),Ce(t(je),{key:2,class:"ml-[4px]",onClick:t(T)},{default:x(()=>[b(t(Jt))]),_:1},8,["onClick"]))])]),default:x(()=>[o("div",ac,[o("div",null,[b(A,{ref:"filterFormRef",class:"header-av-query-form","label-position":"top",model:t(p),rules:t(C)},{default:x(()=>[b(w,{prop:"filterAirlines"},{default:x(()=>[b(N,{modelValue:t(p).filterAirlines,"onUpdate:modelValue":Z[9]||(Z[9]=te=>t(p).filterAirlines=te),placeholder:J.$t("app.avHistoryFlightCondition.supportInput"),onInput:t(le)},null,8,["modelValue","placeholder","onInput"])]),_:1})]),_:1},8,["model","rules"])]),b(Q,{modelValue:t($),"onUpdate:modelValue":Z[10]||(Z[10]=te=>tt($)?$.value=te:null)},{default:x(()=>[(u(!0),D(be,null,we(t(l),te=>(u(),Ce(t(na),{key:te.code,label:te.code,disabled:te.disabled},{default:x(()=>[(u(),D("svg",oc,[o("use",{"xlink:href":te.logo},null,8,nc)])),te.name.length>4?(u(),Ce(F,{key:0,effect:"dark",content:te.name,placement:"top"},{default:x(()=>[o("span",{class:"inline-block ml-[4px] w-[160px] truncate",textContent:m(`${te.code}-${te.name}`)},null,8,ic)]),_:2},1032,["content"])):(u(),D("span",{key:1,class:"inline-block ml-[4px] w-[160px]",textContent:m(`${te.code}-${te.name}`)},null,8,rc))]),_:2},1032,["label","disabled"]))),128))]),_:1},8,["modelValue"]),o("div",sc,[o("div",null,[o("span",{onClick:Z[11]||(Z[11]=(...te)=>t(H)&&t(H)(...te))},m(J.$t("app.avHistoryFlightCondition.sure")),1),o("span",{onClick:Z[12]||(Z[12]=(...te)=>t(Y)&&t(Y)(...te))},m(J.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible","onShow"]),(he=t(v).screenPopoverData)!=null&&he.length?(u(),Ce(ja,{key:0,"screen-popover":t(v),width:120,onConfirm:t(V)},null,8,["screen-popover","onConfirm"])):de("",!0),(pe=t(h).screenPopoverData)!=null&&pe.length?(u(),Ce(ja,{key:1,"screen-popover":t(h),width:80,onConfirm:t(c)},null,8,["screen-popover","onConfirm"])):de("",!0)]),o("div",lc,[b(ae,{underline:!1,class:"mr-[14px]",onClick:Z[14]||(Z[14]=te=>t(g)(t(S)))},{default:x(()=>[o("span",{class:ve(["text-[12px]",t(n).sortType===0&&t(I)?"sort-active":""])},m(J.$t("app.avHistoryFlightCondition.departureTime")),3),o("div",cc,[b(t(je),null,{default:x(()=>[b(t(Oa),{class:ve(["h-[6px] leading-[6px]",t(n).ascNum===0?"text-brand-2":"text-gray-5"])},null,8,["class"])]),_:1}),b(t(je),null,{default:x(()=>[b(t(Qa),{class:ve(["h-[6px] leading-[6px]",t(n).ascNum===1?"text-brand-2":"text-gray-5"])},null,8,["class"])]),_:1})])]),_:1}),b(ae,{underline:!1,onClick:Z[15]||(Z[15]=te=>t(g)(t(f)))},{default:x(()=>[o("span",{class:ve(["text-[12px]",t(n).sortType===1&&t(I)?"sort-active":""])},m(J.$t("app.avHistoryFlightCondition.flyTime")),3),o("div",dc,[b(t(je),null,{default:x(()=>[b(t(Oa),{class:ve(t(n).ascNum===2?"text-brand-2":"text-gray-5")},null,8,["class"])]),_:1}),b(t(je),null,{default:x(()=>[b(t(Qa),{class:ve(t(n).ascNum===3?"text-brand-2":"text-gray-5")},null,8,["class"])]),_:1})])]),_:1})])])])}}});const pc=ze(uc,[["__scopeId","data-v-768517f4"]]),gc=(e,a)=>{const{t:i}=ct(),r=P(!1),d=P(i("app.avSearch.quantityBooked")),n=nt([]);return{bookedNumStr:d,bookedTypeList:n,queryBookedType:async()=>{const $={flightNo:e.flightNo,option:"INFT,AVIH,WCHC,UMNR",departureDate:e!=null&&e.departureDate?ue(e.departureDate).format("YYYY-MM-DD"):"",departureTime:e.departureDate?ue(e.departureDate).format("HHmm"):""};try{r.value=!1,a("changeLoading",!0);const{data:p}=await Di($,"01020108"),C=(p==null?void 0:p.value)??[];n.value=C.map(_=>{var f;return{name:_.option,counts:((f=_==null?void 0:_.quotaNumber)==null?void 0:f.split(" "))??["-"]}}),r.value=!0}catch{n.value=[]}finally{a("changeLoading",!1)}},popoverVisible:r}},fc={class:"text-gray-2 text-xs font-normal leading-tight"},mc={class:"text-center text-gray-3 text-xs font-normal leading-none"},hc={class:"text-brand-2 text-xs font-normal leading-tight"},vc=Be({__name:"BookedPopover",props:{flightNo:{},departureDate:{}},emits:["changeLoading"],setup(e,{emit:a}){const i=e,r=a,{bookedNumStr:d,bookedTypeList:n,popoverVisible:l,queryBookedType:$}=gc(i,r);return(p,C)=>{const _=je,f=Dt;return u(),Ce(f,{visible:t(l),"onUpdate:visible":C[1]||(C[1]=S=>tt(l)?l.value=S:null),trigger:"hover","show-arrow":!1,disabled:t(n).length<=0,"popper-class":"booked-proper"},{reference:x(()=>[o("div",{class:ve(["w-[84px] h-5 px-0.5 rounded-sm justify-end items-center gap-1 inline-flex cursor-pointer",t(n).length>0?"bg-yellow-2":""]),onClick:C[0]||(C[0]=(...S)=>t($)&&t($)(...S))},[o("span",hc,m(t(d)),1),t(l)?(u(),Ce(_,{key:1,class:"icon-drop"},{default:x(()=>[b(t(Vt))]),_:1})):(u(),Ce(_,{key:0,class:"icon-drop"},{default:x(()=>[b(t(Jt))]),_:1}))],2)]),default:x(()=>[(u(!0),D(be,null,we(t(n),(S,I)=>(u(),D("div",{key:I,class:"self-stretch px-1.5 py-1 justify-start items-center gap-1.5 inline-flex"},[o("span",fc,m(p.$t(`app.avSearch.${S.name}`)),1),(u(!0),D(be,null,we(S.counts,(L,E)=>(u(),D("span",{key:E,class:"h-4 px-1 bg-gray-7 rounded-sm justify-center items-center inline-flex"},[o("span",mc,m(L),1)]))),128))]))),128))]),_:1},8,["visible","disabled"])}}});const yc=ze(vc,[["__scopeId","data-v-8a886d53"]]),So=Be({__name:"CopyButton",props:{segInfo:{},stopData:{}},setup(e){const a=e,{copy:i,isSupported:r}=on({legacy:!0}),{t:d}=ct(),n=C=>C?Number(C):0,l=C=>C?` ${C.replace(":","")}`:"",$=(C=[])=>{let _=`
`;return C.forEach((f,S)=>{let I="";const L=`${d("app.fightSell.stopPoint")}${S+1}`,E=ue(f.departureDate).isValid()?` ${ue(f.departureDate).format("MM-DD")}`:"",g=` ${f.arrivalAirportCN}`,T=ue(f.arrivalTime,"HH:mm").isValid()?` ${ue(f.arrivalTime,"HH:mm").format("HHmm")}`:"",Y=ue(f.departureTime,"HH:mm").isValid()?` ${ue(f.departureTime,"HH:mm").format("HHmm")}`:"",H=` ${d("app.avSearch.stop")}${ea((f==null?void 0:f.groundTime)??"")}`;I=`${L}${E}${g}${T}${Y}${H}${S===C.length-1?"":`
`}`,_+=I}),_},p=()=>{if(!r||!a.segInfo)return;const{airlines:C}=a.segInfo,_=`${C.airCN}${C.airCode}${C.flightNo}`,f=C.isShared?` ${d("app.querySearch.carrierCompany")}${C.isShared}`:"",S=ue(a.segInfo.departureDate).isValid()?` ${ue(a.segInfo.departureDate).format("MM-DD")}`:"",I=` ${a.segInfo.departureAirportCN}${a.segInfo.departureTerminal??""}`,L=`${a.segInfo.arrivalAirportCN}${a.segInfo.arrivalTerminal??""}`,E=l(a.segInfo.departureTime),g=l(a.segInfo.arrivalTime),T=n(a.segInfo.arrivalArrdays),Y=n(a.segInfo.deptArrdays),H=$(a.stopData);let h="";const v=T-Y;v&&(h=v>0?`+${v}`:`-${v}`);const c=`${_}${f}${S}${I}—${L}${E}${g}${h}${H}`;i(c),Da({message:d("app.batchRefund.copySuccess"),type:"success"})};return(C,_)=>(u(),D("div",{class:"copy-btn bg-gray-0 rounded border border-brand-2 text-brand-2 p-[4px] cursor-pointer hover:bg-brand-4",onClick:p},m(C.$t("app.original.copy")),1))}}),bc=e=>(Lt("data-v-01de63a9"),e=e(),wt(),e),_c={class:"min-w-[343px] bg-gray-3 text-gray-0 p-2"},Cc={class:"flex items-center justify-between"},Ac={class:"mb-2 text-sm font-bold"},Dc={class:"flex items-center my-2 airlines-info"},$c={class:"flex items-center px-2 py-0"},xc={class:"!w-6 !h-6 mr-1 text-xl icon svg-icon airline-icon","aria-hidden":"true"},Sc=["xlink:href"],Tc={class:"h-6 justify-start items-center gap-1 inline-flex"},kc={class:"text-center text-gray-0 text-xs font-normal leading-tight"},Fc={class:"justify-start items-center gap-0.5 flex"},Lc={class:"text-center text-gray-0 text-xs font-normal leading-tight"},wc={key:0,class:"px-1 h-5 bg-brand-3 rounded-sm justify-center items-center flex"},Nc={class:"text-center text-brand-1 text-xs font-normal leading-tight"},Ic=bc(()=>o("div",{class:"split-label w-[1px] h-[14px] bg-gray-6"},null,-1)),Ec={class:"flex items-center px-2 py-0 mx-2 my-0 h-5 rounded-sm bg-brand-3 actual-carrier-box whitespace-nowrap"},Pc={class:"mr-2 text-gray-2"},Oc={class:"text-brand-2"},Qc={key:1},Rc={class:"mb-2 mr-2"},Mc={class:"mb-2 mr-2"},Vc=Be({__name:"FlightInformationPopover",props:{segInfo:{},airportsDbData:{}},setup(e){const a=e,i=P(Zt()),r=Re(()=>a.airportsDbData),d=P(""),n=P(""),l=(_,f,S,I)=>{const L=a.segInfo;L&&(d.value=`${_} ${S} ${L.departureAirportCode??"--"} ${L.departureAirportCN??"--"} ${L.departureTerminal??"--"} `,n.value=`${f} ${I} ${L.arrivalAirportCode??"--"} ${L.arrivalAirportCN??"--"} ${L.arrivalTerminal??"--"} `)},$=()=>{var L,E,g,T;let _="--",f="--";const S=((L=a.segInfo)==null?void 0:L.departureTime)||"--",I=((E=a.segInfo)==null?void 0:E.arrivalTime)||"--";a.segInfo&&(_=(g=a.segInfo)!=null&&g.departureDate?ue(a.segInfo.departureDate).format("MM-DD"):"--",f=(T=a.segInfo)!=null&&T.arrivalDate?ue(a.segInfo.arrivalDate).format("MM-DD"):"--"),l(_,f,S,I)},p=_=>{var f,S,I,L,E,g;return i.value==="en"?(I=(S=(f=r.value??[])==null?void 0:f.filter(T=>T.code===_))==null?void 0:S[0])==null?void 0:I.enName:(g=(E=(L=r.value??[])==null?void 0:L.filter(T=>T.code===_))==null?void 0:E[0])==null?void 0:g.cnName},C=_=>ea(_);return Ke(()=>a.segInfo,()=>{$()},{deep:!0}),mt(()=>{$()}),(_,f)=>{const S=Dt;return u(),Ce(S,{width:"auto",persistent:!1,class:"flight-common-info-detail","popper-class":"flight-common-info-detail-popover",placement:"top"},{reference:x(()=>[ft(_.$slots,"default",{},void 0,!0)]),default:x(()=>{var I,L,E,g,T,Y,H,h,v,c,V,y,q,O,k,B,ee,le,K;return[o("div",_c,[o("div",Cc,[o("div",Ac,m(`${p(((I=_.segInfo)==null?void 0:I.departureAirportCode)??"")}-${p(((L=_.segInfo)==null?void 0:L.arrivalAirportCode)??"")}`),1),b(So,{"seg-info":_.segInfo},null,8,["seg-info"])]),o("div",Dc,[o("div",$c,[o("span",null,[(u(),D("svg",xc,[o("use",{"xlink:href":"#icon-"+((T=(g=(E=_.segInfo)==null?void 0:E.airlines)==null?void 0:g.airCode)==null?void 0:T.toLowerCase())+"-c"},null,8,Sc)]))]),o("div",Tc,[o("div",kc,m(((H=(Y=_.segInfo)==null?void 0:Y.airlines)==null?void 0:H.airCN)??""),1),o("div",Fc,[o("div",Lc,m(`${((v=(h=_.segInfo)==null?void 0:h.airlines)==null?void 0:v.airCode)??""}${((V=(c=_.segInfo)==null?void 0:c.airlines)==null?void 0:V.flightNo)??""}`),1),(q=(y=_.segInfo)==null?void 0:y.airlines)!=null&&q.isShared?(u(),D("div",wc,[o("div",Nc,m(_.$t("app.querySearch.shareFlight")),1)])):de("",!0)])])]),(k=(O=_.segInfo)==null?void 0:O.airlines)!=null&&k.isShared?(u(),D(be,{key:0},[Ic,o("div",Ec,[o("span",Pc,m(_.$t("app.querySearch.carrierCompany")),1),o("span",Oc,m(`${((ee=(B=_.segInfo)==null?void 0:B.airlines)==null?void 0:ee.isShared)??""}`),1)])],64)):de("",!0),(le=_.segInfo)!=null&&le.flightTime?(u(),D("div",Qc,[o("span",null,m(_.$t("app.querySearch.flightDuration")),1),o("span",null,m(C(((K=_.segInfo)==null?void 0:K.flightTime)??"")??""),1)])):de("",!0)]),o("div",null,[o("div",Rc,m(d.value),1),o("div",Mc,m(n.value),1)])])]}),_:3})}}});const Ga=ze(Vc,[["__scopeId","data-v-01de63a9"]]),Bc={class:"flex items-center"},Uc={class:"flex items-center justify-center w-5 h-5"},Yc={class:"grow shrink basis-0 self-stretch pr-[1.43px] justify-center items-center inline-flex"},Hc={class:"icon svg-icon airline-icon text-[20px]","aria-hidden":"true"},qc=["xlink:href"],jc={class:"h-[22px] justify-start items-center gap-0.5 inline-flex"},Gc={class:"h-[22px] text-sm font-bold leading-[22px] text-gray-1"},Kc={key:0,class:"min-w-[28px] max-w-[37px] h-4 px-0.5 bg-brand-3 rounded-sm justify-center items-center flex"},zc={class:"text-center text-brand-1 text-xs font-normal leading-none"},Jc={key:0,class:"h-4 px-0.5 bg-brand-3 rounded-sm justify-center items-center inline-flex relative top-[-6px]"},Wc={class:"text-brand-1 text-xs font-normal leading-none"},Ka=Be({__name:"AirlineInfo",props:{segInfo:{}},setup(e){return(a,i)=>{var r,d,n,l,$,p,C,_,f,S,I,L,E,g,T;return u(),D("div",Bc,[o("div",{class:ve(["w-[114px] gap-0.5 justify-start inline-flex relative",(d=(r=a.segInfo)==null?void 0:r.airlines)!=null&&d.isShared?"h-[38px] items-start":"h-[22px] items-center"])},[o("div",Uc,[o("div",Yc,[(u(),D("svg",Hc,[o("use",{"xlink:href":"#icon-"+((n=a.segInfo)==null?void 0:n.airlines.airCode.toLowerCase())+"-c"},null,8,qc)]))])]),o("div",{class:ve([($=(l=a.segInfo)==null?void 0:l.airlines)!=null&&$.isShared?"h-[38px]":"h-[22px]"])},[o("div",jc,[o("div",Gc,m(((C=(p=a.segInfo)==null?void 0:p.airlines)==null?void 0:C.airCode)??"")+m(((f=(_=a.segInfo)==null?void 0:_.airlines)==null?void 0:f.flightNo)??""),1),((I=(S=a.segInfo)==null?void 0:S.airlines)==null?void 0:I.isShared)??""?(u(),D("div",Kc,[o("div",zc,m(a.$t("app.querySearch.shareFlight")),1)])):de("",!0)]),(E=(L=a.segInfo)==null?void 0:L.airlines)!=null&&E.isShared?(u(),D("div",Jc,[o("div",Wc,m(`${((T=(g=a.segInfo)==null?void 0:g.airlines)==null?void 0:T.isShared)??""}`),1)])):de("",!0)],2)],2)])}}}),Zc={sellInternationalCreatePNR:"sell-international-createPNR",sellDomesticCreatePNR:"sell-domestic-createPNR",zhPassengerInfoQuery:"zh-passenger-info-query",sellInternationalMileageCreatePNR:"sell-mileage-international-createPNR",sellAgentAvUniversalStudios:"sell-agent-av-universal-studios",sellAgentCreatePNRSaveOrderToAIG:"sell-agent-create-pnr-save-order-to-aig"},Xc="/sgui/assets/service-food-2cad3905.svg",ed="/sgui/assets/service-wifi-b5879185.svg",td=(e,a)=>{const i=P(!1),{t:r}=ct(),d=P(),n=nt({}),l=nt({}),$=nt([]),p=P([]),C=nt({render(){return gt("em",{class:"iconfont icon-calendar"})}}),_=k=>ea(k),{personalizationRules:f}=On(Zc.sellAgentAvUniversalStudios),S=k=>k.length===0?"":ue(k).format("MM-DD"),I={FOOD:Xc,NETWORK:ed},L=k=>{const B=Dn();if(nn($.value))return B.isAfter(k);const ee=$.value[0].getTime(),le=9*24*3600*1e3;return k.getTime()>ee+le||k.getTime()<ee},E=()=>{const k=ue(p.value.length?p.value[0]:"").format("YYYY-MM-DD"),B=ue(p.value.length?p.value[1]:"").format("YYYY-MM-DD");a("multiDayQuery",e.segIndex,k,B)},g=()=>{a("retractMultiDayQuery",e.segIndex)},T=(k,B,ee,le,K)=>({date:S(k)??"",time:B??"",code:ee??"",airportCn:le??"",terminal:K??""}),Y=()=>{if(!e.segInfo)return;const{departureDate:k,departureTime:B,departureAirportCode:ee,departureAirportCN:le,departureTerminal:K,arrivalDate:J,arrivalTime:Z,arrivalAirportCode:Q,arrivalAirportCN:F,arrivalTerminal:N}=e.segInfo;n.value=T(k,B,ee,le,K),l.value=T(J,Z,Q,F,N)},H=async()=>{var B,ee,le,K;const k={airCode:e.segInfo.airlines.airCode,departureDate:ue(e.segInfo.departureDate).format("YYYY-MM-DD"),flightNo:`${e.segInfo.airlines.airCode}${e.segInfo.airlines.flightNo}`};if((d.value??[]).length===0)try{i.value=!0;const{data:J}=await yo(k,"01010201"),Z=((le=(ee=((B=J.value)==null?void 0:B.flightInfoList)??[])==null?void 0:ee[0])==null?void 0:le.segments)??[],Q=Z==null?void 0:Z.findLastIndex(w=>{var A;return w.arrivalAirportCode===((A=e.segInfo)==null?void 0:A.arrivalAirportCode)}),F=Q>0?Z==null?void 0:Z.slice(0,Q+1):Z,N=[];for(let w=0;w<F.length-1;w++){const{arrivalDate:A,arrivalTime:ae,arrivalAirportCode:ge,arrivalAirportCN:he,arrivalTerminal:pe,airlines:te,groundTime:xe}=F[w],me={arrivalDate:A,arrivalTime:ae,arrivalAirportCode:ge,arrivalAirportCN:he,arrivalTerminal:pe,airlines:te,groundTime:xe};if(w+1!==F.length){const{departureDate:Ee,departureTime:X}=F[w+1]??[];me.departureDate=Ee,me.departureTime=X}me.airlines=(K=F[w])==null?void 0:K.airlines,N.push(me)}d.value=N}finally{i.value=!1}},h=k=>k==="MEAL"?"FOOD":k==="ADHOC"?"NETWORK":"",v=k=>{$.value=k},c=k=>{i.value=k},V=k=>{var B,ee;return(B=k==null?void 0:k.airlines)!=null&&B.isShared?(ee=k==null?void 0:k.airlines)==null?void 0:ee.isShared:`${k==null?void 0:k.airlines.airCode}${k==null?void 0:k.airlines.flightNo}`},y=k=>k&&k!=="--"?`-${k}`:"",q=k=>{switch(k){case"B":return`${k}${r("app.avSearch.breakfast")}`;case"L":case"D":return`${k}${r("app.avSearch.prepareDinner")}`;case"S":return`${k}${r("app.avSearch.snacks")}`;case"M":return`${k}${r("app.avSearch.prepareMeals")}`;default:return`${k}${r("app.avSearch.meal")}`}},O=k=>{switch(k){case"SKYTEAM":return`${r("app.avSearch.allianceZT")}`;case"STAR ALLIANCE":return`${r("app.avSearch.allianceZS")}`;case"ONEWORLD":return`${r("app.avSearch.allianceZ7")}`;default:return`${k}`}};return mt(()=>{Y()}),Et(()=>{(d.value??[]).length=0,$.value.length=0,p.value.length=0}),{getStopInfo:H,flightTimeFormat:_,stopData:d,departureTitle:n,arrivalTitle:l,combineFlight:T,formatMMDD:S,personalizationRules:f,deptReturnDate:p,disabledDate:L,queryMultiDay:E,changeMultiDate:g,serviceIcon:I,checkServiceType:h,calendarChange:v,loading:i,datePrefix:C,buildTerminalText:y,changeLoading:c,getFlightNo:V,getMealDescription:q,getAlliance:O}},ad=e=>{const a=P(),i=P(""),r=P(""),d=P(Zt()),n=Re(()=>e.airportsDbData),l=_=>_.length===0?"":ue(_).format("MM-DD");return{stopData:a,departureTitle:i,arrivalTitle:r,combineFlight:(_,f,S,I,L)=>`${l(_)??""} ${f??""} ${S??""} ${I??""} ${L??""}`,formatMMDD:l,getAirCity:_=>{var f,S,I,L,E,g;return d.value==="en"?(I=(S=(f=n.value??[])==null?void 0:f.filter(T=>T.code===_))==null?void 0:S[0])==null?void 0:I.enName:(g=(E=(L=n.value??[])==null?void 0:L.filter(T=>T.code===_))==null?void 0:E[0])==null?void 0:g.cnName},flightTimeFormat:_=>ea(_)}},od=ad,nd={class:"flex flex-col p-2 text-xs bg-gray-3 text-gray-0 min-w-[343px]"},id={class:"flex items-center justify-between"},rd={class:"text-sm font-bold"},sd={class:"mx-0 mt-2 flex items-center h-6"},ld={class:"flex items-center stop-content-company-data"},cd={class:"!w-6 !h-6 inline-block air-icon icon svg-icon airline-icon","aria-hidden":"true"},dd=["xlink:href"],ud={class:"h-6 justify-start items-center gap-1 inline-flex"},pd={class:"text-center text-gray-0 text-xs font-normal leading-tight"},gd={class:"justify-start items-center gap-0.5 flex"},fd={class:"text-center text-gray-0 text-xs font-normal leading-tight"},md={key:0,class:"px-1 h-5 bg-brand-3 rounded-sm justify-center items-center flex"},hd={class:"text-center text-brand-1 text-xs font-normal leading-tight"},vd=o("span",{class:"w-[1px] h-[14px] split-label mx-[5px] bg-gray-6"},null,-1),yd={class:"px-2 py-0.5 my-0 rounded-sm bg-brand-3 actual-carrier-box whitespace-nowrap inline-block"},bd={class:"mr-2 text-gray-2"},_d={class:"text-brand-2"},Cd={key:1,class:"ml-2"},Ad={class:"mt-[10px] mb-2"},Dd={class:"justify-start items-center gap-5 inline-flex"},$d=o("div",{class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},null,-1),xd={class:"flex-col justify-start items-start gap-2 inline-flex"},Sd={class:"justify-start items-center gap-2.5 inline-flex"},Td={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},kd={class:"text-gray-0 text-xs font-normal leading-5"},Fd={class:"text-gray-0 text-xs font-normal leading-tight"},Ld={class:"h-10 justify-start items-center gap-5 inline-flex"},wd={class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},Nd={class:"flex-col justify-start items-start gap-2 inline-flex"},Id={class:"justify-start items-center gap-2.5 inline-flex"},Ed={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},Pd={class:"text-gray-0 text-xs font-normal leading-5"},Od={class:"text-gray-0 text-xs font-normal leading-5"},Qd={class:"text-gray-0 text-xs font-normal leading-tight"},Rd={class:"text-gray-0 text-xs font-normal leading-tight"},Md={class:"mt-[8px]"},Vd={class:"justify-start items-center gap-5 inline-flex"},Bd=o("div",{class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},null,-1),Ud={class:"flex-col justify-start items-start gap-2 inline-flex"},Yd={class:"justify-start items-center gap-2.5 inline-flex"},Hd={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},qd={class:"text-gray-0 text-xs font-normal leading-5"},jd={class:"text-gray-0 text-xs font-normal leading-tight"},Gd=Be({__name:"AirStopMessagebox",props:{segInfo:{},stopData:{},departureTitle:{},arrivalTitle:{},airportsDbData:{}},setup(e){const a=e,{formatMMDD:i,getAirCity:r,flightTimeFormat:d}=od(a);return(n,l)=>{var p;const $=Dt;return Number(((p=n.segInfo)==null?void 0:p.stopCity)??0)>0?(u(),Ce($,{key:0,width:"auto",trigger:"click",teleported:!0,"popper-class":"flight-item-agent-stop-over-popover"},{reference:x(()=>[ft(n.$slots,"default")]),default:x(()=>{var C,_,f,S,I,L,E,g,T,Y,H,h,v,c,V,y,q;return[o("div",nd,[o("div",id,[o("div",rd,m(`${t(r)(((C=n.segInfo)==null?void 0:C.departureAirportCode)??"")}-${t(r)(((_=n.segInfo)==null?void 0:_.arrivalAirportCode)??"")}`),1),b(So,{"seg-info":n.segInfo,"stop-data":n.stopData},null,8,["seg-info","stop-data"])]),o("div",sd,[o("div",ld,[o("span",null,[(u(),D("svg",cd,[o("use",{"xlink:href":"#icon-"+((f=n.segInfo)==null?void 0:f.airlines.airCode.toLowerCase())+"-c"},null,8,dd)]))]),o("div",ud,[o("div",pd,m(((I=(S=n.segInfo)==null?void 0:S.airlines)==null?void 0:I.airCN)??""),1),o("div",gd,[o("div",fd,m(`${((E=(L=n.segInfo)==null?void 0:L.airlines)==null?void 0:E.airCode)??""}${((T=(g=n.segInfo)==null?void 0:g.airlines)==null?void 0:T.flightNo)??""}`),1),(H=(Y=n.segInfo)==null?void 0:Y.airlines)!=null&&H.isShared?(u(),D("div",md,[o("div",hd,m(n.$t("app.querySearch.shareFlight")),1)])):de("",!0)])])]),(v=(h=n.segInfo)==null?void 0:h.airlines)!=null&&v.isShared?(u(),D(be,{key:0},[vd,o("div",yd,[o("span",bd,m(n.$t("app.querySearch.carrierCompany")),1),o("span",_d,m(`${((V=(c=n.segInfo)==null?void 0:c.airlines)==null?void 0:V.isShared)??""}`),1)])],64)):de("",!0),(y=n.segInfo)!=null&&y.flightTime?(u(),D("div",Cd,[o("span",null,m(n.$t("app.querySearch.flightDuration")),1),o("span",null,m(t(d)(((q=n.segInfo)==null?void 0:q.flightTime)??"")??""),1)])):de("",!0)]),o("div",null,[o("div",Ad,[o("div",Dd,[$d,o("div",xd,[o("div",Sd,[o("div",Td,[o("div",kd,m(`${n.departureTitle.date} ${n.departureTitle.time}`),1)]),o("div",Fd,m(`${n.departureTitle.code} ${n.departureTitle.airportCn} ${n.departureTitle.terminal}`),1)])])])]),(u(!0),D(be,null,we(n.stopData,(O,k)=>(u(),D("div",{key:O.airlines.flightNo},[o("div",Ld,[o("div",wd,m(n.$t("app.fightSell.stopPoint"))+m(k+1),1),o("div",Nd,[o("div",Id,[o("div",Ed,[o("div",Pd,m(`${t(i)(O.arrivalDate??"")} ${O.arrivalTime??""}`),1),o("div",Od,m(`${t(i)(O.departureDate??"")} ${O.departureTime??""}`),1)]),o("div",Qd,m(`${O.arrivalAirportCode??""} ${O.arrivalAirportCN??""} ${O.arrivalTerminal??""}`),1),o("div",Rd,m(`${n.$t("app.avSearch.stop")}${t(ea)((O==null?void 0:O.groundTime)??"")}`),1)])])])]))),128)),o("div",Md,[o("div",Vd,[Bd,o("div",Ud,[o("div",Yd,[o("div",Hd,[o("div",qd,m(`${n.arrivalTitle.date} ${n.arrivalTitle.time}`),1)]),o("div",jd,m(`${n.arrivalTitle.code} ${n.arrivalTitle.airportCn} ${n.arrivalTitle.terminal}`),1)])])])])])])]}),_:3})):de("",!0)}}});const kt=e=>(Lt("data-v-c80f8073"),e=e(),wt(),e),Kd={key:0,class:"w-[150px] h-[18px] pl-[34px] justify-start items-center gap-1 inline-flex"},zd=kt(()=>o("div",{class:"px-1.5 py-[3px] bg-yellow-3 rounded justify-start items-start gap-2.5 flex"},[o("div",{class:"text-xs font-normal leading-3 text-yellow-1"},"TCHB")],-1)),Jd={class:"text-xs font-normal leading-3 text-yellow-1"},Wd={class:"flex items-center justify-center w-full flight-item text-gray-1"},Zd={key:0,class:"w-[24px] h-[24px] mr-2.5 px-2 py-0.5 bg-gray-7 rounded justify-center items-center inline-flex"},Xd={class:"text-xs font-bold leading-tight text-center text-gray-2"},eu={key:1,class:"w-[24px] h-[24px] mr-2.5 px-2 py-0.5 inline-flex"},tu={class:"flex-grow-0 flex-shrink-0 header-left-box basis-30 md-hidden"},au=kt(()=>o("div",{class:"border-r border-solid border-gray-7 min-h-[42px] mx-[16px] md-hidden"},null,-1)),ou={class:"flex items-center header-right"},nu={class:"flight-base-info"},iu={class:"md-box justify-between w-[170px]"},ru={class:"flex text-xs"},su={class:"min-w-[20px] min-h-box-20"},lu={class:"ml-[5px] min-w-[20px] min-h-box-20"},cu={class:"flex items-end justify-center"},du={class:"date-box w-12"},uu={class:"flex flex-col justify-around"},pu={class:"relative text-sm font-bold min-h-box-22"},gu={class:"absolute top-0 text-xs font-normal stopping text-red-1 right-[-8px]"},fu={class:"text-xs font-normal min-h-box-20 whitespace-nowrap"},mu=kt(()=>o("div",{class:"space-divider"}," ",-1)),hu={class:"stop-info-box"},vu={class:"stop-divider"},yu=kt(()=>o("div",{class:"stop-point-circle-white"},null,-1)),bu=kt(()=>o("div",{class:"stop-point-circle-triangle"},null,-1)),_u={class:"flex h-[20px] leading-[20px] text-gray-4 text-xs font-normal"},Cu=kt(()=>o("div",{class:"space-divider"}," ",-1)),Au={class:"w-12"},Du={class:"flex flex-col justify-around"},$u={class:"relative text-sm font-bold min-h-box-22 text-end"},xu={class:"stopping text-xs font-normal text-red-1 absolute top-0 right-[-16px]"},Su={class:"text-xs font-normal text-right min-h-box-20 whitespace-nowrap"},Tu={class:"md-item-right flex items-center"},ku={class:"flex"},Fu={class:"flex text-xs text-right flex-col justify-around ml-[16px] md-hidden"},Lu={class:"min-w-[20px] min-h-box-20"},wu={class:"min-w-[20px] min-h-box-20"},Nu=kt(()=>o("div",{class:"border-r border-solid border-gray-200 h-[42px] mx-[16px] md-hidden"},null,-1)),Iu={class:"header-right-asr text-xs min-w-[36px] md-header-right-asr"},Eu={key:0,class:"min-h-box-20 md:mr-2.5"},Pu={key:0,class:"whitespace-nowrap"},Ou={key:1,class:"whitespace-nowrap"},Qu={class:"min-h-box-20"},Ru={class:"header-right-wifi-box"},Mu={key:0,class:"service-box"},Vu={key:0,class:"icon","aria-hidden":"true"},Bu=kt(()=>o("use",{"xlink:href":"#icon-wifi"},null,-1)),Uu=[Bu],Yu={key:0,class:"header-right-personal-box"},Hu={class:"item-right"},qu={class:"text-xs"},ju={key:1,class:"flex justify-end multi-day-box relative cursor-pointer"},Gu={class:"text-xs text-brand-2 leading-5 absolute right-5 mr-0.5"},Ku={class:"flex-grow-[18]"},zu={class:"flex flex-row items-center mt-[6px] gap-2.5 md:pl-12 lg:pl-[180px] xl:pl-[180px]"},Ju={key:0,class:"px-0.5 bg-gray-7 rounded-sm justify-center items-center inline-flex"},Wu={class:"text-center text-gray-3 text-xs leading-4"},Zu={key:1,class:"px-0.5 bg-gray-7 rounded-sm justify-center items-center inline-flex"},Xu={class:"text-center text-gray-3 text-xs leading-4"},ep={class:"ml-1"},tp=Be({__name:"FlightCommonInfo",props:{segIndex:{},segInfo:{},multiDayCabin:{},sortIndex:{},segOrder:{},isTransfer:{type:Boolean},ignoreResolution:{type:Boolean},queryLowestFare:{type:Boolean},airportsDbData:{}},emits:["multiDayQuery","retractMultiDayQuery"],setup(e,{emit:a}){const i=e,r=a,{getStopInfo:d,personalizationRules:n,flightTimeFormat:l,departureTitle:$,stopData:p,arrivalTitle:C,deptReturnDate:_,disabledDate:f,queryMultiDay:S,changeMultiDate:I,calendarChange:L,loading:E,datePrefix:g,buildTerminalText:T,changeLoading:Y,getFlightNo:H,getMealDescription:h,getAlliance:v}=td(i,r);return(c,V)=>{var B,ee,le,K,J,Z,Q,F,N,w,A,ae,ge,he,pe,te,xe,me,Ee,X,oe,M,U,W,ne,Se,ke,Pe,Ae,Fe,He,We;const y=xa,q=vt,O=go,k=sa;return yt((u(),D("div",{class:ve(["inline-flex flex-col items-start justify-center w-full",c.ignoreResolution?"":"need-resolution"])},[(B=c.segInfo)!=null&&B.tcFlight?(u(),D("div",Kd,[zd,o("div",Jd,m((ee=c.segInfo)==null?void 0:ee.tcFlight),1)])):de("",!0),o("div",Wd,[c.segOrder===0?(u(),D("div",Zd,[o("div",Xd,m(isNaN(c.sortIndex)?"":c.sortIndex+1),1)])):(u(),D("div",eu)),o("div",tu,[b(Ga,{"seg-info":c.segInfo,"airports-db-data":c.airportsDbData},{default:x(()=>[b(Ka,{"seg-info":c.segInfo},null,8,["seg-info"])]),_:1},8,["seg-info","airports-db-data"])]),au,o("div",ou,[o("div",nu,[o("div",iu,[b(Ga,{"seg-info":c.segInfo,"airports-db-data":c.airportsDbData},{default:x(()=>[b(Ka,{"seg-info":c.segInfo},null,8,["seg-info"])]),_:1},8,["seg-info","airports-db-data"]),o("div",ru,[o("div",su,m(((le=c.segInfo)==null?void 0:le.connectLevel)??""),1),o("div",lu,m(((J=(K=c.segInfo)==null?void 0:K.airlines)==null?void 0:J.planeType)??""),1)])]),o("div",cu,[o("div",du,[o("span",uu,[o("div",pu,[fe(m(((Z=c.segInfo)==null?void 0:Z.departureTime)??"")+" ",1),o("span",gu,m(((Q=c.segInfo)==null?void 0:Q.deptArrdays)??""),1)]),o("div",fu,m(((F=c.segInfo)==null?void 0:F.departureAirportCode)??"")+" "+m(t(T)(((N=c.segInfo)==null?void 0:N.departureTerminal)??"")),1)])]),mu,o("div",hu,[o("div",{class:ve(Number(((w=c.segInfo)==null?void 0:w.stopCity)??0)>0?"stop-info":"no-stop-info")},[o("span",null,[b(Gd,{"seg-info":c.segInfo,"stop-data":t(p),"departure-title":t($),"arrival-title":t(C),"airports-db-data":c.airportsDbData},{default:x(()=>{var j;return[o("div",{class:"text-xs font-bold whitespace-nowrap text-center cursor-pointer h-[20px] leading-[20px] text-brand-2",onClick:V[0]||(V[0]=(...G)=>t(d)&&t(d)(...G))},m(`${c.$t("app.fightSell.afterStop")} ${(j=c.segInfo)==null?void 0:j.stopCity}`),1)]}),_:1},8,["seg-info","stop-data","departure-title","arrival-title","airports-db-data"]),o("div",vu,[yu,b(y,{class:"divider"},{default:x(()=>{var j;return[o("div",{class:ve(Number(((j=c.segInfo)==null?void 0:j.stopCity)??0)>0?"stop-point-circle":"")},null,2)]}),_:1}),bu])]),o("div",_u,[o("span",null,m(t(l)(((A=c.segInfo)==null?void 0:A.flightTime)??"")??""),1)])],2)]),Cu,o("div",Au,[o("span",Du,[o("div",$u,[fe(m(((ae=c.segInfo)==null?void 0:ae.arrivalTime)??"")+" ",1),o("span",xu,m(((ge=c.segInfo)==null?void 0:ge.arrivalArrdays)??""),1)]),o("div",Su,m(((he=c.segInfo)==null?void 0:he.arrivalAirportCode)??"")+" "+m(t(T)(((pe=c.segInfo)==null?void 0:pe.arrivalTerminal)??"")),1)])])])]),o("div",Tu,[o("div",ku,[o("div",Fu,[o("div",Lu,m(((te=c.segInfo)==null?void 0:te.connectLevel)??""),1),o("div",wu,m(((me=(xe=c.segInfo)==null?void 0:xe.airlines)==null?void 0:me.planeType)??""),1)]),Nu,o("div",Iu,[(Ee=c.segInfo)!=null&&Ee.commonMeal?(u(),D("div",Eu,[((X=c.segInfo)==null?void 0:X.commonMeal)==="S"?(u(),D("div",Pu,[b(q,{effect:"dark",content:`${(oe=c.segInfo)==null?void 0:oe.commonMeal}${c.$t("app.avSearch.snacksDimOrSum")}`,placement:"top"},{default:x(()=>{var j;return[fe(m(t(h)(((j=c.segInfo)==null?void 0:j.commonMeal)??"")),1)]}),_:1},8,["content"])])):(u(),D("div",Ou,m(t(h)(((M=c.segInfo)==null?void 0:M.commonMeal)??"")),1))])):de("",!0),o("div",Qu,"ASR"+m(((U=c.segInfo)==null?void 0:U.asr)??""),1)]),o("div",Ru,[(ne=(W=c.segInfo)==null?void 0:W.airlines)!=null&&ne.airService?(u(),D("div",Mu,[c.segInfo.airlines.airService.find(j=>j.code==="ADHOC")?(u(),D("svg",Vu,Uu)):de("",!0)])):de("",!0)]),t(n)?(u(),D("div",Yu,[b(t(Qn),{"rule-info":t(n),origin:((Se=c.segInfo)==null?void 0:Se.arrivalAirportCode)??"",destination:(ke=c.segInfo)==null?void 0:ke.departureAirportCode},null,8,["rule-info","origin","destination"])])):de("",!0)]),o("div",Hu,[c.queryLowestFare?de("",!0):(u(),D(be,{key:0},[c.multiDayCabin.day!==""?(u(),D("div",{key:0,class:"flex w-full h-5 text-brand-2 justify-end items-center px-0.5 gap-1 cursor-pointer",onClick:V[1]||(V[1]=(...j)=>t(I)&&t(I)(...j))},[o("span",qu,m(c.$t("app.avSearch.fold")),1),b(t(je),{size:16,class:"mt-[-4px]"},{default:x(()=>[b(t(Vt))]),_:1})])):de("",!0),c.multiDayCabin.day===""?(u(),D("div",ju,[o("div",Gu,m(c.$t("app.avSearch.multiDayQuery")),1),c.multiDayCabin.day===""?(u(),Ce(O,{key:0,modelValue:t(_),"onUpdate:modelValue":V[2]||(V[2]=j=>tt(_)?_.value=j:null),class:"av-range-picker",format:"YYYY-MM-DD",type:"daterange","prefix-icon":t(g),editable:!1,"disabled-date":t(f),"popper-class":"query-flight-date-picker-style",onChange:t(S),onCalendarChange:t(L)},null,8,["modelValue","prefix-icon","disabled-date","onChange","onCalendarChange"])):de("",!0)])):de("",!0)],64)),b(yc,{"flight-no":t(H)(c.segInfo),"departure-date":((Pe=c.segInfo)==null?void 0:Pe.departureDate)??"",onChangeLoading:t(Y)},null,8,["flight-no","departure-date","onChangeLoading"])])])]),o("div",Ku,[ft(c.$slots,"default",{},void 0,!0)])]),o("div",zu,[(Ae=c.segInfo)!=null&&Ae.alliance?(u(),D("div",Ju,[o("div",Wu,[o("span",null,m(t(v)(((Fe=c.segInfo)==null?void 0:Fe.alliance)??"")),1)])])):de("",!0),(He=c.segInfo)!=null&&He.flightDistance?(u(),D("div",Zu,[o("div",Xu,[o("span",null,m(c.$t("app.avSearch.flightDistance")),1),o("span",ep,m(((We=c.segInfo)==null?void 0:We.flightDistance)??""),1)])])):de("",!0)])],2)),[[k,t(E)]])}}});const ap=ze(tp,[["__scopeId","data-v-c80f8073"]]),op=(e,a)=>{const{t:i}=ct(),r=P({tktNum:e.tktNum??1}),d=P([]),n=g=>g==="A"||/^[1-9]$/.test(g),l=g=>["L","Q","S"].includes(g),$=g=>n(g)||l(g),p=async(g,T)=>{const{segIndex:Y,day:H,cabins:h,segInfo:v}=e;let c="",V="";g>-1&&h&&h[g]&&(c=h[g].cabinName,V=h[g].state);const y={segIndex:Y??"",day:H??"",cabinName:c,cabinStatus:V,cancel:!1,tktNum:T?r.value.tktNum:0,segInfo:v};if(e.activeCabin===c||g===-1){y.cancel=!0,a("selectCabin",y);return}a("selectCabin",y)},C=Rt(async(g,T)=>{const{notSelectable:Y,activeTag:H,avQueryFromFastQuery:h}=e;e.autoPlaceholder&&!h&&await rn(H,i,gt)||Y||p(g,T)}),_=(g,T,Y)=>{if(g)return"cabin-item cabin-item-sk";const H="cabin-item cabin-item-av",h=`${H} cabin-item-gray`,v=`${H} cabin-item-yellow`;let c=H;return $(T)?l(T)&&(c=v):c=h,e.activeCabin===Y&&(!e.avQueryFromFastQuery||e.autoPlaceholder)&&(c=`${c} cabin-item-active`),c};return{getCabinStyle:_,showChosenCabin:C,cabinClass:(g,T)=>{const{state:Y,cabinName:H}=T,h=e.notSelectable?"not-selectable":"";return`${_(g,Y,H)} ${h}`},form:r,confirm:()=>{a("closeCabinPopover",r.value.tktNum)},cancel:()=>{a("closeCabinPopover")},showPopover:()=>{r.value={tktNum:e.tktNum??1}},checkCharacter:g=>{const T=g.key,Y=/^[1-9]$/;g.returnValue=Y.test(T)?T:0},cabinRef:d}},np={class:"grow basis-32 cabin-info"},ip=["onClick"],rp={class:"flex items-center justify-center"},sp={key:1},lp=Be({__name:"FlightCabin",props:{segIndex:{},cabins:{},skQuery:{type:Boolean},day:{},segInfo:{},notSelectable:{type:Boolean},activeCabin:{},activeTag:{},confirmText:{},avQueryFromFastQuery:{type:Boolean},autoPlaceholder:{type:Boolean},visibleCabinPopover:{type:Boolean},tktNum:{}},emits:["selectCabin","closeCabinPopover"],setup(e,{emit:a}){const i=e,r=a,{cabinClass:d,showChosenCabin:n,form:l,cancel:$,confirm:p,showPopover:C,checkCharacter:_,cabinRef:f}=op(i,r);return(S,I)=>{const L=Rn,E=qt,g=jt,T=ra,Y=Dt;return u(),D("div",np,[(u(!0),D(be,null,we(S.cabins??[],(H,h)=>(u(),D("div",{key:`${h}${new Date}`,class:ve(t(d)(S.skQuery,H)),onClick:Mt(v=>t(n)(h),["stop"])},[S.activeCabin===H.cabinName&&S.visibleCabinPopover&&!S.avQueryFromFastQuery?(u(),Ce(Y,{key:0,ref_for:!0,ref:v=>{v&&t(f).push(v)},teleported:!1,visible:S.activeCabin===H.cabinName&&S.visibleCabinPopover,trigger:"manual",width:t(sn)()==="zh-cn"?"190":"300",onShow:t(C)},{reference:x(()=>[o("div",null,m(S.skQuery?H.cabinName:H.cabinName+H.state),1)]),default:x(()=>[S.activeCabin===H.cabinName?(u(),D("div",{key:0,class:"seize-seat-popover-content",onClick:I[1]||(I[1]=Mt(()=>{},["stop"]))},[S.tktNum?de("",!0):(u(),Ce(g,{key:0,model:t(l)},{default:x(()=>[b(E,{label:S.$t("app.pnrManagement.flight.numberOfOccupiedPassengers"),class:"num-input"},{default:x(()=>[b(L,{modelValue:t(l).tktNum,"onUpdate:modelValue":I[0]||(I[0]=v=>t(l).tktNum=v),min:1,max:9,size:"small",onKeydown:t(_)},null,8,["modelValue","onKeydown"])]),_:1},8,["label"])]),_:1},8,["model"])),o("div",rp,[b(T,{type:"primary",size:"mini",onClick:t(p)},{default:x(()=>[fe(m(S.confirmText||S.$t("app.pnrManagement.flight.confirm")),1)]),_:1},8,["onClick"]),S.avQueryFromFastQuery?(u(),Ce(T,{key:0,size:"mini",onClick:t($)},{default:x(()=>[fe(m(S.$t("app.pnrManagement.flight.cancel")),1)]),_:1},8,["onClick"])):de("",!0)])])):de("",!0)]),_:2},1032,["visible","width","onShow"])):(u(),D("div",sp,m(S.skQuery?H.cabinName:H.cabinName+H.state),1))],10,ip))),128))])}}});const za=ze(lp,[["__scopeId","data-v-235747be"]]),cp={class:"relative mt-[10px] mb-[10px] border-b-[1px] border-dashed border-gray-7"},dp={class:"absolute -top-[8px] left-[200px] flex justify-center items-center pl-[9px] pr-[9px] h-[18px] text-[12px] text-gray-2 rounded bg-brand-7"},up=Be({__name:"TransferDivider",props:{transferText:{}},setup(e){return(a,i)=>(u(),D("div",cp,[o("div",dp,[o("span",null,m(a.transferText),1)])]))}}),pp=(e,a)=>{const{t:i}=ct(),r=P(!1),d=P(),n=P(""),l=nt(!1),$=P([]),p=nt([]),C=[],_=[],f=no(e.segmentData),S=Q=>`${f}${Q}`,I=Q=>ue(Q||e.departureDate).format("YYYY-MM-DD"),L=Q=>{const F=Q.split("/"),N=F[F.length-1],w=$.value.find(A=>{var ae;return(ae=A.multDayCabins)==null?void 0:ae.find(ge=>{const he=ge.key===Q;return he&&(ge.activeCabin=""),he})});w&&_.forEach(A=>{`${w.segIndex}>${I(N)}`==`${A.segIndex}>${I(A.day)}`&&(A.cabinName="",A.cabinStatus="")})},E=Q=>{$.value.find(F=>{var N;if(Q.includes(F.segIndex))return(N=F.multDayCabins)==null?void 0:N.find(w=>{const A=Q===`${F.segIndex}>${I(w.day)}`;return A&&(w.activeCabin=""),A})}),_.forEach(F=>{Q===`${F.segIndex}>${I(F.day)}`&&(F.cabinName="",F.cabinStatus="")})},g=Q=>{const F=[];return(Q??[]).forEach(N=>{const w={cabinName:N.cabinName,state:N.state,day:N.day};F.push(w)}),F},T=Q=>Q.multDayCabins.length===1&&Q.multDayCabins[0].day==="",Y=()=>{$.value.forEach(Q=>{Q.multDayCabins.forEach(F=>{if(T(Q))return;const N=(C??[]).find(w=>w.key===Q.segIndex)??{};if(N){const w=(N.cabins??[]).find(A=>F.day===(A==null?void 0:A.dateTime))??{};if(w){const A=g(w.cabinNos);F.cabins=A,F.activeCabin=""}}})})},H=()=>{if(e.flightNoQueryFlag||l.value)return!0;const Q=_.filter(F=>F.cabinName!=="");return(Q==null?void 0:Q.length)===e.segmentData.segments.length},h=(Q,F,N,w)=>_.filter(A=>A.segIndex===Q).some(A=>A.cabinName===F&&A.cabinStatus===N&&A.day===w),v=(Q,F)=>{const{cabinName:N,cancel:w,day:A}=Q;return!e.autoSelectedCabin||e.flightNoQueryFlag||F||$.value.length<2||w?!1:($.value.forEach(ae=>{ae.multDayCabins.forEach(he=>{if(he.activeCabin="",I(he.day)===I(A)&&he.cabins.find(te=>te.cabinName===N)){const te=he.cabins.find(xe=>xe.cabinName===N);if(te){const xe=_.find(me=>me.segIndex===ae.segIndex);xe&&(xe.cabinName=N,xe.cabinStatus=te.state,xe.day=A,he.activeCabin=N)}}});const ge={transferCabinCache:_,day:A,travelKey:ae.segIndex,segmentData:e.segmentData,tktType:e.tktType};a("selectCabin",ge,n.value)}),!0)},c=()=>{var ae;if(!n.value)return;const Q=n.value.split("-"),F=Number(Q[0]),N=Number(Q[1]),w=(ae=$.value[F])==null?void 0:ae.multDayCabins[N];w.activeCabin="";const A=_.find(ge=>`${ge.segIndex}>${I(ge.day)}`==`${S(F)}>${I(w.day)}`);A&&(A.cabinName="",A.cabinStatus=""),n.value=""},V=(Q,F,N)=>{n.value=`${F}-${N}`;const{segIndex:w,day:A,cabinName:ae,cabinStatus:ge,cancel:he,segInfo:pe,tktNum:te}=Q;if(v(Q,F))return;$.value.forEach(me=>{me.segIndex===w?me.multDayCabins.forEach(Ee=>{I(Ee.day)===I(A)&&(Ee.activeCabin=he?"":ae)}):e.flightNoQueryFlag&&me.multDayCabins.forEach(Ee=>{I(Ee.day)===I(A)&&(Ee.activeCabin="")})});const xe={transferCabinCache:_,day:A,travelKey:w,segmentData:e.segmentData,tktType:e.tktType,tktNum:te};if(he||h(w,ae,ge,A)){_.filter(me=>me.segIndex===w).forEach(me=>{me.cabinName="",me.cabinStatus="",me.day=A}),a("cancelSelectCabin",xe);return}_.forEach(me=>{me.segIndex===w?(me.cabinName=ae,me.cabinStatus=ge,me.day=A):e.flightNoQueryFlag&&(me.cabinName="",me.cabinStatus="",me.day="")}),xe.transferCabinCache=_.filter(me=>me.cabinName),pe&&(H()||e.autoPlaceholder)&&a("selectCabin",xe,n.value)},y=(Q,F,N,w)=>{const A=w,ae=[];if(A&&A.airlines){const he={airCode:A.airlines.airCode,flightNo:`${A.airlines.airCode}${A.airlines.flightNo}`,departureCity:A.departureAirportCode,arrivalCity:A.arrivalAirportCode};ae.push(he)}return{airlines:ae,departureStart:F,departureEnd:N,index:e.segmentData.pkId}},q=(Q,F)=>{l.value=!1,$.value.filter(N=>N.segIndex===Q).forEach(N=>{const w=[],A=F;if(A){const ae=g(A.cabins),ge=I(A.departureDate),he={day:"",isShowMultDayQuery:!0,cabins:ae,activeCabin:"",key:pa(A,ge)};w.push(he),N.multDayCabins=w,C.forEach((pe,te)=>{Q===pe.key&&C.splice(te,1)})}})},O=async(Q,F,N,w)=>{var A,ae,ge,he,pe;try{r.value=!0,l.value=!0;const{data:te}=await _i(y(Q,F,N,w),"01010209"),xe=((A=te.value)==null?void 0:A.flightInfoList)??[];if(xe.length>0&&((ge=(ae=xe[0])==null?void 0:ae.segments)==null?void 0:ge.length)>0){const me=xe[0],Ee=(pe=(he=me==null?void 0:me.segments)==null?void 0:he[0])==null?void 0:pe.cabins;if(Ee){const X={key:Q,cabins:Ee};C.push(X),$.value.filter(oe=>oe.segIndex===Q).forEach(oe=>{var U;const M=[];Ee.forEach((W,ne)=>{const Se=w,ke=I(W.dateTime),Pe={day:W.dateTime,isShowMultDayQuery:ne===0,cabins:W.cabinNos,activeCabin:"",key:pa(Se,ke)};M.push(Pe)}),p.value=g((U=oe==null?void 0:oe.multDayCabins)==null?void 0:U[0].cabins),oe.multDayCabins=M}),Y()}}}catch{q(Q,w)}finally{r.value=!1}},k=(Q,F)=>{var A;const N=[],w={day:"",isShowMultDayQuery:((A=e.segmentData)==null?void 0:A.segments.length)===1,cabins:Q,activeCabin:"",key:pa(F)};return N.push(w),N},B=()=>{var Q;$.value=[],_.length=0,n.value="",e.segmentData&&(((Q=e.segmentData)==null?void 0:Q.segments)??[]).forEach((F,N)=>{const w=g(F.cabins),A={segIndex:S(N),multDayCabins:k(w,F)};$.value.push(A);const{arrivalAirportCode:ae,departureAirportCode:ge,departureDate:he,arrivalDate:pe,airlines:te}=F,xe={segIndex:S(N),day:"",cabinName:"",cabinStatus:"",flightNo:`${(te==null?void 0:te.airCode)??""}${(te==null?void 0:te.flightNo)??""}`,arrivalAirportCode:ae,departureAirportCode:ge,departureDate:he,arrivalDate:pe,segInfo:F};_.push(xe)});for(let F=0;F<e.segmentData.segments.length;F++)if(e.segmentData.segments[F].transferDate="",F<e.segmentData.segments.length-1){const N=e.segmentData.segments[F],w=e.segmentData.segments[F+1],A=ue(`${N.arrivalDate}`).format("YYYY-MM-DD HHmm"),ae=ue(`${w.departureDate}`).format("YYYY-MM-DD HHmm");e.segmentData.segments[F].transferDate=$n(A,ae)}},ee=Q=>{if(!Q)return"";const F=Q.transferDate??"";return Q.tcFlight?`${i("app.fastQuery.assistedSearch.travelDistance",{time:F})}`:e.flightNoQueryFlag?`${i("app.fightSell.afterStopTime",{time:F})}`:`${i("app.fightSell.transfer",{time:F})}`},le=async()=>{const Q=await st("searchLocalData");d.value=(JSON.parse(Q.localData??"")??[]).map(F=>({code:F.airportCode,cnName:F.cityArray[1],enName:F.cityArray[0]}))},K=async Q=>{var F;(((F=e.segmentData)==null?void 0:F.segments)??[]).length&&e.avQueryFromFastQuery&&B(),a("closeCabinPopover",Q)},J=Q=>{const F=Q.split("-"),N=n.value.split("-");return(F==null?void 0:F[0])===e.sortIndex.toString()&&(F==null?void 0:F[1])===N[0]&&e.showPlaceholderPopoverIndex.includes(Ao)?(n.value="",!0):!1},Z=Q=>{const F=Q.split("-"),N=n.value.split("-");((F==null?void 0:F[0])!==e.sortIndex.toString()||(F==null?void 0:F[1])===N[0]&&(F!=null&&F[2])&&(F==null?void 0:F[2])!==N[1])&&c()};return Ke(()=>e.showPlaceholderPopoverIndex,Q=>{if(e.autoPlaceholder&&n.value){if(J(Q))return;Z(Q)}}),mt(()=>{var Q;le(),B(),(Q=$.value)!=null&&Q.length&&Y(),Wt.on(`${Ue.DELETE_LEFT_FLIGHT}${e.activeTag}`,F=>{if(F.includes(Ue.DELETE_LEFT_PLACEHOLDER_FLIGHT)){const N=F.replace(Ue.DELETE_LEFT_PLACEHOLDER_FLIGHT,"");L(N);return}E(F)})}),Et(()=>{$.value.length=0,p.value.length=0,C.length=0,_.length=0,Wt.off(`${Ue.DELETE_LEFT_FLIGHT}${e.activeTag}`)}),{selectCabin:V,multDayQuery:O,retractMultDayQuery:q,segCabins:$,multDayCabinQ:l,loading:r,transferText:ee,visibleCabinPopoverIndex:n,closeCabinPopover:c,confirmTktNum:K,airportsDbData:d}},gp={class:"w-full box-border border border-brand-3 mb-[10px] last:mb-0 rounded flex-none flex flex-col items-start px-[10px] py-[6px] gap-2.5 order-3 grow-0 self-stretch"},fp={key:0},mp={class:"flex"},hp=o("div",{class:"self-center bg-gray-7 h-px flex-1 ml-1 text-xs font-bold"},null,-1),vp={key:0},yp={key:1,class:"text-center leading-6"},bp={key:1},_p={class:"flex"},Cp={key:0,class:"px-0.5 bg-brand-3 rounded-sm justify-center items-center inline-flex"},Ap={class:"text-center text-brand-1 text-xs leading-4"},Dp={key:0,class:"ml-1"},$p={key:1,class:"ml-1"},xp=Be({__name:"FlightItemInfo",props:{segmentData:{},tktType:{},sortIndex:{},departureDate:{},notSelectable:{type:Boolean},autoSelectedCabin:{type:Boolean},flightNoQueryFlag:{type:Boolean},queryLowestFare:{type:Boolean},activeTag:{},visibleCabinPopover:{type:Boolean},showPlaceholderPopoverIndex:{},autoPlaceholder:{type:Boolean},avQueryFromFastQuery:{type:Boolean},confirmText:{},tktNum:{}},emits:["selectCabin","closeCabinPopover"],setup(e,{expose:a,emit:i}){const r=e,d=i,{segCabins:n,multDayQuery:l,retractMultDayQuery:$,selectCabin:p,multDayCabinQ:C,loading:_,transferText:f,airportsDbData:S,visibleCabinPopoverIndex:I,closeCabinPopover:L,confirmTktNum:E}=pp(r,d);return a({closeCabinPopover:L}),(g,T)=>{const Y=sa;return yt((u(),D("div",gp,[(u(!0),D(be,null,we(t(n)??[],(H,h)=>{var v,c,V,y;return u(),D("div",{key:`${h}${new Date}`,class:"w-full"},[b(ap,{"seg-info":(v=g.segmentData)==null?void 0:v.segments[h],"seg-index":H.segIndex,"seg-order":h,"is-transfer":t(n).length>1,"sort-index":g.sortIndex,"ignore-resolution":g.notSelectable,"multi-day-cabin":H.multDayCabins[0],"query-lowest-fare":g.queryLowestFare,"airports-db-data":t(S),onMultiDayQuery:(q,O,k)=>{var B;return t(l)(q,O,k,(B=g.segmentData)==null?void 0:B.segments[h])},onRetractMultiDayQuery:q=>{var O;return t($)(q,(O=g.segmentData)==null?void 0:O.segments[h])}},{default:x(()=>[t(C)?(u(),D("div",fp,[(u(!0),D(be,null,we((H==null?void 0:H.multDayCabins)??[],(q,O)=>{var k;return u(),D("div",{key:`${O}${new Date}`},[o("div",mp,[o("div",null,m((q==null?void 0:q.day)??""),1),hp]),q.cabins.length>0?(u(),D("div",vp,[b(za,{"visible-cabin-popover":g.visibleCabinPopover&&t(I)===`${h}-${O}`,cabins:q.cabins,"active-cabin":q.activeCabin,"sk-query":!1,"seg-index":H.segIndex,day:q.day,"not-selectable":g.notSelectable,"seg-info":(k=g.segmentData)==null?void 0:k.segments[h],"active-tag":g.activeTag,"auto-placeholder":g.autoPlaceholder,"tkt-num":g.tktNum,"confirm-text":g.confirmText,"av-query-from-fast-query":g.avQueryFromFastQuery,onSelectCabin:B=>t(p)(B,h,O),onCloseCabinPopover:t(E)},null,8,["visible-cabin-popover","cabins","active-cabin","seg-index","day","not-selectable","seg-info","active-tag","auto-placeholder","tkt-num","confirm-text","av-query-from-fast-query","onSelectCabin","onCloseCabinPopover"])])):(u(),D("div",yp,"NO-OP"))])}),128))])):(u(),D("div",bp,[(u(!0),D(be,null,we((H==null?void 0:H.multDayCabins)??[],(q,O)=>{var k,B,ee,le,K,J,Z,Q,F;return u(),D("div",{key:`${O}${new Date}`},[o("div",_p,[o("div",null,m((q==null?void 0:q.day)??""),1)]),g.queryLowestFare?(u(),D("div",Cp,[o("div",Ap,[o("span",null,m(g.$t("app.avSearch.lowestFare")),1),(B=(k=g.segmentData)==null?void 0:k.segments[h])!=null&&B.fare||(le=(ee=g.segmentData)==null?void 0:ee.segments[h])!=null&&le.fareClass?(u(),D("span",Dp,m(((J=(K=g.segmentData)==null?void 0:K.segments[h])==null?void 0:J.fareClass)??"")+m(`: ${((Q=(Z=g.segmentData)==null?void 0:Z.segments[h])==null?void 0:Q.fare)??""}`),1)):(u(),D("span",$p,"NO PRICE"))])])):de("",!0),b(za,{"visible-cabin-popover":g.visibleCabinPopover&&t(I)===`${h}-${O}`,cabins:q.cabins,"active-cabin":q.activeCabin,"sk-query":!1,"seg-index":H.segIndex,day:q.day,"not-selectable":g.notSelectable,"seg-info":(F=g.segmentData)==null?void 0:F.segments[h],"active-tag":g.activeTag,"auto-placeholder":g.autoPlaceholder,"tkt-num":g.tktNum,"confirm-text":g.confirmText,"av-query-from-fast-query":g.avQueryFromFastQuery,onSelectCabin:N=>t(p)(N,h,O),onCloseCabinPopover:t(E)},null,8,["visible-cabin-popover","cabins","active-cabin","seg-index","day","not-selectable","seg-info","active-tag","auto-placeholder","tkt-num","confirm-text","av-query-from-fast-query","onSelectCabin","onCloseCabinPopover"])])}),128))]))]),_:2},1032,["seg-info","seg-index","seg-order","is-transfer","sort-index","ignore-resolution","multi-day-cabin","query-lowest-fare","airports-db-data","onMultiDayQuery","onRetractMultiDayQuery"]),((V=(c=g.segmentData)==null?void 0:c.segments[h])==null?void 0:V.transferDate)!==""?(u(),Ce(up,{key:0,"transfer-text":t(f)((y=g.segmentData)==null?void 0:y.segments[h]),class:"my-3.5"},null,8,["transfer-text"])):de("",!0)])}),128))])),[[Y,t(_)]])}}}),Sp=(e,a)=>{const{t:i}=ct(),r=ln(),d=cn(),n=la(),l="",$=P([]),p=P(l),C=P(e.avQueryFromFastQuery?i("app.pnrManagement.flight.bookTicket"):""),_=66,f=P(!1),S=P(!1),I=Xt(),L=It(),{activeTag:E,orderInfo:g}=Yt(L),{saveCrsTemporaryOrder:T}=Mn(),Y=ca(),H=Re(()=>{var se;const z=e.avQueryFromFastQuery?Qt:L.activeTag;return(se=Y.flightByDate[z])==null?void 0:se.flightDataList.size}),h=P([]),v=P(!1),c=P(!1),V=P({filterCondition:[],sortTypeNum:0,filterDeparture:[],filterArrival:[],transferTimes:[],filterDepartureTime:[],filterArrivalTime:[]}),y=P(),q=P(),O=P(),k=P(!1),B=Re(()=>{const z=[];return[...e.avSearchData??[]].forEach(se=>{((se==null?void 0:se.segments)??[]).forEach(re=>{var De,_e;z.includes(((De=re==null?void 0:re.airlines)==null?void 0:De.airCode)??"")||z.push(((_e=re==null?void 0:re.airlines)==null?void 0:_e.airCode)??"")})}),z}),ee=Re(()=>Array.from(new Set([...e.avSearchData??[]].map(z=>{var se;return((se=z.segments??[])==null?void 0:se[0].departureAirportCode)??""})))),le=Re(()=>Array.from(new Set([...e.avSearchData??[]].map(z=>{var se,re;return((re=(se=z.segments??[])==null?void 0:se[z.segments.length-1])==null?void 0:re.arrivalAirportCode)??""})))),K=Re(()=>{var z;return(z=I.getters.userPreferences)==null?void 0:z.autoSelectCabinClass}),J=P(0),Z=Re(()=>{var se;return!(v.value&&c.value)&&((se=I.getters.userPreferences)==null?void 0:se.autoOccupy)&&(e.avQueryFromFastQuery||!Number(g.value.get(E.value).psgType)&&g.value.get(E.value).type==="2")}),Q=P(0);let F=!1;const N=Re(()=>e.currentPage??1),w=P(0),A=z=>z||ue(e.queryForm.departureDate).format("YYYY-MM-DD"),ae=()=>{var se;const z=(se=I.state.user)==null?void 0:se.agentManageStatus;return z===3||z===4},ge=async z=>{var re;switch(f.value=(e.avSearchData??[]).length,await St(),h.value=[...e.avSearchData??[]],z.filterCondition.length>0&&(h.value=h.value.filter(De=>{let _e=!1;return z.filterCondition.forEach(Le=>De.segments.forEach(Qe=>{Le===Qe.airlines.airCode&&(_e=!0)})),_e})),z.filterDeparture.length>0&&(h.value=h.value.filter(De=>{let _e=!1;return z.filterDeparture.forEach(Le=>{var Qe,Oe;Le===((Oe=(Qe=De.segments??[])==null?void 0:Qe[0])==null?void 0:Oe.departureAirportCode)&&(_e=!0)}),_e})),z.filterArrival.length>0&&(h.value=h.value.filter(De=>{let _e=!1;return z.filterArrival.forEach(Le=>{var Qe,Oe;Le===((Oe=(Qe=De.segments??[])==null?void 0:Qe[De.segments.length-1])==null?void 0:Oe.arrivalAirportCode)&&(_e=!0)}),_e})),z.transferTimes.length>0&&(h.value=h.value.filter(De=>{const _e=De.segments.length-1,Le=[];return De.segments.some(Oe=>Number((Oe==null?void 0:Oe.stopCity)??0))?(Le.push(-1),_e&&Le.push(_e)):Le.push(_e),Le.some(Oe=>z.transferTimes.includes(Oe.toString()))})),h.value=Ol(z,h.value),z.sortTypeNum){case 0:h.value.sort((De,_e)=>{var Le,Qe;return ue((((Le=De.segments)==null?void 0:Le[0])??[]).departureDate).unix()-ue((((Qe=_e.segments)==null?void 0:Qe[0])??[]).departureDate).unix()});break;case 1:h.value.sort((De,_e)=>{var Le,Qe;return ue((((Le=_e.segments)==null?void 0:Le[0])??[]).departureDate).unix()-ue((((Qe=De.segments)==null?void 0:Qe[0])??[]).departureDate).unix()});break;case 2:qa(h.value,!0);break;case 3:qa(h.value,!1);break}Q.value+=1;const se=((re=q.value)==null?void 0:re.getBoundingClientRect().height)/_+3;J.value=(h.value??[]).length<se?(h.value??[]).length:Number(se.toFixed(0)),k.value=!k.value,h.value.length||Pe(!1)},he=()=>{const z=J.value+3;z>(h.value??[]).length?J.value=(h.value??[]).length:J.value=z},pe=z=>{const{transferCabinCache:se,day:re,travelKey:De}=z;if(e.queryForm.flightNumber){const _e=`${De}>${A(re)}`;L.delSeizeSeatInfoFlight(E.value,_e)}else se.forEach(_e=>A(re)===A(_e.day)&&L.delSeizeSeatInfoFlight(E.value,`${_e.segIndex}>${A(_e.day)}`));lt(Ue.UPDATE_FLIGHT_LIST,"")},te=(z,se)=>(z??[]).map(re=>{var _e,Le;const De=re.cabins;return{airline:"",airNo:`${re.airlines.airCode??""}${re.airlines.flightNo??""}`,fltClass:((Le=(_e=De==null?void 0:De[0])==null?void 0:_e.cabinName)==null?void 0:Le.substring(0,1))??"",orgCity:re.departureAirportCode??"",actionCode:re.actionCode??"",desCity:re.arrivalAirportCode??"",tktNum:se.length,departureTime:ue(re.departureDate).format("YYYY-MM-DD"),type:0}}),xe=async(z,se)=>{S.value=!0;try{const{data:re}=await bo(se);re.value==="OK"&&(Da({message:i("app.avSearch.addSegmentSuccess"),type:"success"}),T(dn(bn.UPDATE,g.value.get(E.value))),lt(`PnrManagement.vue${z}`,z))}finally{S.value=!1}},me=(z,se)=>{var re,De;if(((re=z.segments)==null?void 0:re[0].segmentType)==="1"){const _e=z.segments.map(Oe=>{var at;return(at=Oe.cabins[0])==null?void 0:at.cabinName}).join(""),Le=se.map(Oe=>Oe==null?void 0:Oe.cabinName).join("");return(_e!==Le||!["RR","HK"].includes(((De=z.segments)==null?void 0:De[0].actionCode)??""))&&(z.disabled=!0),!0}return!1},Ee=z=>{const{transferCabinCache:se}=z,De=(g.value.get(`${E.value}`).flight??[]).find(_e=>_e.disabled?!1:se.find(Le=>{const Qe=Le.segInfo,Oe=ue(Qe.departureDate).format("YYYY-MM-DD"),at=`${Qe.airlines.airCode}${Qe.airlines.flightNo}${Oe}`,Ne=_e.segments[0],it=ue(Ne.departureDate).format("YYYY-MM-DD"),dt=`${Ne.airlines.airCode}${Ne.airlines.flightNo}${it}`;return Ne.segmentType!=="2"&&at===dt}));if(De){if(me(De,se))return;L.delSeizeSeatInfoFlight(E.value,De.key)}},X=(z,se,re)=>{if((se==null?void 0:se.segments)??[])for(let _e=0;_e<se.segments.length;_e++){const Le=se.segments[_e];if(z===re)return Le}return null},oe=z=>{const{transferCabinCache:se,day:re,travelKey:De,segmentData:_e,tktType:Le}=Xe(z),Qe=[];return se.forEach((Oe,at)=>{var it;const Ne=Oe.segInfo;if(Ne&&`${A(Oe.day)}`==`${A(re)}`){const dt=[],ht={cabinName:Oe.cabinName,state:Oe.cabinStatus,day:Oe.day};dt.push(ht);const ot={airlines:Ne.airlines,arrDays:Ne.arrDays,deptArrdays:Ne.deptArrdays,arrivalArrdays:Ne.arrivalArrdays,arrivalAirportCN:Ne.arrivalAirportCN,arrivalAirportCode:Ne.arrivalAirportCode,arrivalDate:(re==null?void 0:re.length)>0?`${re}${Ne.arrivalDate.substring(10)}`:Ne.arrivalDate,arrivalTerminal:Ne.arrivalTerminal,arrivalTime:Ne.arrivalTime,asr:Ne.asr,cabins:dt,connectLevel:Ne.connectLevel,departureAirportCN:Ne.departureAirportCN,departureAirportCode:Ne.departureAirportCode,departureDate:(re==null?void 0:re.length)>0?`${re}${Ne.departureDate.substring(10)}`:Ne.departureDate,departureTerminal:Ne.departureTerminal,departureTime:Ne.departureTime,flightDistance:Ne.flightDistance,flightTime:Ne.flightTime,stopCity:Ne.stopCity,marriedSegmentNumber:Ne.marriedSegmentNumber,transferDate:at>0?(it=X(se[at-1].segIndex,_e,De))==null?void 0:it.transferDate:"",tcFlight:Ne.tcFlight,commonMeal:Ne.commonMeal,etInd:Ne.etInd??!1,segmentType:"0"},bt=ue(ot.departureDate).format("YYYY-MM-DD"),Kt={arriveCity:{code:Oe.arrivalAirportCode,name:""},departureCity:{code:Oe.departureAirportCode,name:""},date:{departureDate:bt},segments:[ot],tktType:Le??"",key:`${Oe.segIndex}>${A(Oe.day)}`,openFlag:!1};Qe.push(Kt)}}),Qe},M=(z,se)=>{const re=z.split("-");return se?`${re[0]}-${re[1]}-${se}`:`${re[0]}-${re[1]}`},U=async z=>{if(!z){p.value=l;return}let se=z??1,re=Ue.PLACEHOLDER_FLIGHT;e.avQueryFromFastQuery&&(n.closeFastQuery(),e.placeholderFlagTag||(re=Ue.FAST_QUERY_AV_PLACEHOLDER_FLIGHT),d.path.includes("crs/pnrManagement")||(I.dispatch("setFullLoading",!0),await r.push({path:"v2/crs/pnrManagement"}),await St()),e.placeholderFlagTag&&(await L.setActiveTag(e.placeholderFlagTag),se=w.value)),p.value=M(p.value,Ao),L.setPlaceholderFlightsParams({flightKeys:[],params:{bookAirSegs:[]},seizeSeatInfoFlight:[]},se),lt(Ue.UPDATE_FLIGHT_LIST,`${re}-${E.value}`),I.dispatch("setFullLoading",!1)},W=async(z,se,re)=>{var ht;const{transferCabinCache:De,day:_e,segmentData:Le,tktNum:Qe}=z,Oe=g.value.get(E.value),at=(ht=Oe==null?void 0:Oe.specialContent)==null?void 0:ht.includes(i("app.basic.occupy"));if(!e.queryForm.flightNumber&&Le.segments.length>1&&!_e){const ot=De.filter(bt=>`${A(bt.day)}`==`${A(_e)}`&&bt.cabinName).length===Le.segments.length;if(p.value=M(se),!ot)return}p.value=at?M(se):se;const Ne=re.map(ot=>ot.key??"");let it=Qe??1;if(at){it=Oe.flight.find(Kt=>Kt.segments.some(R=>R.segmentType==="2")).segments[0].tktNum??1;const bt=ga(it??1,re);L.setPlaceholderFlightsParams({flightKeys:Ne,params:bt,seizeSeatInfoFlight:re}),lt(Ue.UPDATE_FLIGHT_LIST,`${Ue.PLACEHOLDER_FLIGHT}-${E.value}`);return}const dt=ga(it??1,re);L.setPlaceholderFlightsParams({flightKeys:Ne,params:dt,seizeSeatInfoFlight:re})},ne=async(z,se,re)=>{var Ne,it,dt;const{transferCabinCache:De,day:_e,segmentData:Le,tktNum:Qe}=z;if(!e.queryForm.flightNumber&&Le.segments.length>1&&!_e){const ht=De.filter(ot=>`${A(ot.day)}`==`${A(_e)}`&&ot.cabinName).length===Le.segments.length;if(p.value=M(se),!ht)return}const Oe=re.map(ht=>ht.key??"");p.value=se,w.value=e.placeholderFlagTag?((dt=(it=(Ne=g.value.get(e.placeholderFlagTag))==null?void 0:Ne.flight)==null?void 0:it[0])==null?void 0:dt.segments[0].tktNum)??0:0;const at=ga(Qe??1,re);L.setPlaceholderFlightsParams({flightKeys:Oe,params:at,seizeSeatInfoFlight:re}),L.setPlaceholderFlightsQueryForm(e.queryForm)},Se=(z,se)=>{const{transferCabinCache:re,day:De,segmentData:_e}=Xe(z);!e.queryForm.flightNumber&&_e.segments.length>1&&!De&&!(re.filter(Qe=>`${A(Qe.day)}`==`${A(De)}`&&Qe.cabinName).length===_e.segments.length)||(se.forEach(Le=>L.setFlight(E.value,Le)),lt(Ue.UPDATE_FLIGHT_LIST,""))},ke=async(z,se)=>{const re=z,De=oe(z);if(Z.value){if(e.avQueryFromFastQuery){ne(z,se,De);return}W(z,se,De);return}p.value=M(se),Ee(re),Se(re,De)},Pe=z=>a("update:scrollHide",z),Ae=()=>{N.value!==1&&a("turnPage",N.value,"P")},Fe=z=>{a("screeningDirect",z)},He=z=>{a("changeDate",z)},We=()=>{a("turnPage",N.value,"N")},j=z=>!((z==="pre"||!z)&&N.value===1),G=Rt(z=>{if(!O.value||e.avQueryFromFastQuery)return;const se=z.deltaY<0,re=z.deltaY>0;if(se){F=!1,Pe(F);return}if(re){F=!0,Pe(F);return}},300),ce=()=>{var z;(z=O.value)==null||z.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"}),F=!1,Pe(!1)};return Ke(()=>e.searchCompleteKey,async()=>{h.value=[],await St(),ge(V.value)}),mt(async()=>{v.value=await I.getters.userSeatDisabled,c.value=ae(),V.value.sortTypeNum=-1,ge(V.value),document.addEventListener("click",()=>{U()})}),Et(()=>{h.value.length=0}),{loadFlightData:he,cancelSelectCabin:pe,selectCabin:ke,flightDataListSize:H,updateKey:Q,count:J,airlineList:B,SortFlightData:ge,flightData:h,turnPre:Ae,currentPage:N,turnNext:We,screeningDirect:Fe,isAllowTurn:j,changeDate:He,dynamicScrollerRef:y,flightsContainerRef:q,flightsBoxRef:O,scrollToTop:ce,scrollFlightsContent:G,loading:S,moreDeparture:ee,moreArrival:le,activeTag:E,buildSegmentInfo:te,doAddSegment:xe,updateFlightListFlag:k,autoSelectedCabin:K,showPlaceholderPopoverIndex:p,flightItemInfoRefs:$,autoPlaceholder:Z,closeCabinPopover:U,confirmText:C,placeholderNum:w,showContainer:f}},Tp={key:0,class:"w-full h-8 mt-2.5 px-2.5 bg-yellow-3 rounded border border-solid border-yellow-2 flex-col justify-center items-start gap-2.5 inline-flex"},kp={class:"justify-start items-center gap-1 inline-flex text-yellow-1"},Fp={class:"text-xs leading-5"},Lp={class:"pagination"},wp={class:"page-panel"},Np={key:1,class:"dynamic-no-data"},Ip=Be({__name:"FlightContainer",props:{queryForm:{},avSearchData:{},currentPage:{},searchCompleteKey:{default:""},queryResDateGroupBySessionId:{},flightOpRightTop:{},avQueryFromFastQuery:{type:Boolean},isQueryCurrentDay:{type:Boolean},placeholderFlagTag:{}},emits:["screeningDirect","turnPage","changeDate","update:scrollHide"],setup(e,{expose:a,emit:i}){const r=e,d=i,{updateKey:n,airlineList:l,SortFlightData:$,screeningDirect:p,flightData:C,selectCabin:_,scrollToTop:f,dynamicScrollerRef:S,updateFlightListFlag:I,autoPlaceholder:L,placeholderNum:E,changeDate:g,flightDataListSize:T,cancelSelectCabin:Y,scrollFlightsContent:H,flightsContainerRef:h,flightsBoxRef:v,showContainer:c,activeTag:V,closeCabinPopover:y,confirmText:q,flightItemInfoRefs:O,isAllowTurn:k,turnPre:B,turnNext:ee,loadFlightData:le,count:K,loading:J,moreDeparture:Z,moreArrival:Q,autoSelectedCabin:F,showPlaceholderPopoverIndex:N}=Sp(r,d);return a({closeCabinPopover:y}),(w,A)=>{const ae=je,ge=sa,he=ui;return u(),D("div",{ref_key:"dynamicScrollerRef",ref:S,class:"dynamic-scroller relative"},[b(pc,{airlines:t(l),"av-search-data":w.avSearchData,"current-flight-list":t(C),"update-flight-list-flag":t(I),"query-form-params":w.queryForm,"query-res-date-group-by-session-id":w.queryResDateGroupBySessionId,"more-departure":t(Z),"more-arrival":t(Q),onSortFlight:t($),onScreeningDirect:t(p),onChangeDate:t(g)},null,8,["airlines","av-search-data","current-flight-list","update-flight-list-flag","query-form-params","query-res-date-group-by-session-id","more-departure","more-arrival","onSortFlight","onScreeningDirect","onChangeDate"]),t(c)?(u(),D(be,{key:0},[w.isQueryCurrentDay?de("",!0):(u(),D("div",Tp,[o("div",kp,[b(ae,{size:16},{default:x(()=>[b(t(un))]),_:1}),o("div",Fp,m(w.$t("app.avSearch.noFlightTip")),1)])])),yt((u(),D("div",{ref_key:"flightsContainerRef",ref:h,"infinite-scroll-distance":"3",class:"infinite-list infinite-list-height flight-list-scroller overflow-hidden flex-1",onWheel:A[0]||(A[0]=(...pe)=>t(H)&&t(H)(...pe))},[t(T)>0&&t(C).length>0?(u(),D("div",{key:0,ref_key:"flightsBoxRef",ref:v,class:"py-[10px]"},[(u(!0),D(be,null,we(t(K),pe=>{var te;return u(),Ce(xp,{key:`${pe}${t(n)}`,ref_for:!0,ref:xe=>{xe&&t(O).push(xe)},"not-selectable":!!w.avQueryFromFastQuery&&!t(L),"flight-no-query-flag":!!w.queryForm.flightNumber,"sort-index":pe-1,"tkt-type":t(C)[pe-1].tktType,"segment-data":t(C)[pe-1],"departure-date":w.queryForm.departureDate,"active-tag":t(V),"show-placeholder-popover-index":t(N),"more-departure":t(Z),"more-arrival":t(Q),"query-lowest-fare":w.queryForm.lowestPrice,"auto-selected-cabin":t(F),"visible-cabin-popover":t(L)&&Number((te=t(N).split("-"))==null?void 0:te[0])===pe-1&&t(N).split("-").length===3,"tkt-num":t(E),"av-query-from-fast-query":w.avQueryFromFastQuery,"confirm-text":t(q),"auto-placeholder":t(L),onCloseCabinPopover:t(y),onSelectCabin:(xe,me)=>t(_)(xe,`${pe-1}-${me}`),onCancelSelectCabin:t(Y)},null,8,["not-selectable","flight-no-query-flag","sort-index","tkt-type","segment-data","departure-date","active-tag","show-placeholder-popover-index","more-departure","more-arrival","query-lowest-fare","auto-selected-cabin","visible-cabin-popover","tkt-num","av-query-from-fast-query","confirm-text","auto-placeholder","onCloseCabinPopover","onSelectCabin","onCancelSelectCabin"])}),128))],512)):de("",!0)],32)),[[ge,t(J)],[he,t(le)]]),o("div",Lp,[o("div",{class:ve(["operate",t(k)("pre")?"able-to-operate":"disable-to-operate"]),"data-gid":"01010211",onClick:A[1]||(A[1]=(...pe)=>t(B)&&t(B)(...pe))},m(w.$t("app.avSearch.previousPage")),3),o("span",wp,m(w.$t("app.avSearch.noPage",{currentPage:w.currentPage})),1),o("div",{class:ve(["operate",t(k)("next")?"able-to-operate":"disable-to-operate"]),"data-gid":"01010212",onClick:A[2]||(A[2]=(...pe)=>t(ee)&&t(ee)(...pe))},m(w.$t("app.avSearch.nextPage")),3)]),w.avQueryFromFastQuery?de("",!0):(u(),D("div",{key:1,class:"absolute right-[1px] bottom-[60px] cursor-pointer w-[28px] h-[28px] p-[6px] bg-white rounded shadow border border-brand-2 animate-bounce flex items-center justify-center",onClick:A[3]||(A[3]=(...pe)=>t(f)&&t(f)(...pe))},[b(ae,{class:"w-[16px] h-[16px]",size:"16px"},{default:x(()=>[b(t(pn),{class:"text-brand-2"})]),_:1})]))],64)):(u(),D("div",Np,m(w.$t("app.avSearch.noData")),1))],512)}}});const Ep=ze(Ip,[["__scopeId","data-v-f1d7d1e5"]]),Pp=(e,a)=>{const{t:i}=ct(),r=Xt(),d=io("--bkc-el-color-primary",null),n=P(),l=P(),$=P(),p=It(),{orderInfo:C,activeTag:_}=Yt(p),f=P(""),S=P({...e.ssQueryForm,tktNum:e.tktNum||e.ssQueryForm.tktNum}),I=nt({render(){return gt("em",{class:"iconfont icon-calendar"})}});let L="";const E=(N,w,A)=>{if(w.length>3||!$t.test(w)){A(new Error(i("app.fastQuery.headerQuery.formatError")));return}if(S.value.arrivalAirportCode&&w===S.value.arrivalAirportCode){A(new Error(i("app.fastQuery.headerQuery.identical")));return}A()},g=(N,w,A)=>{if(w.length>3||!$t.test(w)){A(new Error(i("app.fastQuery.headerQuery.formatError")));return}if(S.value.departureAirportCode&&w===S.value.departureAirportCode){A(new Error(i("app.fastQuery.headerQuery.identical")));return}A()},T=(N,w,A)=>{if(w){const ae=/^\d+$/.test(w.slice(0,2))?"":w.slice(0,2);hn.test(w)?ae?!S.value.airlines||ae===S.value.airlines?A():A(new Error(i("app.avSearch.validateFltAirline"))):A():A(new Error(i("app.avSearch.validateFltNo")));return}},Y=(N,w,A)=>{if(w&&L&&Number(w)!==Number(L)){A(new Error(i("app.fastQuery.headerQuery.inconsistentNum")));return}A()},H=Re(()=>({departureAirportCode:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:E,trigger:"blur"}],arrivalAirportCode:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:g,trigger:"blur"}],departureDate:[{required:!S.value.isOpen,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"}],cabinCode:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:gn,message:new Error(i("app.fastQuery.headerQuery.inputCorrectCabin")),trigger:"blur"}],airlines:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:oo,message:new Error(i("app.fastQuery.headerQuery.correctAirlineNum")),trigger:"blur"}],flightNumber:[{required:!S.value.isOpen,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{validator:T,trigger:"blur"}],actionCode:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:fn,message:new Error(i("app.fastQuery.headerQuery.inputCorrectActionCode")),trigger:"blur"}],tktNum:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:mn,message:new Error(i("app.fastQuery.headerQuery.inputCorrectTktNum")),trigger:"blur"},{validator:Y,trigger:"blur"}]})),h=()=>{const N=S.value.departureAirportCode;S.value.departureAirportCode=S.value.arrivalAirportCode,S.value.arrivalAirportCode=N;const w=S.value.originName;S.value.originName=S.value.destName,S.value.destName=w},v=()=>{a("inputTktNumBlur")},c=N=>{const w=Ta();return N.getTime()<w.getTime()},V=N=>{S.value.seamlessOrDa===N?S.value.seamlessOrDa="":S.value.seamlessOrDa=N},y=N=>{a("deleteSsFlight",N)},q=async(N,w)=>{a("update:addSegLoading",!0);try{const{data:A}=await bo(w);A.value==="OK"&&(Da({message:i("app.avSearch.addSegmentSuccess"),type:"success"}),lt(`PnrManagement.vue${N}`,N))}finally{a("update:addSegLoading",!1)}},O=(N,w)=>{let A=-1;return S.value.arrivalAirportCode===N.segments[0].departureAirportCode?S.value.departureAirportCode===N.segments[N.segments.length-1].arrivalAirportCode?(A=w+1,A):(A=w,A):((S.value.departureAirportCode===N.segments[N.segments.length-1].arrivalAirportCode||!N.openFlag)&&(A=w+1),A)},k=(N,w)=>{const A=C.value.get(N);Ra(A)?A.rebook.flight=Xe(w):A.flight=Xe(w),A.flightNotNeedSort=!1,C.value.set(N,A),lt(Ue.UPDATE_FLIGHT_LIST,"")},B=(N,w)=>{w.forEach(A=>{y(A);const{departureAirportCode:ae,arrivalAirportCode:ge,departureDate:he,departureTime:pe,arrivalDate:te,arrivalTime:xe,departureTerminal:me,arrivalTerminal:Ee,departureAirportCN:X,arrivalAirportCN:oe,actionCode:M}=A,U={},W={};W.airCode=A.airlines,W.airCN=A.airCN??"",W.isShared=A.isShared??"",W.flightNo=A.isOpen?"OPEN":/^\d+[a_zA_Z]?$/.test(A.flightNumber)?A.flightNumber:A.flightNumber.slice(2),U.departureAirportCode=ae,U.departureAirportCN=X??"",U.arrivalAirportCode=ge,U.arrivalAirportCN=oe??"",U.departureDate=he?`${ue(he).format("YYYY-MM-DD HH:mm")}`:"",U.cabins=[{cabinName:A.cabinCode,state:""}],U.airlines=W,M&&(U.actionCode=M),U.segmentType="0",Object.assign(U,{departureTime:pe,arrivalDate:te,arrivalTime:xe,departureTerminal:me,arrivalTerminal:Ee});const ne={code:A.arrivalAirportCode,name:""},Se={code:A.departureAirportCode,name:""},ke={};if(ke.openFlag=A.isOpen,ke.segments=[U],ke.arriveCity=ne,ke.departureCity=Se,ke.key=no(ke),A.departureDate&&A.isOpen){let Pe=0;N.some((Ae,Fe)=>{if(ue(A.departureDate).isBefore(ue(Ae.segments[0].departureDate),"day"))return Pe=Fe,!0;if(ue(A.departureDate).isSame(ue(Ae.segments[0].departureDate),"day")){if(N.length>1&&N.length!==Fe+1&&Ae.segments[Ae.segments.length-1].arrivalAirportCode===N[Fe+1].segments[0].departureAirportCode)return!1;if(Pe=O(Ae,Fe),Pe>-1)return!0}return Pe=Fe+1,!1}),N.splice(Pe,0,ke),k(_.value,N);return}N.push(ke),p.setFlight(_.value,ke),lt(Ue.UPDATE_FLIGHT_LIST,"")})},ee=async N=>{L=N;let w=!1;return await n.value.validate(A=>{w=A}),w},le=N=>ue(N).isValid()?ue(N).format("YYYY-MM-DDTHH:mm"):"",K=async(N,w)=>{L=w,S.value[N]&&await n.value.validateField(N)},J=N=>{const w={bookAirSegs:[]};return N.forEach(A=>{const{tktNum:ae,actionCode:ge,cabinCode:he,departureAirportCode:pe,arrivalAirportCode:te,departureDate:xe,airlines:me,flightNumber:Ee,departureTerminal:X,arrivalTerminal:oe,isShared:M}=A,U=/^\d+[a_zA_Z]?$/.test(Ee)?Ee:Ee.slice(2),W={fltClass:he??"",orgCity:pe,desCity:te,departureTime:le(xe),arrivalTime:le(xe),airCode:me+U,carrierAirline:M??"",tktNum:(ae==null?void 0:ae.toString())??"",equipmentCode:"",etInd:!0,departureTerminal:X??"",arrivalTerminal:oe??"",actionCode:ge};w.bookAirSegs.push(W)}),w},Z=N=>{var xe;const w=_.value,A=Ra(C.value.get(w));let ae=[],ge=[];const he=C.value.get(w).flight??[],pe=((xe=C.value.get(w).rebook)==null?void 0:xe.flight)??[];if(A?(ae=[...pe],ge=[...he,...pe]):(ae=[...he],ge=[...he]),ge.length>0?ge.length&&ge.some(me=>!me.openFlag):N.some(me=>!me.isOpen)){const me=[],Ee=[];if(N.forEach(oe=>oe.isOpen||e.isGroup||C.value.get(w).type==="1"?me.push(oe):Ee.push(oe)),B(ae,me),!Ee.length){lt(Ue.UPDATE_FLIGHT_LIST,Ue.SS_ADD_FLIGHT_SUCCESS);return}const X=J(Ee);p.setPlaceholderFlightsParams({flightKeys:[],params:X,seizeSeatInfoFlight:[]}),r.dispatch("setFullLoading",!0),lt(Ue.UPDATE_FLIGHT_LIST,`${Ue.EDIT_FLIGHT_PLACEHOLDER}${Ue.SS_ADD_FLIGHT_PLACEHOLDER}`)}else ro.confirm(i("app.avSearch.addOpenFlightTips"),{icon:gt(je,{color:d.value,size:32},()=>gt(so)),customClass:"warning-p-msg crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:i("app.avSearch.confirm")})},Q=()=>{S.value.flightNumber="",S.value.actionCode="",S.value.tktNum=e.tktNum??"",n.value.clearValidate()},F=N=>{f.value=N};return Ke(()=>e.tktNum,()=>{e.tktNum&&(S.value.tktNum=e.tktNum)}),{queryFormRef:n,queryForm:S,FORM_RULES:H,revertFromTo:h,disabledDate:c,datePrefix:I,seamlessOrDaClick:V,agentAirportOriginRef:l,agentAirportDestinationRef:$,openChange:Q,ssValidate:ee,ssValidateField:K,doAddSegment:q,addSsFlightsToCache:Z,focusErrorTip:F,inputTktNumBlur:v,deleteSsFlight:y,currentError:f}},Op={class:"add-ss-flight flex justify-between"},Qp={class:"header-av-query-form flex flex-1"},Rp=Be({__name:"AddSSFlight",props:{cityOrAirport:{},ssQueryForm:{},index:{},showDelete:{type:Boolean},addSegLoading:{type:Boolean},isGroup:{type:Boolean},tktNum:{}},emits:["deleteSsFlight","update:addSegLoading","inputTktNumBlur"],setup(e,{expose:a,emit:i}){const r=e,d=i,{FORM_RULES:n,queryFormRef:l,revertFromTo:$,disabledDate:p,datePrefix:C,openChange:_,queryForm:f,deleteSsFlight:S,inputTktNumBlur:I,agentAirportOriginRef:L,agentAirportDestinationRef:E,ssValidate:g,ssValidateField:T,addSsFlightsToCache:Y,focusErrorTip:H,currentError:h}=Pp(r,d);return a({ssValidate:g,ssValidateField:T,queryForm:f,addSsFlightsToCache:Y}),(v,c)=>{const V=Bn,y=qt,q=je,O=Nt,k=jt;return u(),D("div",Op,[b(k,{ref_key:"queryFormRef",ref:l,model:t(f),rules:t(n),"validate-on-rule-change":!1,class:"flight-from"},{default:x(()=>[o("div",null,[b(y,{class:"open-item",label:v.$t("app.avSearch.openFlight")},{default:x(()=>[b(V,{modelValue:t(f).isOpen,"onUpdate:modelValue":c[0]||(c[0]=B=>t(f).isOpen=B),"inline-prompt":"","active-text":v.$t("app.avSearch.openYes"),"inactive-text":v.$t("app.avSearch.openNo"),onChange:t(_)},null,8,["modelValue","active-text","inactive-text","onChange"])]),_:1},8,["label"])]),o("div",Qp,[b(y,{label:" ",class:"agent-airport-item",prop:"departureAirportCode"},{default:x(()=>[b(Ut,{ref_key:"agentAirportOriginRef",ref:L,modelValue:t(f).departureAirportCode,"onUpdate:modelValue":c[1]||(c[1]=B=>t(f).departureAirportCode=B),name:t(f).originName,"onUpdate:name":c[2]||(c[2]=B=>t(f).originName=B),"is-agent-city":v.cityOrAirport,"prefix-title":v.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),o("div",{class:"mt-[6px] ml-[2px] mr-[2px] w-[14px] h-[14px] cursor-pointer",onClick:c[3]||(c[3]=(...B)=>t($)&&t($)(...B))},[b(q,{class:"sort-text"},{default:x(()=>[b(t(Aa))]),_:1})]),b(y,{class:"agent-airport-item",prop:"arrivalAirportCode"},{default:x(()=>[b(Ut,{ref_key:"agentAirportDestinationRef",ref:E,modelValue:t(f).arrivalAirportCode,"onUpdate:modelValue":c[4]||(c[4]=B=>t(f).arrivalAirportCode=B),name:t(f).destName,"onUpdate:name":c[5]||(c[5]=B=>t(f).destName=B),"is-agent-city":v.cityOrAirport,"prefix-title":v.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),b(y,{label:" ",class:"departure-date",prop:"departureDate"},{default:x(()=>[b(Sa,{modelValue:t(f).departureDate,"onUpdate:modelValue":c[6]||(c[6]=B=>t(f).departureDate=B),type:"date","value-format":"YYYY-MM-DD","auto-compute-date":"","disabled-date":t(p),"disable-end-date":new Date().toString(),"prefix-icon":t(C),placeholder:v.$t("app.avSearch.date")},null,8,["modelValue","disabled-date","disable-end-date","prefix-icon","placeholder"])]),_:1}),b(y,{label:" ",class:ve(["airline-item",t(h)==="cabin"?"currentError":""]),prop:"cabinCode"},{default:x(()=>[b(O,{modelValue:t(f).cabinCode,"onUpdate:modelValue":c[7]||(c[7]=B=>t(f).cabinCode=B),modelModifiers:{trim:!0},class:"airlines-input",placeholder:v.$t("app.avSearch.cabinCode"),onInput:c[8]||(c[8]=B=>t(f).cabinCode=t(f).cabinCode.toUpperCase()),onFocus:c[9]||(c[9]=B=>t(H)("cabin"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"]),b(y,{label:" ",class:ve(["airline-item",t(h)==="airline"?"currentError":""]),prop:"airlines"},{default:x(()=>[b(O,{modelValue:t(f).airlines,"onUpdate:modelValue":c[10]||(c[10]=B=>t(f).airlines=B),modelModifiers:{trim:!0},class:"airlines-input",placeholder:v.$t("app.avSearch.airline"),onInput:c[11]||(c[11]=B=>t(f).airlines=t(f).airlines.toUpperCase()),onFocus:c[12]||(c[12]=B=>t(H)("airline"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"]),t(f).isOpen?de("",!0):(u(),Ce(y,{key:0,label:" ",class:ve([t(h)==="flightNo"?"currentError":""]),prop:"flightNumber"},{default:x(()=>[b(O,{modelValue:t(f).flightNumber,"onUpdate:modelValue":c[13]||(c[13]=B=>t(f).flightNumber=B),modelModifiers:{trim:!0},class:"small-width",placeholder:v.$t("app.avSearch.flightNumber"),onInput:c[14]||(c[14]=B=>t(f).flightNumber=t(f).flightNumber.toUpperCase()),onFocus:c[15]||(c[15]=B=>t(H)("flightNo"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"])),!t(f).isOpen&&!v.isGroup?(u(),Ce(y,{key:1,label:" ",class:ve([t(h)==="actionCode"?"currentError":""]),prop:"actionCode"},{default:x(()=>[b(O,{modelValue:t(f).actionCode,"onUpdate:modelValue":c[16]||(c[16]=B=>t(f).actionCode=B),modelModifiers:{trim:!0},class:"small-width",placeholder:v.$t("app.avSearch.applicationStatus"),onInput:c[17]||(c[17]=B=>{var ee;return t(f).actionCode=(ee=t(f).actionCode)==null?void 0:ee.toUpperCase()}),onFocus:c[18]||(c[18]=B=>t(H)("actionCode"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"])):de("",!0),!t(f).isOpen&&!v.isGroup?(u(),Ce(y,{key:2,label:" ",class:ve([t(h)==="tktNum"?"currentError":""]),prop:"tktNum"},{default:x(()=>[b(O,{modelValue:t(f).tktNum,"onUpdate:modelValue":c[19]||(c[19]=B=>t(f).tktNum=B),modelModifiers:{trim:!0},class:"airlines-input",disabled:!!v.tktNum,placeholder:v.$t("app.avSearch.numberOfPeople"),onFocus:c[20]||(c[20]=B=>t(H)("tktNum")),onBlur:t(I)},null,8,["modelValue","disabled","placeholder","onBlur"])]),_:1},8,["class"])):de("",!0)])]),_:1},8,["model","rules"]),v.showDelete?(u(),D("div",{key:0,class:"text-center mb-[18px] ml-[5px]",onClick:c[21]||(c[21]=B=>t(S)(v.ssQueryForm))},[b(q,{class:"cursor-pointer iconfont icon-delete",size:"20",color:"var(--bkc-el-color-primary)"})])):de("",!0)])}}});const Mp=ze(Rp,[["__scopeId","data-v-92e010ae"]]),Vp={class:"add-group-flight"},Bp={class:"flex justify-end"},Up=Be({__name:"AddGroupFlight",props:{cityOrAirport:{}},emits:["addGroupFlight"],setup(e,{emit:a}){const i=a,{t:r}=ct(),d=P(),n=P({departureAirportCode:"",originName:"",arrivalAirportCode:"",destName:"",departureDate:""}),l=nt({render(){return gt("em",{class:"iconfont icon-calendar"})}}),$=(L,E,g)=>{if(E.length>3||!$t.test(E)){g(new Error(r("app.fastQuery.headerQuery.formatError")));return}if(n.value.arrivalAirportCode&&E===n.value.arrivalAirportCode){g(new Error(r("app.fastQuery.headerQuery.identical")));return}g()},p=(L,E,g)=>{if(E.length>3||!$t.test(E)){g(new Error(r("app.fastQuery.headerQuery.formatError")));return}if(n.value.departureAirportCode&&E===n.value.departureAirportCode){g(new Error(r("app.fastQuery.headerQuery.identical")));return}g()},C=Re(()=>({departureAirportCode:[{required:!0,message:r("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:$,trigger:"blur"}],arrivalAirportCode:[{required:!0,message:r("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:p,trigger:"blur"}]})),_=()=>{const L=n.value.departureAirportCode;n.value.departureAirportCode=n.value.arrivalAirportCode,n.value.arrivalAirportCode=L;const E=n.value.originName;n.value.originName=n.value.destName,n.value.destName=E},f=L=>{const E=Ta();return L.getTime()<E.getTime()},S=()=>{n.value={departureAirportCode:"",originName:"",arrivalAirportCode:"",destName:"",departureDate:""},d.value.clearValidate(),d.value.resetFields()},I=()=>{d.value.validate(L=>{if(L){const{departureAirportCode:E,arrivalAirportCode:g,departureDate:T}=n.value,Y={date:{departureDate:T},segments:[{airlines:{flightNo:"ARNK"},departureAirportCode:E,arrivalAirportCode:g,departureDate:ue(T).isValid()?`${ue(T).format("YYYY-MM-DD")} 00:00`:"",segmentType:"0"}]};S(),i("addGroupFlight",Y)}})};return(L,E)=>{const g=qt,T=je,Y=jt,H=ra;return u(),D("div",Vp,[b(Y,{ref_key:"groupSegmentFormRef",ref:d,model:n.value,rules:C.value,"validate-on-rule-change":!1,class:"group-flight-from",onKeyup:aa(I,["enter"])},{default:x(()=>[b(g,{label:" ",class:"agent-airport-item",prop:"departureAirportCode"},{default:x(()=>[b(Ut,{ref:"agentAirportOriginRef",modelValue:n.value.departureAirportCode,"onUpdate:modelValue":E[0]||(E[0]=h=>n.value.departureAirportCode=h),name:n.value.originName,"onUpdate:name":E[1]||(E[1]=h=>n.value.originName=h),"is-agent-city":L.cityOrAirport,"prefix-title":L.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),o("div",{class:"revert-icon",onClick:_},[b(T,{class:"sort-text"},{default:x(()=>[b(t(Aa))]),_:1})]),b(g,{class:"agent-airport-item",prop:"arrivalAirportCode"},{default:x(()=>[b(Ut,{ref:"agentAirportDestinationRef",modelValue:n.value.arrivalAirportCode,"onUpdate:modelValue":E[2]||(E[2]=h=>n.value.arrivalAirportCode=h),name:n.value.destName,"onUpdate:name":E[3]||(E[3]=h=>n.value.destName=h),"is-agent-city":L.cityOrAirport,"prefix-title":L.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),b(g,{label:" ",class:"departure-date",prop:"departureDate"},{default:x(()=>[b(Sa,{modelValue:n.value.departureDate,"onUpdate:modelValue":E[4]||(E[4]=h=>n.value.departureDate=h),type:"date","value-format":"YYYY-MM-DD","auto-compute-date":"","disabled-date":f,"disable-end-date":new Date().toString(),"prefix-icon":l.value,placeholder:L.$t("app.avSearch.date")},null,8,["modelValue","disable-end-date","prefix-icon","placeholder"])]),_:1})]),_:1},8,["model","rules"]),o("div",Bp,[b(H,{type:"primary",onClick:I},{default:x(()=>[fe(m(L.$t("app.avSearch.addEnter")),1)]),_:1})])])}}});const Yp=ze(Up,[["__scopeId","data-v-384be854"]]),Hp=e=>{const a={[qe.FLIGHT_SS]:{value:"",required:!0},[qe.COMPANY_CODE]:{value:"",required:!0},[qe.FLIGHT_NUMBER]:{value:"",required:!0},[qe.CABIN_CODE]:{value:"",required:!0},[qe.DEPARTURE_DATE]:{value:"",required:!0},[qe.DEPARTURE_AIRPORT]:{value:"",required:!0},[qe.ARRIVAL_AIRPORT]:{value:"",required:!0}};return e||Object.assign(a,{[qe.ACTION_CODE]:{value:"",required:!0},[qe.TKT_NUM]:{value:"",required:!0}}),a},Ca=(e,a)=>{const i=a?qe.ARRIVAL_AIRPORT:qe.TKT_NUM,r=Hp(a);let d=e.replaceAll(/ */gi,"");try{if(Object.keys(r).forEach(n=>{const l=vn.get(n);if(l){const $=l.exec(d),p=r[n];if(!$||$.index){if(p!=null&&p.required)throw new Error;return}p.value=$[0],d=d.slice($[0].length),n===i&&(d=""),n===qe.FLIGHT_NUMBER&&/^[a-zA-Z]+$/.test(d.slice(0,2))&&(p.value=`${p.value}${d.slice(0,1)}`,d=d.slice(1))}}),d)throw new Error;if(r[qe.DEPARTURE_DATE].value.length>5){const n=To(r[qe.DEPARTURE_DATE].value);if(ue(`${n}`).isBefore(ue(new Date).format("YYYY-MM-DD")))return{valid:!0,dateValid:!1,flightInfoForm:r}}return{valid:!0,dateValid:!0,flightInfoForm:r}}catch{return{valid:!1,dateValid:!0,flightInfoForm:r}}},qp=e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),To=e=>{if(wn.test(e)){const a=ue(new Date).format("YYYY").slice(0,2),[i,r,d]=e.match(/(\d{2})([A-Za-z]{3})(\d{2})/).slice(1),n=`${a}${d}`,l=ue(`${n}-${qp(r)}-${i}`,"YYYY-MMM-DD");if(!l.isValid())throw new Error;return l.format("YYYY-MM-DD")}else{const a=po(e,ue(new Date).format("YYYY-MM-DD").substring(0,4)),i=ue(`${a}`);return i.isBefore(ue(new Date).format("YYYY-MM-DD"))?i.add(1,"year").format("YYYY-MM-DD"):i.format("YYYY-MM-DD")}},jp=(e,a)=>{var r,d;const i={id:$a(),airlines:e[qe.COMPANY_CODE].value,isOpen:!1,destName:"",originName:"",seamlessOrDa:"",departureAirportCode:e[qe.DEPARTURE_AIRPORT].value,arrivalAirportCode:e[qe.ARRIVAL_AIRPORT].value,departureDate:To(e[qe.DEPARTURE_DATE].value),cabinCode:e[qe.CABIN_CODE].value,flightNumber:e[qe.FLIGHT_NUMBER].value};return a||(i.actionCode=((r=e[qe.ACTION_CODE])==null?void 0:r.value)??"",i.tktNum=((d=e[qe.TKT_NUM])==null?void 0:d.value)??""),i},Gp=(e,a,i)=>{const r=[];return e.some(l=>{const{valid:$,flightInfoForm:p}=Ca(l,i);return a&&!i&&(p[qe.TKT_NUM].value=a),r.push(p),!$})?[]:r.map(l=>jp(l,i))??[]},Ja=e=>({isOpen:!1,destName:"",originName:"",departureAirportCode:"",arrivalAirportCode:"",departureDate:"",airlines:"",cabinCode:"",seamlessOrDa:"",flightNumber:"",actionCode:"",tktNum:e,id:$a()}),Kp=e=>{const{t:a}=ct(),i=P(!1),r=P(),d=P(!1),n=P(),l=It(),$=Xt(),{activeTag:p,orderInfo:C,originPnrData:_}=Yt(l),f=e.avQueryFromFastQuery?Qt:p.value,S=Re(()=>{var R,ie;return C.value.get(p.value).type==="1"?(R=C.value.get(p.value))==null?void 0:R.group:((ie=C.value.get(p.value))==null?void 0:ie.psgType)==="1"}),I=Re(()=>{var R,ie;return(ie=(R=C.value.get(p.value))==null?void 0:R.newChange)==null?void 0:ie.isNewVoluntaryRescheduling}),L=Re(()=>{var R;return(((R=C.value.get(p.value))==null?void 0:R.type)??"")==="2"}),E=Re(()=>{var R;return((R=Array.from(C.value.keys()))==null?void 0:R.find(ie=>{var Te,$e;return($e=(Te=C.value.get(ie))==null?void 0:Te.specialContent)==null?void 0:$e.includes(a("app.basic.occupy"))}))??""}),g=Re(()=>{var ie,Te,$e;const R=(Te=(ie=C.value.get(p.value))==null?void 0:ie.flight)==null?void 0:Te.find(Ie=>Ie.segments.some(Ge=>Number(Ge.segmentType)));return(($e=R==null?void 0:R.segments[0])==null?void 0:$e.tktNum)??""}),T=ca(),Y=P(),H=P(0),h=P(!1),v=P({}),c=P(""),V=P(),y=P("AV"),q=Re(()=>{const R=T.flightByDate[f].flightDataList;return R.size>0||(B.value=""),R}),O=P(!0),k=P(""),B=P(""),ee=new Map,le=new Map,K=new Map,J=nt(),Z=P(1),Q=P({departureCity:"",arriveCity:""});let F=[];const N=P(),w=P(),A=P([Ja(g.value)]),ae=P([]),ge=P(),he=P({segmentList:""}),pe=P(!1),te=P(!1),xe=()=>{var ie;const R=(ie=$.state.user)==null?void 0:ie.agentManageStatus;return R===3||R===4},me=(R,ie,Te)=>{if(ie){const Ge=ie.replace(/\n+/gi,`
`).replace(/ +/gi," ").trim().split(`
`),Je=Ge.some(Ve=>!Ca(Ve,S.value).valid),Ze=Ge.some(Ve=>!Ca(Ve,S.value).dateValid);!Je&&Ze?Te(new Error(a("app.fastQuery.useInternationalPriceQuery.flightDateError"))):Je&&!Ze&&Te(new Error(a("app.fastQuery.useInternationalPriceQuery.flightErrorError")))}Te()},Ee={segmentList:[{required:!0,message:a("app.fastQuery.useInternationalPriceQuery.required"),trigger:"blur"},{validator:me,trigger:"blur"}]},X=R=>{const ie=[];return R.carrierFlight&&ie.push("O"),R.lowestPrice&&ie.push("P"),R.timeSequence&&ie.push("E"),R.unsharedFlight&&ie.push("NO"),ie.length>0?ie.join("|"):""},oe=(R,ie,Te,$e)=>{var _t,Me,rt,Ct,At;const Ie=Co(p.value,C.value),Ge=((Me=(_t=_.value)==null?void 0:_t.get(p.value))==null?void 0:Me.flight)??[],Je=$e?M(R.origin,R.destination,$e):M(R.origin,R.destination,R.departureDate),Ze=((Ct=(rt=T.flightByDate[f].flightDataList.get(Je))==null?void 0:rt.queryForm)==null?void 0:Ct.departureDate)??"";c.value=((At=T.flightByDate[f].flightDataList.get(Je))==null?void 0:At.sessionId)??"";const Ve=ie&&Te?Ze:R.departureDate,ye={departureCity:R.origin??"",arriveCity:R.destination??"",departureTime:R.departureDateTime??"",departureDate:Ve??"",flightNo:R.flightNumber??"",onlyDirect:R.onlyDirectFlight?"D":"",airCode:R.airlines??"",seamlessOrDa:R.seamlessOrDa==="DA"?R.seamlessOrDa:"seamless",queryType:"REAL_TIME_AV",pagingType:"R",sessionId:"",flightType:"",transitCity:(R.transitTerminal??"").toUpperCase()};return L.value&&Ie.length&&!e.avQueryFromFastQuery&&(ye.preOccupySegmentInfoList=ia(!1,Ie)),!L.value&&Ge.length&&!e.avQueryFromFastQuery&&(ye.preOccupySegmentInfoList=ia(!0,Ge)),ye.flightType=X(R),c.value&&(ye.sessionId=c.value??""),Te&&(ye.pagingType=Te),ye.pagingType==="R"&&(Z.value=1,ye.sessionId=""),ye},M=(R,ie,Te)=>`${R}${ie}${Te}`,U=async R=>{v.value=R,ne()},W=async()=>{let R=!1;const ie=pe.value&&te.value;return await yn(ie,a,gt)&&(R=!0),R},ne=async(R,ie,Te)=>{var $e,Ie,Ge,Je,Ze,Ve;i.value=!0,h.value=!1;try{const ye=R&&ie&&Te&&JSON.stringify(Te)!=="{}"?Xe(Te):Xe(v.value),Me=(await yo(oe(ye,R,ie,ee.get(`${ye.origin}${ye.destination}${ye.departureDate}`)),"01010207")).data.value;if(((Me==null?void 0:Me.flightInfoList)??[]).length<=0){J.value=[];return}if(V.value=ye,R&&ie&&(ie==="N"?Z.value=R+1:ie==="P"&&(Z.value=R-1)),Me.sessionId){if(c.value=Me.sessionId,Me.firstQueryDate=ye.departureDate,le.set(`${ye.origin}${ye.destination}${ye.departureDate}`,ye.departureDate),(Me.flightInfoList??[]).length>0&&Me.flightInfoList[0].segments&&Me.flightInfoList[0].segments[0].departureDate){const ut=ue(Me.flightInfoList[0].segments[0].departureDate).format("YYYY-MM-DD");ye.departureDate!==ut&&ee.set(`${ye.origin}${ye.destination}${ye.departureDate}`,ut)}}else Me.sessionId=c.value,Me.firstQueryDate=le.get(`${ye.origin}${ye.destination}${ye.departureDate}`)||ye.departureDate;O.value=Me.flightInfoList[0].segments[0].departureDate?ue(Me.flightInfoList[0].segments[0].departureDate).format("YYYY-MM-DD")===ye.departureDate:!0,J.value=el(Me.flightInfoList),Me.flightInfoList=J.value;let rt="",Ct="",At="";if((Me.flightInfoList??[]).length>0&&Me.flightInfoList[0].segments){let ut="",zt="";const Ft=Me.flightInfoList[0].segments,wa=Ft[0].departureDate?ue(Ft[0].departureDate).format("YYYY-MM-DD"):ye.departureDate;if(K.set(`${ye.origin}${ye.destination}${ye.departureDate}`,wa),ye.flightNumber){if(!F.length){const da=await st("searchLocalData");F=JSON.parse((da==null?void 0:da.localData)??"")}ut=(($e=Ft==null?void 0:Ft[0])==null?void 0:$e.departureAirportCode)??"",zt=((Ie=Ft[Ft.length-1])==null?void 0:Ie.arrivalAirportCode)??""}else ut=ye.origin,zt=ye.destination;const Na=ee.get(`${ye.origin}${ye.destination}${ye.departureDate}`);At=Na?M(ut,zt,Na):M(ut,zt,ye.departureDate),Ct=M(ut,zt,wa),rt=Ct}else rt=M(ye.origin,ye.destination,ye.departureDate);if(Me.sessionId!==null){const ut={...ye};Q.value.departureCity=ut.origin,Q.value.arriveCity=ut.destination,B.value=`${rt}+${new Date().getTime().toString()}`}Me||(Z.value=((Je=(Ge=q==null?void 0:q.value)==null?void 0:Ge.get(rt))==null?void 0:Je.currentPage)??1),(ie==="P"||ie==="N")&&(Ct!==At&&T.delFlightDataList(f,At),(Me.flightInfoList??[]).length>0&&Me.flightInfoList[0].segments&&Me.flightInfoList[0].segments[0].departureDate?ee.set(`${ye.origin}${ye.destination}${ye.departureDate}`,ue((Ve=(Ze=Me.flightInfoList[0])==null?void 0:Ze.segments[0])==null?void 0:Ve.departureDate).format("YYYY-MM-DD")):ee.set(`${ye.origin}${ye.destination}${ye.departureDate}`,ye.departureDate)),k.value=rt.substring(rt.length-10),T.setFlightDataList(f,Xe(ye),Me,rt,Z.value)}finally{i.value=!1}},Se=async R=>{v.value=R,await ne(),Rt(()=>{!e.avQueryFromFastQuery&&(J.value??[]).length>0&&(h.value=!0)},300)()},ke=async(R,ie)=>{v.value=R,await ne(),v.value=ie,await ne(),Rt(()=>{!e.avQueryFromFastQuery&&(J.value??[]).length>0&&(h.value=!0)},300)()},Pe=R=>{N.value=R},Ae=R=>{const ie=k.value||v.value.departureDate;R==="pre"&&ie!==js()&&(v.value.departureDate=Ks(ie),ne()),R==="next"&&(v.value.departureDate=Gs(ie),ne())},Fe=R=>{Q.value.departureCity=R.substring(0,3),Q.value.arriveCity=R.substring(3,6)},He=R=>{var ie,Te,$e,Ie,Ge,Je,Ze,Ve;k.value=(R==null?void 0:R.substring(R.length-10))??"",q.value.size>=1?(J.value=((Te=(ie=q==null?void 0:q.value)==null?void 0:ie.get(R))==null?void 0:Te.flightInfoList)??[],Z.value=((Ie=($e=q==null?void 0:q.value)==null?void 0:$e.get(R))==null?void 0:Ie.currentPage)??1,c.value=((Je=(Ge=q==null?void 0:q.value)==null?void 0:Ge.get(R))==null?void 0:Je.sessionId)??"",v.value=((Ve=(Ze=q==null?void 0:q.value)==null?void 0:Ze.get(R))==null?void 0:Ve.queryForm)??{},v.value.departureDate=le.get(`${v.value.origin}${v.value.destination}${v.value.departureDate}`)||v.value.departureDate,B.value=R,Fe(R),V.value=v.value,O.value=(R==null?void 0:R.substring(6))===v.value.departureDate):(!R&&(h.value=!1),J.value=[],O.value=!0)},We=(R,ie)=>{Object.assign(v.value,{departureDate:ie.departureDate,destination:ie.arrivalAirportCode,origin:ie.departureAirportCode});const Te=M(v.value.origin,v.value.destination,v.value.departureDate);T.updateFlightDataListKey(f,Te,R,{...v.value}),ne()},j=(R,ie)=>{var Je,Ze,Ve,ye,_t,Me,rt,Ct,At;const Te=((Je=T.flightByDate[f])==null?void 0:Je.activeFlightIndex)??0,$e=Array.from(T.flightByDate[f].flightDataList.entries())[Te][1],Ie=(Ze=B.value)==null?void 0:Ze.slice(6,16);!ee.get(`${(Ve=$e==null?void 0:$e.queryForm)==null?void 0:Ve.origin}${(ye=$e==null?void 0:$e.queryForm)==null?void 0:ye.destination}${(_t=$e==null?void 0:$e.queryForm)==null?void 0:_t.departureDate}`)&&Ie!==((Me=$e==null?void 0:$e.queryForm)==null?void 0:Me.departureDate)&&ee.set(`${(rt=$e==null?void 0:$e.queryForm)==null?void 0:rt.origin}${(Ct=$e==null?void 0:$e.queryForm)==null?void 0:Ct.destination}${(At=$e==null?void 0:$e.queryForm)==null?void 0:At.departureDate}`,Ie),ne(Z.value,ie,($e==null?void 0:$e.queryForm)??{})},G=async()=>{const R=ao("08100117"),{data:ie}=await Ci(R);w.value=ie.value},ce=R=>{l.setAvTabCheck(p.value,R==="AV"),R==="AV"&&ot(),y.value=R,h.value=!1},z=()=>{var R;H.value=((R=Y.value)==null?void 0:R.getBoundingClientRect().top)??0},se=()=>{l.setDefaultJumpHistory(p.value)},re=()=>{A.value.push({isOpen:!1,destName:"",originName:"",departureAirportCode:"",arrivalAirportCode:"",departureDate:"",airlines:"",cabinCode:"",seamlessOrDa:"",flightNumber:"",id:$a()})},De=(R,ie,Te)=>{R&&(ae.value[Te]=R)},_e=()=>{var ie,Te;const R=((Te=(ie=ae.value.find($e=>{var Ie,Ge;return(Ge=(Ie=$e.queryForm)==null?void 0:Ie.tktNum)==null?void 0:Ge.toString()}))==null?void 0:ie.queryForm)==null?void 0:Te.tktNum)??"";ae.value.forEach($e=>$e.ssValidateField("tktNum",R))},Le=async()=>{var Je,Ze;const R=((Ze=(Je=ae.value.find(Ve=>{var ye,_t;return(_t=(ye=Ve.queryForm)==null?void 0:ye.tktNum)==null?void 0:_t.toString()}))==null?void 0:Je.queryForm)==null?void 0:Ze.tktNum)??"";A.value=[];const ie=[];for(let Ve=0;Ve<ae.value.length;Ve++)ie.push(ae.value[Ve].ssValidate(R)),A.value.push(ae.value[Ve].queryForm);const Te=A.value.some(Ve=>!Ve.isOpen);if(L.value&&!S.value&&Te&&await W()||(await Promise.all(ie)).some(Ve=>!Ve))return;const Ie=C.value.get(p.value).flight??[];if(!(Ie.length>0?Ie.length&&Ie.some(Ve=>!Ve.openFlag):A.value.some(Ve=>!Ve.isOpen))){ro.confirm(a("app.avSearch.addOpenFlightTips"),{icon:gt(je,{color:io("--bkc-el-color-primary",null).value,size:32},()=>gt(so)),customClass:"warning-p-msg crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:a("app.avSearch.confirm")});return}S.value&&(d.value=!0,await Xs(A.value,F,p.value,_.value,C.value),d.value=!1),A.value.length&&ae.value[0].addSsFlightsToCache(A.value)},Qe=()=>{ae.value=[],ot(),A.value=[Ja(g.value)],y.value="AV"},Oe=R=>{A.value=A.value.filter(ie=>ie.id!==R.id),ae.value=ae.value.filter(ie=>ie.queryForm.id!==R.id)},at=R=>{var ie;T.setClickFlightSearchFlag(f,!((ie=T.flightByDate[f])!=null&&ie.clickFlightSearchFlag)),V.value=Xe(R),v.value=R},Ne=R=>{ee.delete(R)},it=R=>R.replace(/\n+/gi,`
`).replace(/ +/gi," ").trim().split(`
`),dt=()=>{var R;(R=ge.value)==null||R.validate(async ie=>{if(ie){const Te=Gp(it(he.value.segmentList),g.value,S.value);A.value.push(...Te);const $e=A.value.filter(Ie=>!(Ie.departureAirportCode&&Ie.arrivalAirportCode&&Ie.departureDate&&Ie.cabinCode&&Ie.flightNumber&&Ie.airlines)).map(Ie=>Ie.id);ae.value=ae.value.filter(Ie=>!$e.includes(Ie.queryForm.id)),A.value=A.value.filter(Ie=>Ie.departureAirportCode||Ie.arrivalAirportCode||Ie.departureDate||Ie.cabinCode||Ie.flightNumber||Ie.airlines)}})},ht=()=>{var R;e.avQueryFromFastQuery&&((R=n.value)==null||R.closeCabinPopover())},ot=()=>{var R;(R=ge.value)==null||R.resetFields(),he.value.segmentList=""},bt=R=>{var ie;R!=null&&R.includes(Ue.PLACEHOLDER_FLIGHT)&&l.placeholderFlightsQueryForm.origin&&(V.value=Xe(l.placeholderFlightsQueryForm),l.setPlaceholderFlightsQueryForm({}),(ie=r.value)==null||ie.search())},Kt=R=>{const ie=p.value,Te=`ARNK/${ie}/${new Date().getTime().toString()}`;l.setFlight(ie,{...R,key:Te}),lt(Ue.UPDATE_FLIGHT_LIST,""),y.value="AV"};return mt(async()=>{pe.value=await $.getters.userSeatDisabled,te.value=xe(),$.dispatch("setFullLoading",!1);const R=p.value,ie=await st("searchLocalData");F=JSON.parse((ie==null?void 0:ie.localData)??""),z(),T.flightByDate[f]||T.initAgentSell(f),await G(),Wt.on(Ue.UPDATE_FLIGHT_LIST,Te=>{!e.avQueryFromFastQuery&&R&&R===p.value&&bt(Te),Te!=null&&Te.includes(Ue.SS_ADD_FLIGHT_SUCCESS)&&R===p.value&&Qe()}),!e.avQueryFromFastQuery&&R&&bt(Ue.PLACEHOLDER_FLIGHT)}),Et(()=>{l.setPlaceholderFlightsParams({flightKeys:[],params:{bookAirSegs:[]},seizeSeatInfoFlight:[]}),F.length=0,J.value=[]}),{searchClick:Se,searchRoundClick:ke,placeholderFlagTag:E,title:Q,checkTab:ce,avSearchData:J,screeningDirect:U,loadingInstance:i,queryData:He,searchCompleteKey:B,turnPage:j,currentPage:Z,queryResDateGroupBySessionId:K,changeDate:Ae,queryForm:v,tabType:y,callQueryApi:We,scrollHide:h,flightOpRightRef:Y,flightOpRightTop:H,cityOrAirport:w,newChangeFlag:I,toBack:se,addMultSs:re,ssQueryFormList:A,addSsFlight:Le,deleteSsFlight:Oe,handleSetFromRefList:De,historyQueryForm:V,addSegLoading:d,echoAndQuery:at,closeAVHistory:Ne,isQueryCurrentDay:O,importFormRef:ge,importForm:he,IMPORT_FORM_RULES:Ee,importSsFlight:dt,flightContainerRef:n,closeCabinPopover:ht,addGroupFlight:Kt,headerAvQueryRef:r,resetSsImportClick:ot,tktNum:g,inputTktNumBlur:_e,isGroup:S,fromRefList:ae,flightSearchFlag:f,getRoundTripFlag:Pe,roundTripFlag:N}},ko=e=>(Lt("data-v-d622a442"),e=e(),wt(),e),zp={class:"overflow-hidden"},Jp={class:"ml-[4px] h-[20px] leading-[20px]"},Wp={class:"mr-[20px] font-bold leading-normal text-gray-1"},Zp={key:0,class:"h-full flex flex-col gap-[14px]"},Xp=ko(()=>o("div",{class:"mb-0 border-b border-dashed border-gray-5"},null,-1)),eg={class:"flex-1 overflow-hidden"},tg={class:"flex justify-center items-center flex-col mt-[86px]"},ag=["alt"],og={class:"mt-[19px] text-lg leading-[24px] font-bold text-gray-2"},ng={class:"text-base text-gray-4"},ig={key:1,class:"w-full ss-box"},rg={class:"justify-end flex items-end"},sg={key:0,class:"warning-tip mb-[16px] p-[4px] flex items-center text-yellow-1 bg-yellow-3 border border-solid border-yellow-2 rounded-sm text-[12px]"},lg={class:"flex items-center"},cg=ko(()=>o("em",{class:"iconfont icon-warning-circle-fill mr-[6px]"},null,-1)),dg={class:"flex justify-end"},ug={key:2},pg=Be({__name:"FlightOpRight",props:{editFlag:{type:Boolean},avQueryFromFastQuery:{type:Boolean}},setup(e,{expose:a}){const i=e,{avSearchData:r,screeningDirect:d,queryData:n,tabType:l,searchCompleteKey:$,turnPage:p,currentPage:C,callQueryApi:_,scrollHide:f,flightOpRightTop:S,flightContainerRef:I,closeCabinPopover:L,isGroup:E,queryResDateGroupBySessionId:g,changeDate:T,queryForm:Y,searchClick:H,searchRoundClick:h,cityOrAirport:v,loadingInstance:c,checkTab:V,newChangeFlag:y,toBack:q,flightOpRightRef:O,historyQueryForm:k,addGroupFlight:B,inputTktNumBlur:ee,addMultSs:le,ssQueryFormList:K,addSsFlight:J,deleteSsFlight:Z,handleSetFromRefList:Q,addSegLoading:F,echoAndQuery:N,closeAVHistory:w,isQueryCurrentDay:A,importFormRef:ae,importForm:ge,tktNum:he,fromRefList:pe,IMPORT_FORM_RULES:te,importSsFlight:xe,resetSsImportClick:me,placeholderFlagTag:Ee,headerAvQueryRef:X,flightSearchFlag:oe,getRoundTripFlag:M,roundTripFlag:U}=Kp(i);return a({echoAndQuery:N}),(W,ne)=>{const Se=je,ke=ra,Pe=Nt,Ae=qt,Fe=jt,He=xa,We=fo,j=sa;return u(),D("div",{ref_key:"flightOpRightRef",ref:O,class:ve([W.avQueryFromFastQuery?"fast-query-flight-op-right":"flight-op-right"]),onClick:ne[10]||(ne[10]=G=>t(L)())},[o("div",zp,[W.editFlag?(u(),D("div",{key:0,class:ve(["transition-height duration-700 flex items-center relative",t(f)?"h-0":"h-[20px] my-1"])},[ft(W.$slots,"default",{},void 0,!0)],2)):W.avQueryFromFastQuery?de("",!0):(u(),D("div",{key:1,class:ve(["transition-height duration-700 flex items-center relative",t(f)?"h-0":"h-[20px] my-1"])},[t(y)?(u(),D("div",{key:0,class:"back-box text-brand-2 cursor-pointer flex items-center mr-[10px]",onClick:ne[0]||(ne[0]=(...G)=>t(q)&&t(q)(...G))},[b(Se,{class:"icon iconfont icon-left"}),o("div",Jp,m(W.$t("app.pnrManagement.btnGroups.Back")),1)])):de("",!0),o("div",Wp,m(W.$t("app.pnrManagement.flight.addSegmentTitle")),1),o("div",null,[o("div",{class:ve(["tab-container rounded-tl-sm rounded-bl-sm border-y border-l",{"active-container border-r":t(l)==="AV"}]),onClick:ne[1]||(ne[1]=G=>t(V)("AV"))},[o("div",{class:ve(["tab-container-box",{"active-box":t(l)==="AV"}])},m(`AV${W.$t("app.avSearch.search")}[AV]`),3)],2),o("div",{class:ve(["tab-container border-y border-x",{"active-container border-l":t(l)==="SS"}]),onClick:ne[2]||(ne[2]=G=>t(V)("SS"))},[o("div",{class:ve(["tab-container-box",{"active-box":t(l)==="SS"}])},m(`${W.$t("app.avSearch.addSeg")}[SS]`),3)],2),o("div",{class:ve(["tab-container rounded-tr-sm rounded-br-sm border-y border-r",{"active-container border-l":t(l)==="SA"}]),onClick:ne[3]||(ne[3]=G=>t(V)("SA"))},[o("div",{class:ve(["tab-container-box",{"active-box":t(l)==="SA"}])},m(`${W.$t("app.pnrManagement.flight.addGroundSection")}[SA]`),3)],2)]),t(l)==="SS"?(u(),Ce(ke,{key:1,link:"",type:"primary",size:"small",class:"ml-auto mr-3.5",onClick:t(le)},{default:x(()=>[fe(m(W.$t("app.avSearch.addMult")),1)]),_:1},8,["onClick"])):de("",!0)],2))]),o("div",{class:ve(["mt-[14px] flex-1 flex-col overflow-hidden",t(f)||W.avQueryFromFastQuery?"!mt-0":""])},[t(l)==="AV"?(u(),D("div",Zp,[o("div",{class:ve(["transition-height duration-75 overflow-hidden",t(f)?"max-h-0":"max-h-[200px]"])},[b(Al,{ref_key:"headerAvQueryRef",ref:X,"av-query-from-fast-query":W.avQueryFromFastQuery,"city-or-airport":t(v),"history-query-form":t(k),"flight-search-flag":t(oe),onSearchClick:t(H),onSearchRoundClick:t(h),onGetRoundTripFlag:t(M)},null,8,["av-query-from-fast-query","city-or-airport","history-query-form","flight-search-flag","onSearchClick","onSearchRoundClick","onGetRoundTripFlag"]),Xp],2),b(Pl,{class:"mr-[6px]","av-query-from-fast-query":W.avQueryFromFastQuery,"search-complete-key":t($),"query-form":t(Y),"round-trip-flag":t(U),onQueryData:t(n),onCallQueryApi:t(_),onCloseAVHistory:t(w)},null,8,["av-query-from-fast-query","search-complete-key","query-form","round-trip-flag","onQueryData","onCallQueryApi","onCloseAVHistory"]),yt((u(),D("div",eg,[(t(r)??[]).length>0?(u(),Ce(Ep,{key:0,ref_key:"flightContainerRef",ref:I,scrollHide:t(f),"onUpdate:scrollHide":ne[4]||(ne[4]=G=>tt(f)?f.value=G:null),"search-complete-key":t($),"query-form":t(Y),"av-search-data":t(r),"current-page":t(C),"query-res-date-group-by-session-id":t(g),"flight-op-right-top":t(S),"placeholder-flag-tag":t(Ee),"av-query-from-fast-query":W.avQueryFromFastQuery,"is-query-current-day":t(A),onScreeningDirect:t(d),onChangeDate:t(T),onTurnPage:t(p)},null,8,["scrollHide","search-complete-key","query-form","av-search-data","current-page","query-res-date-group-by-session-id","flight-op-right-top","placeholder-flag-tag","av-query-from-fast-query","is-query-current-day","onScreeningDirect","onChangeDate","onTurnPage"])):de("",!0),o("div",tg,[o("img",{src:An,alt:W.$t("app.fastQuery.skQuerys.nodata")},null,8,ag),o("div",og,m(W.$t("app.fastQuery.skQuerys.noOrderInfo")),1),o("div",ng,m(W.$t("app.fastQuery.skQuerys.plChangeConditionsSearchAgain")),1)])])),[[j,t(c)]])])):t(l)==="SS"?yt((u(),D("div",ig,[b(We,null,{default:x(()=>[b(Fe,{ref_key:"importFormRef",ref:ae,model:t(ge),rules:t(te),inline:!0,class:"import-flight-from"},{default:x(()=>[b(Ae,{label:W.$t("app.avSearch.fastImport"),class:"import-flight-input",prop:"segmentList"},{default:x(()=>[b(Pe,{modelValue:t(ge).segmentList,"onUpdate:modelValue":ne[5]||(ne[5]=G=>t(ge).segmentList=G),type:"textarea",autosize:"",placeholder:W.$t(`app.avSearch.${t(E)?"ssImportTipGroup":"ssImportTip"}`),class:"seg-item",onBlur:ne[6]||(ne[6]=G=>t(ge).segmentList=t(ge).segmentList.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o("div",rg,[b(ke,{type:"primary",onClick:ne[7]||(ne[7]=G=>t(xe)())},{default:x(()=>[fe(m(W.$t("app.fastQuery.useInternationalPriceQuery.import")),1)]),_:1}),b(ke,{onClick:t(me)},{default:x(()=>[fe(m(W.$t("app.fastQuery.headerQuery.reset")),1)]),_:1},8,["onClick"])])]),_:1},8,["model","rules"]),b(He,{class:"el-bkc-divider"}),o("div",{class:"mt-[14px]",onKeyup:ne[9]||(ne[9]=aa((...G)=>t(J)&&t(J)(...G),["enter"]))},[(u(!0),D(be,null,we(t(K),(G,ce)=>{var z,se;return u(),Ce(Mp,{key:G.id,ref_for:!0,ref:re=>t(Q)(re,G.id,ce),addSegLoading:t(F),"onUpdate:addSegLoading":ne[8]||(ne[8]=re=>tt(F)?F.value=re:null),"city-or-airport":t(v),index:ce,"ss-query-form":((se=(z=t(pe))==null?void 0:z[ce])==null?void 0:se.queryForm)||G,"show-delete":t(K).length>1,"tkt-num":t(he),"is-group":t(E),onInputTktNumBlur:t(ee),onDeleteSsFlight:t(Z)},null,8,["addSegLoading","city-or-airport","index","ss-query-form","show-delete","tkt-num","is-group","onInputTktNumBlur","onDeleteSsFlight"])}),128)),t(E)?de("",!0):(u(),D("div",sg,[o("div",lg,[cg,fe(m(W.$t("app.fastQuery.headerQuery.addSegmentTip")),1)])])),o("div",dg,[b(ke,{type:"primary",onClick:t(J)},{default:x(()=>[fe(m(W.$t("app.avSearch.addEnter")),1)]),_:1},8,["onClick"])])],32)]),_:1})])),[[j,t(F)]]):(u(),D("div",ug,[b(Yp,{"city-or-airport":t(v),onAddGroupFlight:t(B)},null,8,["city-or-airport","onAddGroupFlight"])]))],2)],2)}}});const vf=ze(pg,[["__scopeId","data-v-d622a442"]]),yf=(e,a,i)=>Ye(`${Ht}/v2/queue/queryQueueList`,{headers:{gid:a}},{ignoreError:i,originalValue:!0}).post(e).json(),bf=(e,a,i)=>Ye(`${Ht}/v2/query/pnr/queuePnr`,{headers:{gid:a}},{ignoreError:i,originalValue:!0}).post(e).json(),_f=(e,a)=>Ye(`${Ht}/v2/queue/subQueueCrsPnr`,{headers:{gid:a}},{originalValue:!0}).post(e).json(),Cf=(e,a)=>Ye(`${Ht}/v2/queue/unsubscribeCrsQueue`,{headers:{gid:a}}).post(e).json(),Af=(e,a)=>Ye(`${Ht}/v2/queue/updateCrsQueueReadStatus`,{headers:{gid:a}},{ignoreError:!0,originalValue:!0}).post(e).json(),Df=(e,a)=>Ye(`${Ht}/v2/queue/subQueueCrsPnrDetail`,{headers:{gid:a}},{originalValue:!0}).post(e).json();export{Ut as A,Gg as B,Ga as C,Gd as D,ui as E,Qt as F,Zg as G,Xg as H,ef as I,nf as J,af as K,of as L,lf as M,pf as N,Df as O,ff as P,ni as Q,Zs as R,rf as S,sf as T,cf as U,Af as V,jg as W,Jg as X,Vr as _,tf as a,Un as b,ma as c,yo as d,df as e,mf as f,Gt as g,hf as h,gf as i,ca as j,vf as k,hi as l,Wg as m,yf as n,Tt as o,bf as p,Ci as q,Yg as r,Hg as s,zg as t,uf as u,vi as v,_f as w,qg as x,Kg as y,Cf as z};
