import{L as Nu,h6 as ec,dR as Lu,h7 as xu,q as xs,N as Au,v as Su,r as co,w as os,a3 as _u,x as Kn,y as fa,z as Sr,P as Pi,D as ha,A as be,H as ji,C as Pu,b9 as ku,J as po,ak as ss,Q as cs,G as Si,al as nc,F as Fu,X as Cu,_ as Iu,K as ju,ab as Ou,aF as Bu,aT as rc,B as la,ai as ic,aj as ac,ah as Mu,c5 as Eu,at as Oi,aH as ye,ew as Ac,ev as Sc,ex as Vr,eK as As,h1 as gs}from"./index-9381ab2b.js";import{u as oc,E as Tu}from"./index-c19c3f80.js";import{E as qu,a as Du}from"./index-094198d8.js";import{E as Ru}from"./index-951011fc.js";import{_ as zu}from"./_plugin-vue_export-helper-c27b6911.js";import{z as ms,u as Uu}from"./browser-6cfa1fde.js";const Hu=Nu({title:String,confirmButtonText:String,cancelButtonText:String,confirmButtonType:{type:String,values:ec,default:"primary"},cancelButtonType:{type:String,values:ec,default:"text"},icon:{type:Lu,default:()=>xu},iconColor:{type:String,default:"#f90"},hideIcon:{type:Boolean,default:!1},hideAfter:{type:Number,default:200},teleported:oc.teleported,persistent:oc.persistent,width:{type:[String,Number],default:150}}),Vu={confirm:i=>i instanceof MouseEvent,cancel:i=>i instanceof MouseEvent},Wu=xs({name:"ElPopconfirm"}),Gu=xs({...Wu,props:Hu,emits:Vu,setup(i,{emit:e}){const n=i,{t:a}=Au(),c=Su("popconfirm"),o=co(),l=()=>{var S,p;(p=(S=o.value)==null?void 0:S.onClose)==null||p.call(S)},h=os(()=>({width:_u(n.width)})),f=S=>{e("confirm",S),l()},g=S=>{e("cancel",S),l()},x=os(()=>n.confirmButtonText||a("el.popconfirm.confirmButtonText")),L=os(()=>n.cancelButtonText||a("el.popconfirm.cancelButtonText"));return(S,p)=>(Kn(),fa(be(Tu),Cu({ref_key:"tooltipRef",ref:o,trigger:"click",effect:"light"},S.$attrs,{"popper-class":`${be(c).namespace.value}-popover`,"popper-style":be(h),teleported:S.teleported,"fallback-placements":["bottom","top","right","left"],"hide-after":S.hideAfter,persistent:S.persistent}),{content:Sr(()=>[Pi("div",{class:ha(be(c).b())},[Pi("div",{class:ha(be(c).e("main"))},[!S.hideIcon&&S.icon?(Kn(),fa(be(ji),{key:0,class:ha(be(c).e("icon")),style:Pu({color:S.iconColor})},{default:Sr(()=>[(Kn(),fa(ku(S.icon)))]),_:1},8,["class","style"])):po("v-if",!0),ss(" "+cs(S.title),1)],2),Pi("div",{class:ha(be(c).e("action"))},[Si(be(nc),{size:"small",type:S.cancelButtonType==="text"?"":S.cancelButtonType,text:S.cancelButtonType==="text",onClick:g},{default:Sr(()=>[ss(cs(be(L)),1)]),_:1},8,["type","text"]),Si(be(nc),{size:"small",type:S.confirmButtonType==="text"?"":S.confirmButtonType,text:S.confirmButtonType==="text",onClick:f},{default:Sr(()=>[ss(cs(be(x)),1)]),_:1},8,["type","text"])],2)],2)]),default:Sr(()=>[S.$slots.reference?Fu(S.$slots,"reference",{key:0}):po("v-if",!0)]),_:3},16,["popper-class","popper-style","teleported","hide-after","persistent"]))}});var Ju=Iu(Gu,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/popconfirm/src/popconfirm.vue"]]);const Wl=ju(Ju),Yu=(i,e)=>{const{t:n}=Ou(),a=co({secondFactorType:i.secondFactorType||"certificate",secondFactorCode:i.secondFactorCode||"NI",secondFactorValue:""}),c=co([{label:n("app.agentTicketQuery.certificateNo"),value:"certificate"},{label:n("app.pnrManagement.queryArea.queryLabel.pnrNo"),value:"PNR"},{label:n("app.agentTicketQuery.name"),value:"name"}]),o=co([{label:`NI ${n("app.agentTicketQuery.certs.by_IDAndResidence")}`,value:"NI"},{label:`PP ${n("app.agentTicketQuery.certs.by_PassportAndOther")}`,value:"PP"},{label:`UU ${n("app.agentTicketQuery.certs.by_Unable")}`,value:"UU"}]),l=()=>{a.value.secondFactorValue="",e("updateFormData",rc(a.value)),e("clearPropValidate")},h=()=>{a.value.secondFactorType==="PNR"?a.value.secondFactorCode="CN":a.value.secondFactorType==="name"?a.value.secondFactorCode="NM":a.value.secondFactorCode="NI",l()},f=()=>{var x;a.value.secondFactorValue=((x=a.value.secondFactorValue)==null?void 0:x.toUpperCase())??"",e("updateFormData",rc(a.value)),e("validateProp")},g=x=>{switch(x){case"certificate":return n("app.agentTicketQuery.inputCertificateNo");case"PNR":return n("app.agentTicketQuery.inputPNRNo");case"name":return n("app.agentTicketQuery.validate.inputPassengerName")}};return Bu(()=>{a.value.secondFactorType=i.secondFactorType,a.value.secondFactorCode=i.secondFactorCode,a.value.secondFactorValue=i.secondFactorValue}),{formData:a,secondFactorTypeList:c,secondFactorCodeList:o,changeSecondFactorType:h,changeSecondFactorCode:l,changeSecondFactorValue:f,getInputPlaceHolder:g}},Xu=Yu,Ku={class:"certificate-number inline-block"},$u={class:"h-8 justify-center items-center gap-1 inline-flex"},Zu={class:"justify-start items-center flex"},Qu={key:0,class:"h-8 bg-white justify-between items-center flex"},tl={key:1,class:"h-8 bg-white justify-between items-center flex"},el={class:"h-8 bg-white rounded-tr-sm rounded-br-sm justify-start items-center flex relative"},nl=xs({__name:"CertificateNumber",props:{itemProps:{},secondFactorType:{},secondFactorCode:{},secondFactorValue:{},hideSecondFactorType:{type:Boolean}},emits:["updateFormData","validateProp","clearPropValidate"],setup(i,{emit:e}){const n=e,a=i,{formData:c,secondFactorTypeList:o,secondFactorCodeList:l,changeSecondFactorType:h,changeSecondFactorCode:f,changeSecondFactorValue:g,getInputPlaceHolder:x}=Xu(a,n);return(L,S)=>{const p=qu,O=Du,C=Mu,T=Ru;return Kn(),la("div",Ku,[Pi("div",$u,[Pi("div",Zu,[L.hideSecondFactorType?po("",!0):(Kn(),la("div",Qu,[Si(O,{modelValue:be(c).secondFactorType,"onUpdate:modelValue":S[0]||(S[0]=_=>be(c).secondFactorType=_),placeholder:L.$t("app.ticketQuery.choose"),class:"second-factor-type",onChange:be(h)},{default:Sr(()=>[(Kn(!0),la(ic,null,ac(be(o),_=>(Kn(),fa(p,{key:_.value,label:_.label,value:_.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange"])])),be(c).secondFactorType==="certificate"?(Kn(),la("div",tl,[Si(O,{modelValue:be(c).secondFactorCode,"onUpdate:modelValue":S[1]||(S[1]=_=>be(c).secondFactorCode=_),placeholder:L.$t("app.ticketQuery.choose"),class:"second-factor-code",onChange:be(f)},{default:Sr(()=>[(Kn(!0),la(ic,null,ac(be(l),_=>(Kn(),fa(p,{key:_.value,label:_.label,value:_.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange"])])):po("",!0),Si(T,{prop:L.itemProps},{default:Sr(()=>[Pi("div",el,[Si(C,{modelValue:be(c).secondFactorValue,"onUpdate:modelValue":S[2]||(S[2]=_=>be(c).secondFactorValue=_),modelModifiers:{trim:!0},placeholder:be(x)(be(c).secondFactorType),class:ha([be(c).secondFactorType==="certificate"?"certificate-no":"number-no"]),clearable:"",onBlur:be(g)},null,8,["modelValue","placeholder","class","onBlur"])])]),_:1},8,["prop"])])])])}}});const Gl=zu(nl,[["__scopeId","data-v-22262218"]]);const Ss=Eu.global.t,Jl=async(i,e,n=!1)=>Oi.alert(ye("div",{class:"text-lg text-gray-1"},i),{icon:ye("em",{class:"iconfont icon-info-circle-line text-brand-2 text-[32px]"}),customClass:"alert-message-common crs-btn-ui",confirmButtonText:e,showCancelButton:n,showClose:!1}),Yl=async i=>Oi.confirm(i,{icon:ye(ji,{color:Vr("--bkc-tw-green-2",null).value,size:32},()=>ye(As)),customClass:"success-message-common crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,confirmButtonText:Ss("app.avSearch.confirm"),showCancelButton:!1}),Xl=async(i,e,n="warn")=>{const a=i.map(c=>ye("p",{className:"flex",style:{"margin-top":"8px"}},[ye("i",{className:`iconfont mr-2.5 ${c.success?"icon-ticket text-green-2":"icon-close text-red-400"}`}),ye("span",{className:`font-bold text-[14px] mr-2.5 ${c.ticketNo?"":"hidden"}`},c.ticketNo),ye("span",{className:"text-[14px] w-[240px] whitespace-nowrap overflow-hidden text-ellipsis"},c.passengerName)]));return Oi.confirm(ye("div",{},[ye("p",{className:"text-[18px]",style:{"margin-bottom":"16px"}},e??""),a]),{icon:ye(ji,{color:n==="warn"?Vr("--bkc-tw-brand-2",null).value:Vr("--bkc-tw-red-1",null).value,size:32},()=>ye(n==="warn"?Ac:Sc)),customClass:"invalidated-warning-message crs-btn-ui",closeOnClickModal:!1,showClose:!1,showCancelButton:!1,dangerouslyUseHTMLString:!0,draggable:!0})},Kl=async(i,e,n="warn")=>{const a=i.map(c=>ye("p",{className:"flex",style:{"margin-top":"8px"}},[ye("i",{className:"iconfont mr-2.5 icon-ticket text-green-2"}),ye("span",{className:"text-[14px] w-[240px] whitespace-nowrap overflow-hidden text-ellipsis"},c)]));return Oi.confirm(ye("div",{},[ye("p",{className:"text-[18px]",style:{"margin-bottom":"16px"}},e??""),a]),{icon:ye(ji,{color:n==="warn"?Vr("--bkc-tw-brand-2",null).value:Vr("--bkc-tw-red-1",null).value,size:32},()=>ye(n==="warn"?Ac:Sc)),customClass:"invalidated-warning-message crs-btn-ui",closeOnClickModal:!1,showClose:!1,showCancelButton:!1,dangerouslyUseHTMLString:!0})},$l=async(i,e)=>{const n={icon:ye(ji,{color:Vr("--bkc-tw-green-2",null).value,size:32},()=>ye(As)),customClass:"crs-btn-ui crs-btn-message-ui manual-refund-icon",dangerouslyUseHTMLString:!0,confirmButtonText:Ss("app.agentTicketRefund.sure"),closeOnClickModal:!1,showConfirmButton:!0,showCancelButton:!1,showClose:!1};return Oi.confirm(ye("div",{className:"whitespace-pre"},[ye("p",{className:"text-[18px]",style:{"margin-bottom":"16px"}},i??""),e]),n)},Zl=async(i,e,n,a)=>{const c={icon:ye(ji,{color:Vr("--bkc-tw-green-2",null).value,size:32},()=>ye(As)),customClass:"crs-btn-ui crs-btn-message-ui manual-refund-icon",dangerouslyUseHTMLString:!0,confirmButtonText:e,closeButtonText:Ss("app.agentTicketRefund.cancel"),closeOnClickModal:!1,showConfirmButton:n,showCancelButton:!0,showClose:!1};return Oi.confirm(ye("div",{className:"whitespace-pre"},[ye("p",{className:"text-[18px]"},i??""),a]),c)};function fe(i){"@babel/helpers - typeof";return fe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fe(i)}var zt=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();function us(){zt.console&&typeof zt.console.log=="function"&&zt.console.log.apply(zt.console,arguments)}var ve={log:us,warn:function(i){zt.console&&(typeof zt.console.warn=="function"?zt.console.warn.apply(zt.console,arguments):us.call(null,arguments))},error:function(i){zt.console&&(typeof zt.console.error=="function"?zt.console.error.apply(zt.console,arguments):us(i))}};function ls(i,e,n){var a=new XMLHttpRequest;a.open("GET",i),a.responseType="blob",a.onload=function(){zr(a.response,e,n)},a.onerror=function(){ve.error("could not download file")},a.send()}function sc(i){var e=new XMLHttpRequest;e.open("HEAD",i,!1);try{e.send()}catch{}return e.status>=200&&e.status<=299}function io(i){try{i.dispatchEvent(new MouseEvent("click"))}catch{var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),i.dispatchEvent(e)}}var da,vs,zr=zt.saveAs||((typeof window>"u"?"undefined":fe(window))!=="object"||window!==zt?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(i,e,n){var a=zt.URL||zt.webkitURL,c=document.createElement("a");e=e||i.name||"download",c.download=e,c.rel="noopener",typeof i=="string"?(c.href=i,c.origin!==location.origin?sc(c.href)?ls(i,e,n):io(c,c.target="_blank"):io(c)):(c.href=a.createObjectURL(i),setTimeout(function(){a.revokeObjectURL(c.href)},4e4),setTimeout(function(){io(c)},0))}:"msSaveOrOpenBlob"in navigator?function(i,e,n){if(e=e||i.name||"download",typeof i=="string")if(sc(i))ls(i,e,n);else{var a=document.createElement("a");a.href=i,a.target="_blank",setTimeout(function(){io(a)})}else navigator.msSaveOrOpenBlob(function(c,o){return o===void 0?o={autoBom:!1}:fe(o)!=="object"&&(ve.warn("Deprecated: Expected third argument to be a object"),o={autoBom:!o}),o.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(c.type)?new Blob([String.fromCharCode(65279),c],{type:c.type}):c}(i,n),e)}:function(i,e,n,a){if((a=a||open("","_blank"))&&(a.document.title=a.document.body.innerText="downloading..."),typeof i=="string")return ls(i,e,n);var c=i.type==="application/octet-stream",o=/constructor/i.test(zt.HTMLElement)||zt.safari,l=/CriOS\/[\d]+/.test(navigator.userAgent);if((l||c&&o)&&(typeof FileReader>"u"?"undefined":fe(FileReader))==="object"){var h=new FileReader;h.onloadend=function(){var x=h.result;x=l?x:x.replace(/^data:[^;]*;/,"data:attachment/file;"),a?a.location.href=x:location=x,a=null},h.readAsDataURL(i)}else{var f=zt.URL||zt.webkitURL,g=f.createObjectURL(i);a?a.location=g:location.href=g,a=null,setTimeout(function(){f.revokeObjectURL(g)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function _c(i){var e;i=i||"",this.ok=!1,i.charAt(0)=="#"&&(i=i.substr(1,6)),i={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[i=(i=i.replace(/ /g,"")).toLowerCase()]||i;for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(h){return[parseInt(h[1]),parseInt(h[2]),parseInt(h[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(h){return[parseInt(h[1],16),parseInt(h[2],16),parseInt(h[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(h){return[parseInt(h[1]+h[1],16),parseInt(h[2]+h[2],16),parseInt(h[3]+h[3],16)]}}],a=0;a<n.length;a++){var c=n[a].re,o=n[a].process,l=c.exec(i);l&&(e=o(l),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var h=this.r.toString(16),f=this.g.toString(16),g=this.b.toString(16);return h.length==1&&(h="0"+h),f.length==1&&(f="0"+f),g.length==1&&(g="0"+g),"#"+h+f+g}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function hs(i,e){var n=i[0],a=i[1],c=i[2],o=i[3];n=$e(n,a,c,o,e[0],7,-680876936),o=$e(o,n,a,c,e[1],12,-389564586),c=$e(c,o,n,a,e[2],17,606105819),a=$e(a,c,o,n,e[3],22,-**********),n=$e(n,a,c,o,e[4],7,-176418897),o=$e(o,n,a,c,e[5],12,**********),c=$e(c,o,n,a,e[6],17,-**********),a=$e(a,c,o,n,e[7],22,-45705983),n=$e(n,a,c,o,e[8],7,**********),o=$e(o,n,a,c,e[9],12,-**********),c=$e(c,o,n,a,e[10],17,-42063),a=$e(a,c,o,n,e[11],22,-**********),n=$e(n,a,c,o,e[12],7,**********),o=$e(o,n,a,c,e[13],12,-40341101),c=$e(c,o,n,a,e[14],17,-**********),n=Ze(n,a=$e(a,c,o,n,e[15],22,**********),c,o,e[1],5,-165796510),o=Ze(o,n,a,c,e[6],9,-**********),c=Ze(c,o,n,a,e[11],14,643717713),a=Ze(a,c,o,n,e[0],20,-373897302),n=Ze(n,a,c,o,e[5],5,-701558691),o=Ze(o,n,a,c,e[10],9,38016083),c=Ze(c,o,n,a,e[15],14,-660478335),a=Ze(a,c,o,n,e[4],20,-405537848),n=Ze(n,a,c,o,e[9],5,568446438),o=Ze(o,n,a,c,e[14],9,-1019803690),c=Ze(c,o,n,a,e[3],14,-187363961),a=Ze(a,c,o,n,e[8],20,1163531501),n=Ze(n,a,c,o,e[13],5,-1444681467),o=Ze(o,n,a,c,e[2],9,-51403784),c=Ze(c,o,n,a,e[7],14,1735328473),n=Qe(n,a=Ze(a,c,o,n,e[12],20,-1926607734),c,o,e[5],4,-378558),o=Qe(o,n,a,c,e[8],11,-2022574463),c=Qe(c,o,n,a,e[11],16,1839030562),a=Qe(a,c,o,n,e[14],23,-35309556),n=Qe(n,a,c,o,e[1],4,-1530992060),o=Qe(o,n,a,c,e[4],11,1272893353),c=Qe(c,o,n,a,e[7],16,-155497632),a=Qe(a,c,o,n,e[10],23,-1094730640),n=Qe(n,a,c,o,e[13],4,681279174),o=Qe(o,n,a,c,e[0],11,-358537222),c=Qe(c,o,n,a,e[3],16,-722521979),a=Qe(a,c,o,n,e[6],23,76029189),n=Qe(n,a,c,o,e[9],4,-640364487),o=Qe(o,n,a,c,e[12],11,-421815835),c=Qe(c,o,n,a,e[15],16,530742520),n=tn(n,a=Qe(a,c,o,n,e[2],23,-995338651),c,o,e[0],6,-198630844),o=tn(o,n,a,c,e[7],10,1126891415),c=tn(c,o,n,a,e[14],15,-1416354905),a=tn(a,c,o,n,e[5],21,-57434055),n=tn(n,a,c,o,e[12],6,1700485571),o=tn(o,n,a,c,e[3],10,-1894986606),c=tn(c,o,n,a,e[10],15,-1051523),a=tn(a,c,o,n,e[1],21,-2054922799),n=tn(n,a,c,o,e[8],6,1873313359),o=tn(o,n,a,c,e[15],10,-30611744),c=tn(c,o,n,a,e[6],15,-1560198380),a=tn(a,c,o,n,e[13],21,1309151649),n=tn(n,a,c,o,e[4],6,-145523070),o=tn(o,n,a,c,e[11],10,-1120210379),c=tn(c,o,n,a,e[2],15,718787259),a=tn(a,c,o,n,e[9],21,-343485551),i[0]=_r(n,i[0]),i[1]=_r(a,i[1]),i[2]=_r(c,i[2]),i[3]=_r(o,i[3])}function vo(i,e,n,a,c,o){return e=_r(_r(e,i),_r(a,o)),_r(e<<c|e>>>32-c,n)}function $e(i,e,n,a,c,o,l){return vo(e&n|~e&a,i,e,c,o,l)}function Ze(i,e,n,a,c,o,l){return vo(e&a|n&~a,i,e,c,o,l)}function Qe(i,e,n,a,c,o,l){return vo(e^n^a,i,e,c,o,l)}function tn(i,e,n,a,c,o,l){return vo(n^(e|~a),i,e,c,o,l)}function Pc(i){var e,n=i.length,a=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=i.length;e+=64)hs(a,rl(i.substring(e-64,e)));i=i.substring(e-64);var c=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<i.length;e++)c[e>>2]|=i.charCodeAt(e)<<(e%4<<3);if(c[e>>2]|=128<<(e%4<<3),e>55)for(hs(a,c),e=0;e<16;e++)c[e]=0;return c[14]=8*n,hs(a,c),a}function rl(i){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=i.charCodeAt(e)+(i.charCodeAt(e+1)<<8)+(i.charCodeAt(e+2)<<16)+(i.charCodeAt(e+3)<<24);return n}da=zt.atob.bind(zt),vs=zt.btoa.bind(zt);var cc="0123456789abcdef".split("");function il(i){for(var e="",n=0;n<4;n++)e+=cc[i>>8*n+4&15]+cc[i>>8*n&15];return e}function al(i){return String.fromCharCode((255&i)>>0,(65280&i)>>8,(16711680&i)>>16,(**********&i)>>24)}function bs(i){return Pc(i).map(al).join("")}var ol=function(i){for(var e=0;e<i.length;e++)i[e]=il(i[e]);return i.join("")}(Pc("hello"))!="5d41402abc4b2a76b9719d911017c592";function _r(i,e){if(ol){var n=(65535&i)+(65535&e);return(i>>16)+(e>>16)+(n>>16)<<16|65535&n}return i+e&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function ys(i,e){var n,a,c,o;if(i!==n){for(var l=(c=i,o=1+(256/i.length>>0),new Array(o+1).join(c)),h=[],f=0;f<256;f++)h[f]=f;var g=0;for(f=0;f<256;f++){var x=h[f];g=(g+x+l.charCodeAt(f))%256,h[f]=h[g],h[g]=x}n=i,a=h}else h=a;var L=e.length,S=0,p=0,O="";for(f=0;f<L;f++)p=(p+(x=h[S=(S+1)%256]))%256,h[S]=h[p],h[p]=x,l=h[(h[S]+h[p])%256],O+=String.fromCharCode(e.charCodeAt(f)^l);return O}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var uc={print:4,modify:8,copy:16,"annot-forms":32};function xi(i,e,n,a){this.v=1,this.r=2;var c=192;i.forEach(function(h){if(uc.perm!==void 0)throw new Error("Invalid permission: "+h);c+=uc[h]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var o=(e+this.padding).substr(0,32),l=(n+this.padding).substr(0,32);this.O=this.processOwnerPassword(o,l),this.P=-(1+(255^c)),this.encryptionKey=bs(o+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(a)).substr(0,5),this.U=ys(this.encryptionKey,this.padding)}function Ai(i){if(/[^\u0000-\u00ff]/.test(i))throw new Error("Invalid PDF Name Object: "+i+", Only accept ASCII characters.");for(var e="",n=i.length,a=0;a<n;a++){var c=i.charCodeAt(a);c<33||c===35||c===37||c===40||c===41||c===47||c===60||c===62||c===91||c===93||c===123||c===125||c>126?e+="#"+("0"+c.toString(16)).slice(-2):e+=i[a]}return e}function lc(i){if(fe(i)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(n,a,c){if(c=c||!1,typeof n!="string"||typeof a!="function"||typeof c!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(n)||(e[n]={});var o=Math.random().toString(35);return e[n][o]=[a,!!c],o},this.unsubscribe=function(n){for(var a in e)if(e[a][n])return delete e[a][n],Object.keys(e[a]).length===0&&delete e[a],!0;return!1},this.publish=function(n){if(e.hasOwnProperty(n)){var a=Array.prototype.slice.call(arguments,1),c=[];for(var o in e[n]){var l=e[n][o];try{l[0].apply(i,a)}catch(h){zt.console&&ve.error("jsPDF PubSub Error",h.message,h)}l[1]&&c.push(o)}c.length&&c.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function go(i){if(!(this instanceof go))return new go(i);var e="opacity,stroke-opacity".split(",");for(var n in i)i.hasOwnProperty(n)&&e.indexOf(n)>=0&&(this[n]=i[n]);this.id="",this.objectNumber=-1}function kc(i,e){this.gState=i,this.matrix=e,this.id="",this.objectNumber=-1}function Ur(i,e,n,a,c){if(!(this instanceof Ur))return new Ur(i,e,n,a,c);this.type=i==="axial"?2:3,this.coords=e,this.colors=n,kc.call(this,a,c)}function _i(i,e,n,a,c){if(!(this instanceof _i))return new _i(i,e,n,a,c);this.boundingBox=i,this.xStep=e,this.yStep=n,this.stream="",this.cloneIndex=0,kc.call(this,a,c)}function Ut(i){var e,n=typeof arguments[0]=="string"?arguments[0]:"p",a=arguments[1],c=arguments[2],o=arguments[3],l=[],h=1,f=16,g="S",x=null;fe(i=i||{})==="object"&&(n=i.orientation,a=i.unit||a,c=i.format||c,o=i.compress||i.compressPdf||o,(x=i.encryption||null)!==null&&(x.userPassword=x.userPassword||"",x.ownerPassword=x.ownerPassword||"",x.userPermissions=x.userPermissions||[]),h=typeof i.userUnit=="number"?Math.abs(i.userUnit):1,i.precision!==void 0&&(e=i.precision),i.floatPrecision!==void 0&&(f=i.floatPrecision),g=i.defaultPathOperation||"S"),l=i.filters||(o===!0?["FlateEncode"]:l),a=a||"mm",n=(""+(n||"P")).toLowerCase();var L=i.putOnlyUsedFonts||!1,S={},p={internal:{},__private__:{}};p.__private__.PubSub=lc;var O="1.3",C=p.__private__.getPdfVersion=function(){return O};p.__private__.setPdfVersion=function(s){O=s};var T={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};p.__private__.getPageFormats=function(){return T};var _=p.__private__.getPageFormat=function(s){return T[s]};c=c||"a4";var M={COMPAT:"compat",ADVANCED:"advanced"},$=M.COMPAT;function st(){this.saveGraphicsState(),B(new Rt(Ft,0,0,-Ft,0,fr()*Ft).toString()+" cm"),this.setFontSize(this.getFontSize()/Ft),g="n",$=M.ADVANCED}function dt(){this.restoreGraphicsState(),g="S",$=M.COMPAT}var Lt=p.__private__.combineFontStyleAndFontWeight=function(s,v){if(s=="bold"&&v=="normal"||s=="bold"&&v==400||s=="normal"&&v=="italic"||s=="bold"&&v=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return v&&(s=v==400||v==="normal"?s==="italic"?"italic":"normal":v!=700&&v!=="bold"||s!=="normal"?(v==700?"bold":v)+""+s:"bold"),s};p.advancedAPI=function(s){var v=$===M.COMPAT;return v&&st.call(this),typeof s!="function"||(s(this),v&&dt.call(this)),this},p.compatAPI=function(s){var v=$===M.ADVANCED;return v&&dt.call(this),typeof s!="function"||(s(this),v&&st.call(this)),this},p.isAdvancedAPI=function(){return $===M.ADVANCED};var rt,G=function(s){if($!==M.ADVANCED)throw new Error(s+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},vt=p.roundToPrecision=p.__private__.roundToPrecision=function(s,v){var I=e||v;if(isNaN(s)||isNaN(I))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return s.toFixed(I).replace(/0+$/,"")};rt=p.hpf=p.__private__.hpf=typeof f=="number"?function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(s,f)}:f==="smart"?function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(s,s>-1&&s<1?16:5)}:function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(s,16)};var bt=p.f2=p.__private__.f2=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.f2");return vt(s,2)},k=p.__private__.f3=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.f3");return vt(s,3)},F=p.scale=p.__private__.scale=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.scale");return $===M.COMPAT?s*Ft:$===M.ADVANCED?s:void 0},H=function(s){return $===M.COMPAT?fr()-s:$===M.ADVANCED?s:void 0},D=function(s){return F(H(s))};p.__private__.setPrecision=p.setPrecision=function(s){typeof parseInt(s,10)=="number"&&(e=parseInt(s,10))};var ct,ot="00000000000000000000000000000000",mt=p.__private__.getFileId=function(){return ot},tt=p.__private__.setFileId=function(s){return ot=s!==void 0&&/^[a-fA-F0-9]{32}$/.test(s)?s.toUpperCase():ot.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),x!==null&&(Xe=new xi(x.userPermissions,x.userPassword,x.ownerPassword,ot)),ot};p.setFileId=function(s){return tt(s),this},p.getFileId=function(){return mt()};var pt=p.__private__.convertDateToPDFDate=function(s){var v=s.getTimezoneOffset(),I=v<0?"+":"-",q=Math.floor(Math.abs(v/60)),Y=Math.abs(v%60),it=[I,E(q),"'",E(Y),"'"].join("");return["D:",s.getFullYear(),E(s.getMonth()+1),E(s.getDate()),E(s.getHours()),E(s.getMinutes()),E(s.getSeconds()),it].join("")},ft=p.__private__.convertPDFDateToDate=function(s){var v=parseInt(s.substr(2,4),10),I=parseInt(s.substr(6,2),10)-1,q=parseInt(s.substr(8,2),10),Y=parseInt(s.substr(10,2),10),it=parseInt(s.substr(12,2),10),yt=parseInt(s.substr(14,2),10);return new Date(v,I,q,Y,it,yt,0)},Et=p.__private__.setCreationDate=function(s){var v;if(s===void 0&&(s=new Date),s instanceof Date)v=pt(s);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(s))throw new Error("Invalid argument passed to jsPDF.setCreationDate");v=s}return ct=v},w=p.__private__.getCreationDate=function(s){var v=ct;return s==="jsDate"&&(v=ft(ct)),v};p.setCreationDate=function(s){return Et(s),this},p.getCreationDate=function(s){return w(s)};var j,E=p.__private__.padd2=function(s){return("0"+parseInt(s)).slice(-2)},V=p.__private__.padd2Hex=function(s){return("00"+(s=s.toString())).substr(s.length)},J=0,Z=[],et=[],Q=0,xt=[],Nt=[],Ot=!1,jt=et,Vt=function(){J=0,Q=0,et=[],Z=[],xt=[],tr=Me(),kn=Me()};p.__private__.setCustomOutputDestination=function(s){Ot=!0,jt=s};var at=function(s){Ot||(jt=s)};p.__private__.resetCustomOutputDestination=function(){Ot=!1,jt=et};var B=p.__private__.out=function(s){return s=s.toString(),Q+=s.length+1,jt.push(s),jt},Kt=p.__private__.write=function(s){return B(arguments.length===1?s.toString():Array.prototype.join.call(arguments," "))},Mt=p.__private__.getArrayBuffer=function(s){for(var v=s.length,I=new ArrayBuffer(v),q=new Uint8Array(I);v--;)q[v]=s.charCodeAt(v);return I},wt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];p.__private__.getStandardFonts=function(){return wt};var At=i.fontSize||16;p.__private__.setFontSize=p.setFontSize=function(s){return At=$===M.ADVANCED?s/Ft:s,this};var kt,Pt=p.__private__.getFontSize=p.getFontSize=function(){return $===M.COMPAT?At:At*Ft},Tt=i.R2L||!1;p.__private__.setR2L=p.setR2L=function(s){return Tt=s,this},p.__private__.getR2L=p.getR2L=function(){return Tt};var Gt,Qt=p.__private__.setZoomMode=function(s){var v=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(s))kt=s;else if(isNaN(s)){if(v.indexOf(s)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+s+'" is not recognized.');kt=s}else kt=parseInt(s,10)};p.__private__.getZoomMode=function(){return kt};var te,ie=p.__private__.setPageMode=function(s){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(s)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+s+'" is not recognized.');Gt=s};p.__private__.getPageMode=function(){return Gt};var de=p.__private__.setLayoutMode=function(s){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(s)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+s+'" is not recognized.');te=s};p.__private__.getLayoutMode=function(){return te},p.__private__.setDisplayMode=p.setDisplayMode=function(s,v,I){return Qt(s),de(v),ie(I),this};var Ht={title:"",subject:"",author:"",keywords:"",creator:""};p.__private__.getDocumentProperty=function(s){if(Object.keys(Ht).indexOf(s)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Ht[s]},p.__private__.getDocumentProperties=function(){return Ht},p.__private__.setDocumentProperties=p.setProperties=p.setDocumentProperties=function(s){for(var v in Ht)Ht.hasOwnProperty(v)&&s[v]&&(Ht[v]=s[v]);return this},p.__private__.setDocumentProperty=function(s,v){if(Object.keys(Ht).indexOf(s)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Ht[s]=v};var ee,Ft,Ye,oe,Sn,ge={},Le={},Dn=[],ce={},kr={},Ae={},_n={},Qn=null,Se=0,Jt=[],ue=new lc(p),Fr=i.hotfixes||[],We={},Rn={},zn=[],Rt=function s(v,I,q,Y,it,yt){if(!(this instanceof s))return new s(v,I,q,Y,it,yt);isNaN(v)&&(v=1),isNaN(I)&&(I=0),isNaN(q)&&(q=0),isNaN(Y)&&(Y=1),isNaN(it)&&(it=0),isNaN(yt)&&(yt=0),this._matrix=[v,I,q,Y,it,yt]};Object.defineProperty(Rt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(s){this._matrix[0]=s}}),Object.defineProperty(Rt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(s){this._matrix[1]=s}}),Object.defineProperty(Rt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(s){this._matrix[2]=s}}),Object.defineProperty(Rt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(s){this._matrix[3]=s}}),Object.defineProperty(Rt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(s){this._matrix[4]=s}}),Object.defineProperty(Rt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(s){this._matrix[5]=s}}),Object.defineProperty(Rt.prototype,"a",{get:function(){return this._matrix[0]},set:function(s){this._matrix[0]=s}}),Object.defineProperty(Rt.prototype,"b",{get:function(){return this._matrix[1]},set:function(s){this._matrix[1]=s}}),Object.defineProperty(Rt.prototype,"c",{get:function(){return this._matrix[2]},set:function(s){this._matrix[2]=s}}),Object.defineProperty(Rt.prototype,"d",{get:function(){return this._matrix[3]},set:function(s){this._matrix[3]=s}}),Object.defineProperty(Rt.prototype,"e",{get:function(){return this._matrix[4]},set:function(s){this._matrix[4]=s}}),Object.defineProperty(Rt.prototype,"f",{get:function(){return this._matrix[5]},set:function(s){this._matrix[5]=s}}),Object.defineProperty(Rt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Rt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Rt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Rt.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),Rt.prototype.join=function(s){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(rt).join(s)},Rt.prototype.multiply=function(s){var v=s.sx*this.sx+s.shy*this.shx,I=s.sx*this.shy+s.shy*this.sy,q=s.shx*this.sx+s.sy*this.shx,Y=s.shx*this.shy+s.sy*this.sy,it=s.tx*this.sx+s.ty*this.shx+this.tx,yt=s.tx*this.shy+s.ty*this.sy+this.ty;return new Rt(v,I,q,Y,it,yt)},Rt.prototype.decompose=function(){var s=this.sx,v=this.shy,I=this.shx,q=this.sy,Y=this.tx,it=this.ty,yt=Math.sqrt(s*s+v*v),Ct=(s/=yt)*I+(v/=yt)*q;I-=s*Ct,q-=v*Ct;var qt=Math.sqrt(I*I+q*q);return Ct/=qt,s*(q/=qt)<v*(I/=qt)&&(s=-s,v=-v,Ct=-Ct,yt=-yt),{scale:new Rt(yt,0,0,qt,0,0),translate:new Rt(1,0,0,1,Y,it),rotate:new Rt(s,v,-v,s,0,0),skew:new Rt(1,0,Ct,1,0,0)}},Rt.prototype.toString=function(s){return this.join(" ")},Rt.prototype.inversed=function(){var s=this.sx,v=this.shy,I=this.shx,q=this.sy,Y=this.tx,it=this.ty,yt=1/(s*q-v*I),Ct=q*yt,qt=-v*yt,$t=-I*yt,Yt=s*yt;return new Rt(Ct,qt,$t,Yt,-Ct*Y-$t*it,-qt*Y-Yt*it)},Rt.prototype.applyToPoint=function(s){var v=s.x*this.sx+s.y*this.shx+this.tx,I=s.x*this.shy+s.y*this.sy+this.ty;return new ai(v,I)},Rt.prototype.applyToRectangle=function(s){var v=this.applyToPoint(s),I=this.applyToPoint(new ai(s.x+s.w,s.y+s.h));return new Vi(v.x,v.y,I.x-v.x,I.y-v.y)},Rt.prototype.clone=function(){var s=this.sx,v=this.shy,I=this.shx,q=this.sy,Y=this.tx,it=this.ty;return new Rt(s,v,I,q,Y,it)},p.Matrix=Rt;var Pn=p.matrixMult=function(s,v){return v.multiply(s)},Un=new Rt(1,0,0,1,0,0);p.unitMatrix=p.identityMatrix=Un;var an=function(s,v){if(!kr[s]){var I=(v instanceof Ur?"Sh":"P")+(Object.keys(ce).length+1).toString(10);v.id=I,kr[s]=I,ce[I]=v,ue.publish("addPattern",v)}};p.ShadingPattern=Ur,p.TilingPattern=_i,p.addShadingPattern=function(s,v){return G("addShadingPattern()"),an(s,v),this},p.beginTilingPattern=function(s){G("beginTilingPattern()"),ja(s.boundingBox[0],s.boundingBox[1],s.boundingBox[2]-s.boundingBox[0],s.boundingBox[3]-s.boundingBox[1],s.matrix)},p.endTilingPattern=function(s,v){G("endTilingPattern()"),v.stream=Nt[j].join(`
`),an(s,v),ue.publish("endTilingPattern",v),zn.pop().restore()};var De=p.__private__.newObject=function(){var s=Me();return fn(s,!0),s},Me=p.__private__.newObjectDeferred=function(){return J++,Z[J]=function(){return Q},J},fn=function(s,v){return v=typeof v=="boolean"&&v,Z[s]=Q,v&&B(s+" 0 obj"),s},Gr=p.__private__.newAdditionalObject=function(){var s={objId:Me(),content:""};return xt.push(s),s},tr=Me(),kn=Me(),Fn=p.__private__.decodeColorString=function(s){var v=s.split(" ");if(v.length!==2||v[1]!=="g"&&v[1]!=="G")v.length===5&&(v[4]==="k"||v[4]==="K")&&(v=[(1-v[0])*(1-v[3]),(1-v[1])*(1-v[3]),(1-v[2])*(1-v[3]),"r"]);else{var I=parseFloat(v[0]);v=[I,I,I,"r"]}for(var q="#",Y=0;Y<3;Y++)q+=("0"+Math.floor(255*parseFloat(v[Y])).toString(16)).slice(-2);return q},Cn=p.__private__.encodeColorString=function(s){var v;typeof s=="string"&&(s={ch1:s});var I=s.ch1,q=s.ch2,Y=s.ch3,it=s.ch4,yt=s.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof I=="string"&&I.charAt(0)!=="#"){var Ct=new _c(I);if(Ct.ok)I=Ct.toHex();else if(!/^\d*\.?\d*$/.test(I))throw new Error('Invalid color "'+I+'" passed to jsPDF.encodeColorString.')}if(typeof I=="string"&&/^#[0-9A-Fa-f]{3}$/.test(I)&&(I="#"+I[1]+I[1]+I[2]+I[2]+I[3]+I[3]),typeof I=="string"&&/^#[0-9A-Fa-f]{6}$/.test(I)){var qt=parseInt(I.substr(1),16);I=qt>>16&255,q=qt>>8&255,Y=255&qt}if(q===void 0||it===void 0&&I===q&&q===Y)if(typeof I=="string")v=I+" "+yt[0];else switch(s.precision){case 2:v=bt(I/255)+" "+yt[0];break;case 3:default:v=k(I/255)+" "+yt[0]}else if(it===void 0||fe(it)==="object"){if(it&&!isNaN(it.a)&&it.a===0)return v=["1.","1.","1.",yt[1]].join(" ");if(typeof I=="string")v=[I,q,Y,yt[1]].join(" ");else switch(s.precision){case 2:v=[bt(I/255),bt(q/255),bt(Y/255),yt[1]].join(" ");break;default:case 3:v=[k(I/255),k(q/255),k(Y/255),yt[1]].join(" ")}}else if(typeof I=="string")v=[I,q,Y,it,yt[2]].join(" ");else switch(s.precision){case 2:v=[bt(I),bt(q),bt(Y),bt(it),yt[2]].join(" ");break;case 3:default:v=[k(I),k(q),k(Y),k(it),yt[2]].join(" ")}return v},Hn=p.__private__.getFilters=function(){return l},vn=p.__private__.putStream=function(s){var v=(s=s||{}).data||"",I=s.filters||Hn(),q=s.alreadyAppliedFilters||[],Y=s.addLength1||!1,it=v.length,yt=s.objectId,Ct=function(Ke){return Ke};if(x!==null&&yt===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");x!==null&&(Ct=Xe.encryptor(yt,0));var qt={};I===!0&&(I=["FlateEncode"]);var $t=s.additionalKeyValues||[],Yt=(qt=Ut.API.processDataByFilters!==void 0?Ut.API.processDataByFilters(v,I):{data:v,reverseChain:[]}).reverseChain+(Array.isArray(q)?q.join(" "):q.toString());if(qt.data.length!==0&&($t.push({key:"Length",value:qt.data.length}),Y===!0&&$t.push({key:"Length1",value:it})),Yt.length!=0)if(Yt.split("/").length-1==1)$t.push({key:"Filter",value:Yt});else{$t.push({key:"Filter",value:"["+Yt+"]"});for(var re=0;re<$t.length;re+=1)if($t[re].key==="DecodeParms"){for(var xe=[],_e=0;_e<qt.reverseChain.split("/").length-1;_e+=1)xe.push("null");xe.push($t[re].value),$t[re].value="["+xe.join(" ")+"]"}}B("<<");for(var Ee=0;Ee<$t.length;Ee++)B("/"+$t[Ee].key+" "+$t[Ee].value);B(">>"),qt.data.length!==0&&(B("stream"),B(Ct(qt.data)),B("endstream"))},Vn=p.__private__.putPage=function(s){var v=s.number,I=s.data,q=s.objId,Y=s.contentsObjId;fn(q,!0),B("<</Type /Page"),B("/Parent "+s.rootDictionaryObjId+" 0 R"),B("/Resources "+s.resourceDictionaryObjId+" 0 R"),B("/MediaBox ["+parseFloat(rt(s.mediaBox.bottomLeftX))+" "+parseFloat(rt(s.mediaBox.bottomLeftY))+" "+rt(s.mediaBox.topRightX)+" "+rt(s.mediaBox.topRightY)+"]"),s.cropBox!==null&&B("/CropBox ["+rt(s.cropBox.bottomLeftX)+" "+rt(s.cropBox.bottomLeftY)+" "+rt(s.cropBox.topRightX)+" "+rt(s.cropBox.topRightY)+"]"),s.bleedBox!==null&&B("/BleedBox ["+rt(s.bleedBox.bottomLeftX)+" "+rt(s.bleedBox.bottomLeftY)+" "+rt(s.bleedBox.topRightX)+" "+rt(s.bleedBox.topRightY)+"]"),s.trimBox!==null&&B("/TrimBox ["+rt(s.trimBox.bottomLeftX)+" "+rt(s.trimBox.bottomLeftY)+" "+rt(s.trimBox.topRightX)+" "+rt(s.trimBox.topRightY)+"]"),s.artBox!==null&&B("/ArtBox ["+rt(s.artBox.bottomLeftX)+" "+rt(s.artBox.bottomLeftY)+" "+rt(s.artBox.topRightX)+" "+rt(s.artBox.topRightY)+"]"),typeof s.userUnit=="number"&&s.userUnit!==1&&B("/UserUnit "+s.userUnit),ue.publish("putPage",{objId:q,pageContext:Jt[v],pageNumber:v,page:I}),B("/Contents "+Y+" 0 R"),B(">>"),B("endobj");var it=I.join(`
`);return $===M.ADVANCED&&(it+=`
Q`),fn(Y,!0),vn({data:it,filters:Hn(),objectId:Y}),B("endobj"),q},Cr=p.__private__.putPages=function(){var s,v,I=[];for(s=1;s<=Se;s++)Jt[s].objId=Me(),Jt[s].contentsObjId=Me();for(s=1;s<=Se;s++)I.push(Vn({number:s,data:Nt[s],objId:Jt[s].objId,contentsObjId:Jt[s].contentsObjId,mediaBox:Jt[s].mediaBox,cropBox:Jt[s].cropBox,bleedBox:Jt[s].bleedBox,trimBox:Jt[s].trimBox,artBox:Jt[s].artBox,userUnit:Jt[s].userUnit,rootDictionaryObjId:tr,resourceDictionaryObjId:kn}));fn(tr,!0),B("<</Type /Pages");var q="/Kids [";for(v=0;v<Se;v++)q+=I[v]+" 0 R ";B(q+"]"),B("/Count "+Se),B(">>"),B("endobj"),ue.publish("postPutPages")},Jr=function(s){ue.publish("putFont",{font:s,out:B,newObject:De,putStream:vn}),s.isAlreadyPutted!==!0&&(s.objectNumber=De(),B("<<"),B("/Type /Font"),B("/BaseFont /"+Ai(s.postScriptName)),B("/Subtype /Type1"),typeof s.encoding=="string"&&B("/Encoding /"+s.encoding),B("/FirstChar 32"),B("/LastChar 255"),B(">>"),B("endobj"))},Yr=function(){for(var s in ge)ge.hasOwnProperty(s)&&(L===!1||L===!0&&S.hasOwnProperty(s))&&Jr(ge[s])},Xr=function(s){s.objectNumber=De();var v=[];v.push({key:"Type",value:"/XObject"}),v.push({key:"Subtype",value:"/Form"}),v.push({key:"BBox",value:"["+[rt(s.x),rt(s.y),rt(s.x+s.width),rt(s.y+s.height)].join(" ")+"]"}),v.push({key:"Matrix",value:"["+s.matrix.toString()+"]"});var I=s.pages[1].join(`
`);vn({data:I,additionalKeyValues:v,objectId:s.objectNumber}),B("endobj")},Kr=function(){for(var s in We)We.hasOwnProperty(s)&&Xr(We[s])},pa=function(s,v){var I,q=[],Y=1/(v-1);for(I=0;I<1;I+=Y)q.push(I);if(q.push(1),s[0].offset!=0){var it={offset:0,color:s[0].color};s.unshift(it)}if(s[s.length-1].offset!=1){var yt={offset:1,color:s[s.length-1].color};s.push(yt)}for(var Ct="",qt=0,$t=0;$t<q.length;$t++){for(I=q[$t];I>s[qt+1].offset;)qt++;var Yt=s[qt].offset,re=(I-Yt)/(s[qt+1].offset-Yt),xe=s[qt].color,_e=s[qt+1].color;Ct+=V(Math.round((1-re)*xe[0]+re*_e[0]).toString(16))+V(Math.round((1-re)*xe[1]+re*_e[1]).toString(16))+V(Math.round((1-re)*xe[2]+re*_e[2]).toString(16))}return Ct.trim()},bo=function(s,v){v||(v=21);var I=De(),q=pa(s.colors,v),Y=[];Y.push({key:"FunctionType",value:"0"}),Y.push({key:"Domain",value:"[0.0 1.0]"}),Y.push({key:"Size",value:"["+v+"]"}),Y.push({key:"BitsPerSample",value:"8"}),Y.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),Y.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),vn({data:q,additionalKeyValues:Y,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:I}),B("endobj"),s.objectNumber=De(),B("<< /ShadingType "+s.type),B("/ColorSpace /DeviceRGB");var it="/Coords ["+rt(parseFloat(s.coords[0]))+" "+rt(parseFloat(s.coords[1]))+" ";s.type===2?it+=rt(parseFloat(s.coords[2]))+" "+rt(parseFloat(s.coords[3])):it+=rt(parseFloat(s.coords[2]))+" "+rt(parseFloat(s.coords[3]))+" "+rt(parseFloat(s.coords[4]))+" "+rt(parseFloat(s.coords[5])),B(it+="]"),s.matrix&&B("/Matrix ["+s.matrix.toString()+"]"),B("/Function "+I+" 0 R"),B("/Extend [true true]"),B(">>"),B("endobj")},yo=function(s,v){var I=Me(),q=De();v.push({resourcesOid:I,objectOid:q}),s.objectNumber=q;var Y=[];Y.push({key:"Type",value:"/Pattern"}),Y.push({key:"PatternType",value:"1"}),Y.push({key:"PaintType",value:"1"}),Y.push({key:"TilingType",value:"1"}),Y.push({key:"BBox",value:"["+s.boundingBox.map(rt).join(" ")+"]"}),Y.push({key:"XStep",value:rt(s.xStep)}),Y.push({key:"YStep",value:rt(s.yStep)}),Y.push({key:"Resources",value:I+" 0 R"}),s.matrix&&Y.push({key:"Matrix",value:"["+s.matrix.toString()+"]"}),vn({data:s.stream,additionalKeyValues:Y,objectId:s.objectNumber}),B("endobj")},$r=function(s){var v;for(v in ce)ce.hasOwnProperty(v)&&(ce[v]instanceof Ur?bo(ce[v]):ce[v]instanceof _i&&yo(ce[v],s))},ga=function(s){for(var v in s.objectNumber=De(),B("<<"),s)switch(v){case"opacity":B("/ca "+bt(s[v]));break;case"stroke-opacity":B("/CA "+bt(s[v]))}B(">>"),B("endobj")},wo=function(){var s;for(s in Ae)Ae.hasOwnProperty(s)&&ga(Ae[s])},Bi=function(){for(var s in B("/XObject <<"),We)We.hasOwnProperty(s)&&We[s].objectNumber>=0&&B("/"+s+" "+We[s].objectNumber+" 0 R");ue.publish("putXobjectDict"),B(">>")},No=function(){Xe.oid=De(),B("<<"),B("/Filter /Standard"),B("/V "+Xe.v),B("/R "+Xe.r),B("/U <"+Xe.toHexString(Xe.U)+">"),B("/O <"+Xe.toHexString(Xe.O)+">"),B("/P "+Xe.P),B(">>"),B("endobj")},ma=function(){for(var s in B("/Font <<"),ge)ge.hasOwnProperty(s)&&(L===!1||L===!0&&S.hasOwnProperty(s))&&B("/"+s+" "+ge[s].objectNumber+" 0 R");B(">>")},Lo=function(){if(Object.keys(ce).length>0){for(var s in B("/Shading <<"),ce)ce.hasOwnProperty(s)&&ce[s]instanceof Ur&&ce[s].objectNumber>=0&&B("/"+s+" "+ce[s].objectNumber+" 0 R");ue.publish("putShadingPatternDict"),B(">>")}},Zr=function(s){if(Object.keys(ce).length>0){for(var v in B("/Pattern <<"),ce)ce.hasOwnProperty(v)&&ce[v]instanceof p.TilingPattern&&ce[v].objectNumber>=0&&ce[v].objectNumber<s&&B("/"+v+" "+ce[v].objectNumber+" 0 R");ue.publish("putTilingPatternDict"),B(">>")}},xo=function(){if(Object.keys(Ae).length>0){var s;for(s in B("/ExtGState <<"),Ae)Ae.hasOwnProperty(s)&&Ae[s].objectNumber>=0&&B("/"+s+" "+Ae[s].objectNumber+" 0 R");ue.publish("putGStateDict"),B(">>")}},Fe=function(s){fn(s.resourcesOid,!0),B("<<"),B("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),ma(),Lo(),Zr(s.objectOid),xo(),Bi(),B(">>"),B("endobj")},va=function(){var s=[];Yr(),wo(),Kr(),$r(s),ue.publish("putResources"),s.forEach(Fe),Fe({resourcesOid:kn,objectOid:Number.MAX_SAFE_INTEGER}),ue.publish("postPutResources")},ba=function(){ue.publish("putAdditionalObjects");for(var s=0;s<xt.length;s++){var v=xt[s];fn(v.objId,!0),B(v.content),B("endobj")}ue.publish("postPutAdditionalObjects")},ya=function(s){Le[s.fontName]=Le[s.fontName]||{},Le[s.fontName][s.fontStyle]=s.id},Mi=function(s,v,I,q,Y){var it={id:"F"+(Object.keys(ge).length+1).toString(10),postScriptName:s,fontName:v,fontStyle:I,encoding:q,isStandardFont:Y||!1,metadata:{}};return ue.publish("addFont",{font:it,instance:this}),ge[it.id]=it,ya(it),it.id},Ao=function(s){for(var v=0,I=wt.length;v<I;v++){var q=Mi.call(this,s[v][0],s[v][1],s[v][2],wt[v][3],!0);L===!1&&(S[q]=!0);var Y=s[v][0].split("-");ya({id:q,fontName:Y[0],fontStyle:Y[1]||""})}ue.publish("addFonts",{fonts:ge,dictionary:Le})},In=function(s){return s.foo=function(){try{return s.apply(this,arguments)}catch(q){var v=q.stack||"";~v.indexOf(" at ")&&(v=v.split(" at ")[1]);var I="Error in function "+v.split(`
`)[0].split("<")[0]+": "+q.message;if(!zt.console)throw new Error(I);zt.console.error(I,q),zt.alert&&alert(I)}},s.foo.bar=s,s.foo},Qr=function(s,v){var I,q,Y,it,yt,Ct,qt,$t,Yt;if(Y=(v=v||{}).sourceEncoding||"Unicode",yt=v.outputEncoding,(v.autoencode||yt)&&ge[ee].metadata&&ge[ee].metadata[Y]&&ge[ee].metadata[Y].encoding&&(it=ge[ee].metadata[Y].encoding,!yt&&ge[ee].encoding&&(yt=ge[ee].encoding),!yt&&it.codePages&&(yt=it.codePages[0]),typeof yt=="string"&&(yt=it[yt]),yt)){for(qt=!1,Ct=[],I=0,q=s.length;I<q;I++)($t=yt[s.charCodeAt(I)])?Ct.push(String.fromCharCode($t)):Ct.push(s[I]),Ct[I].charCodeAt(0)>>8&&(qt=!0);s=Ct.join("")}for(I=s.length;qt===void 0&&I!==0;)s.charCodeAt(I-1)>>8&&(qt=!0),I--;if(!qt)return s;for(Ct=v.noBOM?[]:[254,255],I=0,q=s.length;I<q;I++){if((Yt=($t=s.charCodeAt(I))>>8)>>8)throw new Error("Character at position "+I+" of string '"+s+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Ct.push(Yt),Ct.push($t-(Yt<<8))}return String.fromCharCode.apply(void 0,Ct)},on=p.__private__.pdfEscape=p.pdfEscape=function(s,v){return Qr(s,v).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ei=p.__private__.beginPage=function(s){Nt[++Se]=[],Jt[Se]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(s[0]),topRightY:Number(s[1])}},Na(Se),at(Nt[j])},wa=function(s,v){var I,q,Y;switch(n=v||n,typeof s=="string"&&(I=_(s.toLowerCase()),Array.isArray(I)&&(q=I[0],Y=I[1])),Array.isArray(s)&&(q=s[0]*Ft,Y=s[1]*Ft),isNaN(q)&&(q=c[0],Y=c[1]),(q>14400||Y>14400)&&(ve.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),q=Math.min(14400,q),Y=Math.min(14400,Y)),c=[q,Y],n.substr(0,1)){case"l":Y>q&&(c=[Y,q]);break;case"p":q>Y&&(c=[Y,q])}Ei(c),Pa(Ri),B(jn),Ui!==0&&B(Ui+" J"),Hi!==0&&B(Hi+" j"),ue.publish("addPage",{pageNumber:Se})},So=function(s){s>0&&s<=Se&&(Nt.splice(s,1),Jt.splice(s,1),Se--,j>Se&&(j=Se),this.setPage(j))},Na=function(s){s>0&&s<=Se&&(j=s)},_o=p.__private__.getNumberOfPages=p.getNumberOfPages=function(){return Nt.length-1},La=function(s,v,I){var q,Y=void 0;return I=I||{},s=s!==void 0?s:ge[ee].fontName,v=v!==void 0?v:ge[ee].fontStyle,q=s.toLowerCase(),Le[q]!==void 0&&Le[q][v]!==void 0?Y=Le[q][v]:Le[s]!==void 0&&Le[s][v]!==void 0?Y=Le[s][v]:I.disableWarning===!1&&ve.warn("Unable to look up font label for font '"+s+"', '"+v+"'. Refer to getFontList() for available fonts."),Y||I.noFallback||(Y=Le.times[v])==null&&(Y=Le.times.normal),Y},Po=p.__private__.putInfo=function(){var s=De(),v=function(q){return q};for(var I in x!==null&&(v=Xe.encryptor(s,0)),B("<<"),B("/Producer ("+on(v("jsPDF "+Ut.version))+")"),Ht)Ht.hasOwnProperty(I)&&Ht[I]&&B("/"+I.substr(0,1).toUpperCase()+I.substr(1)+" ("+on(v(Ht[I]))+")");B("/CreationDate ("+on(v(ct))+")"),B(">>"),B("endobj")},Ti=p.__private__.putCatalog=function(s){var v=(s=s||{}).rootDictionaryObjId||tr;switch(De(),B("<<"),B("/Type /Catalog"),B("/Pages "+v+" 0 R"),kt||(kt="fullwidth"),kt){case"fullwidth":B("/OpenAction [3 0 R /FitH null]");break;case"fullheight":B("/OpenAction [3 0 R /FitV null]");break;case"fullpage":B("/OpenAction [3 0 R /Fit]");break;case"original":B("/OpenAction [3 0 R /XYZ null null 1]");break;default:var I=""+kt;I.substr(I.length-1)==="%"&&(kt=parseInt(kt)/100),typeof kt=="number"&&B("/OpenAction [3 0 R /XYZ null null "+bt(kt)+"]")}switch(te||(te="continuous"),te){case"continuous":B("/PageLayout /OneColumn");break;case"single":B("/PageLayout /SinglePage");break;case"two":case"twoleft":B("/PageLayout /TwoColumnLeft");break;case"tworight":B("/PageLayout /TwoColumnRight")}Gt&&B("/PageMode /"+Gt),ue.publish("putCatalog"),B(">>"),B("endobj")},ko=p.__private__.putTrailer=function(){B("trailer"),B("<<"),B("/Size "+(J+1)),B("/Root "+J+" 0 R"),B("/Info "+(J-1)+" 0 R"),x!==null&&B("/Encrypt "+Xe.oid+" 0 R"),B("/ID [ <"+ot+"> <"+ot+"> ]"),B(">>")},Fo=p.__private__.putHeader=function(){B("%PDF-"+O),B("%ºß¬à")},Co=p.__private__.putXRef=function(){var s="0000000000";B("xref"),B("0 "+(J+1)),B("0000000000 65535 f ");for(var v=1;v<=J;v++)typeof Z[v]=="function"?B((s+Z[v]()).slice(-10)+" 00000 n "):Z[v]!==void 0?B((s+Z[v]).slice(-10)+" 00000 n "):B("0000000000 00000 n ")},er=p.__private__.buildDocument=function(){Vt(),at(et),ue.publish("buildDocument"),Fo(),Cr(),ba(),va(),x!==null&&No(),Po(),Ti();var s=Q;return Co(),ko(),B("startxref"),B(""+s),B("%%EOF"),at(Nt[j]),et.join(`
`)},ti=p.__private__.getBlob=function(s){return new Blob([Mt(s)],{type:"application/pdf"})},ei=p.output=p.__private__.output=In(function(s,v){switch(typeof(v=v||{})=="string"?v={filename:v}:v.filename=v.filename||"generated.pdf",s){case void 0:return er();case"save":p.save(v.filename);break;case"arraybuffer":return Mt(er());case"blob":return ti(er());case"bloburi":case"bloburl":if(zt.URL!==void 0&&typeof zt.URL.createObjectURL=="function")return zt.URL&&zt.URL.createObjectURL(ti(er()))||void 0;ve.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var I="",q=er();try{I=vs(q)}catch{I=vs(unescape(encodeURIComponent(q)))}return"data:application/pdf;filename="+v.filename+";base64,"+I;case"pdfobjectnewwindow":if(Object.prototype.toString.call(zt)==="[object Window]"){var Y="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",it=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';v.pdfObjectUrl&&(Y=v.pdfObjectUrl,it="");var yt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+Y+'"'+it+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(v)+");<\/script></body></html>",Ct=zt.open();return Ct!==null&&Ct.document.write(yt),Ct}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(zt)==="[object Window]"){var qt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(v.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+v.filename+'" width="500px" height="400px" /></body></html>',$t=zt.open();if($t!==null){$t.document.write(qt);var Yt=this;$t.document.documentElement.querySelector("#pdfViewer").onload=function(){$t.document.title=v.filename,$t.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(Yt.output("bloburl"))}}return $t}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(zt)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var re='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",v)+'"></iframe></body></html>',xe=zt.open();if(xe!==null&&(xe.document.write(re),xe.document.title=v.filename),xe||typeof safari>"u")return xe;break;case"datauri":case"dataurl":return zt.document.location.href=this.output("datauristring",v);default:return null}}),xa=function(s){return Array.isArray(Fr)===!0&&Fr.indexOf(s)>-1};switch(a){case"pt":Ft=1;break;case"mm":Ft=72/25.4;break;case"cm":Ft=72/2.54;break;case"in":Ft=72;break;case"px":Ft=xa("px_scaling")==1?.75:96/72;break;case"pc":case"em":Ft=12;break;case"ex":Ft=6;break;default:if(typeof a!="number")throw new Error("Invalid unit: "+a);Ft=a}var Xe=null;Et(),tt();var Io=function(s){return x!==null?Xe.encryptor(s,0):function(v){return v}},Aa=p.__private__.getPageInfo=p.getPageInfo=function(s){if(isNaN(s)||s%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Jt[s].objId,pageNumber:s,pageContext:Jt[s]}},Wt=p.__private__.getPageInfoByObjId=function(s){if(isNaN(s)||s%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var v in Jt)if(Jt[v].objId===s)break;return Aa(v)},jo=p.__private__.getCurrentPageInfo=p.getCurrentPageInfo=function(){return{objId:Jt[j].objId,pageNumber:j,pageContext:Jt[j]}};p.addPage=function(){return wa.apply(this,arguments),this},p.setPage=function(){return Na.apply(this,arguments),at.call(this,Nt[j]),this},p.insertPage=function(s){return this.addPage(),this.movePage(j,s),this},p.movePage=function(s,v){var I,q;if(s>v){I=Nt[s],q=Jt[s];for(var Y=s;Y>v;Y--)Nt[Y]=Nt[Y-1],Jt[Y]=Jt[Y-1];Nt[v]=I,Jt[v]=q,this.setPage(v)}else if(s<v){I=Nt[s],q=Jt[s];for(var it=s;it<v;it++)Nt[it]=Nt[it+1],Jt[it]=Jt[it+1];Nt[v]=I,Jt[v]=q,this.setPage(v)}return this},p.deletePage=function(){return So.apply(this,arguments),this},p.__private__.text=p.text=function(s,v,I,q,Y){var it,yt,Ct,qt,$t,Yt,re,xe,_e,Ee=(q=q||{}).scope||this;if(typeof s=="number"&&typeof v=="number"&&(typeof I=="string"||Array.isArray(I))){var Ke=I;I=v,v=s,s=Ke}if(arguments[3]instanceof Rt?(G("The transform parameter of text() with a Matrix value"),_e=Y):(Ct=arguments[4],qt=arguments[5],fe(re=arguments[3])==="object"&&re!==null||(typeof Ct=="string"&&(qt=Ct,Ct=null),typeof re=="string"&&(qt=re,re=null),typeof re=="number"&&(Ct=re,re=null),q={flags:re,angle:Ct,align:qt})),isNaN(v)||isNaN(I)||s==null)throw new Error("Invalid arguments passed to jsPDF.text");if(s.length===0)return Ee;var ze="",On=!1,dn=typeof q.lineHeightFactor=="number"?q.lineHeightFactor:jr,Jn=Ee.internal.scaleFactor;function Oa(we){return we=we.split("	").join(Array(q.TabLen||9).join(" ")),on(we,re)}function Yi(we){for(var Ne,Ie=we.concat(),Re=[],or=Ie.length;or--;)typeof(Ne=Ie.shift())=="string"?Re.push(Ne):Array.isArray(we)&&(Ne.length===1||Ne[1]===void 0&&Ne[2]===void 0)?Re.push(Ne[0]):Re.push([Ne[0],Ne[1],Ne[2]]);return Re}function Xi(we,Ne){var Ie;if(typeof we=="string")Ie=Ne(we)[0];else if(Array.isArray(we)){for(var Re,or,ra=we.concat(),mi=[],qa=ra.length;qa--;)typeof(Re=ra.shift())=="string"?mi.push(Ne(Re)[0]):Array.isArray(Re)&&typeof Re[0]=="string"&&(or=Ne(Re[0],Re[1],Re[2]),mi.push([or[0],or[1],or[2]]));Ie=mi}return Ie}var si=!1,Ki=!0;if(typeof s=="string")si=!0;else if(Array.isArray(s)){var $i=s.concat();yt=[];for(var ci,Ge=$i.length;Ge--;)(typeof(ci=$i.shift())!="string"||Array.isArray(ci)&&typeof ci[0]!="string")&&(Ki=!1);si=Ki}if(si===!1)throw new Error('Type of text must be string or Array. "'+s+'" is not recognized.');typeof s=="string"&&(s=s.match(/[\r?\n]/)?s.split(/\r\n|\r|\n/g):[s]);var ui=At/Ee.internal.scaleFactor,li=ui*(dn-1);switch(q.baseline){case"bottom":I-=li;break;case"top":I+=ui-li;break;case"hanging":I+=ui-2*li;break;case"middle":I+=ui/2-li}if((Yt=q.maxWidth||0)>0&&(typeof s=="string"?s=Ee.splitTextToSize(s,Yt):Object.prototype.toString.call(s)==="[object Array]"&&(s=s.reduce(function(we,Ne){return we.concat(Ee.splitTextToSize(Ne,Yt))},[]))),it={text:s,x:v,y:I,options:q,mutex:{pdfEscape:on,activeFontKey:ee,fonts:ge,activeFontSize:At}},ue.publish("preProcessText",it),s=it.text,Ct=(q=it.options).angle,!(_e instanceof Rt)&&Ct&&typeof Ct=="number"){Ct*=Math.PI/180,q.rotationDirection===0&&(Ct=-Ct),$===M.ADVANCED&&(Ct=-Ct);var hi=Math.cos(Ct),Zi=Math.sin(Ct);_e=new Rt(hi,Zi,-Zi,hi,0,0)}else Ct&&Ct instanceof Rt&&(_e=Ct);$!==M.ADVANCED||_e||(_e=Un),($t=q.charSpace||ii)!==void 0&&(ze+=rt(F($t))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(xe=q.horizontalScale)!==void 0&&(ze+=rt(100*xe)+` Tz
`),q.lang;var sn=-1,Uo=q.renderingMode!==void 0?q.renderingMode:q.stroke,Qi=Ee.internal.getCurrentPageInfo().pageContext;switch(Uo){case 0:case!1:case"fill":sn=0;break;case 1:case!0:case"stroke":sn=1;break;case 2:case"fillThenStroke":sn=2;break;case 3:case"invisible":sn=3;break;case 4:case"fillAndAddForClipping":sn=4;break;case 5:case"strokeAndAddPathForClipping":sn=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":sn=6;break;case 7:case"addToPathForClipping":sn=7}var Ba=Qi.usedRenderingMode!==void 0?Qi.usedRenderingMode:-1;sn!==-1?ze+=sn+` Tr
`:Ba!==-1&&(ze+=`0 Tr
`),sn!==-1&&(Qi.usedRenderingMode=sn),qt=q.align||"left";var bn,fi=At*dn,Ma=Ee.internal.pageSize.getWidth(),Ea=ge[ee];$t=q.charSpace||ii,Yt=q.maxWidth||0,re=Object.assign({autoencode:!0,noBOM:!0},q.flags);var dr=[],Mr=function(we){return Ee.getStringUnitWidth(we,{font:Ea,charSpace:$t,fontSize:At,doKerning:!1})*At/Jn};if(Object.prototype.toString.call(s)==="[object Array]"){var cn;yt=Yi(s),qt!=="left"&&(bn=yt.map(Mr));var en,pr=0;if(qt==="right"){v-=bn[0],s=[],Ge=yt.length;for(var rr=0;rr<Ge;rr++)rr===0?(en=Gn(v),cn=nr(I)):(en=F(pr-bn[rr]),cn=-fi),s.push([yt[rr],en,cn]),pr=bn[rr]}else if(qt==="center"){v-=bn[0]/2,s=[],Ge=yt.length;for(var ir=0;ir<Ge;ir++)ir===0?(en=Gn(v),cn=nr(I)):(en=F((pr-bn[ir])/2),cn=-fi),s.push([yt[ir],en,cn]),pr=bn[ir]}else if(qt==="left"){s=[],Ge=yt.length;for(var di=0;di<Ge;di++)s.push(yt[di])}else if(qt==="justify"&&Ea.encoding==="Identity-H"){s=[],Ge=yt.length,Yt=Yt!==0?Yt:Ma;for(var ar=0,Ce=0;Ce<Ge;Ce++)if(cn=Ce===0?nr(I):-fi,en=Ce===0?Gn(v):ar,Ce<Ge-1){var ta=F((Yt-bn[Ce])/(yt[Ce].split(" ").length-1)),nn=yt[Ce].split(" ");s.push([nn[0]+" ",en,cn]),ar=0;for(var yn=1;yn<nn.length;yn++){var pi=(Mr(nn[yn-1]+" "+nn[yn])-Mr(nn[yn]))*Jn+ta;yn==nn.length-1?s.push([nn[yn],pi,0]):s.push([nn[yn]+" ",pi,0]),ar-=pi}}else s.push([yt[Ce],en,cn]);s.push(["",ar,0])}else{if(qt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(s=[],Ge=yt.length,Yt=Yt!==0?Yt:Ma,Ce=0;Ce<Ge;Ce++)cn=Ce===0?nr(I):-fi,en=Ce===0?Gn(v):0,Ce<Ge-1?dr.push(rt(F((Yt-bn[Ce])/(yt[Ce].split(" ").length-1)))):dr.push(0),s.push([yt[Ce],en,cn])}}var Ta=typeof q.R2L=="boolean"?q.R2L:Tt;Ta===!0&&(s=Xi(s,function(we,Ne,Ie){return[we.split("").reverse().join(""),Ne,Ie]})),it={text:s,x:v,y:I,options:q,mutex:{pdfEscape:on,activeFontKey:ee,fonts:ge,activeFontSize:At}},ue.publish("postProcessText",it),s=it.text,On=it.mutex.isHex||!1;var ea=ge[ee].encoding;ea!=="WinAnsiEncoding"&&ea!=="StandardEncoding"||(s=Xi(s,function(we,Ne,Ie){return[Oa(we),Ne,Ie]})),yt=Yi(s),s=[];for(var Er,Tr,gr,qr=0,gi=1,Dr=Array.isArray(yt[0])?gi:qr,mr="",na=function(we,Ne,Ie){var Re="";return Ie instanceof Rt?(Ie=typeof q.angle=="number"?Pn(Ie,new Rt(1,0,0,1,we,Ne)):Pn(new Rt(1,0,0,1,we,Ne),Ie),$===M.ADVANCED&&(Ie=Pn(new Rt(1,0,0,-1,0,0),Ie)),Re=Ie.join(" ")+` Tm
`):Re=rt(we)+" "+rt(Ne)+` Td
`,Re},wn=0;wn<yt.length;wn++){switch(mr="",Dr){case gi:gr=(On?"<":"(")+yt[wn][0]+(On?">":")"),Er=parseFloat(yt[wn][1]),Tr=parseFloat(yt[wn][2]);break;case qr:gr=(On?"<":"(")+yt[wn]+(On?">":")"),Er=Gn(v),Tr=nr(I)}dr!==void 0&&dr[wn]!==void 0&&(mr=dr[wn]+` Tw
`),wn===0?s.push(mr+na(Er,Tr,_e)+gr):Dr===qr?s.push(mr+gr):Dr===gi&&s.push(mr+na(Er,Tr,_e)+gr)}s=Dr===qr?s.join(` Tj
T* `):s.join(` Tj
`),s+=` Tj
`;var Nn=`BT
/`;return Nn+=ee+" "+At+` Tf
`,Nn+=rt(At*dn)+` TL
`,Nn+=Or+`
`,Nn+=ze,Nn+=s,B(Nn+="ET"),S[ee]=!0,Ee};var Oo=p.__private__.clip=p.clip=function(s){return B(s==="evenodd"?"W*":"W"),this};p.clipEvenOdd=function(){return Oo("evenodd")},p.__private__.discardPath=p.discardPath=function(){return B("n"),this};var Wn=p.__private__.isValidStyle=function(s){var v=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(s)!==-1&&(v=!0),v};p.__private__.setDefaultPathOperation=p.setDefaultPathOperation=function(s){return Wn(s)&&(g=s),this};var Sa=p.__private__.getStyle=p.getStyle=function(s){var v=g;switch(s){case"D":case"S":v="S";break;case"F":v="f";break;case"FD":case"DF":v="B";break;case"f":case"f*":case"B":case"B*":v=s}return v},_a=p.close=function(){return B("h"),this};p.stroke=function(){return B("S"),this},p.fill=function(s){return ni("f",s),this},p.fillEvenOdd=function(s){return ni("f*",s),this},p.fillStroke=function(s){return ni("B",s),this},p.fillStrokeEvenOdd=function(s){return ni("B*",s),this};var ni=function(s,v){fe(v)==="object"?Mo(v,s):B(s)},qi=function(s){s===null||$===M.ADVANCED&&s===void 0||(s=Sa(s),B(s))};function Bo(s,v,I,q,Y){var it=new _i(v||this.boundingBox,I||this.xStep,q||this.yStep,this.gState,Y||this.matrix);it.stream=this.stream;var yt=s+"$$"+this.cloneIndex+++"$$";return an(yt,it),it}var Mo=function(s,v){var I=kr[s.key],q=ce[I];if(q instanceof Ur)B("q"),B(Eo(v)),q.gState&&p.setGState(q.gState),B(s.matrix.toString()+" cm"),B("/"+I+" sh"),B("Q");else if(q instanceof _i){var Y=new Rt(1,0,0,-1,0,fr());s.matrix&&(Y=Y.multiply(s.matrix||Un),I=Bo.call(q,s.key,s.boundingBox,s.xStep,s.yStep,Y).id),B("q"),B("/Pattern cs"),B("/"+I+" scn"),q.gState&&p.setGState(q.gState),B(v),B("Q")}},Eo=function(s){switch(s){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Di=p.moveTo=function(s,v){return B(rt(F(s))+" "+rt(D(v))+" m"),this},Ir=p.lineTo=function(s,v){return B(rt(F(s))+" "+rt(D(v))+" l"),this},lr=p.curveTo=function(s,v,I,q,Y,it){return B([rt(F(s)),rt(D(v)),rt(F(I)),rt(D(q)),rt(F(Y)),rt(D(it)),"c"].join(" ")),this};p.__private__.line=p.line=function(s,v,I,q,Y){if(isNaN(s)||isNaN(v)||isNaN(I)||isNaN(q)||!Wn(Y))throw new Error("Invalid arguments passed to jsPDF.line");return $===M.COMPAT?this.lines([[I-s,q-v]],s,v,[1,1],Y||"S"):this.lines([[I-s,q-v]],s,v,[1,1]).stroke()},p.__private__.lines=p.lines=function(s,v,I,q,Y,it){var yt,Ct,qt,$t,Yt,re,xe,_e,Ee,Ke,ze,On;if(typeof s=="number"&&(On=I,I=v,v=s,s=On),q=q||[1,1],it=it||!1,isNaN(v)||isNaN(I)||!Array.isArray(s)||!Array.isArray(q)||!Wn(Y)||typeof it!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Di(v,I),yt=q[0],Ct=q[1],$t=s.length,Ke=v,ze=I,qt=0;qt<$t;qt++)(Yt=s[qt]).length===2?(Ke=Yt[0]*yt+Ke,ze=Yt[1]*Ct+ze,Ir(Ke,ze)):(re=Yt[0]*yt+Ke,xe=Yt[1]*Ct+ze,_e=Yt[2]*yt+Ke,Ee=Yt[3]*Ct+ze,Ke=Yt[4]*yt+Ke,ze=Yt[5]*Ct+ze,lr(re,xe,_e,Ee,Ke,ze));return it&&_a(),qi(Y),this},p.path=function(s){for(var v=0;v<s.length;v++){var I=s[v],q=I.c;switch(I.op){case"m":Di(q[0],q[1]);break;case"l":Ir(q[0],q[1]);break;case"c":lr.apply(this,q);break;case"h":_a()}}return this},p.__private__.rect=p.rect=function(s,v,I,q,Y){if(isNaN(s)||isNaN(v)||isNaN(I)||isNaN(q)||!Wn(Y))throw new Error("Invalid arguments passed to jsPDF.rect");return $===M.COMPAT&&(q=-q),B([rt(F(s)),rt(D(v)),rt(F(I)),rt(F(q)),"re"].join(" ")),qi(Y),this},p.__private__.triangle=p.triangle=function(s,v,I,q,Y,it,yt){if(isNaN(s)||isNaN(v)||isNaN(I)||isNaN(q)||isNaN(Y)||isNaN(it)||!Wn(yt))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[I-s,q-v],[Y-I,it-q],[s-Y,v-it]],s,v,[1,1],yt,!0),this},p.__private__.roundedRect=p.roundedRect=function(s,v,I,q,Y,it,yt){if(isNaN(s)||isNaN(v)||isNaN(I)||isNaN(q)||isNaN(Y)||isNaN(it)||!Wn(yt))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Ct=4/3*(Math.SQRT2-1);return Y=Math.min(Y,.5*I),it=Math.min(it,.5*q),this.lines([[I-2*Y,0],[Y*Ct,0,Y,it-it*Ct,Y,it],[0,q-2*it],[0,it*Ct,-Y*Ct,it,-Y,it],[2*Y-I,0],[-Y*Ct,0,-Y,-it*Ct,-Y,-it],[0,2*it-q],[0,-it*Ct,Y*Ct,-it,Y,-it]],s+Y,v,[1,1],yt,!0),this},p.__private__.ellipse=p.ellipse=function(s,v,I,q,Y){if(isNaN(s)||isNaN(v)||isNaN(I)||isNaN(q)||!Wn(Y))throw new Error("Invalid arguments passed to jsPDF.ellipse");var it=4/3*(Math.SQRT2-1)*I,yt=4/3*(Math.SQRT2-1)*q;return Di(s+I,v),lr(s+I,v-yt,s+it,v-q,s,v-q),lr(s-it,v-q,s-I,v-yt,s-I,v),lr(s-I,v+yt,s-it,v+q,s,v+q),lr(s+it,v+q,s+I,v+yt,s+I,v),qi(Y),this},p.__private__.circle=p.circle=function(s,v,I,q){if(isNaN(s)||isNaN(v)||isNaN(I)||!Wn(q))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(s,v,I,I,q)},p.setFont=function(s,v,I){return I&&(v=Lt(v,I)),ee=La(s,v,{disableWarning:!1}),this};var To=p.__private__.getFont=p.getFont=function(){return ge[La.apply(p,arguments)]};p.__private__.getFontList=p.getFontList=function(){var s,v,I={};for(s in Le)if(Le.hasOwnProperty(s))for(v in I[s]=[],Le[s])Le[s].hasOwnProperty(v)&&I[s].push(v);return I},p.addFont=function(s,v,I,q,Y){var it=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&it.indexOf(arguments[3])!==-1?Y=arguments[3]:arguments[3]&&it.indexOf(arguments[3])==-1&&(I=Lt(I,q)),Y=Y||"Identity-H",Mi.call(this,s,v,I,Y)};var jr,Ri=i.lineWidth||.200025,ri=p.__private__.getLineWidth=p.getLineWidth=function(){return Ri},Pa=p.__private__.setLineWidth=p.setLineWidth=function(s){return Ri=s,B(rt(F(s))+" w"),this};p.__private__.setLineDash=Ut.API.setLineDash=Ut.API.setLineDashPattern=function(s,v){if(s=s||[],v=v||0,isNaN(v)||!Array.isArray(s))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return s=s.map(function(I){return rt(F(I))}).join(" "),v=rt(F(v)),B("["+s+"] "+v+" d"),this};var ka=p.__private__.getLineHeight=p.getLineHeight=function(){return At*jr};p.__private__.getLineHeight=p.getLineHeight=function(){return At*jr};var Fa=p.__private__.setLineHeightFactor=p.setLineHeightFactor=function(s){return typeof(s=s||1.15)=="number"&&(jr=s),this},Ca=p.__private__.getLineHeightFactor=p.getLineHeightFactor=function(){return jr};Fa(i.lineHeight);var Gn=p.__private__.getHorizontalCoordinate=function(s){return F(s)},nr=p.__private__.getVerticalCoordinate=function(s){return $===M.ADVANCED?s:Jt[j].mediaBox.topRightY-Jt[j].mediaBox.bottomLeftY-F(s)},qo=p.__private__.getHorizontalCoordinateString=p.getHorizontalCoordinateString=function(s){return rt(Gn(s))},hr=p.__private__.getVerticalCoordinateString=p.getVerticalCoordinateString=function(s){return rt(nr(s))},jn=i.strokeColor||"0 G";p.__private__.getStrokeColor=p.getDrawColor=function(){return Fn(jn)},p.__private__.setStrokeColor=p.setDrawColor=function(s,v,I,q){return jn=Cn({ch1:s,ch2:v,ch3:I,ch4:q,pdfColorType:"draw",precision:2}),B(jn),this};var zi=i.fillColor||"0 g";p.__private__.getFillColor=p.getFillColor=function(){return Fn(zi)},p.__private__.setFillColor=p.setFillColor=function(s,v,I,q){return zi=Cn({ch1:s,ch2:v,ch3:I,ch4:q,pdfColorType:"fill",precision:2}),B(zi),this};var Or=i.textColor||"0 g",Do=p.__private__.getTextColor=p.getTextColor=function(){return Fn(Or)};p.__private__.setTextColor=p.setTextColor=function(s,v,I,q){return Or=Cn({ch1:s,ch2:v,ch3:I,ch4:q,pdfColorType:"text",precision:3}),this};var ii=i.charSpace,Ro=p.__private__.getCharSpace=p.getCharSpace=function(){return parseFloat(ii||0)};p.__private__.setCharSpace=p.setCharSpace=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return ii=s,this};var Ui=0;p.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},p.__private__.setLineCap=p.setLineCap=function(s){var v=p.CapJoinStyles[s];if(v===void 0)throw new Error("Line cap style of '"+s+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Ui=v,B(v+" J"),this};var Hi=0;p.__private__.setLineJoin=p.setLineJoin=function(s){var v=p.CapJoinStyles[s];if(v===void 0)throw new Error("Line join style of '"+s+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Hi=v,B(v+" j"),this},p.__private__.setLineMiterLimit=p.__private__.setMiterLimit=p.setLineMiterLimit=p.setMiterLimit=function(s){if(s=s||0,isNaN(s))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return B(rt(F(s))+" M"),this},p.GState=go,p.setGState=function(s){(s=typeof s=="string"?Ae[_n[s]]:Ia(null,s)).equals(Qn)||(B("/"+s.id+" gs"),Qn=s)};var Ia=function(s,v){if(!s||!_n[s]){var I=!1;for(var q in Ae)if(Ae.hasOwnProperty(q)&&Ae[q].equals(v)){I=!0;break}if(I)v=Ae[q];else{var Y="GS"+(Object.keys(Ae).length+1).toString(10);Ae[Y]=v,v.id=Y}return s&&(_n[s]=v.id),ue.publish("addGState",v),v}};p.addGState=function(s,v){return Ia(s,v),this},p.saveGraphicsState=function(){return B("q"),Dn.push({key:ee,size:At,color:Or}),this},p.restoreGraphicsState=function(){B("Q");var s=Dn.pop();return ee=s.key,At=s.size,Or=s.color,Qn=null,this},p.setCurrentTransformationMatrix=function(s){return B(s.toString()+" cm"),this},p.comment=function(s){return B("#"+s),this};var ai=function(s,v){var I=s||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return I},set:function(it){isNaN(it)||(I=parseFloat(it))}});var q=v||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return q},set:function(it){isNaN(it)||(q=parseFloat(it))}});var Y="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return Y},set:function(it){Y=it.toString()}}),this},Vi=function(s,v,I,q){ai.call(this,s,v),this.type="rect";var Y=I||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return Y},set:function(yt){isNaN(yt)||(Y=parseFloat(yt))}});var it=q||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return it},set:function(yt){isNaN(yt)||(it=parseFloat(yt))}}),this},Wi=function(){this.page=Se,this.currentPage=j,this.pages=Nt.slice(0),this.pagesContext=Jt.slice(0),this.x=Ye,this.y=oe,this.matrix=Sn,this.width=Br(j),this.height=fr(j),this.outputDestination=jt,this.id="",this.objectNumber=-1};Wi.prototype.restore=function(){Se=this.page,j=this.currentPage,Jt=this.pagesContext,Nt=this.pages,Ye=this.x,oe=this.y,Sn=this.matrix,Gi(j,this.width),Ji(j,this.height),jt=this.outputDestination};var ja=function(s,v,I,q,Y){zn.push(new Wi),Se=j=0,Nt=[],Ye=s,oe=v,Sn=Y,Ei([I,q])},zo=function(s){if(Rn[s])zn.pop().restore();else{var v=new Wi,I="Xo"+(Object.keys(We).length+1).toString(10);v.id=I,Rn[s]=I,We[I]=v,ue.publish("addFormObject",v),zn.pop().restore()}};for(var oi in p.beginFormObject=function(s,v,I,q,Y){return ja(s,v,I,q,Y),this},p.endFormObject=function(s){return zo(s),this},p.doFormObject=function(s,v){var I=We[Rn[s]];return B("q"),B(v.toString()+" cm"),B("/"+I.id+" Do"),B("Q"),this},p.getFormObject=function(s){var v=We[Rn[s]];return{x:v.x,y:v.y,width:v.width,height:v.height,matrix:v.matrix}},p.save=function(s,v){return s=s||"generated.pdf",(v=v||{}).returnPromise=v.returnPromise||!1,v.returnPromise===!1?(zr(ti(er()),s),typeof zr.unload=="function"&&zt.setTimeout&&setTimeout(zr.unload,911),this):new Promise(function(I,q){try{var Y=zr(ti(er()),s);typeof zr.unload=="function"&&zt.setTimeout&&setTimeout(zr.unload,911),I(Y)}catch(it){q(it.message)}})},Ut.API)Ut.API.hasOwnProperty(oi)&&(oi==="events"&&Ut.API.events.length?function(s,v){var I,q,Y;for(Y=v.length-1;Y!==-1;Y--)I=v[Y][0],q=v[Y][1],s.subscribe.apply(s,[I].concat(typeof q=="function"?[q]:q))}(ue,Ut.API.events):p[oi]=Ut.API[oi]);var Br=p.getPageWidth=function(s){return(Jt[s=s||j].mediaBox.topRightX-Jt[s].mediaBox.bottomLeftX)/Ft},Gi=p.setPageWidth=function(s,v){Jt[s].mediaBox.topRightX=v*Ft+Jt[s].mediaBox.bottomLeftX},fr=p.getPageHeight=function(s){return(Jt[s=s||j].mediaBox.topRightY-Jt[s].mediaBox.bottomLeftY)/Ft},Ji=p.setPageHeight=function(s,v){Jt[s].mediaBox.topRightY=v*Ft+Jt[s].mediaBox.bottomLeftY};return p.internal={pdfEscape:on,getStyle:Sa,getFont:To,getFontSize:Pt,getCharSpace:Ro,getTextColor:Do,getLineHeight:ka,getLineHeightFactor:Ca,getLineWidth:ri,write:Kt,getHorizontalCoordinate:Gn,getVerticalCoordinate:nr,getCoordinateString:qo,getVerticalCoordinateString:hr,collections:{},newObject:De,newAdditionalObject:Gr,newObjectDeferred:Me,newObjectDeferredBegin:fn,getFilters:Hn,putStream:vn,events:ue,scaleFactor:Ft,pageSize:{getWidth:function(){return Br(j)},setWidth:function(s){Gi(j,s)},getHeight:function(){return fr(j)},setHeight:function(s){Ji(j,s)}},encryptionOptions:x,encryption:Xe,getEncryptor:Io,output:ei,getNumberOfPages:_o,pages:Nt,out:B,f2:bt,f3:k,getPageInfo:Aa,getPageInfoByObjId:Wt,getCurrentPageInfo:jo,getPDFVersion:C,Point:ai,Rectangle:Vi,Matrix:Rt,hasHotfix:xa},Object.defineProperty(p.internal.pageSize,"width",{get:function(){return Br(j)},set:function(s){Gi(j,s)},enumerable:!0,configurable:!0}),Object.defineProperty(p.internal.pageSize,"height",{get:function(){return fr(j)},set:function(s){Ji(j,s)},enumerable:!0,configurable:!0}),Ao.call(p,wt),ee="F1",wa(c,n),ue.publish("initialized"),p}xi.prototype.lsbFirstWord=function(i){return String.fromCharCode(i>>0&255,i>>8&255,i>>16&255,i>>24&255)},xi.prototype.toHexString=function(i){return i.split("").map(function(e){return("0"+(255&e.charCodeAt(0)).toString(16)).slice(-2)}).join("")},xi.prototype.hexToBytes=function(i){for(var e=[],n=0;n<i.length;n+=2)e.push(String.fromCharCode(parseInt(i.substr(n,2),16)));return e.join("")},xi.prototype.processOwnerPassword=function(i,e){return ys(bs(e).substr(0,5),i)},xi.prototype.encryptor=function(i,e){var n=bs(this.encryptionKey+String.fromCharCode(255&i,i>>8&255,i>>16&255,255&e,e>>8&255)).substr(0,10);return function(a){return ys(n,a)}},go.prototype.equals=function(i){var e,n="id,objectNumber,equals";if(!i||fe(i)!==fe(this))return!1;var a=0;for(e in this)if(!(n.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!i.hasOwnProperty(e)||this[e]!==i[e])return!1;a++}for(e in i)i.hasOwnProperty(e)&&n.indexOf(e)<0&&a--;return a===0},Ut.API={events:[]},Ut.version="3.0.0";var ke=Ut.API,_s=1,Wr=function(i){return i.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ni=function(i){return i.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Xt=function(i){return i.toFixed(2)},Ar=function(i){return i.toFixed(5)};ke.__acroform__={};var hn=function(i,e){i.prototype=Object.create(e.prototype),i.prototype.constructor=i},hc=function(i){return i*_s},Xn=function(i){var e=new Cc,n=Bt.internal.getHeight(i)||0,a=Bt.internal.getWidth(i)||0;return e.BBox=[0,0,Number(Xt(a)),Number(Xt(n))],e},sl=ke.__acroform__.setBit=function(i,e){if(i=i||0,e=e||0,isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return i|=1<<e},cl=ke.__acroform__.clearBit=function(i,e){if(i=i||0,e=e||0,isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return i&=~(1<<e)},ul=ke.__acroform__.getBit=function(i,e){if(isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return i&1<<e?1:0},je=ke.__acroform__.getBitForPdf=function(i,e){if(isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return ul(i,e-1)},Oe=ke.__acroform__.setBitForPdf=function(i,e){if(isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return sl(i,e-1)},Be=ke.__acroform__.clearBitForPdf=function(i,e){if(isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return cl(i,e-1)},ll=ke.__acroform__.calculateCoordinates=function(i,e){var n=e.internal.getHorizontalCoordinate,a=e.internal.getVerticalCoordinate,c=i[0],o=i[1],l=i[2],h=i[3],f={};return f.lowerLeft_X=n(c)||0,f.lowerLeft_Y=a(o+h)||0,f.upperRight_X=n(c+l)||0,f.upperRight_Y=a(o)||0,[Number(Xt(f.lowerLeft_X)),Number(Xt(f.lowerLeft_Y)),Number(Xt(f.upperRight_X)),Number(Xt(f.upperRight_Y))]},hl=function(i){if(i.appearanceStreamContent)return i.appearanceStreamContent;if(i.V||i.DV){var e=[],n=i._V||i.DV,a=ws(i,n),c=i.scope.internal.getFont(i.fontName,i.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(i.scope.__private__.encodeColorString(i.color)),e.push("/"+c+" "+Xt(a.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(a.text),e.push("ET"),e.push("Q"),e.push("EMC");var o=Xn(i);return o.scope=i.scope,o.stream=e.join(`
`),o}},ws=function(i,e){var n=i.fontSize===0?i.maxFontSize:i.fontSize,a={text:"",fontSize:""},c=(e=(e=e.substr(0,1)=="("?e.substr(1):e).substr(e.length-1)==")"?e.substr(0,e.length-1):e).split(" ");c=i.multiline?c.map(function(k){return k.split(`
`)}):c.map(function(k){return[k]});var o=n,l=Bt.internal.getHeight(i)||0;l=l<0?-l:l;var h=Bt.internal.getWidth(i)||0;h=h<0?-h:h;var f=function(k,F,H){if(k+1<c.length){var D=F+" "+c[k+1][0];return ao(D,i,H).width<=h-4}return!1};o++;t:for(;o>0;){e="",o--;var g,x,L=ao("3",i,o).height,S=i.multiline?l-o:(l-L)/2,p=S+=2,O=0,C=0,T=0;if(o<=0){e=`(...) Tj
`,e+="% Width of Text: "+ao(e,i,o=12).width+", FieldWidth:"+h+`
`;break}for(var _="",M=0,$=0;$<c.length;$++)if(c.hasOwnProperty($)){var st=!1;if(c[$].length!==1&&T!==c[$].length-1){if((L+2)*(M+2)+2>l)continue t;_+=c[$][T],st=!0,C=$,$--}else{_=(_+=c[$][T]+" ").substr(_.length-1)==" "?_.substr(0,_.length-1):_;var dt=parseInt($),Lt=f(dt,_,o),rt=$>=c.length-1;if(Lt&&!rt){_+=" ",T=0;continue}if(Lt||rt){if(rt)C=dt;else if(i.multiline&&(L+2)*(M+2)+2>l)continue t}else{if(!i.multiline||(L+2)*(M+2)+2>l)continue t;C=dt}}for(var G="",vt=O;vt<=C;vt++){var bt=c[vt];if(i.multiline){if(vt===C){G+=bt[T]+" ",T=(T+1)%bt.length;continue}if(vt===O){G+=bt[bt.length-1]+" ";continue}}G+=bt[0]+" "}switch(G=G.substr(G.length-1)==" "?G.substr(0,G.length-1):G,x=ao(G,i,o).width,i.textAlign){case"right":g=h-x-2;break;case"center":g=(h-x)/2;break;case"left":default:g=2}e+=Xt(g)+" "+Xt(p)+` Td
`,e+="("+Wr(G)+`) Tj
`,e+=-Xt(g)+` 0 Td
`,p=-(o+2),x=0,O=st?C:C+1,M++,_=""}break}return a.text=e,a.fontSize=o,a},ao=function(i,e,n){var a=e.scope.internal.getFont(e.fontName,e.fontStyle),c=e.scope.getStringUnitWidth(i,{font:a,fontSize:parseFloat(n),charSpace:0})*parseFloat(n);return{height:e.scope.getStringUnitWidth("3",{font:a,fontSize:parseFloat(n),charSpace:0})*parseFloat(n)*1.5,width:c}},fl={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},dl=function(i,e){var n={type:"reference",object:i};e.internal.getPageInfo(i.page).pageContext.annotations.find(function(a){return a.type===n.type&&a.object===n.object})===void 0&&e.internal.getPageInfo(i.page).pageContext.annotations.push(n)},pl=function(i,e){for(var n in i)if(i.hasOwnProperty(n)){var a=n,c=i[n];e.internal.newObjectDeferredBegin(c.objId,!0),fe(c)==="object"&&typeof c.putStream=="function"&&c.putStream(),delete i[a]}},gl=function(i,e){if(e.scope=i,i.internal!==void 0&&(i.internal.acroformPlugin===void 0||i.internal.acroformPlugin.isInitialized===!1)){if(Tn.FieldNum=0,i.internal.acroformPlugin=JSON.parse(JSON.stringify(fl)),i.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");_s=i.internal.scaleFactor,i.internal.acroformPlugin.acroFormDictionaryRoot=new Ic,i.internal.acroformPlugin.acroFormDictionaryRoot.scope=i,i.internal.acroformPlugin.acroFormDictionaryRoot._eventID=i.internal.events.subscribe("postPutResources",function(){(function(n){n.internal.events.unsubscribe(n.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete n.internal.acroformPlugin.acroFormDictionaryRoot._eventID,n.internal.acroformPlugin.printedOut=!0})(i)}),i.internal.events.subscribe("buildDocument",function(){(function(n){n.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var a=n.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var c in a)if(a.hasOwnProperty(c)){var o=a[c];o.objId=void 0,o.hasAnnotation&&dl(o,n)}})(i)}),i.internal.events.subscribe("putCatalog",function(){(function(n){if(n.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");n.internal.write("/AcroForm "+n.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(i)}),i.internal.events.subscribe("postPutPages",function(n){(function(a,c){var o=!a;for(var l in a||(c.internal.newObjectDeferredBegin(c.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),c.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),a=a||c.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(a.hasOwnProperty(l)){var h=a[l],f=[],g=h.Rect;if(h.Rect&&(h.Rect=ll(h.Rect,c)),c.internal.newObjectDeferredBegin(h.objId,!0),h.DA=Bt.createDefaultAppearanceStream(h),fe(h)==="object"&&typeof h.getKeyValueListForStream=="function"&&(f=h.getKeyValueListForStream()),h.Rect=g,h.hasAppearanceStream&&!h.appearanceStreamContent){var x=hl(h);f.push({key:"AP",value:"<</N "+x+">>"}),c.internal.acroformPlugin.xForms.push(x)}if(h.appearanceStreamContent){var L="";for(var S in h.appearanceStreamContent)if(h.appearanceStreamContent.hasOwnProperty(S)){var p=h.appearanceStreamContent[S];if(L+="/"+S+" ",L+="<<",Object.keys(p).length>=1||Array.isArray(p)){for(var l in p)if(p.hasOwnProperty(l)){var O=p[l];typeof O=="function"&&(O=O.call(c,h)),L+="/"+l+" "+O+" ",c.internal.acroformPlugin.xForms.indexOf(O)>=0||c.internal.acroformPlugin.xForms.push(O)}}else typeof(O=p)=="function"&&(O=O.call(c,h)),L+="/"+l+" "+O,c.internal.acroformPlugin.xForms.indexOf(O)>=0||c.internal.acroformPlugin.xForms.push(O);L+=">>"}f.push({key:"AP",value:`<<
`+L+">>"})}c.internal.putStream({additionalKeyValues:f,objectId:h.objId}),c.internal.out("endobj")}o&&pl(c.internal.acroformPlugin.xForms,c)})(n,i)}),i.internal.acroformPlugin.isInitialized=!0}},Fc=ke.__acroform__.arrayToPdfArray=function(i,e,n){var a=function(l){return l};if(Array.isArray(i)){for(var c="[",o=0;o<i.length;o++)switch(o!==0&&(c+=" "),fe(i[o])){case"boolean":case"number":case"object":c+=i[o].toString();break;case"string":i[o].substr(0,1)!=="/"?(e!==void 0&&n&&(a=n.internal.getEncryptor(e)),c+="("+Wr(a(i[o].toString()))+")"):c+=i[o].toString()}return c+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},fs=function(i,e,n){var a=function(c){return c};return e!==void 0&&n&&(a=n.internal.getEncryptor(e)),(i=i||"").toString(),i="("+Wr(a(i))+")"},$n=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(i){this._objId=i}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};$n.prototype.toString=function(){return this.objId+" 0 R"},$n.prototype.putStream=function(){var i=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:i,objectId:this.objId}),this.scope.internal.out("endobj")},$n.prototype.getKeyValueListForStream=function(){var i=[],e=Object.getOwnPropertyNames(this).filter(function(o){return o!="content"&&o!="appearanceStreamContent"&&o!="scope"&&o!="objId"&&o.substring(0,1)!="_"});for(var n in e)if(Object.getOwnPropertyDescriptor(this,e[n]).configurable===!1){var a=e[n],c=this[a];c&&(Array.isArray(c)?i.push({key:a,value:Fc(c,this.objId,this.scope)}):c instanceof $n?(c.scope=this.scope,i.push({key:a,value:c.objId+" 0 R"})):typeof c!="function"&&i.push({key:a,value:c}))}return i};var Cc=function(){$n.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var i,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(n){e=n}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(n){i=n.trim()},get:function(){return i||null}})};hn(Cc,$n);var Ic=function(){$n.call(this);var i,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(i){var n=function(a){return a};return this.scope&&(n=this.scope.internal.getEncryptor(this.objId)),"("+Wr(n(i))+")"}},set:function(n){i=n}})};hn(Ic,$n);var Tn=function i(){$n.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(_){if(isNaN(_))throw new Error('Invalid value "'+_+'" for attribute F supplied.');e=_}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!je(e,3)},set:function(_){_?this.F=Oe(e,3):this.F=Be(e,3)}});var n=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return n},set:function(_){if(isNaN(_))throw new Error('Invalid value "'+_+'" for attribute Ff supplied.');n=_}});var a=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(a.length!==0)return a},set:function(_){a=_!==void 0?_:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[0])?0:a[0]},set:function(_){a[0]=_}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[1])?0:a[1]},set:function(_){a[1]=_}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[2])?0:a[2]},set:function(_){a[2]=_}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[3])?0:a[3]},set:function(_){a[3]=_}});var c="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return c},set:function(_){switch(_){case"/Btn":case"/Tx":case"/Ch":case"/Sig":c=_;break;default:throw new Error('Invalid value "'+_+'" for attribute FT supplied.')}}});var o=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!o||o.length<1){if(this instanceof mo)return;o="FieldObject"+i.FieldNum++}var _=function(M){return M};return this.scope&&(_=this.scope.internal.getEncryptor(this.objId)),"("+Wr(_(o))+")"},set:function(_){o=_.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return o},set:function(_){o=_}});var l="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return l},set:function(_){l=_}});var h="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return h},set:function(_){h=_}});var f=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return f},set:function(_){f=_}});var g=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return g===void 0?50/_s:g},set:function(_){g=_}});var x="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return x},set:function(_){x=_}});var L="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!L||this instanceof mo||this instanceof Hr))return fs(L,this.objId,this.scope)},set:function(_){_=_.toString(),L=_}});var S=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(S)return this instanceof Ve?S:fs(S,this.objId,this.scope)},set:function(_){_=_.toString(),S=this instanceof Ve?_:_.substr(0,1)==="("?Ni(_.substr(1,_.length-2)):Ni(_)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof Ve?Ni(S.substr(1,S.length-1)):S},set:function(_){_=_.toString(),S=this instanceof Ve?"/"+_:_}});var p=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(p)return p},set:function(_){this.V=_}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(p)return this instanceof Ve?p:fs(p,this.objId,this.scope)},set:function(_){_=_.toString(),p=this instanceof Ve?_:_.substr(0,1)==="("?Ni(_.substr(1,_.length-2)):Ni(_)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof Ve?Ni(p.substr(1,p.length-1)):p},set:function(_){_=_.toString(),p=this instanceof Ve?"/"+_:_}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var O,C=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return C},set:function(_){_=!!_,C=_}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(O)return O},set:function(_){O=_}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,1)},set:function(_){_?this.Ff=Oe(this.Ff,1):this.Ff=Be(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,2)},set:function(_){_?this.Ff=Oe(this.Ff,2):this.Ff=Be(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,3)},set:function(_){_?this.Ff=Oe(this.Ff,3):this.Ff=Be(this.Ff,3)}});var T=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(T!==null)return T},set:function(_){if([0,1,2].indexOf(_)===-1)throw new Error('Invalid value "'+_+'" for attribute Q supplied.');T=_}}),Object.defineProperty(this,"textAlign",{get:function(){var _;switch(T){case 0:default:_="left";break;case 1:_="center";break;case 2:_="right"}return _},configurable:!0,enumerable:!0,set:function(_){switch(_){case"right":case 2:T=2;break;case"center":case 1:T=1;break;case"left":case 0:default:T=0}}})};hn(Tn,$n);var ki=function(){Tn.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var i=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return i},set:function(n){i=n}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return i},set:function(n){i=n}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return Fc(e,this.objId,this.scope)},set:function(n){var a,c;c=[],typeof(a=n)=="string"&&(c=function(o,l,h){h||(h=1);for(var f,g=[];f=l.exec(o);)g.push(f[h]);return g}(a,/\((.*?)\)/g)),e=c}}),this.getOptions=function(){return e},this.setOptions=function(n){e=n,this.sort&&e.sort()},this.addOption=function(n){n=(n=n||"").toString(),e.push(n),this.sort&&e.sort()},this.removeOption=function(n,a){for(a=a||!1,n=(n=n||"").toString();e.indexOf(n)!==-1&&(e.splice(e.indexOf(n),1),a!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,18)},set:function(n){n?this.Ff=Oe(this.Ff,18):this.Ff=Be(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,19)},set:function(n){this.combo===!0&&(n?this.Ff=Oe(this.Ff,19):this.Ff=Be(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,20)},set:function(n){n?(this.Ff=Oe(this.Ff,20),e.sort()):this.Ff=Be(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,22)},set:function(n){n?this.Ff=Oe(this.Ff,22):this.Ff=Be(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,23)},set:function(n){n?this.Ff=Oe(this.Ff,23):this.Ff=Be(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,27)},set:function(n){n?this.Ff=Oe(this.Ff,27):this.Ff=Be(this.Ff,27)}}),this.hasAppearanceStream=!1};hn(ki,Tn);var Fi=function(){ki.call(this),this.fontName="helvetica",this.combo=!1};hn(Fi,ki);var Ci=function(){Fi.call(this),this.combo=!0};hn(Ci,Fi);var uo=function(){Ci.call(this),this.edit=!0};hn(uo,Ci);var Ve=function(){Tn.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,15)},set:function(n){n?this.Ff=Oe(this.Ff,15):this.Ff=Be(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,16)},set:function(n){n?this.Ff=Oe(this.Ff,16):this.Ff=Be(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,17)},set:function(n){n?this.Ff=Oe(this.Ff,17):this.Ff=Be(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,26)},set:function(n){n?this.Ff=Oe(this.Ff,26):this.Ff=Be(this.Ff,26)}});var i,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var n=function(o){return o};if(this.scope&&(n=this.scope.internal.getEncryptor(this.objId)),Object.keys(e).length!==0){var a,c=[];for(a in c.push("<<"),e)c.push("/"+a+" ("+Wr(n(e[a]))+")");return c.push(">>"),c.join(`
`)}},set:function(n){fe(n)==="object"&&(e=n)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(n){typeof n=="string"&&(e.CA=n)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return i},set:function(n){i=n}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return i.substr(1,i.length-1)},set:function(n){i="/"+n}})};hn(Ve,Tn);var lo=function(){Ve.call(this),this.pushButton=!0};hn(lo,Ve);var Ii=function(){Ve.call(this),this.radio=!0,this.pushButton=!1;var i=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return i},set:function(e){i=e!==void 0?e:[]}})};hn(Ii,Ve);var mo=function(){var i,e;Tn.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return i},set:function(c){i=c}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(c){e=c}});var n,a={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var c=function(h){return h};this.scope&&(c=this.scope.internal.getEncryptor(this.objId));var o,l=[];for(o in l.push("<<"),a)l.push("/"+o+" ("+Wr(c(a[o]))+")");return l.push(">>"),l.join(`
`)},set:function(c){fe(c)==="object"&&(a=c)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return a.CA||""},set:function(c){typeof c=="string"&&(a.CA=c)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(c){n=c}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(c){n="/"+c}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Bt.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};hn(mo,Tn),Ii.prototype.setAppearance=function(i){if(!("createAppearanceStream"in i)||!("getCA"in i))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var n=this.Kids[e];n.appearanceStreamContent=i.createAppearanceStream(n.optionName),n.caption=i.getCA()}},Ii.prototype.createOption=function(i){var e=new mo;return e.Parent=this,e.optionName=i,this.Kids.push(e),ml.call(this.scope,e),e};var ho=function(){Ve.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Bt.CheckBox.createAppearanceStream()};hn(ho,Ve);var Hr=function(){Tn.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,13)},set:function(e){e?this.Ff=Oe(this.Ff,13):this.Ff=Be(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,21)},set:function(e){e?this.Ff=Oe(this.Ff,21):this.Ff=Be(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,23)},set:function(e){e?this.Ff=Oe(this.Ff,23):this.Ff=Be(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,24)},set:function(e){e?this.Ff=Oe(this.Ff,24):this.Ff=Be(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,25)},set:function(e){e?this.Ff=Oe(this.Ff,25):this.Ff=Be(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,26)},set:function(e){e?this.Ff=Oe(this.Ff,26):this.Ff=Be(this.Ff,26)}});var i=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return i},set:function(e){i=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return i},set:function(e){Number.isInteger(e)&&(i=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};hn(Hr,Tn);var fo=function(){Hr.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,14)},set:function(i){i?this.Ff=Oe(this.Ff,14):this.Ff=Be(this.Ff,14)}}),this.password=!0};hn(fo,Hr);var Bt={CheckBox:{createAppearanceStream:function(){return{N:{On:Bt.CheckBox.YesNormal},D:{On:Bt.CheckBox.YesPushDown,Off:Bt.CheckBox.OffPushDown}}},YesPushDown:function(i){var e=Xn(i);e.scope=i.scope;var n=[],a=i.scope.internal.getFont(i.fontName,i.fontStyle).id,c=i.scope.__private__.encodeColorString(i.color),o=ws(i,i.caption);return n.push("0.749023 g"),n.push("0 0 "+Xt(Bt.internal.getWidth(i))+" "+Xt(Bt.internal.getHeight(i))+" re"),n.push("f"),n.push("BMC"),n.push("q"),n.push("0 0 1 rg"),n.push("/"+a+" "+Xt(o.fontSize)+" Tf "+c),n.push("BT"),n.push(o.text),n.push("ET"),n.push("Q"),n.push("EMC"),e.stream=n.join(`
`),e},YesNormal:function(i){var e=Xn(i);e.scope=i.scope;var n=i.scope.internal.getFont(i.fontName,i.fontStyle).id,a=i.scope.__private__.encodeColorString(i.color),c=[],o=Bt.internal.getHeight(i),l=Bt.internal.getWidth(i),h=ws(i,i.caption);return c.push("1 g"),c.push("0 0 "+Xt(l)+" "+Xt(o)+" re"),c.push("f"),c.push("q"),c.push("0 0 1 rg"),c.push("0 0 "+Xt(l-1)+" "+Xt(o-1)+" re"),c.push("W"),c.push("n"),c.push("0 g"),c.push("BT"),c.push("/"+n+" "+Xt(h.fontSize)+" Tf "+a),c.push(h.text),c.push("ET"),c.push("Q"),e.stream=c.join(`
`),e},OffPushDown:function(i){var e=Xn(i);e.scope=i.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+Xt(Bt.internal.getWidth(i))+" "+Xt(Bt.internal.getHeight(i))+" re"),n.push("f"),e.stream=n.join(`
`),e}},RadioButton:{Circle:{createAppearanceStream:function(i){var e={D:{Off:Bt.RadioButton.Circle.OffPushDown},N:{}};return e.N[i]=Bt.RadioButton.Circle.YesNormal,e.D[i]=Bt.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(i){var e=Xn(i);e.scope=i.scope;var n=[],a=Bt.internal.getWidth(i)<=Bt.internal.getHeight(i)?Bt.internal.getWidth(i)/4:Bt.internal.getHeight(i)/4;a=Number((.9*a).toFixed(5));var c=Bt.internal.Bezier_C,o=Number((a*c).toFixed(5));return n.push("q"),n.push("1 0 0 1 "+Ar(Bt.internal.getWidth(i)/2)+" "+Ar(Bt.internal.getHeight(i)/2)+" cm"),n.push(a+" 0 m"),n.push(a+" "+o+" "+o+" "+a+" 0 "+a+" c"),n.push("-"+o+" "+a+" -"+a+" "+o+" -"+a+" 0 c"),n.push("-"+a+" -"+o+" -"+o+" -"+a+" 0 -"+a+" c"),n.push(o+" -"+a+" "+a+" -"+o+" "+a+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e},YesPushDown:function(i){var e=Xn(i);e.scope=i.scope;var n=[],a=Bt.internal.getWidth(i)<=Bt.internal.getHeight(i)?Bt.internal.getWidth(i)/4:Bt.internal.getHeight(i)/4;a=Number((.9*a).toFixed(5));var c=Number((2*a).toFixed(5)),o=Number((c*Bt.internal.Bezier_C).toFixed(5)),l=Number((a*Bt.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+Ar(Bt.internal.getWidth(i)/2)+" "+Ar(Bt.internal.getHeight(i)/2)+" cm"),n.push(c+" 0 m"),n.push(c+" "+o+" "+o+" "+c+" 0 "+c+" c"),n.push("-"+o+" "+c+" -"+c+" "+o+" -"+c+" 0 c"),n.push("-"+c+" -"+o+" -"+o+" -"+c+" 0 -"+c+" c"),n.push(o+" -"+c+" "+c+" -"+o+" "+c+" 0 c"),n.push("f"),n.push("Q"),n.push("0 g"),n.push("q"),n.push("1 0 0 1 "+Ar(Bt.internal.getWidth(i)/2)+" "+Ar(Bt.internal.getHeight(i)/2)+" cm"),n.push(a+" 0 m"),n.push(a+" "+l+" "+l+" "+a+" 0 "+a+" c"),n.push("-"+l+" "+a+" -"+a+" "+l+" -"+a+" 0 c"),n.push("-"+a+" -"+l+" -"+l+" -"+a+" 0 -"+a+" c"),n.push(l+" -"+a+" "+a+" -"+l+" "+a+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e},OffPushDown:function(i){var e=Xn(i);e.scope=i.scope;var n=[],a=Bt.internal.getWidth(i)<=Bt.internal.getHeight(i)?Bt.internal.getWidth(i)/4:Bt.internal.getHeight(i)/4;a=Number((.9*a).toFixed(5));var c=Number((2*a).toFixed(5)),o=Number((c*Bt.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+Ar(Bt.internal.getWidth(i)/2)+" "+Ar(Bt.internal.getHeight(i)/2)+" cm"),n.push(c+" 0 m"),n.push(c+" "+o+" "+o+" "+c+" 0 "+c+" c"),n.push("-"+o+" "+c+" -"+c+" "+o+" -"+c+" 0 c"),n.push("-"+c+" -"+o+" -"+o+" -"+c+" 0 -"+c+" c"),n.push(o+" -"+c+" "+c+" -"+o+" "+c+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join(`
`),e}},Cross:{createAppearanceStream:function(i){var e={D:{Off:Bt.RadioButton.Cross.OffPushDown},N:{}};return e.N[i]=Bt.RadioButton.Cross.YesNormal,e.D[i]=Bt.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(i){var e=Xn(i);e.scope=i.scope;var n=[],a=Bt.internal.calculateCross(i);return n.push("q"),n.push("1 1 "+Xt(Bt.internal.getWidth(i)-2)+" "+Xt(Bt.internal.getHeight(i)-2)+" re"),n.push("W"),n.push("n"),n.push(Xt(a.x1.x)+" "+Xt(a.x1.y)+" m"),n.push(Xt(a.x2.x)+" "+Xt(a.x2.y)+" l"),n.push(Xt(a.x4.x)+" "+Xt(a.x4.y)+" m"),n.push(Xt(a.x3.x)+" "+Xt(a.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join(`
`),e},YesPushDown:function(i){var e=Xn(i);e.scope=i.scope;var n=Bt.internal.calculateCross(i),a=[];return a.push("0.749023 g"),a.push("0 0 "+Xt(Bt.internal.getWidth(i))+" "+Xt(Bt.internal.getHeight(i))+" re"),a.push("f"),a.push("q"),a.push("1 1 "+Xt(Bt.internal.getWidth(i)-2)+" "+Xt(Bt.internal.getHeight(i)-2)+" re"),a.push("W"),a.push("n"),a.push(Xt(n.x1.x)+" "+Xt(n.x1.y)+" m"),a.push(Xt(n.x2.x)+" "+Xt(n.x2.y)+" l"),a.push(Xt(n.x4.x)+" "+Xt(n.x4.y)+" m"),a.push(Xt(n.x3.x)+" "+Xt(n.x3.y)+" l"),a.push("s"),a.push("Q"),e.stream=a.join(`
`),e},OffPushDown:function(i){var e=Xn(i);e.scope=i.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+Xt(Bt.internal.getWidth(i))+" "+Xt(Bt.internal.getHeight(i))+" re"),n.push("f"),e.stream=n.join(`
`),e}}},createDefaultAppearanceStream:function(i){var e=i.scope.internal.getFont(i.fontName,i.fontStyle).id,n=i.scope.__private__.encodeColorString(i.color);return"/"+e+" "+i.fontSize+" Tf "+n}};Bt.internal={Bezier_C:.551915024494,calculateCross:function(i){var e=Bt.internal.getWidth(i),n=Bt.internal.getHeight(i),a=Math.min(e,n);return{x1:{x:(e-a)/2,y:(n-a)/2+a},x2:{x:(e-a)/2+a,y:(n-a)/2},x3:{x:(e-a)/2,y:(n-a)/2},x4:{x:(e-a)/2+a,y:(n-a)/2+a}}}},Bt.internal.getWidth=function(i){var e=0;return fe(i)==="object"&&(e=hc(i.Rect[2])),e},Bt.internal.getHeight=function(i){var e=0;return fe(i)==="object"&&(e=hc(i.Rect[3])),e};var ml=ke.addField=function(i){if(gl(this,i),!(i instanceof Tn))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=i).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),i.page=i.scope.internal.getCurrentPageInfo().pageNumber,this};ke.AcroFormChoiceField=ki,ke.AcroFormListBox=Fi,ke.AcroFormComboBox=Ci,ke.AcroFormEditBox=uo,ke.AcroFormButton=Ve,ke.AcroFormPushButton=lo,ke.AcroFormRadioButton=Ii,ke.AcroFormCheckBox=ho,ke.AcroFormTextField=Hr,ke.AcroFormPasswordField=fo,ke.AcroFormAppearance=Bt,ke.AcroForm={ChoiceField:ki,ListBox:Fi,ComboBox:Ci,EditBox:uo,Button:Ve,PushButton:lo,RadioButton:Ii,CheckBox:ho,TextField:Hr,PasswordField:fo,Appearance:Bt},Ut.AcroForm={ChoiceField:ki,ListBox:Fi,ComboBox:Ci,EditBox:uo,Button:Ve,PushButton:lo,RadioButton:Ii,CheckBox:ho,TextField:Hr,PasswordField:fo,Appearance:Bt};function jc(i){return i.reduce(function(e,n,a){return e[n]=a,e},{})}(function(i){i.__addimage__={};var e="UNKNOWN",n={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},a=i.__addimage__.getImageFileTypeByImageData=function(k,F){var H,D,ct,ot,mt,tt=e;if((F=F||e)==="RGBA"||k.data!==void 0&&k.data instanceof Uint8ClampedArray&&"height"in k&&"width"in k)return"RGBA";if(Lt(k))for(mt in n)for(ct=n[mt],H=0;H<ct.length;H+=1){for(ot=!0,D=0;D<ct[H].length;D+=1)if(ct[H][D]!==void 0&&ct[H][D]!==k[D]){ot=!1;break}if(ot===!0){tt=mt;break}}else for(mt in n)for(ct=n[mt],H=0;H<ct.length;H+=1){for(ot=!0,D=0;D<ct[H].length;D+=1)if(ct[H][D]!==void 0&&ct[H][D]!==k.charCodeAt(D)){ot=!1;break}if(ot===!0){tt=mt;break}}return tt===e&&F!==e&&(tt=F),tt},c=function k(F){for(var H=this.internal.write,D=this.internal.putStream,ct=(0,this.internal.getFilters)();ct.indexOf("FlateEncode")!==-1;)ct.splice(ct.indexOf("FlateEncode"),1);F.objectId=this.internal.newObject();var ot=[];if(ot.push({key:"Type",value:"/XObject"}),ot.push({key:"Subtype",value:"/Image"}),ot.push({key:"Width",value:F.width}),ot.push({key:"Height",value:F.height}),F.colorSpace===T.INDEXED?ot.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(F.palette.length/3-1)+" "+("sMask"in F&&F.sMask!==void 0?F.objectId+2:F.objectId+1)+" 0 R]"}):(ot.push({key:"ColorSpace",value:"/"+F.colorSpace}),F.colorSpace===T.DEVICE_CMYK&&ot.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),ot.push({key:"BitsPerComponent",value:F.bitsPerComponent}),"decodeParameters"in F&&F.decodeParameters!==void 0&&ot.push({key:"DecodeParms",value:"<<"+F.decodeParameters+">>"}),"transparency"in F&&Array.isArray(F.transparency)){for(var mt="",tt=0,pt=F.transparency.length;tt<pt;tt++)mt+=F.transparency[tt]+" "+F.transparency[tt]+" ";ot.push({key:"Mask",value:"["+mt+"]"})}F.sMask!==void 0&&ot.push({key:"SMask",value:F.objectId+1+" 0 R"});var ft=F.filter!==void 0?["/"+F.filter]:void 0;if(D({data:F.data,additionalKeyValues:ot,alreadyAppliedFilters:ft,objectId:F.objectId}),H("endobj"),"sMask"in F&&F.sMask!==void 0){var Et="/Predictor "+F.predictor+" /Colors 1 /BitsPerComponent "+F.bitsPerComponent+" /Columns "+F.width,w={width:F.width,height:F.height,colorSpace:"DeviceGray",bitsPerComponent:F.bitsPerComponent,decodeParameters:Et,data:F.sMask};"filter"in F&&(w.filter=F.filter),k.call(this,w)}if(F.colorSpace===T.INDEXED){var j=this.internal.newObject();D({data:G(new Uint8Array(F.palette)),objectId:j}),H("endobj")}},o=function(){var k=this.internal.collections.addImage_images;for(var F in k)c.call(this,k[F])},l=function(){var k,F=this.internal.collections.addImage_images,H=this.internal.write;for(var D in F)H("/I"+(k=F[D]).index,k.objectId,"0","R")},h=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",o),this.internal.events.subscribe("putXobjectDict",l))},f=function(){var k=this.internal.collections.addImage_images;return h.call(this),k},g=function(){return Object.keys(this.internal.collections.addImage_images).length},x=function(k){return typeof i["process"+k.toUpperCase()]=="function"},L=function(k){return fe(k)==="object"&&k.nodeType===1},S=function(k,F){if(k.nodeName==="IMG"&&k.hasAttribute("src")){var H=""+k.getAttribute("src");if(H.indexOf("data:image/")===0)return da(unescape(H).split("base64,").pop());var D=i.loadFile(H,!0);if(D!==void 0)return D}if(k.nodeName==="CANVAS"){if(k.width===0||k.height===0)throw new Error("Given canvas must have data. Canvas width: "+k.width+", height: "+k.height);var ct;switch(F){case"PNG":ct="image/png";break;case"WEBP":ct="image/webp";break;case"JPEG":case"JPG":default:ct="image/jpeg"}return da(k.toDataURL(ct,1).split("base64,").pop())}},p=function(k){var F=this.internal.collections.addImage_images;if(F){for(var H in F)if(k===F[H].alias)return F[H]}},O=function(k,F,H){return k||F||(k=-96,F=-96),k<0&&(k=-1*H.width*72/k/this.internal.scaleFactor),F<0&&(F=-1*H.height*72/F/this.internal.scaleFactor),k===0&&(k=F*H.width/H.height),F===0&&(F=k*H.height/H.width),[k,F]},C=function(k,F,H,D,ct,ot){var mt=O.call(this,H,D,ct),tt=this.internal.getCoordinateString,pt=this.internal.getVerticalCoordinateString,ft=f.call(this);if(H=mt[0],D=mt[1],ft[ct.index]=ct,ot){ot*=Math.PI/180;var Et=Math.cos(ot),w=Math.sin(ot),j=function(V){return V.toFixed(4)},E=[j(Et),j(w),j(-1*w),j(Et),0,0,"cm"]}this.internal.write("q"),ot?(this.internal.write([1,"0","0",1,tt(k),pt(F+D),"cm"].join(" ")),this.internal.write(E.join(" ")),this.internal.write([tt(H),"0","0",tt(D),"0","0","cm"].join(" "))):this.internal.write([tt(H),"0","0",tt(D),tt(k),pt(F+D),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+ct.index+" Do"),this.internal.write("Q")},T=i.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};i.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var _=i.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},M=i.__addimage__.sHashCode=function(k){var F,H,D=0;if(typeof k=="string")for(H=k.length,F=0;F<H;F++)D=(D<<5)-D+k.charCodeAt(F),D|=0;else if(Lt(k))for(H=k.byteLength/2,F=0;F<H;F++)D=(D<<5)-D+k[F],D|=0;return D},$=i.__addimage__.validateStringAsBase64=function(k){(k=k||"").toString().trim();var F=!0;return k.length===0&&(F=!1),k.length%4!=0&&(F=!1),/^[A-Za-z0-9+/]+$/.test(k.substr(0,k.length-2))===!1&&(F=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(k.substr(-2))===!1&&(F=!1),F},st=i.__addimage__.extractImageFromDataUrl=function(k){var F=(k=k||"").split("base64,"),H=null;if(F.length===2){var D=/^data:(\w*\/\w*);*(charset=(?!charset=)[\w=-]*)*;*$/.exec(F[0]);Array.isArray(D)&&(H={mimeType:D[1],charset:D[2],data:F[1]})}return H},dt=i.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};i.__addimage__.isArrayBuffer=function(k){return dt()&&k instanceof ArrayBuffer};var Lt=i.__addimage__.isArrayBufferView=function(k){return dt()&&typeof Uint32Array<"u"&&(k instanceof Int8Array||k instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&k instanceof Uint8ClampedArray||k instanceof Int16Array||k instanceof Uint16Array||k instanceof Int32Array||k instanceof Uint32Array||k instanceof Float32Array||k instanceof Float64Array)},rt=i.__addimage__.binaryStringToUint8Array=function(k){for(var F=k.length,H=new Uint8Array(F),D=0;D<F;D++)H[D]=k.charCodeAt(D);return H},G=i.__addimage__.arrayBufferToBinaryString=function(k){for(var F="",H=Lt(k)?k:new Uint8Array(k),D=0;D<H.length;D+=8192)F+=String.fromCharCode.apply(null,H.subarray(D,D+8192));return F};i.addImage=function(){var k,F,H,D,ct,ot,mt,tt,pt;if(typeof arguments[1]=="number"?(F=e,H=arguments[1],D=arguments[2],ct=arguments[3],ot=arguments[4],mt=arguments[5],tt=arguments[6],pt=arguments[7]):(F=arguments[1],H=arguments[2],D=arguments[3],ct=arguments[4],ot=arguments[5],mt=arguments[6],tt=arguments[7],pt=arguments[8]),fe(k=arguments[0])==="object"&&!L(k)&&"imageData"in k){var ft=k;k=ft.imageData,F=ft.format||F||e,H=ft.x||H||0,D=ft.y||D||0,ct=ft.w||ft.width||ct,ot=ft.h||ft.height||ot,mt=ft.alias||mt,tt=ft.compression||tt,pt=ft.rotation||ft.angle||pt}var Et=this.internal.getFilters();if(tt===void 0&&Et.indexOf("FlateEncode")!==-1&&(tt="SLOW"),isNaN(H)||isNaN(D))throw new Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var w=vt.call(this,k,F,mt,tt);return C.call(this,H,D,ct,ot,w,pt),this};var vt=function(k,F,H,D){var ct,ot,mt;if(typeof k=="string"&&a(k)===e){k=unescape(k);var tt=bt(k,!1);(tt!==""||(tt=i.loadFile(k,!0))!==void 0)&&(k=tt)}if(L(k)&&(k=S(k,F)),F=a(k,F),!x(F))throw new Error("addImage does not support files of type '"+F+"', please ensure that a plugin for '"+F+"' support is added.");if(((mt=H)==null||mt.length===0)&&(H=function(pt){return typeof pt=="string"||Lt(pt)?M(pt):Lt(pt.data)?M(pt.data):null}(k)),(ct=p.call(this,H))||(dt()&&(k instanceof Uint8Array||F==="RGBA"||(ot=k,k=rt(k))),ct=this["process"+F.toUpperCase()](k,g.call(this),H,function(pt){return pt&&typeof pt=="string"&&(pt=pt.toUpperCase()),pt in i.image_compression?pt:_.NONE}(D),ot)),!ct)throw new Error("An unknown error occurred whilst processing the image.");return ct},bt=i.__addimage__.convertBase64ToBinaryString=function(k,F){var H;F=typeof F!="boolean"||F;var D,ct="";if(typeof k=="string"){D=(H=st(k))!==null?H.data:k;try{ct=da(D)}catch(ot){if(F)throw $(D)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+ot.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return ct};i.getImageProperties=function(k){var F,H,D="";if(L(k)&&(k=S(k)),typeof k=="string"&&a(k)===e&&((D=bt(k,!1))===""&&(D=i.loadFile(k)||""),k=D),H=a(k),!x(H))throw new Error("addImage does not support files of type '"+H+"', please ensure that a plugin for '"+H+"' support is added.");if(!dt()||k instanceof Uint8Array||(k=rt(k)),!(F=this["process"+H.toUpperCase()](k)))throw new Error("An unknown error occurred whilst processing the image");return F.fileType=H,F}})(Ut.API),function(i){var e=function(n){if(n!==void 0&&n!="")return!0};Ut.API.events.push(["addPage",function(n){this.internal.getPageInfo(n.pageNumber).pageContext.annotations=[]}]),i.events.push(["putPage",function(n){for(var a,c,o,l=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString,f=this.internal.getPageInfoByObjId(n.objId),g=n.pageContext.annotations,x=!1,L=0;L<g.length&&!x;L++)switch((a=g[L]).type){case"link":(e(a.options.url)||e(a.options.pageNumber))&&(x=!0);break;case"reference":case"text":case"freetext":x=!0}if(x!=0){this.internal.write("/Annots [");for(var S=0;S<g.length;S++){a=g[S];var p=this.internal.pdfEscape,O=this.internal.getEncryptor(n.objId);switch(a.type){case"reference":this.internal.write(" "+a.object.objId+" 0 R ");break;case"text":var C=this.internal.newAdditionalObject(),T=this.internal.newAdditionalObject(),_=this.internal.getEncryptor(C.objId),M=a.title||"Note";o="<</Type /Annot /Subtype /Text "+(c="/Rect ["+l(a.bounds.x)+" "+h(a.bounds.y+a.bounds.h)+" "+l(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y)+"] ")+"/Contents ("+p(_(a.contents))+")",o+=" /Popup "+T.objId+" 0 R",o+=" /P "+f.objId+" 0 R",o+=" /T ("+p(_(M))+") >>",C.content=o;var $=C.objId+" 0 R";o="<</Type /Annot /Subtype /Popup "+(c="/Rect ["+l(a.bounds.x+30)+" "+h(a.bounds.y+a.bounds.h)+" "+l(a.bounds.x+a.bounds.w+30)+" "+h(a.bounds.y)+"] ")+" /Parent "+$,a.open&&(o+=" /Open true"),o+=" >>",T.content=o,this.internal.write(C.objId,"0 R",T.objId,"0 R");break;case"freetext":c="/Rect ["+l(a.bounds.x)+" "+h(a.bounds.y)+" "+l(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y+a.bounds.h)+"] ";var st=a.color||"#000000";o="<</Type /Annot /Subtype /FreeText "+c+"/Contents ("+p(O(a.contents))+")",o+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+st+")",o+=" /Border [0 0 0]",o+=" >>",this.internal.write(o);break;case"link":if(a.options.name){var dt=this.annotations._nameMap[a.options.name];a.options.pageNumber=dt.page,a.options.top=dt.y}else a.options.top||(a.options.top=0);if(c="/Rect ["+a.finalBounds.x+" "+a.finalBounds.y+" "+a.finalBounds.w+" "+a.finalBounds.h+"] ",o="",a.options.url)o="<</Type /Annot /Subtype /Link "+c+"/Border [0 0 0] /A <</S /URI /URI ("+p(O(a.options.url))+") >>";else if(a.options.pageNumber)switch(o="<</Type /Annot /Subtype /Link "+c+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(a.options.pageNumber).objId+" 0 R",a.options.magFactor=a.options.magFactor||"XYZ",a.options.magFactor){case"Fit":o+=" /Fit]";break;case"FitH":o+=" /FitH "+a.options.top+"]";break;case"FitV":a.options.left=a.options.left||0,o+=" /FitV "+a.options.left+"]";break;case"XYZ":default:var Lt=h(a.options.top);a.options.left=a.options.left||0,a.options.zoom===void 0&&(a.options.zoom=0),o+=" /XYZ "+a.options.left+" "+Lt+" "+a.options.zoom+"]"}o!=""&&(o+=" >>",this.internal.write(o))}}this.internal.write("]")}}]),i.createAnnotation=function(n){var a=this.internal.getCurrentPageInfo();switch(n.type){case"link":this.link(n.bounds.x,n.bounds.y,n.bounds.w,n.bounds.h,n);break;case"text":case"freetext":a.pageContext.annotations.push(n)}},i.link=function(n,a,c,o,l){var h=this.internal.getCurrentPageInfo(),f=this.internal.getCoordinateString,g=this.internal.getVerticalCoordinateString;h.pageContext.annotations.push({finalBounds:{x:f(n),y:g(a),w:f(n+c),h:g(a+o)},options:l,type:"link"})},i.textWithLink=function(n,a,c,o){var l,h,f=this.getTextWidth(n),g=this.internal.getLineHeight()/this.internal.scaleFactor;if(o.maxWidth!==void 0){h=o.maxWidth;var x=this.splitTextToSize(n,h).length;l=Math.ceil(g*x)}else h=f,l=g;return this.text(n,a,c,o),c+=.2*g,o.align==="center"&&(a-=f/2),o.align==="right"&&(a-=f),this.link(a,c-g,h,l,o),f},i.getTextWidth=function(n){var a=this.internal.getFontSize();return this.getStringUnitWidth(n)*a/this.internal.scaleFactor}}(Ut.API),function(i){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},n={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},a={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},c=[1570,1571,1573,1575];i.__arabicParser__={};var o=i.__arabicParser__.isInArabicSubstitutionA=function(C){return e[C.charCodeAt(0)]!==void 0},l=i.__arabicParser__.isArabicLetter=function(C){return typeof C=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(C)},h=i.__arabicParser__.isArabicEndLetter=function(C){return l(C)&&o(C)&&e[C.charCodeAt(0)].length<=2},f=i.__arabicParser__.isArabicAlfLetter=function(C){return l(C)&&c.indexOf(C.charCodeAt(0))>=0};i.__arabicParser__.arabicLetterHasIsolatedForm=function(C){return l(C)&&o(C)&&e[C.charCodeAt(0)].length>=1};var g=i.__arabicParser__.arabicLetterHasFinalForm=function(C){return l(C)&&o(C)&&e[C.charCodeAt(0)].length>=2};i.__arabicParser__.arabicLetterHasInitialForm=function(C){return l(C)&&o(C)&&e[C.charCodeAt(0)].length>=3};var x=i.__arabicParser__.arabicLetterHasMedialForm=function(C){return l(C)&&o(C)&&e[C.charCodeAt(0)].length==4},L=i.__arabicParser__.resolveLigatures=function(C){var T=0,_=n,M="",$=0;for(T=0;T<C.length;T+=1)_[C.charCodeAt(T)]!==void 0?($++,typeof(_=_[C.charCodeAt(T)])=="number"&&(M+=String.fromCharCode(_),_=n,$=0),T===C.length-1&&(_=n,M+=C.charAt(T-($-1)),T-=$-1,$=0)):(_=n,M+=C.charAt(T-$),T-=$,$=0);return M};i.__arabicParser__.isArabicDiacritic=function(C){return C!==void 0&&a[C.charCodeAt(0)]!==void 0};var S=i.__arabicParser__.getCorrectForm=function(C,T,_){return l(C)?o(C)===!1?-1:!g(C)||!l(T)&&!l(_)||!l(_)&&h(T)||h(C)&&!l(T)||h(C)&&f(T)||h(C)&&h(T)?0:x(C)&&l(T)&&!h(T)&&l(_)&&g(_)?3:h(C)||!l(_)?1:2:-1},p=function(C){var T=0,_=0,M=0,$="",st="",dt="",Lt=(C=C||"").split("\\s+"),rt=[];for(T=0;T<Lt.length;T+=1){for(rt.push(""),_=0;_<Lt[T].length;_+=1)$=Lt[T][_],st=Lt[T][_-1],dt=Lt[T][_+1],l($)?(M=S($,st,dt),rt[T]+=M!==-1?String.fromCharCode(e[$.charCodeAt(0)][M]):$):rt[T]+=$;rt[T]=L(rt[T])}return rt.join(" ")},O=i.__arabicParser__.processArabic=i.processArabic=function(){var C,T=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,_=[];if(Array.isArray(T)){var M=0;for(_=[],M=0;M<T.length;M+=1)Array.isArray(T[M])?_.push([p(T[M][0]),T[M][1],T[M][2]]):_.push([p(T[M])]);C=_}else C=p(T);return typeof arguments[0]=="string"?C:(arguments[0].text=C,arguments[0])};i.events.push(["preProcessText",O])}(Ut.API),Ut.API.autoPrint=function(i){var e;switch((i=i||{}).variant=i.variant||"non-conform",i.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},function(i){var e=function(){var n=void 0;Object.defineProperty(this,"pdf",{get:function(){return n},set:function(h){n=h}});var a=150;Object.defineProperty(this,"width",{get:function(){return a},set:function(h){a=isNaN(h)||Number.isInteger(h)===!1||h<0?150:h,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=a+1)}});var c=300;Object.defineProperty(this,"height",{get:function(){return c},set:function(h){c=isNaN(h)||Number.isInteger(h)===!1||h<0?300:h,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=c+1)}});var o=[];Object.defineProperty(this,"childNodes",{get:function(){return o},set:function(h){o=h}});var l={};Object.defineProperty(this,"style",{get:function(){return l},set:function(h){l=h}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(n,a){var c;if((n=n||"2d")!=="2d")return null;for(c in a)this.pdf.context2d.hasOwnProperty(c)&&(this.pdf.context2d[c]=a[c]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},i.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(Ut.API),function(i){var e={left:0,top:0,bottom:0,right:0},n=!1,a=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),c.call(this))},c=function(){this.internal.__cell__.lastCell=new o,this.internal.__cell__.pages=1},o=function(){var f=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return f},set:function(C){f=C}});var g=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return g},set:function(C){g=C}});var x=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return x},set:function(C){x=C}});var L=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return L},set:function(C){L=C}});var S=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return S},set:function(C){S=C}});var p=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return p},set:function(C){p=C}});var O=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return O},set:function(C){O=C}}),this};o.prototype.clone=function(){return new o(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},o.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},i.setHeaderFunction=function(f){return a.call(this),this.internal.__cell__.headerFunction=typeof f=="function"?f:void 0,this},i.getTextDimensions=function(f,g){a.call(this);var x=(g=g||{}).fontSize||this.getFontSize(),L=g.font||this.getFont(),S=g.scaleFactor||this.internal.scaleFactor,p=0,O=0,C=0,T=this;if(!Array.isArray(f)&&typeof f!="string"){if(typeof f!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");f=String(f)}var _=g.maxWidth;_>0?typeof f=="string"?f=this.splitTextToSize(f,_):Object.prototype.toString.call(f)==="[object Array]"&&(f=f.reduce(function($,st){return $.concat(T.splitTextToSize(st,_))},[])):f=Array.isArray(f)?f:[f];for(var M=0;M<f.length;M++)p<(C=this.getStringUnitWidth(f[M],{font:L})*x)&&(p=C);return p!==0&&(O=f.length),{w:p/=S,h:Math.max((O*x*this.getLineHeightFactor()-x*(this.getLineHeightFactor()-1))/S,0)}},i.cellAddPage=function(){a.call(this),this.addPage();var f=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new o(f.left,f.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var l=i.cell=function(){var f;f=arguments[0]instanceof o?arguments[0]:new o(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),a.call(this);var g=this.internal.__cell__.lastCell,x=this.internal.__cell__.padding,L=this.internal.__cell__.margins||e,S=this.internal.__cell__.tableHeaderRow,p=this.internal.__cell__.printHeaders;return g.lineNumber!==void 0&&(g.lineNumber===f.lineNumber?(f.x=(g.x||0)+(g.width||0),f.y=g.y||0):g.y+g.height+f.height+L.bottom>this.getPageHeight()?(this.cellAddPage(),f.y=L.top,p&&S&&(this.printHeaderRow(f.lineNumber,!0),f.y+=S[0].height)):f.y=g.y+g.height||f.y),f.text[0]!==void 0&&(this.rect(f.x,f.y,f.width,f.height,n===!0?"FD":void 0),f.align==="right"?this.text(f.text,f.x+f.width-x,f.y+x,{align:"right",baseline:"top"}):f.align==="center"?this.text(f.text,f.x+f.width/2,f.y+x,{align:"center",baseline:"top",maxWidth:f.width-x-x}):this.text(f.text,f.x+x,f.y+x,{align:"left",baseline:"top",maxWidth:f.width-x-x})),this.internal.__cell__.lastCell=f,this};i.table=function(f,g,x,L,S){if(a.call(this),!x)throw new Error("No data for PDF table.");var p,O,C,T,_=[],M=[],$=[],st={},dt={},Lt=[],rt=[],G=(S=S||{}).autoSize||!1,vt=S.printHeaders!==!1,bt=S.css&&S.css["font-size"]!==void 0?16*S.css["font-size"]:S.fontSize||12,k=S.margins||Object.assign({width:this.getPageWidth()},e),F=typeof S.padding=="number"?S.padding:3,H=S.headerBackgroundColor||"#c8c8c8",D=S.headerTextColor||"#000";if(c.call(this),this.internal.__cell__.printHeaders=vt,this.internal.__cell__.margins=k,this.internal.__cell__.table_font_size=bt,this.internal.__cell__.padding=F,this.internal.__cell__.headerBackgroundColor=H,this.internal.__cell__.headerTextColor=D,this.setFontSize(bt),L==null)M=_=Object.keys(x[0]),$=_.map(function(){return"left"});else if(Array.isArray(L)&&fe(L[0])==="object")for(_=L.map(function(ft){return ft.name}),M=L.map(function(ft){return ft.prompt||ft.name||""}),$=L.map(function(ft){return ft.align||"left"}),p=0;p<L.length;p+=1)dt[L[p].name]=L[p].width*(19.049976/25.4);else Array.isArray(L)&&typeof L[0]=="string"&&(M=_=L,$=_.map(function(){return"left"}));if(G||Array.isArray(L)&&typeof L[0]=="string")for(p=0;p<_.length;p+=1){for(st[T=_[p]]=x.map(function(ft){return ft[T]}),this.setFont(void 0,"bold"),Lt.push(this.getTextDimensions(M[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),O=st[T],this.setFont(void 0,"normal"),C=0;C<O.length;C+=1)Lt.push(this.getTextDimensions(O[C],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);dt[T]=Math.max.apply(null,Lt)+F+F,Lt=[]}if(vt){var ct={};for(p=0;p<_.length;p+=1)ct[_[p]]={},ct[_[p]].text=M[p],ct[_[p]].align=$[p];var ot=h.call(this,ct,dt);rt=_.map(function(ft){return new o(f,g,dt[ft],ot,ct[ft].text,void 0,ct[ft].align)}),this.setTableHeaderRow(rt),this.printHeaderRow(1,!1)}var mt=L.reduce(function(ft,Et){return ft[Et.name]=Et.align,ft},{});for(p=0;p<x.length;p+=1){"rowStart"in S&&S.rowStart instanceof Function&&S.rowStart({row:p,data:x[p]},this);var tt=h.call(this,x[p],dt);for(C=0;C<_.length;C+=1){var pt=x[p][_[C]];"cellStart"in S&&S.cellStart instanceof Function&&S.cellStart({row:p,col:C,data:pt},this),l.call(this,new o(f,g,dt[_[C]],tt,pt,p+2,mt[_[C]]))}}return this.internal.__cell__.table_x=f,this.internal.__cell__.table_y=g,this};var h=function(f,g){var x=this.internal.__cell__.padding,L=this.internal.__cell__.table_font_size,S=this.internal.scaleFactor;return Object.keys(f).map(function(p){var O=f[p];return this.splitTextToSize(O.hasOwnProperty("text")?O.text:O,g[p]-x-x)},this).map(function(p){return this.getLineHeightFactor()*p.length*L/S+x+x},this).reduce(function(p,O){return Math.max(p,O)},0)};i.setTableHeaderRow=function(f){a.call(this),this.internal.__cell__.tableHeaderRow=f},i.printHeaderRow=function(f,g){if(a.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var x;if(n=!0,typeof this.internal.__cell__.headerFunction=="function"){var L=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new o(L[0],L[1],L[2],L[3],void 0,-1)}this.setFont(void 0,"bold");for(var S=[],p=0;p<this.internal.__cell__.tableHeaderRow.length;p+=1){x=this.internal.__cell__.tableHeaderRow[p].clone(),g&&(x.y=this.internal.__cell__.margins.top||0,S.push(x)),x.lineNumber=f;var O=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),l.call(this,x),this.setTextColor(O)}S.length>0&&this.setTableHeaderRow(S),this.setFont(void 0,"normal"),n=!1}}(Ut.API);var Oc={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},Bc=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],Ns=jc(Bc),Mc=[100,200,300,400,500,600,700,800,900],vl=jc(Mc);function Ls(i){var e=i.family.replace(/"|'/g,"").toLowerCase(),n=function(o){return Oc[o=o||"normal"]?o:"normal"}(i.style),a=function(o){if(!o)return 400;if(typeof o=="number")return o>=100&&o<=900&&o%100==0?o:400;if(/^\d00$/.test(o))return parseInt(o);switch(o){case"bold":return 700;case"normal":default:return 400}}(i.weight),c=function(o){return typeof Ns[o=o||"normal"]=="number"?o:"normal"}(i.stretch);return{family:e,style:n,weight:a,stretch:c,src:i.src||[],ref:i.ref||{name:e,style:[c,n,a].join(" ")}}}function fc(i,e,n,a){var c;for(c=n;c>=0&&c<e.length;c+=a)if(i[e[c]])return i[e[c]];for(c=n;c>=0&&c<e.length;c-=a)if(i[e[c]])return i[e[c]]}var bl={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},dc={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function pc(i){return[i.stretch,i.style,i.weight,i.family].join(" ")}function yl(i,e,n){for(var a=(n=n||{}).defaultFontFamily||"times",c=Object.assign({},bl,n.genericFontFamilies||{}),o=null,l=null,h=0;h<e.length;++h)if(c[(o=Ls(e[h])).family]&&(o.family=c[o.family]),i.hasOwnProperty(o.family)){l=i[o.family];break}if(!(l=l||i[a]))throw new Error("Could not find a font-family for the rule '"+pc(o)+"' and default family '"+a+"'.");if(l=function(f,g){if(g[f])return g[f];var x=Ns[f],L=x<=Ns.normal?-1:1,S=fc(g,Bc,x,L);if(!S)throw new Error("Could not find a matching font-stretch value for "+f);return S}(o.stretch,l),l=function(f,g){if(g[f])return g[f];for(var x=Oc[f],L=0;L<x.length;++L)if(g[x[L]])return g[x[L]];throw new Error("Could not find a matching font-style for "+f)}(o.style,l),!(l=function(f,g){if(g[f])return g[f];if(f===400&&g[500])return g[500];if(f===500&&g[400])return g[400];var x=vl[f],L=fc(g,Mc,x,f<400?-1:1);if(!L)throw new Error("Could not find a matching font-weight for value "+f);return L}(o.weight,l)))throw new Error("Failed to resolve a font for the rule '"+pc(o)+"'.");return l}function gc(i){return i.trimLeft()}function wl(i,e){for(var n=0;n<i.length;){if(i.charAt(n)===e)return[i.substring(0,n),i.substring(n+1)];n+=1}return null}function Nl(i){var e=i.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return e===null?null:[e[0],i.substring(e[0].length)]}var oo,mc,vc,ds=["times"];(function(i){var e,n,a,c,o,l,h,f,g,x=function(w){return w=w||{},this.isStrokeTransparent=w.isStrokeTransparent||!1,this.strokeOpacity=w.strokeOpacity||1,this.strokeStyle=w.strokeStyle||"#000000",this.fillStyle=w.fillStyle||"#000000",this.isFillTransparent=w.isFillTransparent||!1,this.fillOpacity=w.fillOpacity||1,this.font=w.font||"10px sans-serif",this.textBaseline=w.textBaseline||"alphabetic",this.textAlign=w.textAlign||"left",this.lineWidth=w.lineWidth||1,this.lineJoin=w.lineJoin||"miter",this.lineCap=w.lineCap||"butt",this.path=w.path||[],this.transform=w.transform!==void 0?w.transform.clone():new f,this.globalCompositeOperation=w.globalCompositeOperation||"normal",this.globalAlpha=w.globalAlpha||1,this.clip_path=w.clip_path||[],this.currentPoint=w.currentPoint||new l,this.miterLimit=w.miterLimit||10,this.lastPoint=w.lastPoint||new l,this.lineDashOffset=w.lineDashOffset||0,this.lineDash=w.lineDash||[],this.margin=w.margin||[0,0,0,0],this.prevPageLastElemOffset=w.prevPageLastElemOffset||0,this.ignoreClearRect=typeof w.ignoreClearRect!="boolean"||w.ignoreClearRect,this};i.events.push(["initialized",function(){this.context2d=new L(this),e=this.internal.f2,n=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,c=this.internal.getHorizontalCoordinate,o=this.internal.getVerticalCoordinate,l=this.internal.Point,h=this.internal.Rectangle,f=this.internal.Matrix,g=new x}]);var L=function(w){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var j=w;Object.defineProperty(this,"pdf",{get:function(){return j}});var E=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return E},set:function(at){E=!!at}});var V=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return V},set:function(at){V=!!at}});var J=0;Object.defineProperty(this,"posX",{get:function(){return J},set:function(at){isNaN(at)||(J=at)}});var Z=0;Object.defineProperty(this,"posY",{get:function(){return Z},set:function(at){isNaN(at)||(Z=at)}}),Object.defineProperty(this,"margin",{get:function(){return g.margin},set:function(at){var B;typeof at=="number"?B=[at,at,at,at]:((B=new Array(4))[0]=at[0],B[1]=at.length>=2?at[1]:B[0],B[2]=at.length>=3?at[2]:B[0],B[3]=at.length>=4?at[3]:B[1]),g.margin=B}});var et=!1;Object.defineProperty(this,"autoPaging",{get:function(){return et},set:function(at){et=at}});var Q=0;Object.defineProperty(this,"lastBreak",{get:function(){return Q},set:function(at){Q=at}});var xt=[];Object.defineProperty(this,"pageBreaks",{get:function(){return xt},set:function(at){xt=at}}),Object.defineProperty(this,"ctx",{get:function(){return g},set:function(at){at instanceof x&&(g=at)}}),Object.defineProperty(this,"path",{get:function(){return g.path},set:function(at){g.path=at}});var Nt=[];Object.defineProperty(this,"ctxStack",{get:function(){return Nt},set:function(at){Nt=at}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(at){var B;B=S(at),this.ctx.fillStyle=B.style,this.ctx.isFillTransparent=B.a===0,this.ctx.fillOpacity=B.a,this.pdf.setFillColor(B.r,B.g,B.b,{a:B.a}),this.pdf.setTextColor(B.r,B.g,B.b,{a:B.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(at){var B=S(at);this.ctx.strokeStyle=B.style,this.ctx.isStrokeTransparent=B.a===0,this.ctx.strokeOpacity=B.a,B.a===0?this.pdf.setDrawColor(255,255,255):(B.a,this.pdf.setDrawColor(B.r,B.g,B.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(at){["butt","round","square"].indexOf(at)!==-1&&(this.ctx.lineCap=at,this.pdf.setLineCap(at))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(at){isNaN(at)||(this.ctx.lineWidth=at,this.pdf.setLineWidth(at))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(at){["bevel","round","miter"].indexOf(at)!==-1&&(this.ctx.lineJoin=at,this.pdf.setLineJoin(at))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(at){isNaN(at)||(this.ctx.miterLimit=at,this.pdf.setMiterLimit(at))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(at){this.ctx.textBaseline=at}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(at){["right","end","center","left","start"].indexOf(at)!==-1&&(this.ctx.textAlign=at)}});var Ot=null;function jt(at,B){if(Ot===null){var Kt=function(Mt){var wt=[];return Object.keys(Mt).forEach(function(At){Mt[At].forEach(function(kt){var Pt=null;switch(kt){case"bold":Pt={family:At,weight:"bold"};break;case"italic":Pt={family:At,style:"italic"};break;case"bolditalic":Pt={family:At,weight:"bold",style:"italic"};break;case"":case"normal":Pt={family:At}}Pt!==null&&(Pt.ref={name:At,style:kt},wt.push(Pt))})}),wt}(at.getFontList());Ot=function(Mt){for(var wt={},At=0;At<Mt.length;++At){var kt=Ls(Mt[At]),Pt=kt.family,Tt=kt.stretch,Gt=kt.style,Qt=kt.weight;wt[Pt]=wt[Pt]||{},wt[Pt][Tt]=wt[Pt][Tt]||{},wt[Pt][Tt][Gt]=wt[Pt][Tt][Gt]||{},wt[Pt][Tt][Gt][Qt]=kt}return wt}(Kt.concat(B))}return Ot}var Vt=null;Object.defineProperty(this,"fontFaces",{get:function(){return Vt},set:function(at){Ot=null,Vt=at}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(at){var B;if(this.ctx.font=at,(B=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(at))!==null){var Kt=B[1],Mt=(B[2],B[3]),wt=B[4],At=(B[5],B[6]),kt=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(wt)[2];wt=Math.floor(kt==="px"?parseFloat(wt)*this.pdf.internal.scaleFactor:kt==="em"?parseFloat(wt)*this.pdf.getFontSize():parseFloat(wt)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(wt);var Pt=function(Ht){var ee,Ft,Ye=[],oe=Ht.trim();if(oe==="")return ds;if(oe in dc)return[dc[oe]];for(;oe!=="";){switch(Ft=null,ee=(oe=gc(oe)).charAt(0)){case'"':case"'":Ft=wl(oe.substring(1),ee);break;default:Ft=Nl(oe)}if(Ft===null||(Ye.push(Ft[0]),(oe=gc(Ft[1]))!==""&&oe.charAt(0)!==","))return ds;oe=oe.replace(/^,/,"")}return Ye}(At);if(this.fontFaces){var Tt=yl(jt(this.pdf,this.fontFaces),Pt.map(function(Ht){return{family:Ht,stretch:"normal",weight:Mt,style:Kt}}));this.pdf.setFont(Tt.ref.name,Tt.ref.style)}else{var Gt="";(Mt==="bold"||parseInt(Mt,10)>=700||Kt==="bold")&&(Gt="bold"),Kt==="italic"&&(Gt+="italic"),Gt.length===0&&(Gt="normal");for(var Qt="",te={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},ie=0;ie<Pt.length;ie++){if(this.pdf.internal.getFont(Pt[ie],Gt,{noFallback:!0,disableWarning:!0})!==void 0){Qt=Pt[ie];break}if(Gt==="bolditalic"&&this.pdf.internal.getFont(Pt[ie],"bold",{noFallback:!0,disableWarning:!0})!==void 0)Qt=Pt[ie],Gt="bold";else if(this.pdf.internal.getFont(Pt[ie],"normal",{noFallback:!0,disableWarning:!0})!==void 0){Qt=Pt[ie],Gt="normal";break}}if(Qt===""){for(var de=0;de<Pt.length;de++)if(te[Pt[de]]){Qt=te[Pt[de]];break}}Qt=Qt===""?"Times":Qt,this.pdf.setFont(Qt,Gt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(at){this.ctx.globalCompositeOperation=at}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(at){this.ctx.globalAlpha=at}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(at){this.ctx.lineDashOffset=at,Et.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(at){this.ctx.lineDash=at,Et.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(at){this.ctx.ignoreClearRect=!!at}})};L.prototype.setLineDash=function(w){this.lineDash=w},L.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},L.prototype.fill=function(){st.call(this,"fill",!1)},L.prototype.stroke=function(){st.call(this,"stroke",!1)},L.prototype.beginPath=function(){this.path=[{type:"begin"}]},L.prototype.moveTo=function(w,j){if(isNaN(w)||isNaN(j))throw ve.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var E=this.ctx.transform.applyToPoint(new l(w,j));this.path.push({type:"mt",x:E.x,y:E.y}),this.ctx.lastPoint=new l(w,j)},L.prototype.closePath=function(){var w=new l(0,0),j=0;for(j=this.path.length-1;j!==-1;j--)if(this.path[j].type==="begin"&&fe(this.path[j+1])==="object"&&typeof this.path[j+1].x=="number"){w=new l(this.path[j+1].x,this.path[j+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new l(w.x,w.y)},L.prototype.lineTo=function(w,j){if(isNaN(w)||isNaN(j))throw ve.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var E=this.ctx.transform.applyToPoint(new l(w,j));this.path.push({type:"lt",x:E.x,y:E.y}),this.ctx.lastPoint=new l(E.x,E.y)},L.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),st.call(this,null,!0)},L.prototype.quadraticCurveTo=function(w,j,E,V){if(isNaN(E)||isNaN(V)||isNaN(w)||isNaN(j))throw ve.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var J=this.ctx.transform.applyToPoint(new l(E,V)),Z=this.ctx.transform.applyToPoint(new l(w,j));this.path.push({type:"qct",x1:Z.x,y1:Z.y,x:J.x,y:J.y}),this.ctx.lastPoint=new l(J.x,J.y)},L.prototype.bezierCurveTo=function(w,j,E,V,J,Z){if(isNaN(J)||isNaN(Z)||isNaN(w)||isNaN(j)||isNaN(E)||isNaN(V))throw ve.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var et=this.ctx.transform.applyToPoint(new l(J,Z)),Q=this.ctx.transform.applyToPoint(new l(w,j)),xt=this.ctx.transform.applyToPoint(new l(E,V));this.path.push({type:"bct",x1:Q.x,y1:Q.y,x2:xt.x,y2:xt.y,x:et.x,y:et.y}),this.ctx.lastPoint=new l(et.x,et.y)},L.prototype.arc=function(w,j,E,V,J,Z){if(isNaN(w)||isNaN(j)||isNaN(E)||isNaN(V)||isNaN(J))throw ve.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(Z=!!Z,!this.ctx.transform.isIdentity){var et=this.ctx.transform.applyToPoint(new l(w,j));w=et.x,j=et.y;var Q=this.ctx.transform.applyToPoint(new l(0,E)),xt=this.ctx.transform.applyToPoint(new l(0,0));E=Math.sqrt(Math.pow(Q.x-xt.x,2)+Math.pow(Q.y-xt.y,2))}Math.abs(J-V)>=2*Math.PI&&(V=0,J=2*Math.PI),this.path.push({type:"arc",x:w,y:j,radius:E,startAngle:V,endAngle:J,counterclockwise:Z})},L.prototype.arcTo=function(w,j,E,V,J){throw new Error("arcTo not implemented.")},L.prototype.rect=function(w,j,E,V){if(isNaN(w)||isNaN(j)||isNaN(E)||isNaN(V))throw ve.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(w,j),this.lineTo(w+E,j),this.lineTo(w+E,j+V),this.lineTo(w,j+V),this.lineTo(w,j),this.lineTo(w+E,j),this.lineTo(w,j)},L.prototype.fillRect=function(w,j,E,V){if(isNaN(w)||isNaN(j)||isNaN(E)||isNaN(V))throw ve.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!p.call(this)){var J={};this.lineCap!=="butt"&&(J.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(J.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(w,j,E,V),this.fill(),J.hasOwnProperty("lineCap")&&(this.lineCap=J.lineCap),J.hasOwnProperty("lineJoin")&&(this.lineJoin=J.lineJoin)}},L.prototype.strokeRect=function(w,j,E,V){if(isNaN(w)||isNaN(j)||isNaN(E)||isNaN(V))throw ve.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");O.call(this)||(this.beginPath(),this.rect(w,j,E,V),this.stroke())},L.prototype.clearRect=function(w,j,E,V){if(isNaN(w)||isNaN(j)||isNaN(E)||isNaN(V))throw ve.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(w,j,E,V))},L.prototype.save=function(w){w=typeof w!="boolean"||w;for(var j=this.pdf.internal.getCurrentPageInfo().pageNumber,E=0;E<this.pdf.internal.getNumberOfPages();E++)this.pdf.setPage(E+1),this.pdf.internal.out("q");if(this.pdf.setPage(j),w){this.ctx.fontSize=this.pdf.internal.getFontSize();var V=new x(this.ctx);this.ctxStack.push(this.ctx),this.ctx=V}},L.prototype.restore=function(w){w=typeof w!="boolean"||w;for(var j=this.pdf.internal.getCurrentPageInfo().pageNumber,E=0;E<this.pdf.internal.getNumberOfPages();E++)this.pdf.setPage(E+1),this.pdf.internal.out("Q");this.pdf.setPage(j),w&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},L.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var S=function(w){var j,E,V,J;if(w.isCanvasGradient===!0&&(w=w.getColor()),!w)return{r:0,g:0,b:0,a:0,style:w};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(w))j=0,E=0,V=0,J=0;else{var Z=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(w);if(Z!==null)j=parseInt(Z[1]),E=parseInt(Z[2]),V=parseInt(Z[3]),J=1;else if((Z=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(w))!==null)j=parseInt(Z[1]),E=parseInt(Z[2]),V=parseInt(Z[3]),J=parseFloat(Z[4]);else{if(J=1,typeof w=="string"&&w.charAt(0)!=="#"){var et=new _c(w);w=et.ok?et.toHex():"#000000"}w.length===4?(j=w.substring(1,2),j+=j,E=w.substring(2,3),E+=E,V=w.substring(3,4),V+=V):(j=w.substring(1,3),E=w.substring(3,5),V=w.substring(5,7)),j=parseInt(j,16),E=parseInt(E,16),V=parseInt(V,16)}}return{r:j,g:E,b:V,a:J,style:w}},p=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},O=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};L.prototype.fillText=function(w,j,E,V){if(isNaN(j)||isNaN(E)||typeof w!="string")throw ve.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(V=isNaN(V)?void 0:V,!p.call(this)){var J=tt(this.ctx.transform.rotation),Z=this.ctx.transform.scaleX;F.call(this,{text:w,x:j,y:E,scale:Z,angle:J,align:this.textAlign,maxWidth:V})}},L.prototype.strokeText=function(w,j,E,V){if(isNaN(j)||isNaN(E)||typeof w!="string")throw ve.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!O.call(this)){V=isNaN(V)?void 0:V;var J=tt(this.ctx.transform.rotation),Z=this.ctx.transform.scaleX;F.call(this,{text:w,x:j,y:E,scale:Z,renderingMode:"stroke",angle:J,align:this.textAlign,maxWidth:V})}},L.prototype.measureText=function(w){if(typeof w!="string")throw ve.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var j=this.pdf,E=this.pdf.internal.scaleFactor,V=j.internal.getFontSize(),J=j.getStringUnitWidth(w)*V/j.internal.scaleFactor,Z=function(et){var Q=(et=et||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return Q}}),this};return new Z({width:J*=Math.round(96*E/72*1e4)/1e4})},L.prototype.scale=function(w,j){if(isNaN(w)||isNaN(j))throw ve.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var E=new f(w,0,0,j,0,0);this.ctx.transform=this.ctx.transform.multiply(E)},L.prototype.rotate=function(w){if(isNaN(w))throw ve.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var j=new f(Math.cos(w),Math.sin(w),-Math.sin(w),Math.cos(w),0,0);this.ctx.transform=this.ctx.transform.multiply(j)},L.prototype.translate=function(w,j){if(isNaN(w)||isNaN(j))throw ve.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var E=new f(1,0,0,1,w,j);this.ctx.transform=this.ctx.transform.multiply(E)},L.prototype.transform=function(w,j,E,V,J,Z){if(isNaN(w)||isNaN(j)||isNaN(E)||isNaN(V)||isNaN(J)||isNaN(Z))throw ve.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var et=new f(w,j,E,V,J,Z);this.ctx.transform=this.ctx.transform.multiply(et)},L.prototype.setTransform=function(w,j,E,V,J,Z){w=isNaN(w)?1:w,j=isNaN(j)?0:j,E=isNaN(E)?0:E,V=isNaN(V)?1:V,J=isNaN(J)?0:J,Z=isNaN(Z)?0:Z,this.ctx.transform=new f(w,j,E,V,J,Z)};var C=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};L.prototype.drawImage=function(w,j,E,V,J,Z,et,Q,xt){var Nt=this.pdf.getImageProperties(w),Ot=1,jt=1,Vt=1,at=1;V!==void 0&&Q!==void 0&&(Vt=Q/V,at=xt/J,Ot=Nt.width/V*Q/V,jt=Nt.height/J*xt/J),Z===void 0&&(Z=j,et=E,j=0,E=0),V!==void 0&&Q===void 0&&(Q=V,xt=J),V===void 0&&Q===void 0&&(Q=Nt.width,xt=Nt.height);for(var B,Kt=this.ctx.transform.decompose(),Mt=tt(Kt.rotate.shx),wt=new f,At=(wt=(wt=(wt=wt.multiply(Kt.translate)).multiply(Kt.skew)).multiply(Kt.scale)).applyToRectangle(new h(Z-j*Vt,et-E*at,V*Ot,J*jt)),kt=T.call(this,At),Pt=[],Tt=0;Tt<kt.length;Tt+=1)Pt.indexOf(kt[Tt])===-1&&Pt.push(kt[Tt]);if($(Pt),this.autoPaging)for(var Gt=Pt[0],Qt=Pt[Pt.length-1],te=Gt;te<Qt+1;te++){this.pdf.setPage(te);var ie=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],de=te===1?this.posY+this.margin[0]:this.margin[0],Ht=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],ee=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],Ft=te===1?0:Ht+(te-2)*ee;if(this.ctx.clip_path.length!==0){var Ye=this.path;B=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=M(B,this.posX+this.margin[3],-Ft+de+this.ctx.prevPageLastElemOffset),dt.call(this,"fill",!0),this.path=Ye}var oe=JSON.parse(JSON.stringify(At));oe=M([oe],this.posX+this.margin[3],-Ft+de+this.ctx.prevPageLastElemOffset)[0];var Sn=(te>Gt||te<Qt)&&C.call(this);Sn&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ie,ee,null).clip().discardPath()),this.pdf.addImage(w,"JPEG",oe.x,oe.y,oe.w,oe.h,null,null,Mt),Sn&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(w,"JPEG",At.x,At.y,At.w,At.h,null,null,Mt)};var T=function(w,j,E){var V=[];j=j||this.pdf.internal.pageSize.width,E=E||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var J=this.posY+this.ctx.prevPageLastElemOffset;switch(w.type){default:case"mt":case"lt":V.push(Math.floor((w.y+J)/E)+1);break;case"arc":V.push(Math.floor((w.y+J-w.radius)/E)+1),V.push(Math.floor((w.y+J+w.radius)/E)+1);break;case"qct":var Z=pt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,w.x1,w.y1,w.x,w.y);V.push(Math.floor((Z.y+J)/E)+1),V.push(Math.floor((Z.y+Z.h+J)/E)+1);break;case"bct":var et=ft(this.ctx.lastPoint.x,this.ctx.lastPoint.y,w.x1,w.y1,w.x2,w.y2,w.x,w.y);V.push(Math.floor((et.y+J)/E)+1),V.push(Math.floor((et.y+et.h+J)/E)+1);break;case"rect":V.push(Math.floor((w.y+J)/E)+1),V.push(Math.floor((w.y+w.h+J)/E)+1)}for(var Q=0;Q<V.length;Q+=1)for(;this.pdf.internal.getNumberOfPages()<V[Q];)_.call(this);return V},_=function(){var w=this.fillStyle,j=this.strokeStyle,E=this.font,V=this.lineCap,J=this.lineWidth,Z=this.lineJoin;this.pdf.addPage(),this.fillStyle=w,this.strokeStyle=j,this.font=E,this.lineCap=V,this.lineWidth=J,this.lineJoin=Z},M=function(w,j,E){for(var V=0;V<w.length;V++)switch(w[V].type){case"bct":w[V].x2+=j,w[V].y2+=E;case"qct":w[V].x1+=j,w[V].y1+=E;case"mt":case"lt":case"arc":default:w[V].x+=j,w[V].y+=E}return w},$=function(w){return w.sort(function(j,E){return j-E})},st=function(w,j){for(var E,V,J=this.fillStyle,Z=this.strokeStyle,et=this.lineCap,Q=this.lineWidth,xt=Math.abs(Q*this.ctx.transform.scaleX),Nt=this.lineJoin,Ot=JSON.parse(JSON.stringify(this.path)),jt=JSON.parse(JSON.stringify(this.path)),Vt=[],at=0;at<jt.length;at++)if(jt[at].x!==void 0)for(var B=T.call(this,jt[at]),Kt=0;Kt<B.length;Kt+=1)Vt.indexOf(B[Kt])===-1&&Vt.push(B[Kt]);for(var Mt=0;Mt<Vt.length;Mt++)for(;this.pdf.internal.getNumberOfPages()<Vt[Mt];)_.call(this);if($(Vt),this.autoPaging)for(var wt=Vt[0],At=Vt[Vt.length-1],kt=wt;kt<At+1;kt++){this.pdf.setPage(kt),this.fillStyle=J,this.strokeStyle=Z,this.lineCap=et,this.lineWidth=xt,this.lineJoin=Nt;var Pt=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],Tt=kt===1?this.posY+this.margin[0]:this.margin[0],Gt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Qt=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],te=kt===1?0:Gt+(kt-2)*Qt;if(this.ctx.clip_path.length!==0){var ie=this.path;E=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=M(E,this.posX+this.margin[3],-te+Tt+this.ctx.prevPageLastElemOffset),dt.call(this,w,!0),this.path=ie}if(V=JSON.parse(JSON.stringify(Ot)),this.path=M(V,this.posX+this.margin[3],-te+Tt+this.ctx.prevPageLastElemOffset),j===!1||kt===0){var de=(kt>wt||kt<At)&&C.call(this);de&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],Pt,Qt,null).clip().discardPath()),dt.call(this,w,j),de&&this.pdf.restoreGraphicsState()}this.lineWidth=Q}else this.lineWidth=xt,dt.call(this,w,j),this.lineWidth=Q;this.path=Ot},dt=function(w,j){if((w!=="stroke"||j||!O.call(this))&&(w==="stroke"||j||!p.call(this))){for(var E,V,J=[],Z=this.path,et=0;et<Z.length;et++){var Q=Z[et];switch(Q.type){case"begin":J.push({begin:!0});break;case"close":J.push({close:!0});break;case"mt":J.push({start:Q,deltas:[],abs:[]});break;case"lt":var xt=J.length;if(Z[et-1]&&!isNaN(Z[et-1].x)&&(E=[Q.x-Z[et-1].x,Q.y-Z[et-1].y],xt>0)){for(;xt>=0;xt--)if(J[xt-1].close!==!0&&J[xt-1].begin!==!0){J[xt-1].deltas.push(E),J[xt-1].abs.push(Q);break}}break;case"bct":E=[Q.x1-Z[et-1].x,Q.y1-Z[et-1].y,Q.x2-Z[et-1].x,Q.y2-Z[et-1].y,Q.x-Z[et-1].x,Q.y-Z[et-1].y],J[J.length-1].deltas.push(E);break;case"qct":var Nt=Z[et-1].x+2/3*(Q.x1-Z[et-1].x),Ot=Z[et-1].y+2/3*(Q.y1-Z[et-1].y),jt=Q.x+2/3*(Q.x1-Q.x),Vt=Q.y+2/3*(Q.y1-Q.y),at=Q.x,B=Q.y;E=[Nt-Z[et-1].x,Ot-Z[et-1].y,jt-Z[et-1].x,Vt-Z[et-1].y,at-Z[et-1].x,B-Z[et-1].y],J[J.length-1].deltas.push(E);break;case"arc":J.push({deltas:[],abs:[],arc:!0}),Array.isArray(J[J.length-1].abs)&&J[J.length-1].abs.push(Q)}}V=j?null:w==="stroke"?"stroke":"fill";for(var Kt=!1,Mt=0;Mt<J.length;Mt++)if(J[Mt].arc)for(var wt=J[Mt].abs,At=0;At<wt.length;At++){var kt=wt[At];kt.type==="arc"?G.call(this,kt.x,kt.y,kt.radius,kt.startAngle,kt.endAngle,kt.counterclockwise,void 0,j,!Kt):H.call(this,kt.x,kt.y),Kt=!0}else if(J[Mt].close===!0)this.pdf.internal.out("h"),Kt=!1;else if(J[Mt].begin!==!0){var Pt=J[Mt].start.x,Tt=J[Mt].start.y;D.call(this,J[Mt].deltas,Pt,Tt),Kt=!0}V&&vt.call(this,V),j&&bt.call(this)}},Lt=function(w){var j=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,E=j*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return w-E;case"top":return w+j-E;case"hanging":return w+j-2*E;case"middle":return w+j/2-E;case"ideographic":return w;case"alphabetic":default:return w}},rt=function(w){return w+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};L.prototype.createLinearGradient=function(){var w=function(){};return w.colorStops=[],w.addColorStop=function(j,E){this.colorStops.push([j,E])},w.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},w.isCanvasGradient=!0,w},L.prototype.createPattern=function(){return this.createLinearGradient()},L.prototype.createRadialGradient=function(){return this.createLinearGradient()};var G=function(w,j,E,V,J,Z,et,Q,xt){for(var Nt=ot.call(this,E,V,J,Z),Ot=0;Ot<Nt.length;Ot++){var jt=Nt[Ot];Ot===0&&(xt?k.call(this,jt.x1+w,jt.y1+j):H.call(this,jt.x1+w,jt.y1+j)),ct.call(this,w,j,jt.x2,jt.y2,jt.x3,jt.y3,jt.x4,jt.y4)}Q?bt.call(this):vt.call(this,et)},vt=function(w){switch(w){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},bt=function(){this.pdf.clip(),this.pdf.discardPath()},k=function(w,j){this.pdf.internal.out(n(w)+" "+a(j)+" m")},F=function(w){var j;switch(w.align){case"right":case"end":j="right";break;case"center":j="center";break;case"left":case"start":default:j="left"}var E=this.pdf.getTextDimensions(w.text),V=Lt.call(this,w.y),J=rt.call(this,V)-E.h,Z=this.ctx.transform.applyToPoint(new l(w.x,V)),et=this.ctx.transform.decompose(),Q=new f;Q=(Q=(Q=Q.multiply(et.translate)).multiply(et.skew)).multiply(et.scale);for(var xt,Nt,Ot,jt=this.ctx.transform.applyToRectangle(new h(w.x,V,E.w,E.h)),Vt=Q.applyToRectangle(new h(w.x,J,E.w,E.h)),at=T.call(this,Vt),B=[],Kt=0;Kt<at.length;Kt+=1)B.indexOf(at[Kt])===-1&&B.push(at[Kt]);if($(B),this.autoPaging)for(var Mt=B[0],wt=B[B.length-1],At=Mt;At<wt+1;At++){this.pdf.setPage(At);var kt=At===1?this.posY+this.margin[0]:this.margin[0],Pt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Tt=this.pdf.internal.pageSize.height-this.margin[2],Gt=Tt-this.margin[0],Qt=this.pdf.internal.pageSize.width-this.margin[1],te=Qt-this.margin[3],ie=At===1?0:Pt+(At-2)*Gt;if(this.ctx.clip_path.length!==0){var de=this.path;xt=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=M(xt,this.posX+this.margin[3],-1*ie+kt),dt.call(this,"fill",!0),this.path=de}var Ht=M([JSON.parse(JSON.stringify(Vt))],this.posX+this.margin[3],-ie+kt+this.ctx.prevPageLastElemOffset)[0];w.scale>=.01&&(Nt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Nt*w.scale),Ot=this.lineWidth,this.lineWidth=Ot*w.scale);var ee=this.autoPaging!=="text";if(ee||Ht.y+Ht.h<=Tt){if(ee||Ht.y>=kt&&Ht.x<=Qt){var Ft=ee?w.text:this.pdf.splitTextToSize(w.text,w.maxWidth||Qt-Ht.x)[0],Ye=M([JSON.parse(JSON.stringify(jt))],this.posX+this.margin[3],-ie+kt+this.ctx.prevPageLastElemOffset)[0],oe=ee&&(At>Mt||At<wt)&&C.call(this);oe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],te,Gt,null).clip().discardPath()),this.pdf.text(Ft,Ye.x,Ye.y,{angle:w.angle,align:j,renderingMode:w.renderingMode}),oe&&this.pdf.restoreGraphicsState()}}else Ht.y<Tt&&(this.ctx.prevPageLastElemOffset+=Tt-Ht.y);w.scale>=.01&&(this.pdf.setFontSize(Nt),this.lineWidth=Ot)}else w.scale>=.01&&(Nt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Nt*w.scale),Ot=this.lineWidth,this.lineWidth=Ot*w.scale),this.pdf.text(w.text,Z.x+this.posX,Z.y+this.posY,{angle:w.angle,align:j,renderingMode:w.renderingMode,maxWidth:w.maxWidth}),w.scale>=.01&&(this.pdf.setFontSize(Nt),this.lineWidth=Ot)},H=function(w,j,E,V){E=E||0,V=V||0,this.pdf.internal.out(n(w+E)+" "+a(j+V)+" l")},D=function(w,j,E){return this.pdf.lines(w,j,E,null,null)},ct=function(w,j,E,V,J,Z,et,Q){this.pdf.internal.out([e(c(E+w)),e(o(V+j)),e(c(J+w)),e(o(Z+j)),e(c(et+w)),e(o(Q+j)),"c"].join(" "))},ot=function(w,j,E,V){for(var J=2*Math.PI,Z=Math.PI/2;j>E;)j-=J;var et=Math.abs(E-j);et<J&&V&&(et=J-et);for(var Q=[],xt=V?-1:1,Nt=j;et>1e-5;){var Ot=Nt+xt*Math.min(et,Z);Q.push(mt.call(this,w,Nt,Ot)),et-=Math.abs(Ot-Nt),Nt=Ot}return Q},mt=function(w,j,E){var V=(E-j)/2,J=w*Math.cos(V),Z=w*Math.sin(V),et=J,Q=-Z,xt=et*et+Q*Q,Nt=xt+et*J+Q*Z,Ot=4/3*(Math.sqrt(2*xt*Nt)-Nt)/(et*Z-Q*J),jt=et-Ot*Q,Vt=Q+Ot*et,at=jt,B=-Vt,Kt=V+j,Mt=Math.cos(Kt),wt=Math.sin(Kt);return{x1:w*Math.cos(j),y1:w*Math.sin(j),x2:jt*Mt-Vt*wt,y2:jt*wt+Vt*Mt,x3:at*Mt-B*wt,y3:at*wt+B*Mt,x4:w*Math.cos(E),y4:w*Math.sin(E)}},tt=function(w){return 180*w/Math.PI},pt=function(w,j,E,V,J,Z){var et=w+.5*(E-w),Q=j+.5*(V-j),xt=J+.5*(E-J),Nt=Z+.5*(V-Z),Ot=Math.min(w,J,et,xt),jt=Math.max(w,J,et,xt),Vt=Math.min(j,Z,Q,Nt),at=Math.max(j,Z,Q,Nt);return new h(Ot,Vt,jt-Ot,at-Vt)},ft=function(w,j,E,V,J,Z,et,Q){var xt,Nt,Ot,jt,Vt,at,B,Kt,Mt,wt,At,kt,Pt,Tt,Gt=E-w,Qt=V-j,te=J-E,ie=Z-V,de=et-J,Ht=Q-Z;for(Nt=0;Nt<41;Nt++)Mt=(B=(Ot=w+(xt=Nt/40)*Gt)+xt*((Vt=E+xt*te)-Ot))+xt*(Vt+xt*(J+xt*de-Vt)-B),wt=(Kt=(jt=j+xt*Qt)+xt*((at=V+xt*ie)-jt))+xt*(at+xt*(Z+xt*Ht-at)-Kt),Nt==0?(At=Mt,kt=wt,Pt=Mt,Tt=wt):(At=Math.min(At,Mt),kt=Math.min(kt,wt),Pt=Math.max(Pt,Mt),Tt=Math.max(Tt,wt));return new h(Math.round(At),Math.round(kt),Math.round(Pt-At),Math.round(Tt-kt))},Et=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var w,j,E=(w=this.ctx.lineDash,j=this.ctx.lineDashOffset,JSON.stringify({lineDash:w,lineDashOffset:j}));this.prevLineDash!==E&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=E)}}})(Ut.API),function(i){var e=function(o){var l,h,f,g,x,L,S,p,O,C;for(h=[],f=0,g=(o+=l="\0\0\0\0".slice(o.length%4||4)).length;g>f;f+=4)(x=(o.charCodeAt(f)<<24)+(o.charCodeAt(f+1)<<16)+(o.charCodeAt(f+2)<<8)+o.charCodeAt(f+3))!==0?(L=(x=((x=((x=((x=(x-(C=x%85))/85)-(O=x%85))/85)-(p=x%85))/85)-(S=x%85))/85)%85,h.push(L+33,S+33,p+33,O+33,C+33)):h.push(122);return function(T,_){for(var M=_;M>0;M--)T.pop()}(h,l.length),String.fromCharCode.apply(String,h)+"~>"},n=function(o){var l,h,f,g,x,L=String,S="length",p=255,O="charCodeAt",C="slice",T="replace";for(o[C](-2),o=o[C](0,-2)[T](/\s/g,"")[T]("z","!!!!!"),f=[],g=0,x=(o+=l="uuuuu"[C](o[S]%5||5))[S];x>g;g+=5)h=52200625*(o[O](g)-33)+614125*(o[O](g+1)-33)+7225*(o[O](g+2)-33)+85*(o[O](g+3)-33)+(o[O](g+4)-33),f.push(p&h>>24,p&h>>16,p&h>>8,p&h);return function(_,M){for(var $=M;$>0;$--)_.pop()}(f,l[S]),L.fromCharCode.apply(L,f)},a=function(o){var l=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((o=o.replace(/\s/g,"")).indexOf(">")!==-1&&(o=o.substr(0,o.indexOf(">"))),o.length%2&&(o+="0"),l.test(o)===!1)return"";for(var h="",f=0;f<o.length;f+=2)h+=String.fromCharCode("0x"+(o[f]+o[f+1]));return h},c=function(o){for(var l=new Uint8Array(o.length),h=o.length;h--;)l[h]=o.charCodeAt(h);return o=(l=ms(l)).reduce(function(f,g){return f+String.fromCharCode(g)},"")};i.processDataByFilters=function(o,l){var h=0,f=o||"",g=[];for(typeof(l=l||[])=="string"&&(l=[l]),h=0;h<l.length;h+=1)switch(l[h]){case"ASCII85Decode":case"/ASCII85Decode":f=n(f),g.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":f=e(f),g.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":f=a(f),g.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":f=f.split("").map(function(x){return("0"+x.charCodeAt().toString(16)).slice(-2)}).join("")+">",g.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":f=c(f),g.push("/FlateDecode");break;default:throw new Error('The filter: "'+l[h]+'" is not implemented')}return{data:f,reverseChain:g.reverse().join(" ")}}}(Ut.API),function(i){i.loadFile=function(e,n,a){return function(c,o,l){o=o!==!1,l=typeof l=="function"?l:function(){};var h=void 0;try{h=function(f,g,x){var L=new XMLHttpRequest,S=0,p=function(O){var C=O.length,T=[],_=String.fromCharCode;for(S=0;S<C;S+=1)T.push(_(255&O.charCodeAt(S)));return T.join("")};if(L.open("GET",f,!g),L.overrideMimeType("text/plain; charset=x-user-defined"),g===!1&&(L.onload=function(){L.status===200?x(p(this.responseText)):x(void 0)}),L.send(null),g&&L.status===200)return p(L.responseText)}(c,o,l)}catch{}return h}(e,n,a)},i.loadImageFile=i.loadFile}(Ut.API),function(i){function e(){return(zt.html2canvas?Promise.resolve(zt.html2canvas):gs(()=>import("./index-9381ab2b.js").then(l=>l.jr),["assets/index-9381ab2b.js","assets/index-58f11664.css"])).catch(function(l){return Promise.reject(new Error("Could not load html2canvas: "+l))}).then(function(l){return l.default?l.default:l})}function n(){return(zt.DOMPurify?Promise.resolve(zt.DOMPurify):gs(()=>import("./purify.es-3fb4e735.js"),[])).catch(function(l){return Promise.reject(new Error("Could not load dompurify: "+l))}).then(function(l){return l.default?l.default:l})}var a=function(l){var h=fe(l);return h==="undefined"?"undefined":h==="string"||l instanceof String?"string":h==="number"||l instanceof Number?"number":h==="function"||l instanceof Function?"function":l&&l.constructor===Array?"array":l&&l.nodeType===1?"element":h==="object"?"object":"unknown"},c=function(l,h){var f=document.createElement(l);for(var g in h.className&&(f.className=h.className),h.innerHTML&&h.dompurify&&(f.innerHTML=h.dompurify.sanitize(h.innerHTML)),h.style)f.style[g]=h.style[g];return f},o=function l(h){var f=Object.assign(l.convert(Promise.resolve()),JSON.parse(JSON.stringify(l.template))),g=l.convert(Promise.resolve(),f);return g=(g=g.setProgress(1,l,1,[l])).set(h)};(o.prototype=Object.create(Promise.prototype)).constructor=o,o.convert=function(l,h){return l.__proto__=h||o.prototype,l},o.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},o.prototype.from=function(l,h){return this.then(function(){switch(h=h||function(f){switch(a(f)){case"string":return"string";case"element":return f.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(l)){case"string":return this.then(n).then(function(f){return this.set({src:c("div",{innerHTML:l,dompurify:f})})});case"element":return this.set({src:l});case"canvas":return this.set({canvas:l});case"img":return this.set({img:l});default:return this.error("Unknown source type.")}})},o.prototype.to=function(l){switch(l){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},o.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var l={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},h=function f(g,x){for(var L=g.nodeType===3?document.createTextNode(g.nodeValue):g.cloneNode(!1),S=g.firstChild;S;S=S.nextSibling)x!==!0&&S.nodeType===1&&S.nodeName==="SCRIPT"||L.appendChild(f(S,x));return g.nodeType===1&&(g.nodeName==="CANVAS"?(L.width=g.width,L.height=g.height,L.getContext("2d").drawImage(g,0,0)):g.nodeName!=="TEXTAREA"&&g.nodeName!=="SELECT"||(L.value=g.value),L.addEventListener("load",function(){L.scrollTop=g.scrollTop,L.scrollLeft=g.scrollLeft},!0)),L}(this.prop.src,this.opt.html2canvas.javascriptEnabled);h.tagName==="BODY"&&(l.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=c("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=c("div",{className:"html2pdf__container",style:l}),this.prop.container.appendChild(h),this.prop.container.firstChild.appendChild(c("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},o.prototype.toCanvas=function(){var l=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(l).then(e).then(function(h){var f=Object.assign({},this.opt.html2canvas);return delete f.onrendered,h(this.prop.container,f)}).then(function(h){(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},o.prototype.toContext2d=function(){var l=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(l).then(e).then(function(h){var f=this.opt.jsPDF,g=this.opt.fontFaces,x=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,L=Object.assign({async:!0,allowTaint:!0,scale:x,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete L.onrendered,f.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,f.context2d.posX=this.opt.x,f.context2d.posY=this.opt.y,f.context2d.margin=this.opt.margin,f.context2d.fontFaces=g,g)for(var S=0;S<g.length;++S){var p=g[S],O=p.src.find(function(C){return C.format==="truetype"});O&&f.addFont(O.url,p.ref.name,p.ref.style)}return L.windowHeight=L.windowHeight||0,L.windowHeight=L.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):L.windowHeight,f.context2d.save(!0),h(this.prop.container,L)}).then(function(h){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},o.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var l=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=l})},o.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},o.prototype.output=function(l,h,f){return(f=f||"pdf").toLowerCase()==="img"||f.toLowerCase()==="image"?this.outputImg(l,h):this.outputPdf(l,h)},o.prototype.outputPdf=function(l,h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(l,h)})},o.prototype.outputImg=function(l){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(l){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+l+'" is not supported.'}})},o.prototype.save=function(l){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(l?{filename:l}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},o.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},o.prototype.set=function(l){if(a(l)!=="object")return this;var h=Object.keys(l||{}).map(function(f){if(f in o.template.prop)return function(){this.prop[f]=l[f]};switch(f){case"margin":return this.setMargin.bind(this,l.margin);case"jsPDF":return function(){return this.opt.jsPDF=l.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,l.pageSize);default:return function(){this.opt[f]=l[f]}}},this);return this.then(function(){return this.thenList(h)})},o.prototype.get=function(l,h){return this.then(function(){var f=l in o.template.prop?this.prop[l]:this.opt[l];return h?h(f):f})},o.prototype.setMargin=function(l){return this.then(function(){switch(a(l)){case"number":l=[l,l,l,l];case"array":if(l.length===2&&(l=[l[0],l[1],l[0],l[1]]),l.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=l}).then(this.setPageSize)},o.prototype.setPageSize=function(l){function h(f,g){return Math.floor(f*g/72*96)}return this.then(function(){(l=l||Ut.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(l.inner={width:l.width-this.opt.margin[1]-this.opt.margin[3],height:l.height-this.opt.margin[0]-this.opt.margin[2]},l.inner.px={width:h(l.inner.width,l.k),height:h(l.inner.height,l.k)},l.inner.ratio=l.inner.height/l.inner.width),this.prop.pageSize=l})},o.prototype.setProgress=function(l,h,f,g){return l!=null&&(this.progress.val=l),h!=null&&(this.progress.state=h),f!=null&&(this.progress.n=f),g!=null&&(this.progress.stack=g),this.progress.ratio=this.progress.val/this.progress.state,this},o.prototype.updateProgress=function(l,h,f,g){return this.setProgress(l?this.progress.val+l:null,h||null,f?this.progress.n+f:null,g?this.progress.stack.concat(g):null)},o.prototype.then=function(l,h){var f=this;return this.thenCore(l,h,function(g,x){return f.updateProgress(null,null,1,[g]),Promise.prototype.then.call(this,function(L){return f.updateProgress(null,g),L}).then(g,x).then(function(L){return f.updateProgress(1),L})})},o.prototype.thenCore=function(l,h,f){f=f||Promise.prototype.then,l&&(l=l.bind(this)),h&&(h=h.bind(this));var g=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:o.convert(Object.assign({},this),Promise.prototype),x=f.call(g,l,h);return o.convert(x,this.__proto__)},o.prototype.thenExternal=function(l,h){return Promise.prototype.then.call(this,l,h)},o.prototype.thenList=function(l){var h=this;return l.forEach(function(f){h=h.thenCore(f)}),h},o.prototype.catch=function(l){l&&(l=l.bind(this));var h=Promise.prototype.catch.call(this,l);return o.convert(h,this)},o.prototype.catchExternal=function(l){return Promise.prototype.catch.call(this,l)},o.prototype.error=function(l){return this.then(function(){throw new Error(l)})},o.prototype.using=o.prototype.set,o.prototype.saveAs=o.prototype.save,o.prototype.export=o.prototype.output,o.prototype.run=o.prototype.then,Ut.getPageSize=function(l,h,f){if(fe(l)==="object"){var g=l;l=g.orientation,h=g.unit||h,f=g.format||f}h=h||"mm",f=f||"a4",l=(""+(l||"P")).toLowerCase();var x,L=(""+f).toLowerCase(),S={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(h){case"pt":x=1;break;case"mm":x=72/25.4;break;case"cm":x=72/2.54;break;case"in":x=72;break;case"px":x=.75;break;case"pc":case"em":x=12;break;case"ex":x=6;break;default:throw"Invalid unit: "+h}var p,O=0,C=0;if(S.hasOwnProperty(L))O=S[L][1]/x,C=S[L][0]/x;else try{O=f[1],C=f[0]}catch{throw new Error("Invalid format: "+f)}if(l==="p"||l==="portrait")l="p",C>O&&(p=C,C=O,O=p);else{if(l!=="l"&&l!=="landscape")throw"Invalid orientation: "+l;l="l",O>C&&(p=C,C=O,O=p)}return{width:C,height:O,unit:h,k:x,orientation:l}},i.html=function(l,h){(h=h||{}).callback=h.callback||function(){},h.html2canvas=h.html2canvas||{},h.html2canvas.canvas=h.html2canvas.canvas||this.canvas,h.jsPDF=h.jsPDF||this,h.fontFaces=h.fontFaces?h.fontFaces.map(Ls):null;var f=new o(h);return h.worker?f:f.from(l).doCallback()}}(Ut.API),Ut.API.addJS=function(i){return vc=i,this.internal.events.subscribe("postPutResources",function(){oo=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(oo+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),mc=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+vc+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){oo!==void 0&&mc!==void 0&&this.internal.out("/Names <</JavaScript "+oo+" 0 R>>")}),this},function(i){var e;i.events.push(["postPutResources",function(){var n=this,a=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var c=n.outline.render().split(/\r\n/),o=0;o<c.length;o++){var l=c[o],h=a.exec(l);if(h!=null){var f=h[1];n.internal.newObjectDeferredBegin(f,!1)}n.internal.write(l)}if(this.outline.createNamedDestinations){var g=this.internal.pages.length,x=[];for(o=0;o<g;o++){var L=n.internal.newObject();x.push(L);var S=n.internal.getPageInfo(o+1);n.internal.write("<< /D["+S.objId+" 0 R /XYZ null null null]>> endobj")}var p=n.internal.newObject();for(n.internal.write("<< /Names [ "),o=0;o<x.length;o++)n.internal.write("(page_"+(o+1)+")"+x[o]+" 0 R");n.internal.write(" ] >>","endobj"),e=n.internal.newObject(),n.internal.write("<< /Dests "+p+" 0 R"),n.internal.write(">>","endobj")}}]),i.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),i.events.push(["initialized",function(){var n=this;n.outline={createNamedDestinations:!1,root:{children:[]}},n.outline.add=function(a,c,o){var l={title:c,options:o,children:[]};return a==null&&(a=this.root),a.children.push(l),l},n.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=n,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},n.outline.genIds_r=function(a){a.id=n.internal.newObjectDeferred();for(var c=0;c<a.children.length;c++)this.genIds_r(a.children[c])},n.outline.renderRoot=function(a){this.objStart(a),this.line("/Type /Outlines"),a.children.length>0&&(this.line("/First "+this.makeRef(a.children[0])),this.line("/Last "+this.makeRef(a.children[a.children.length-1]))),this.line("/Count "+this.count_r({count:0},a)),this.objEnd()},n.outline.renderItems=function(a){for(var c=this.ctx.pdf.internal.getVerticalCoordinateString,o=0;o<a.children.length;o++){var l=a.children[o];this.objStart(l),this.line("/Title "+this.makeString(l.title)),this.line("/Parent "+this.makeRef(a)),o>0&&this.line("/Prev "+this.makeRef(a.children[o-1])),o<a.children.length-1&&this.line("/Next "+this.makeRef(a.children[o+1])),l.children.length>0&&(this.line("/First "+this.makeRef(l.children[0])),this.line("/Last "+this.makeRef(l.children[l.children.length-1])));var h=this.count=this.count_r({count:0},l);if(h>0&&this.line("/Count "+h),l.options&&l.options.pageNumber){var f=n.internal.getPageInfo(l.options.pageNumber);this.line("/Dest ["+f.objId+" 0 R /XYZ 0 "+c(0)+" 0]")}this.objEnd()}for(var g=0;g<a.children.length;g++)this.renderItems(a.children[g])},n.outline.line=function(a){this.ctx.val+=a+`\r
`},n.outline.makeRef=function(a){return a.id+" 0 R"},n.outline.makeString=function(a){return"("+n.internal.pdfEscape(a)+")"},n.outline.objStart=function(a){this.ctx.val+=`\r
`+a.id+` 0 obj\r
<<\r
`},n.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},n.outline.count_r=function(a,c){for(var o=0;o<c.children.length;o++)a.count++,this.count_r(a,c.children[o]);return a.count}}])}(Ut.API),function(i){var e=[192,193,194,195,196,197,198,199];i.processJPEG=function(n,a,c,o,l,h){var f,g=this.decode.DCT_DECODE,x=null;if(typeof n=="string"||this.__addimage__.isArrayBuffer(n)||this.__addimage__.isArrayBufferView(n)){switch(n=l||n,n=this.__addimage__.isArrayBuffer(n)?new Uint8Array(n):n,(f=function(L){for(var S,p=256*L.charCodeAt(4)+L.charCodeAt(5),O=L.length,C={width:0,height:0,numcomponents:1},T=4;T<O;T+=2){if(T+=p,e.indexOf(L.charCodeAt(T+1))!==-1){S=256*L.charCodeAt(T+5)+L.charCodeAt(T+6),C={width:256*L.charCodeAt(T+7)+L.charCodeAt(T+8),height:S,numcomponents:L.charCodeAt(T+9)};break}p=256*L.charCodeAt(T+2)+L.charCodeAt(T+3)}return C}(n=this.__addimage__.isArrayBufferView(n)?this.__addimage__.arrayBufferToBinaryString(n):n)).numcomponents){case 1:h=this.color_spaces.DEVICE_GRAY;break;case 4:h=this.color_spaces.DEVICE_CMYK;break;case 3:h=this.color_spaces.DEVICE_RGB}x={data:n,width:f.width,height:f.height,colorSpace:h,bitsPerComponent:8,filter:g,index:a,alias:c}}return x}}(Ut.API);var Li,so,bc,yc,wc,Ll=function(){var i,e,n;function a(o){var l,h,f,g,x,L,S,p,O,C,T,_,M,$;for(this.data=o,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},L=null;;){switch(l=this.readUInt32(),O=(function(){var st,dt;for(dt=[],st=0;st<4;++st)dt.push(String.fromCharCode(this.data[this.pos++]));return dt}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(l);break;case"fcTL":L&&this.animation.frames.push(L),this.pos+=4,L={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},x=this.readUInt16(),g=this.readUInt16()||100,L.delay=1e3*x/g,L.disposeOp=this.data[this.pos++],L.blendOp=this.data[this.pos++],L.data=[];break;case"IDAT":case"fdAT":for(O==="fdAT"&&(this.pos+=4,l-=4),o=(L!=null?L.data:void 0)||this.imgData,_=0;0<=l?_<l:_>l;0<=l?++_:--_)o.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(f=this.palette.length/3,this.transparency.indexed=this.read(l),this.transparency.indexed.length>f)throw new Error("More transparent colors than palette size");if((C=f-this.transparency.indexed.length)>0)for(M=0;0<=C?M<C:M>C;0<=C?++M:--M)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(l)[0];break;case 2:this.transparency.rgb=this.read(l)}break;case"tEXt":S=(T=this.read(l)).indexOf(0),p=String.fromCharCode.apply(String,T.slice(0,S)),this.text[p]=String.fromCharCode.apply(String,T.slice(S+1));break;case"IEND":return L&&this.animation.frames.push(L),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=($=this.colorType)===4||$===6,h=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*h,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=l}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}a.prototype.read=function(o){var l,h;for(h=[],l=0;0<=o?l<o:l>o;0<=o?++l:--l)h.push(this.data[this.pos++]);return h},a.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.decodePixels=function(o){var l=this.pixelBitlength/8,h=new Uint8Array(this.width*this.height*l),f=0,g=this;if(o==null&&(o=this.imgData),o.length===0)return new Uint8Array(0);function x(L,S,p,O){var C,T,_,M,$,st,dt,Lt,rt,G,vt,bt,k,F,H,D,ct,ot,mt,tt,pt,ft=Math.ceil((g.width-L)/p),Et=Math.ceil((g.height-S)/O),w=g.width==ft&&g.height==Et;for(F=l*ft,bt=w?h:new Uint8Array(F*Et),st=o.length,k=0,T=0;k<Et&&f<st;){switch(o[f++]){case 0:for(M=ct=0;ct<F;M=ct+=1)bt[T++]=o[f++];break;case 1:for(M=ot=0;ot<F;M=ot+=1)C=o[f++],$=M<l?0:bt[T-l],bt[T++]=(C+$)%256;break;case 2:for(M=mt=0;mt<F;M=mt+=1)C=o[f++],_=(M-M%l)/l,H=k&&bt[(k-1)*F+_*l+M%l],bt[T++]=(H+C)%256;break;case 3:for(M=tt=0;tt<F;M=tt+=1)C=o[f++],_=(M-M%l)/l,$=M<l?0:bt[T-l],H=k&&bt[(k-1)*F+_*l+M%l],bt[T++]=(C+Math.floor(($+H)/2))%256;break;case 4:for(M=pt=0;pt<F;M=pt+=1)C=o[f++],_=(M-M%l)/l,$=M<l?0:bt[T-l],k===0?H=D=0:(H=bt[(k-1)*F+_*l+M%l],D=_&&bt[(k-1)*F+(_-1)*l+M%l]),dt=$+H-D,Lt=Math.abs(dt-$),G=Math.abs(dt-H),vt=Math.abs(dt-D),rt=Lt<=G&&Lt<=vt?$:G<=vt?H:D,bt[T++]=(C+rt)%256;break;default:throw new Error("Invalid filter algorithm: "+o[f-1])}if(!w){var j=((S+k*O)*g.width+L)*l,E=k*F;for(M=0;M<ft;M+=1){for(var V=0;V<l;V+=1)h[j++]=bt[E++];j+=(p-1)*l}}k++}}return o=Uu(o),g.interlaceMethod==1?(x(0,0,8,8),x(4,0,8,8),x(0,4,4,8),x(2,0,4,4),x(0,2,2,4),x(1,0,2,2),x(0,1,1,2)):x(0,0,1,1),h},a.prototype.decodePalette=function(){var o,l,h,f,g,x,L,S,p;for(h=this.palette,x=this.transparency.indexed||[],g=new Uint8Array((x.length||0)+h.length),f=0,o=0,l=L=0,S=h.length;L<S;l=L+=3)g[f++]=h[l],g[f++]=h[l+1],g[f++]=h[l+2],g[f++]=(p=x[o++])!=null?p:255;return g},a.prototype.copyToImageData=function(o,l){var h,f,g,x,L,S,p,O,C,T,_;if(f=this.colors,C=null,h=this.hasAlphaChannel,this.palette.length&&(C=(_=this._decodedPalette)!=null?_:this._decodedPalette=this.decodePalette(),f=4,h=!0),O=(g=o.data||o).length,L=C||l,x=S=0,f===1)for(;x<O;)p=C?4*l[x/4]:S,T=L[p++],g[x++]=T,g[x++]=T,g[x++]=T,g[x++]=h?L[p++]:255,S=p;else for(;x<O;)p=C?4*l[x/4]:S,g[x++]=L[p++],g[x++]=L[p++],g[x++]=L[p++],g[x++]=h?L[p++]:255,S=p},a.prototype.decode=function(){var o;return o=new Uint8Array(this.width*this.height*4),this.copyToImageData(o,this.decodePixels()),o};var c=function(){if(Object.prototype.toString.call(zt)==="[object Window]"){try{e=zt.document.createElement("canvas"),n=e.getContext("2d")}catch{return!1}return!0}return!1};return c(),i=function(o){var l;if(c()===!0)return n.width=o.width,n.height=o.height,n.clearRect(0,0,o.width,o.height),n.putImageData(o,0,0),(l=new Image).src=e.toDataURL(),l;throw new Error("This method requires a Browser with Canvas-capability.")},a.prototype.decodeFrames=function(o){var l,h,f,g,x,L,S,p;if(this.animation){for(p=[],h=x=0,L=(S=this.animation.frames).length;x<L;h=++x)l=S[h],f=o.createImageData(l.width,l.height),g=this.decodePixels(new Uint8Array(l.data)),this.copyToImageData(f,g),l.imageData=f,p.push(l.image=i(f));return p}},a.prototype.renderFrame=function(o,l){var h,f,g;return h=(f=this.animation.frames)[l],g=f[l-1],l===0&&o.clearRect(0,0,this.width,this.height),(g!=null?g.disposeOp:void 0)===1?o.clearRect(g.xOffset,g.yOffset,g.width,g.height):(g!=null?g.disposeOp:void 0)===2&&o.putImageData(g.imageData,g.xOffset,g.yOffset),h.blendOp===0&&o.clearRect(h.xOffset,h.yOffset,h.width,h.height),o.drawImage(h.image,h.xOffset,h.yOffset)},a.prototype.animate=function(o){var l,h,f,g,x,L,S=this;return h=0,L=this.animation,g=L.numFrames,f=L.frames,x=L.numPlays,(l=function(){var p,O;if(p=h++%g,O=f[p],S.renderFrame(o,p),g>1&&h/g<x)return S.animation._timeout=setTimeout(l,O.delay)})()},a.prototype.stopAnimation=function(){var o;return clearTimeout((o=this.animation)!=null?o._timeout:void 0)},a.prototype.render=function(o){var l,h;return o._png&&o._png.stopAnimation(),o._png=this,o.width=this.width,o.height=this.height,l=o.getContext("2d"),this.animation?(this.decodeFrames(l),this.animate(l)):(h=l.createImageData(this.width,this.height),this.copyToImageData(h,this.decodePixels()),l.putImageData(h,0,0))},a}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function xl(i){var e=0;if(i[e++]!==71||i[e++]!==73||i[e++]!==70||i[e++]!==56||(i[e++]+1&253)!=56||i[e++]!==97)throw new Error("Invalid GIF 87a/89a header.");var n=i[e++]|i[e++]<<8,a=i[e++]|i[e++]<<8,c=i[e++],o=c>>7,l=1<<(7&c)+1;i[e++],i[e++];var h=null,f=null;o&&(h=e,f=l,e+=3*l);var g=!0,x=[],L=0,S=null,p=0,O=null;for(this.width=n,this.height=a;g&&e<i.length;)switch(i[e++]){case 33:switch(i[e++]){case 255:if(i[e]!==11||i[e+1]==78&&i[e+2]==69&&i[e+3]==84&&i[e+4]==83&&i[e+5]==67&&i[e+6]==65&&i[e+7]==80&&i[e+8]==69&&i[e+9]==50&&i[e+10]==46&&i[e+11]==48&&i[e+12]==3&&i[e+13]==1&&i[e+16]==0)e+=14,O=i[e++]|i[e++]<<8,e++;else for(e+=12;;){if(!((k=i[e++])>=0))throw Error("Invalid block size");if(k===0)break;e+=k}break;case 249:if(i[e++]!==4||i[e+4]!==0)throw new Error("Invalid graphics extension block.");var C=i[e++];L=i[e++]|i[e++]<<8,S=i[e++],!(1&C)&&(S=null),p=C>>2&7,e++;break;case 254:for(;;){if(!((k=i[e++])>=0))throw Error("Invalid block size");if(k===0)break;e+=k}break;default:throw new Error("Unknown graphic control label: 0x"+i[e-1].toString(16))}break;case 44:var T=i[e++]|i[e++]<<8,_=i[e++]|i[e++]<<8,M=i[e++]|i[e++]<<8,$=i[e++]|i[e++]<<8,st=i[e++],dt=st>>6&1,Lt=1<<(7&st)+1,rt=h,G=f,vt=!1;st>>7&&(vt=!0,rt=e,G=Lt,e+=3*Lt);var bt=e;for(e++;;){var k;if(!((k=i[e++])>=0))throw Error("Invalid block size");if(k===0)break;e+=k}x.push({x:T,y:_,width:M,height:$,has_local_palette:vt,palette_offset:rt,palette_size:G,data_offset:bt,data_length:e-bt,transparent_index:S,interlaced:!!dt,delay:L,disposal:p});break;case 59:g=!1;break;default:throw new Error("Unknown gif block: 0x"+i[e-1].toString(16))}this.numFrames=function(){return x.length},this.loopCount=function(){return O},this.frameInfo=function(F){if(F<0||F>=x.length)throw new Error("Frame index out of range.");return x[F]},this.decodeAndBlitFrameBGRA=function(F,H){var D=this.frameInfo(F),ct=D.width*D.height,ot=new Uint8Array(ct);Nc(i,D.data_offset,ot,ct);var mt=D.palette_offset,tt=D.transparent_index;tt===null&&(tt=256);var pt=D.width,ft=n-pt,Et=pt,w=4*(D.y*n+D.x),j=4*((D.y+D.height)*n+D.x),E=w,V=4*ft;D.interlaced===!0&&(V+=4*n*7);for(var J=8,Z=0,et=ot.length;Z<et;++Z){var Q=ot[Z];if(Et===0&&(Et=pt,(E+=V)>=j&&(V=4*ft+4*n*(J-1),E=w+(pt+ft)*(J<<1),J>>=1)),Q===tt)E+=4;else{var xt=i[mt+3*Q],Nt=i[mt+3*Q+1],Ot=i[mt+3*Q+2];H[E++]=Ot,H[E++]=Nt,H[E++]=xt,H[E++]=255}--Et}},this.decodeAndBlitFrameRGBA=function(F,H){var D=this.frameInfo(F),ct=D.width*D.height,ot=new Uint8Array(ct);Nc(i,D.data_offset,ot,ct);var mt=D.palette_offset,tt=D.transparent_index;tt===null&&(tt=256);var pt=D.width,ft=n-pt,Et=pt,w=4*(D.y*n+D.x),j=4*((D.y+D.height)*n+D.x),E=w,V=4*ft;D.interlaced===!0&&(V+=4*n*7);for(var J=8,Z=0,et=ot.length;Z<et;++Z){var Q=ot[Z];if(Et===0&&(Et=pt,(E+=V)>=j&&(V=4*ft+4*n*(J-1),E=w+(pt+ft)*(J<<1),J>>=1)),Q===tt)E+=4;else{var xt=i[mt+3*Q],Nt=i[mt+3*Q+1],Ot=i[mt+3*Q+2];H[E++]=xt,H[E++]=Nt,H[E++]=Ot,H[E++]=255}--Et}}}function Nc(i,e,n,a){for(var c=i[e++],o=1<<c,l=o+1,h=l+1,f=c+1,g=(1<<f)-1,x=0,L=0,S=0,p=i[e++],O=new Int32Array(4096),C=null;;){for(;x<16&&p!==0;)L|=i[e++]<<x,x+=8,p===1?p=i[e++]:--p;if(x<f)break;var T=L&g;if(L>>=f,x-=f,T!==o){if(T===l)break;for(var _=T<h?T:C,M=0,$=_;$>o;)$=O[$]>>8,++M;var st=$;if(S+M+(_!==T?1:0)>a)return void ve.log("Warning, gif stream longer than expected.");n[S++]=st;var dt=S+=M;for(_!==T&&(n[S++]=st),$=_;M--;)$=O[$],n[--dt]=255&$,$>>=8;C!==null&&h<4096&&(O[h++]=C<<8|st,h>=g+1&&f<12&&(++f,g=g<<1|1)),C=T}else h=l+1,g=(1<<(f=c+1))-1,C=null}return S!==a&&ve.log("Warning, gif stream shorter than expected."),n}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function ps(i){var e,n,a,c,o,l=Math.floor,h=new Array(64),f=new Array(64),g=new Array(64),x=new Array(64),L=new Array(65535),S=new Array(65535),p=new Array(64),O=new Array(64),C=[],T=0,_=7,M=new Array(64),$=new Array(64),st=new Array(64),dt=new Array(256),Lt=new Array(2048),rt=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],G=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],vt=[0,1,2,3,4,5,6,7,8,9,10,11],bt=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],k=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],F=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],H=[0,1,2,3,4,5,6,7,8,9,10,11],D=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],ct=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function ot(w,j){for(var E=0,V=0,J=new Array,Z=1;Z<=16;Z++){for(var et=1;et<=w[Z];et++)J[j[V]]=[],J[j[V]][0]=E,J[j[V]][1]=Z,V++,E++;E*=2}return J}function mt(w){for(var j=w[0],E=w[1]-1;E>=0;)j&1<<E&&(T|=1<<_),E--,--_<0&&(T==255?(tt(255),tt(0)):tt(T),_=7,T=0)}function tt(w){C.push(w)}function pt(w){tt(w>>8&255),tt(255&w)}function ft(w,j,E,V,J){for(var Z,et=J[0],Q=J[240],xt=function(wt,At){var kt,Pt,Tt,Gt,Qt,te,ie,de,Ht,ee,Ft=0;for(Ht=0;Ht<8;++Ht){kt=wt[Ft],Pt=wt[Ft+1],Tt=wt[Ft+2],Gt=wt[Ft+3],Qt=wt[Ft+4],te=wt[Ft+5],ie=wt[Ft+6];var Ye=kt+(de=wt[Ft+7]),oe=kt-de,Sn=Pt+ie,ge=Pt-ie,Le=Tt+te,Dn=Tt-te,ce=Gt+Qt,kr=Gt-Qt,Ae=Ye+ce,_n=Ye-ce,Qn=Sn+Le,Se=Sn-Le;wt[Ft]=Ae+Qn,wt[Ft+4]=Ae-Qn;var Jt=.707106781*(Se+_n);wt[Ft+2]=_n+Jt,wt[Ft+6]=_n-Jt;var ue=.382683433*((Ae=kr+Dn)-(Se=ge+oe)),Fr=.5411961*Ae+ue,We=1.306562965*Se+ue,Rn=.707106781*(Qn=Dn+ge),zn=oe+Rn,Rt=oe-Rn;wt[Ft+5]=Rt+Fr,wt[Ft+3]=Rt-Fr,wt[Ft+1]=zn+We,wt[Ft+7]=zn-We,Ft+=8}for(Ft=0,Ht=0;Ht<8;++Ht){kt=wt[Ft],Pt=wt[Ft+8],Tt=wt[Ft+16],Gt=wt[Ft+24],Qt=wt[Ft+32],te=wt[Ft+40],ie=wt[Ft+48];var Pn=kt+(de=wt[Ft+56]),Un=kt-de,an=Pt+ie,De=Pt-ie,Me=Tt+te,fn=Tt-te,Gr=Gt+Qt,tr=Gt-Qt,kn=Pn+Gr,Fn=Pn-Gr,Cn=an+Me,Hn=an-Me;wt[Ft]=kn+Cn,wt[Ft+32]=kn-Cn;var vn=.707106781*(Hn+Fn);wt[Ft+16]=Fn+vn,wt[Ft+48]=Fn-vn;var Vn=.382683433*((kn=tr+fn)-(Hn=De+Un)),Cr=.5411961*kn+Vn,Jr=1.306562965*Hn+Vn,Yr=.707106781*(Cn=fn+De),Xr=Un+Yr,Kr=Un-Yr;wt[Ft+40]=Kr+Cr,wt[Ft+24]=Kr-Cr,wt[Ft+8]=Xr+Jr,wt[Ft+56]=Xr-Jr,Ft++}for(Ht=0;Ht<64;++Ht)ee=wt[Ht]*At[Ht],p[Ht]=ee>0?ee+.5|0:ee-.5|0;return p}(w,j),Nt=0;Nt<64;++Nt)O[rt[Nt]]=xt[Nt];var Ot=O[0]-E;E=O[0],Ot==0?mt(V[0]):(mt(V[S[Z=32767+Ot]]),mt(L[Z]));for(var jt=63;jt>0&&O[jt]==0;)jt--;if(jt==0)return mt(et),E;for(var Vt,at=1;at<=jt;){for(var B=at;O[at]==0&&at<=jt;)++at;var Kt=at-B;if(Kt>=16){Vt=Kt>>4;for(var Mt=1;Mt<=Vt;++Mt)mt(Q);Kt&=15}Z=32767+O[at],mt(J[(Kt<<4)+S[Z]]),mt(L[Z]),at++}return jt!=63&&mt(et),E}function Et(w){w=Math.min(Math.max(w,1),100),o!=w&&(function(j){for(var E=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],V=0;V<64;V++){var J=l((E[V]*j+50)/100);J=Math.min(Math.max(J,1),255),h[rt[V]]=J}for(var Z=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],et=0;et<64;et++){var Q=l((Z[et]*j+50)/100);Q=Math.min(Math.max(Q,1),255),f[rt[et]]=Q}for(var xt=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Nt=0,Ot=0;Ot<8;Ot++)for(var jt=0;jt<8;jt++)g[Nt]=1/(h[rt[Nt]]*xt[Ot]*xt[jt]*8),x[Nt]=1/(f[rt[Nt]]*xt[Ot]*xt[jt]*8),Nt++}(w<50?Math.floor(5e3/w):Math.floor(200-2*w)),o=w)}this.encode=function(w,j){j&&Et(j),C=new Array,T=0,_=7,pt(65496),pt(65504),pt(16),tt(74),tt(70),tt(73),tt(70),tt(0),tt(1),tt(1),tt(0),pt(1),pt(1),tt(0),tt(0),function(){pt(65499),pt(132),tt(0);for(var Pt=0;Pt<64;Pt++)tt(h[Pt]);tt(1);for(var Tt=0;Tt<64;Tt++)tt(f[Tt])}(),function(Pt,Tt){pt(65472),pt(17),tt(8),pt(Tt),pt(Pt),tt(3),tt(1),tt(17),tt(0),tt(2),tt(17),tt(1),tt(3),tt(17),tt(1)}(w.width,w.height),function(){pt(65476),pt(418),tt(0);for(var Pt=0;Pt<16;Pt++)tt(G[Pt+1]);for(var Tt=0;Tt<=11;Tt++)tt(vt[Tt]);tt(16);for(var Gt=0;Gt<16;Gt++)tt(bt[Gt+1]);for(var Qt=0;Qt<=161;Qt++)tt(k[Qt]);tt(1);for(var te=0;te<16;te++)tt(F[te+1]);for(var ie=0;ie<=11;ie++)tt(H[ie]);tt(17);for(var de=0;de<16;de++)tt(D[de+1]);for(var Ht=0;Ht<=161;Ht++)tt(ct[Ht])}(),pt(65498),pt(12),tt(3),tt(1),tt(0),tt(2),tt(17),tt(3),tt(17),tt(0),tt(63),tt(0);var E=0,V=0,J=0;T=0,_=7,this.encode.displayName="_encode_";for(var Z,et,Q,xt,Nt,Ot,jt,Vt,at,B=w.data,Kt=w.width,Mt=w.height,wt=4*Kt,At=0;At<Mt;){for(Z=0;Z<wt;){for(Nt=wt*At+Z,jt=-1,Vt=0,at=0;at<64;at++)Ot=Nt+(Vt=at>>3)*wt+(jt=4*(7&at)),At+Vt>=Mt&&(Ot-=wt*(At+1+Vt-Mt)),Z+jt>=wt&&(Ot-=Z+jt-wt+4),et=B[Ot++],Q=B[Ot++],xt=B[Ot++],M[at]=(Lt[et]+Lt[Q+256>>0]+Lt[xt+512>>0]>>16)-128,$[at]=(Lt[et+768>>0]+Lt[Q+1024>>0]+Lt[xt+1280>>0]>>16)-128,st[at]=(Lt[et+1280>>0]+Lt[Q+1536>>0]+Lt[xt+1792>>0]>>16)-128;E=ft(M,g,E,e,a),V=ft($,x,V,n,c),J=ft(st,x,J,n,c),Z+=32}At+=8}if(_>=0){var kt=[];kt[1]=_+1,kt[0]=(1<<_+1)-1,mt(kt)}return pt(65497),new Uint8Array(C)},i=i||50,function(){for(var w=String.fromCharCode,j=0;j<256;j++)dt[j]=w(j)}(),e=ot(G,vt),n=ot(F,H),a=ot(bt,k),c=ot(D,ct),function(){for(var w=1,j=2,E=1;E<=15;E++){for(var V=w;V<j;V++)S[32767+V]=E,L[32767+V]=[],L[32767+V][1]=E,L[32767+V][0]=V;for(var J=-(j-1);J<=-w;J++)S[32767+J]=E,L[32767+J]=[],L[32767+J][1]=E,L[32767+J][0]=j-1+J;w<<=1,j<<=1}}(),function(){for(var w=0;w<256;w++)Lt[w]=19595*w,Lt[w+256>>0]=38470*w,Lt[w+512>>0]=7471*w+32768,Lt[w+768>>0]=-11059*w,Lt[w+1024>>0]=-21709*w,Lt[w+1280>>0]=32768*w+8421375,Lt[w+1536>>0]=-27439*w,Lt[w+1792>>0]=-5329*w}(),Et(i)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function En(i,e){if(this.pos=0,this.buffer=i,this.datav=new DataView(i.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function Lc(i){function e(G){if(!G)throw Error("assert :P")}function n(G,vt,bt){for(var k=0;4>k;k++)if(G[vt+k]!=bt.charCodeAt(k))return!0;return!1}function a(G,vt,bt,k,F){for(var H=0;H<F;H++)G[vt+H]=bt[k+H]}function c(G,vt,bt,k){for(var F=0;F<k;F++)G[vt+F]=bt}function o(G){return new Int32Array(G)}function l(G,vt){for(var bt=[],k=0;k<G;k++)bt.push(new vt);return bt}function h(G,vt){var bt=[];return function k(F,H,D){for(var ct=D[H],ot=0;ot<ct&&(F.push(D.length>H+1?[]:new vt),!(D.length<H+1));ot++)k(F[ot],H+1,D)}(bt,0,G),bt}var f=function(){var G=this;function vt(t,r){for(var u=1<<r-1>>>0;t&u;)u>>>=1;return u?(t&u-1)+u:t}function bt(t,r,u,d,m){e(!(d%u));do t[r+(d-=u)]=m;while(0<d)}function k(t,r,u,d,m){if(e(2328>=m),512>=m)var b=o(512);else if((b=o(m))==null)return 0;return function(y,N,A,P,R,X){var K,W,ht=N,nt=1<<A,z=o(16),U=o(16);for(e(R!=0),e(P!=null),e(y!=null),e(0<A),W=0;W<R;++W){if(15<P[W])return 0;++z[P[W]]}if(z[0]==R)return 0;for(U[1]=0,K=1;15>K;++K){if(z[K]>1<<K)return 0;U[K+1]=U[K]+z[K]}for(W=0;W<R;++W)K=P[W],0<P[W]&&(X[U[K]++]=W);if(U[15]==1)return(P=new F).g=0,P.value=X[0],bt(y,ht,1,nt,P),nt;var ut,gt=-1,lt=nt-1,It=0,St=1,Dt=1,_t=1<<A;for(W=0,K=1,R=2;K<=A;++K,R<<=1){if(St+=Dt<<=1,0>(Dt-=z[K]))return 0;for(;0<z[K];--z[K])(P=new F).g=K,P.value=X[W++],bt(y,ht+It,R,_t,P),It=vt(It,K)}for(K=A+1,R=2;15>=K;++K,R<<=1){if(St+=Dt<<=1,0>(Dt-=z[K]))return 0;for(;0<z[K];--z[K]){if(P=new F,(It&lt)!=gt){for(ht+=_t,ut=1<<(gt=K)-A;15>gt&&!(0>=(ut-=z[gt]));)++gt,ut<<=1;nt+=_t=1<<(ut=gt-A),y[N+(gt=It&lt)].g=ut+A,y[N+gt].value=ht-N-gt}P.g=K-A,P.value=X[W++],bt(y,ht+(It>>A),R,_t,P),It=vt(It,K)}}return St!=2*U[15]-1?0:nt}(t,r,u,d,m,b)}function F(){this.value=this.g=0}function H(){this.value=this.g=0}function D(){this.G=l(5,F),this.H=o(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=l(Ge,H)}function ct(t,r,u,d){e(t!=null),e(r!=null),e(2147483648>d),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=r,t.pa=u,t.Jd=r,t.Yc=u+d,t.Zc=4<=d?u+d-4+1:u,Z(t)}function ot(t,r){for(var u=0;0<r--;)u|=Q(t,128)<<r;return u}function mt(t,r){var u=ot(t,r);return et(t)?-u:u}function tt(t,r,u,d){var m,b=0;for(e(t!=null),e(r!=null),e(4294967288>d),t.Sb=d,t.Ra=0,t.u=0,t.h=0,4<d&&(d=4),m=0;m<d;++m)b+=r[u+m]<<8*m;t.Ra=b,t.bb=d,t.oa=r,t.pa=u}function pt(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<hi-8>>>0,++t.bb,t.u-=8;E(t)&&(t.h=1,t.u=0)}function ft(t,r){if(e(0<=r),!t.h&&r<=li){var u=j(t)&ui[r];return t.u+=r,pt(t),u}return t.h=1,t.u=0}function Et(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function w(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function j(t){return t.Ra>>>(t.u&hi-1)>>>0}function E(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>hi}function V(t,r){t.u=r,t.h=E(t)}function J(t){t.u>=Zi&&(e(t.u>=Zi),pt(t))}function Z(t){e(t!=null&&t.oa!=null),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(t!=null&&t.oa!=null),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function et(t){return ot(t,1)}function Q(t,r){var u=t.Ca;0>t.b&&Z(t);var d=t.b,m=u*r>>>8,b=(t.I>>>d>m)+0;for(b?(u-=m,t.I-=m+1<<d>>>0):u=m+1,d=u,m=0;256<=d;)m+=8,d>>=8;return d=7^m+sn[d],t.b-=d,t.Ca=(u<<d)-1,b}function xt(t,r,u){t[r+0]=u>>24&255,t[r+1]=u>>16&255,t[r+2]=u>>8&255,t[r+3]=u>>0&255}function Nt(t,r){return t[r+0]<<0|t[r+1]<<8}function Ot(t,r){return Nt(t,r)|t[r+2]<<16}function jt(t,r){return Nt(t,r)|Nt(t,r+2)<<16}function Vt(t,r){var u=1<<r;return e(t!=null),e(0<r),t.X=o(u),t.X==null?0:(t.Mb=32-r,t.Xa=r,1)}function at(t,r){e(t!=null),e(r!=null),e(t.Xa==r.Xa),a(r.X,0,t.X,0,1<<r.Xa)}function B(){this.X=[],this.Xa=this.Mb=0}function Kt(t,r,u,d){e(u!=null),e(d!=null);var m=u[0],b=d[0];return m==0&&(m=(t*b+r/2)/r),b==0&&(b=(r*m+t/2)/t),0>=m||0>=b?0:(u[0]=m,d[0]=b,1)}function Mt(t,r){return t+(1<<r)-1>>>r}function wt(t,r){return((4278255360&t)+(4278255360&r)>>>0&4278255360)+((16711935&t)+(16711935&r)>>>0&16711935)>>>0}function At(t,r){G[r]=function(u,d,m,b,y,N,A){var P;for(P=0;P<y;++P){var R=G[t](N[A+P-1],m,b+P);N[A+P]=wt(u[d+P],R)}}}function kt(){this.ud=this.hd=this.jd=0}function Pt(t,r){return((4278124286&(t^r))>>>1)+(t&r)>>>0}function Tt(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function Gt(t,r){return Tt(t+(t-r+.5>>1))}function Qt(t,r,u){return Math.abs(r-u)-Math.abs(t-u)}function te(t,r,u,d,m,b,y){for(d=b[y-1],u=0;u<m;++u)b[y+u]=d=wt(t[r+u],d)}function ie(t,r,u,d,m){var b;for(b=0;b<u;++b){var y=t[r+b],N=y>>8&255,A=16711935&(A=(A=16711935&y)+((N<<16)+N));d[m+b]=(4278255360&y)+A>>>0}}function de(t,r){r.jd=t>>0&255,r.hd=t>>8&255,r.ud=t>>16&255}function Ht(t,r,u,d,m,b){var y;for(y=0;y<d;++y){var N=r[u+y],A=N>>>8,P=N,R=255&(R=(R=N>>>16)+((t.jd<<24>>24)*(A<<24>>24)>>>5));P=255&(P=(P=P+((t.hd<<24>>24)*(A<<24>>24)>>>5))+((t.ud<<24>>24)*(R<<24>>24)>>>5)),m[b+y]=(4278255360&N)+(R<<16)+P}}function ee(t,r,u,d,m){G[r]=function(b,y,N,A,P,R,X,K,W){for(A=X;A<K;++A)for(X=0;X<W;++X)P[R++]=m(N[d(b[y++])])},G[t]=function(b,y,N,A,P,R,X){var K=8>>b.b,W=b.Ea,ht=b.K[0],nt=b.w;if(8>K)for(b=(1<<b.b)-1,nt=(1<<K)-1;y<N;++y){var z,U=0;for(z=0;z<W;++z)z&b||(U=d(A[P++])),R[X++]=m(ht[U&nt]),U>>=K}else G["VP8LMapColor"+u](A,P,ht,nt,R,X,y,N,W)}}function Ft(t,r,u,d,m){for(u=r+u;r<u;){var b=t[r++];d[m++]=b>>16&255,d[m++]=b>>8&255,d[m++]=b>>0&255}}function Ye(t,r,u,d,m){for(u=r+u;r<u;){var b=t[r++];d[m++]=b>>16&255,d[m++]=b>>8&255,d[m++]=b>>0&255,d[m++]=b>>24&255}}function oe(t,r,u,d,m){for(u=r+u;r<u;){var b=(y=t[r++])>>16&240|y>>12&15,y=y>>0&240|y>>28&15;d[m++]=b,d[m++]=y}}function Sn(t,r,u,d,m){for(u=r+u;r<u;){var b=(y=t[r++])>>16&248|y>>13&7,y=y>>5&224|y>>3&31;d[m++]=b,d[m++]=y}}function ge(t,r,u,d,m){for(u=r+u;r<u;){var b=t[r++];d[m++]=b>>0&255,d[m++]=b>>8&255,d[m++]=b>>16&255}}function Le(t,r,u,d,m,b){if(b==0)for(u=r+u;r<u;)xt(d,((b=t[r++])[0]>>24|b[1]>>8&65280|b[2]<<8&16711680|b[3]<<24)>>>0),m+=32;else a(d,m,t,r,u)}function Dn(t,r){G[r][0]=G[t+"0"],G[r][1]=G[t+"1"],G[r][2]=G[t+"2"],G[r][3]=G[t+"3"],G[r][4]=G[t+"4"],G[r][5]=G[t+"5"],G[r][6]=G[t+"6"],G[r][7]=G[t+"7"],G[r][8]=G[t+"8"],G[r][9]=G[t+"9"],G[r][10]=G[t+"10"],G[r][11]=G[t+"11"],G[r][12]=G[t+"12"],G[r][13]=G[t+"13"],G[r][14]=G[t+"0"],G[r][15]=G[t+"0"]}function ce(t){return t==Wo||t==Go||t==Va||t==Jo}function kr(){this.eb=[],this.size=this.A=this.fb=0}function Ae(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function _n(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new kr,this.f.kb=new Ae,this.sd=null}function Qn(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function Se(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function Jt(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function ue(t,r){var u=t.T,d=r.ba.f.RGBA,m=d.eb,b=d.fb+t.ka*d.A,y=xn[r.ba.S],N=t.y,A=t.O,P=t.f,R=t.N,X=t.ea,K=t.W,W=r.cc,ht=r.dc,nt=r.Mc,z=r.Nc,U=t.ka,ut=t.ka+t.T,gt=t.U,lt=gt+1>>1;for(U==0?y(N,A,null,null,P,R,X,K,P,R,X,K,m,b,null,null,gt):(y(r.ec,r.fc,N,A,W,ht,nt,z,P,R,X,K,m,b-d.A,m,b,gt),++u);U+2<ut;U+=2)W=P,ht=R,nt=X,z=K,R+=t.Rc,K+=t.Rc,b+=2*d.A,y(N,(A+=2*t.fa)-t.fa,N,A,W,ht,nt,z,P,R,X,K,m,b-d.A,m,b,gt);return A+=t.fa,t.j+ut<t.o?(a(r.ec,r.fc,N,A,gt),a(r.cc,r.dc,P,R,lt),a(r.Mc,r.Nc,X,K,lt),u--):1&ut||y(N,A,null,null,P,R,X,K,P,R,X,K,m,b+d.A,null,null,gt),u}function Fr(t,r,u){var d=t.F,m=[t.J];if(d!=null){var b=t.U,y=r.ba.S,N=y==Ha||y==Va;r=r.ba.f.RGBA;var A=[0],P=t.ka;A[0]=t.T,t.Kb&&(P==0?--A[0]:(--P,m[0]-=t.width),t.j+t.ka+t.T==t.o&&(A[0]=t.o-t.j-P));var R=r.eb;P=r.fb+P*r.A,t=Ne(d,m[0],t.width,b,A,R,P+(N?0:3),r.A),e(u==A),t&&ce(y)&&Nn(R,P,N,b,A,r.A)}return 0}function We(t){var r=t.ma,u=r.ba.S,d=11>u,m=u==za||u==Ua||u==Ha||u==Vo||u==12||ce(u);if(r.memory=null,r.Ib=null,r.Jb=null,r.Nd=null,!Ki(r.Oa,t,m?11:12))return 0;if(m&&ce(u)&&yt(),t.da)alert("todo:use_scaling");else{if(d){if(r.Ib=Jt,t.Kb){if(u=t.U+1>>1,r.memory=o(t.U+2*u),r.memory==null)return 0;r.ec=r.memory,r.fc=0,r.cc=r.ec,r.dc=r.fc+t.U,r.Mc=r.cc,r.Nc=r.dc+u,r.Ib=ue,yt()}}else alert("todo:EmitYUV");m&&(r.Jb=Fr,d&&Y())}if(d&&!Ts){for(t=0;256>t;++t)nu[t]=89858*(t-128)+Ga>>Wa,au[t]=-22014*(t-128)+Ga,iu[t]=-45773*(t-128),ru[t]=113618*(t-128)+Ga>>Wa;for(t=aa;t<Ko;++t)r=76283*(t-16)+Ga>>Wa,ou[t-aa]=dn(r,255),su[t-aa]=dn(r+8>>4,15);Ts=1}return 1}function Rn(t){var r=t.ma,u=t.U,d=t.T;return e(!(1&t.ka)),0>=u||0>=d?0:(u=r.Ib(t,r),r.Jb!=null&&r.Jb(t,r,u),r.Dc+=u,1)}function zn(t){t.ma.memory=null}function Rt(t,r,u,d){return ft(t,8)!=47?0:(r[0]=ft(t,14)+1,u[0]=ft(t,14)+1,d[0]=ft(t,1),ft(t,3)!=0?0:!t.h)}function Pn(t,r){if(4>t)return t+1;var u=t-2>>1;return(2+(1&t)<<u)+ft(r,u)+1}function Un(t,r){return 120<r?r-120:1<=(u=((u=Uc[r-1])>>4)*t+(8-(15&u)))?u:1;var u}function an(t,r,u){var d=j(u),m=t[r+=255&d].g-8;return 0<m&&(V(u,u.u+8),d=j(u),r+=t[r].value,r+=d&(1<<m)-1),V(u,u.u+t[r].g),t[r].value}function De(t,r,u){return u.g+=t.g,u.value+=t.value<<r>>>0,e(8>=u.g),t.g}function Me(t,r,u){var d=t.xc;return e((r=d==0?0:t.vc[t.md*(u>>d)+(r>>d)])<t.Wb),t.Ya[r]}function fn(t,r,u,d){var m=t.ab,b=t.c*r,y=t.C;r=y+r;var N=u,A=d;for(d=t.Ta,u=t.Ua;0<m--;){var P=t.gc[m],R=y,X=r,K=N,W=A,ht=(A=d,N=u,P.Ea);switch(e(R<X),e(X<=P.nc),P.hc){case 2:Ba(K,W,(X-R)*ht,A,N);break;case 0:var nt=R,z=X,U=A,ut=N,gt=(_t=P).Ea;nt==0&&(Uo(K,W,null,null,1,U,ut),te(K,W+1,0,0,gt-1,U,ut+1),W+=gt,ut+=gt,++nt);for(var lt=1<<_t.b,It=lt-1,St=Mt(gt,_t.b),Dt=_t.K,_t=_t.w+(nt>>_t.b)*St;nt<z;){var se=Dt,le=_t,ae=1;for(Qi(K,W,U,ut-gt,1,U,ut);ae<gt;){var ne=(ae&~It)+lt;ne>gt&&(ne=gt),(0,dr[se[le++]>>8&15])(K,W+ +ae,U,ut+ae-gt,ne-ae,U,ut+ae),ae=ne}W+=gt,ut+=gt,++nt&It||(_t+=St)}X!=P.nc&&a(A,N-ht,A,N+(X-R-1)*ht,ht);break;case 1:for(ht=K,z=W,gt=(K=P.Ea)-(ut=K&~(U=(W=1<<P.b)-1)),nt=Mt(K,P.b),lt=P.K,P=P.w+(R>>P.b)*nt;R<X;){for(It=lt,St=P,Dt=new kt,_t=z+ut,se=z+K;z<_t;)de(It[St++],Dt),Mr(Dt,ht,z,W,A,N),z+=W,N+=W;z<se&&(de(It[St++],Dt),Mr(Dt,ht,z,gt,A,N),z+=gt,N+=gt),++R&U||(P+=nt)}break;case 3:if(K==A&&W==N&&0<P.b){for(z=A,K=ht=N+(X-R)*ht-(ut=(X-R)*Mt(P.Ea,P.b)),W=A,U=N,nt=[],ut=(gt=ut)-1;0<=ut;--ut)nt[ut]=W[U+ut];for(ut=gt-1;0<=ut;--ut)z[K+ut]=nt[ut];bn(P,R,X,A,ht,A,N)}else bn(P,R,X,K,W,A,N)}N=d,A=u}A!=u&&a(d,u,N,A,b)}function Gr(t,r){var u=t.V,d=t.Ba+t.c*t.C,m=r-t.C;if(e(r<=t.l.o),e(16>=m),0<m){var b=t.l,y=t.Ta,N=t.Ua,A=b.width;if(fn(t,m,u,d),m=N=[N],e((u=t.C)<(d=r)),e(b.v<b.va),d>b.o&&(d=b.o),u<b.j){var P=b.j-u;u=b.j,m[0]+=P*A}if(u>=d?u=0:(m[0]+=4*b.v,b.ka=u-b.j,b.U=b.va-b.v,b.T=d-u,u=1),u){if(N=N[0],11>(u=t.ca).S){var R=u.f.RGBA,X=(d=u.S,m=b.U,b=b.T,P=R.eb,R.A),K=b;for(R=R.fb+t.Ma*R.A;0<K--;){var W=y,ht=N,nt=m,z=P,U=R;switch(d){case Ra:cn(W,ht,nt,z,U);break;case za:en(W,ht,nt,z,U);break;case Wo:en(W,ht,nt,z,U),Nn(z,U,0,nt,1,0);break;case Ps:ir(W,ht,nt,z,U);break;case Ua:Le(W,ht,nt,z,U,1);break;case Go:Le(W,ht,nt,z,U,1),Nn(z,U,0,nt,1,0);break;case Ha:Le(W,ht,nt,z,U,0);break;case Va:Le(W,ht,nt,z,U,0),Nn(z,U,1,nt,1,0);break;case Vo:pr(W,ht,nt,z,U);break;case Jo:pr(W,ht,nt,z,U),we(z,U,nt,1,0);break;case ks:rr(W,ht,nt,z,U);break;default:e(0)}N+=A,R+=X}t.Ma+=b}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=u.height)}}t.C=r,e(t.C<=t.i)}function tr(t){var r;if(0<t.ua)return 0;for(r=0;r<t.Wb;++r){var u=t.Ya[r].G,d=t.Ya[r].H;if(0<u[1][d[1]+0].g||0<u[2][d[2]+0].g||0<u[3][d[3]+0].g)return 0}return 1}function kn(t,r,u,d,m,b){if(t.Z!=0){var y=t.qd,N=t.rd;for(e(br[t.Z]!=null);r<u;++r)br[t.Z](y,N,d,m,d,m,b),y=d,N=m,m+=b;t.qd=y,t.rd=N}}function Fn(t,r){var u=t.l.ma,d=u.Z==0||u.Z==1?t.l.j:t.C;if(d=t.C<d?d:t.C,e(r<=t.l.o),r>d){var m=t.l.width,b=u.ca,y=u.tb+m*d,N=t.V,A=t.Ba+t.c*d,P=t.gc;e(t.ab==1),e(P[0].hc==3),Ma(P[0],d,r,N,A,b,y),kn(u,d,r,b,y,m)}t.C=t.Ma=r}function Cn(t,r,u,d,m,b,y){var N=t.$/d,A=t.$%d,P=t.m,R=t.s,X=u+t.$,K=X;m=u+d*m;var W=u+d*b,ht=280+R.ua,nt=t.Pb?N:16777216,z=0<R.ua?R.Wa:null,U=R.wc,ut=X<W?Me(R,A,N):null;e(t.C<b),e(W<=m);var gt=!1;t:for(;;){for(;gt||X<W;){var lt=0;if(N>=nt){var It=X-u;e((nt=t).Pb),nt.wd=nt.m,nt.xd=It,0<nt.s.ua&&at(nt.s.Wa,nt.s.vb),nt=N+Vc}if(A&U||(ut=Me(R,A,N)),e(ut!=null),ut.Qb&&(r[X]=ut.qb,gt=!0),!gt)if(J(P),ut.jc){lt=P,It=r;var St=X,Dt=ut.pd[j(lt)&Ge-1];e(ut.jc),256>Dt.g?(V(lt,lt.u+Dt.g),It[St]=Dt.value,lt=0):(V(lt,lt.u+Dt.g-256),e(256<=Dt.value),lt=Dt.value),lt==0&&(gt=!0)}else lt=an(ut.G[0],ut.H[0],P);if(P.h)break;if(gt||256>lt){if(!gt)if(ut.nd)r[X]=(ut.qb|lt<<8)>>>0;else{if(J(P),gt=an(ut.G[1],ut.H[1],P),J(P),It=an(ut.G[2],ut.H[2],P),St=an(ut.G[3],ut.H[3],P),P.h)break;r[X]=(St<<24|gt<<16|lt<<8|It)>>>0}if(gt=!1,++X,++A>=d&&(A=0,++N,y!=null&&N<=b&&!(N%16)&&y(t,N),z!=null))for(;K<X;)lt=r[K++],z.X[(506832829*lt&**********)>>>z.Mb]=lt}else if(280>lt){if(lt=Pn(lt-256,P),It=an(ut.G[4],ut.H[4],P),J(P),It=Un(d,It=Pn(It,P)),P.h)break;if(X-u<It||m-X<lt)break t;for(St=0;St<lt;++St)r[X+St]=r[X+St-It];for(X+=lt,A+=lt;A>=d;)A-=d,++N,y!=null&&N<=b&&!(N%16)&&y(t,N);if(e(X<=m),A&U&&(ut=Me(R,A,N)),z!=null)for(;K<X;)lt=r[K++],z.X[(506832829*lt&**********)>>>z.Mb]=lt}else{if(!(lt<ht))break t;for(gt=lt-280,e(z!=null);K<X;)lt=r[K++],z.X[(506832829*lt&**********)>>>z.Mb]=lt;lt=X,e(!(gt>>>(It=z).Xa)),r[lt]=It.X[gt],gt=!0}gt||e(P.h==E(P))}if(t.Pb&&P.h&&X<m)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&at(t.s.vb,t.s.Wa);else{if(P.h)break t;y!=null&&y(t,N>b?b:N),t.a=0,t.$=X-u}return 1}return t.a=3,0}function Hn(t){e(t!=null),t.vc=null,t.yc=null,t.Ya=null;var r=t.Wa;r!=null&&(r.X=null),t.vb=null,e(t!=null)}function vn(){var t=new zo;return t==null?null:(t.a=0,t.xb=Is,Dn("Predictor","VP8LPredictors"),Dn("Predictor","VP8LPredictors_C"),Dn("PredictorAdd","VP8LPredictorsAdd"),Dn("PredictorAdd","VP8LPredictorsAdd_C"),Ba=ie,Mr=Ht,cn=Ft,en=Ye,pr=oe,rr=Sn,ir=ge,G.VP8LMapColor32b=fi,G.VP8LMapColor8b=Ea,t)}function Vn(t,r,u,d,m){var b=1,y=[t],N=[r],A=d.m,P=d.s,R=null,X=0;t:for(;;){if(u)for(;b&&ft(A,1);){var K=y,W=N,ht=d,nt=1,z=ht.m,U=ht.gc[ht.ab],ut=ft(z,2);if(ht.Oc&1<<ut)b=0;else{switch(ht.Oc|=1<<ut,U.hc=ut,U.Ea=K[0],U.nc=W[0],U.K=[null],++ht.ab,e(4>=ht.ab),ut){case 0:case 1:U.b=ft(z,3)+2,nt=Vn(Mt(U.Ea,U.b),Mt(U.nc,U.b),0,ht,U.K),U.K=U.K[0];break;case 3:var gt,lt=ft(z,8)+1,It=16<lt?0:4<lt?1:2<lt?2:3;if(K[0]=Mt(U.Ea,It),U.b=It,gt=nt=Vn(lt,1,0,ht,U.K)){var St,Dt=lt,_t=U,se=1<<(8>>_t.b),le=o(se);if(le==null)gt=0;else{var ae=_t.K[0],ne=_t.w;for(le[0]=_t.K[0][0],St=1;St<1*Dt;++St)le[St]=wt(ae[ne+St],le[St-1]);for(;St<4*se;++St)le[St]=0;_t.K[0]=null,_t.K[0]=le,gt=1}}nt=gt;break;case 2:break;default:e(0)}b=nt}}if(y=y[0],N=N[0],b&&ft(A,1)&&!(b=1<=(X=ft(A,4))&&11>=X)){d.a=3;break t}var me;if(me=b)e:{var pe,Zt,Te,un=d,qe=y,ln=N,he=X,gn=u,mn=un.m,Ue=un.s,Je=[null],rn=1,An=0,Yn=Hc[he];n:for(;;){if(gn&&ft(mn,1)){var He=ft(mn,3)+2,cr=Mt(qe,He),Rr=Mt(ln,He),vi=cr*Rr;if(!Vn(cr,Rr,0,un,Je))break n;for(Je=Je[0],Ue.xc=He,pe=0;pe<vi;++pe){var yr=Je[pe]>>8&65535;Je[pe]=yr,yr>=rn&&(rn=yr+1)}}if(mn.h)break n;for(Zt=0;5>Zt;++Zt){var Pe=Fs[Zt];!Zt&&0<he&&(Pe+=1<<he),An<Pe&&(An=Pe)}var $o=l(rn*Yn,F),Rs=rn,zs=l(Rs,D);if(zs==null)var Ya=null;else e(65536>=Rs),Ya=zs;var oa=o(An);if(Ya==null||oa==null||$o==null){un.a=1;break n}var Xa=$o;for(pe=Te=0;pe<rn;++pe){var Mn=Ya[pe],bi=Mn.G,yi=Mn.H,Us=0,Ka=1,Hs=0;for(Zt=0;5>Zt;++Zt){Pe=Fs[Zt],bi[Zt]=Xa,yi[Zt]=Te,!Zt&&0<he&&(Pe+=1<<he);i:{var $a,Zo=Pe,Za=un,sa=oa,lu=Xa,hu=Te,Qo=0,wr=Za.m,fu=ft(wr,1);if(c(sa,0,0,Zo),fu){var du=ft(wr,1)+1,pu=ft(wr,1),Vs=ft(wr,pu==0?1:8);sa[Vs]=1,du==2&&(sa[Vs=ft(wr,8)]=1);var Qa=1}else{var Ws=o(19),Gs=ft(wr,4)+4;if(19<Gs){Za.a=3;var to=0;break i}for($a=0;$a<Gs;++$a)Ws[zc[$a]]=ft(wr,3);var ts=void 0,ca=void 0,Js=Za,gu=Ws,eo=Zo,Ys=sa,es=0,Nr=Js.m,Xs=8,Ks=l(128,F);r:for(;k(Ks,0,7,gu,19);){if(ft(Nr,1)){var mu=2+2*ft(Nr,3);if((ts=2+ft(Nr,mu))>eo)break r}else ts=eo;for(ca=0;ca<eo&&ts--;){J(Nr);var $s=Ks[0+(127&j(Nr))];V(Nr,Nr.u+$s.g);var wi=$s.value;if(16>wi)Ys[ca++]=wi,wi!=0&&(Xs=wi);else{var vu=wi==16,Zs=wi-16,bu=Dc[Zs],Qs=ft(Nr,qc[Zs])+bu;if(ca+Qs>eo)break r;for(var yu=vu?Xs:0;0<Qs--;)Ys[ca++]=yu}}es=1;break r}es||(Js.a=3),Qa=es}(Qa=Qa&&!wr.h)&&(Qo=k(lu,hu,8,sa,Zo)),Qa&&Qo!=0?to=Qo:(Za.a=3,to=0)}if(to==0)break n;if(Ka&&Rc[Zt]==1&&(Ka=Xa[Te].g==0),Us+=Xa[Te].g,Te+=to,3>=Zt){var ua,ns=oa[0];for(ua=1;ua<Pe;++ua)oa[ua]>ns&&(ns=oa[ua]);Hs+=ns}}if(Mn.nd=Ka,Mn.Qb=0,Ka&&(Mn.qb=(bi[3][yi[3]+0].value<<24|bi[1][yi[1]+0].value<<16|bi[2][yi[2]+0].value)>>>0,Us==0&&256>bi[0][yi[0]+0].value&&(Mn.Qb=1,Mn.qb+=bi[0][yi[0]+0].value<<8)),Mn.jc=!Mn.Qb&&6>Hs,Mn.jc){var no,ur=Mn;for(no=0;no<Ge;++no){var Lr=no,xr=ur.pd[Lr],ro=ur.G[0][ur.H[0]+Lr];256<=ro.value?(xr.g=ro.g+256,xr.value=ro.value):(xr.g=0,xr.value=0,Lr>>=De(ro,8,xr),Lr>>=De(ur.G[1][ur.H[1]+Lr],16,xr),Lr>>=De(ur.G[2][ur.H[2]+Lr],0,xr),De(ur.G[3][ur.H[3]+Lr],24,xr))}}}Ue.vc=Je,Ue.Wb=rn,Ue.Ya=Ya,Ue.yc=$o,me=1;break e}me=0}if(!(b=me)){d.a=3;break t}if(0<X){if(P.ua=1<<X,!Vt(P.Wa,X)){d.a=1,b=0;break t}}else P.ua=0;var rs=d,tc=y,wu=N,is=rs.s,as=is.xc;if(rs.c=tc,rs.i=wu,is.md=Mt(tc,as),is.wc=as==0?-1:(1<<as)-1,u){d.xb=$c;break t}if((R=o(y*N))==null){d.a=1,b=0;break t}b=(b=Cn(d,R,0,y,N,N,null))&&!A.h;break t}return b?(m!=null?m[0]=R:(e(R==null),e(u)),d.$=0,u||Hn(P)):Hn(P),b}function Cr(t,r){var u=t.c*t.i,d=u+r+16*r;return e(t.c<=r),t.V=o(d),t.V==null?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+u+r,1)}function Jr(t,r){var u=t.C,d=r-u,m=t.V,b=t.Ba+t.c*u;for(e(r<=t.l.o);0<d;){var y=16<d?16:d,N=t.l.ma,A=t.l.width,P=A*y,R=N.ca,X=N.tb+A*u,K=t.Ta,W=t.Ua;fn(t,y,m,b),Ie(K,W,R,X,P),kn(N,u,u+y,R,X,A),d-=y,m+=y*t.c,u+=y}e(u==r),t.C=t.Ma=r}function Yr(){this.ub=this.yd=this.td=this.Rb=0}function Xr(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Kr(){this.Fb=this.Bb=this.Cb=0,this.Zb=o(4),this.Lb=o(4)}function pa(){this.Yb=function(){var t=[];return function r(u,d,m){for(var b=m[d],y=0;y<b&&(u.push(m.length>d+1?[]:0),!(m.length<d+1));y++)r(u[y],d+1,m)}(t,0,[3,11]),t}()}function bo(){this.jb=o(3),this.Wc=h([4,8],pa),this.Xc=h([4,17],pa)}function yo(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new o(4),this.od=new o(4)}function $r(){this.ld=this.La=this.dd=this.tc=0}function ga(){this.Na=this.la=0}function wo(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Bi(){this.ad=o(384),this.Za=0,this.Ob=o(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function No(){this.uc=this.M=this.Nb=0,this.wa=Array(new $r),this.Y=0,this.ya=Array(new Bi),this.aa=0,this.l=new Zr}function ma(){this.y=o(16),this.f=o(8),this.ea=o(8)}function Lo(){this.cb=this.a=0,this.sc="",this.m=new Et,this.Od=new Yr,this.Kc=new Xr,this.ed=new yo,this.Qa=new Kr,this.Ic=this.$c=this.Aa=0,this.D=new No,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=l(8,Et),this.ia=0,this.pb=l(4,wo),this.Pa=new bo,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new ma),this.Hd=0,this.rb=Array(new ga),this.sb=0,this.wa=Array(new $r),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Bi),this.L=this.aa=0,this.gd=h([4,2],$r),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function Zr(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function xo(){var t=new Lo;return t!=null&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,ia||(ia=ya)),t}function Fe(t,r,u){return t.a==0&&(t.a=r,t.sc=u,t.cb=0),0}function va(t,r,u){return 3<=u&&t[r+0]==157&&t[r+1]==1&&t[r+2]==42}function ba(t,r){if(t==null)return 0;if(t.a=0,t.sc="OK",r==null)return Fe(t,2,"null VP8Io passed to VP8GetHeaders()");var u=r.data,d=r.w,m=r.ha;if(4>m)return Fe(t,7,"Truncated header.");var b=u[d+0]|u[d+1]<<8|u[d+2]<<16,y=t.Od;if(y.Rb=!(1&b),y.td=b>>1&7,y.yd=b>>4&1,y.ub=b>>5,3<y.td)return Fe(t,3,"Incorrect keyframe parameters.");if(!y.yd)return Fe(t,4,"Frame not displayable.");d+=3,m-=3;var N=t.Kc;if(y.Rb){if(7>m)return Fe(t,7,"cannot parse picture header");if(!va(u,d,m))return Fe(t,3,"Bad code word");N.c=16383&(u[d+4]<<8|u[d+3]),N.Td=u[d+4]>>6,N.i=16383&(u[d+6]<<8|u[d+5]),N.Ud=u[d+6]>>6,d+=7,m-=7,t.za=N.c+15>>4,t.Ub=N.i+15>>4,r.width=N.c,r.height=N.i,r.Da=0,r.j=0,r.v=0,r.va=r.width,r.o=r.height,r.da=0,r.ib=r.width,r.hb=r.height,r.U=r.width,r.T=r.height,c((b=t.Pa).jb,0,255,b.jb.length),e((b=t.Qa)!=null),b.Cb=0,b.Bb=0,b.Fb=1,c(b.Zb,0,0,b.Zb.length),c(b.Lb,0,0,b.Lb)}if(y.ub>m)return Fe(t,7,"bad partition length");ct(b=t.m,u,d,y.ub),d+=y.ub,m-=y.ub,y.Rb&&(N.Ld=et(b),N.Kd=et(b)),N=t.Qa;var A,P=t.Pa;if(e(b!=null),e(N!=null),N.Cb=et(b),N.Cb){if(N.Bb=et(b),et(b)){for(N.Fb=et(b),A=0;4>A;++A)N.Zb[A]=et(b)?mt(b,7):0;for(A=0;4>A;++A)N.Lb[A]=et(b)?mt(b,6):0}if(N.Bb)for(A=0;3>A;++A)P.jb[A]=et(b)?ot(b,8):255}else N.Bb=0;if(b.Ka)return Fe(t,3,"cannot parse segment header");if((N=t.ed).zd=et(b),N.Tb=ot(b,6),N.wb=ot(b,3),N.Pc=et(b),N.Pc&&et(b)){for(P=0;4>P;++P)et(b)&&(N.vd[P]=mt(b,6));for(P=0;4>P;++P)et(b)&&(N.od[P]=mt(b,6))}if(t.L=N.Tb==0?0:N.zd?1:2,b.Ka)return Fe(t,3,"cannot parse filter header");var R=m;if(m=A=d,d=A+R,N=R,t.Xb=(1<<ot(t.m,2))-1,R<3*(P=t.Xb))u=7;else{for(A+=3*P,N-=3*P,R=0;R<P;++R){var X=u[m+0]|u[m+1]<<8|u[m+2]<<16;X>N&&(X=N),ct(t.Jc[+R],u,A,X),A+=X,N-=X,m+=3}ct(t.Jc[+P],u,A,N),u=A<d?0:5}if(u!=0)return Fe(t,u,"cannot parse partitions");for(u=ot(A=t.m,7),m=et(A)?mt(A,4):0,d=et(A)?mt(A,4):0,N=et(A)?mt(A,4):0,P=et(A)?mt(A,4):0,A=et(A)?mt(A,4):0,R=t.Qa,X=0;4>X;++X){if(R.Cb){var K=R.Zb[X];R.Fb||(K+=u)}else{if(0<X){t.pb[X]=t.pb[0];continue}K=u}var W=t.pb[X];W.Sc[0]=Yo[dn(K+m,127)],W.Sc[1]=Xo[dn(K+0,127)],W.Eb[0]=2*Yo[dn(K+d,127)],W.Eb[1]=101581*Xo[dn(K+N,127)]>>16,8>W.Eb[1]&&(W.Eb[1]=8),W.Qc[0]=Yo[dn(K+P,117)],W.Qc[1]=Xo[dn(K+A,127)],W.lc=K+A}if(!y.Rb)return Fe(t,4,"Not a key frame.");for(et(b),y=t.Pa,u=0;4>u;++u){for(m=0;8>m;++m)for(d=0;3>d;++d)for(N=0;11>N;++N)P=Q(b,Xc[u][m][d][N])?ot(b,8):Jc[u][m][d][N],y.Wc[u][m].Yb[d][N]=P;for(m=0;17>m;++m)y.Xc[u][m]=y.Wc[u][Kc[m]]}return t.kc=et(b),t.kc&&(t.Bd=ot(b,8)),t.cb=1}function ya(t,r,u,d,m,b,y){var N=r[m].Yb[u];for(u=0;16>m;++m){if(!Q(t,N[u+0]))return m;for(;!Q(t,N[u+1]);)if(N=r[++m].Yb[0],u=0,m==16)return 16;var A=r[m+1].Yb;if(Q(t,N[u+2])){var P=t,R=0;if(Q(P,(K=N)[(X=u)+3]))if(Q(P,K[X+6])){for(N=0,X=2*(R=Q(P,K[X+8]))+(K=Q(P,K[X+9+R])),R=0,K=Wc[X];K[N];++N)R+=R+Q(P,K[N]);R+=3+(8<<X)}else Q(P,K[X+7])?(R=7+2*Q(P,165),R+=Q(P,145)):R=5+Q(P,159);else R=Q(P,K[X+4])?3+Q(P,K[X+5]):2;N=A[2]}else R=1,N=A[1];A=y+Gc[m],0>(P=t).b&&Z(P);var X,K=P.b,W=(X=P.Ca>>1)-(P.I>>K)>>31;--P.b,P.Ca+=W,P.Ca|=1,P.I-=(X+1&W)<<K,b[A]=((R^W)-W)*d[(0<m)+0]}return 16}function Mi(t){var r=t.rb[t.sb-1];r.la=0,r.Na=0,c(t.zc,0,0,t.zc.length),t.ja=0}function Ao(t,r){if(t==null)return 0;if(r==null)return Fe(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!ba(t,r))return 0;if(e(t.cb),r.ac==null||r.ac(r)){r.ob&&(t.L=0);var u=Ja[t.L];if(t.L==2?(t.yb=0,t.zb=0):(t.yb=r.v-u>>4,t.zb=r.j-u>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=r.o+15+u>>4,t.Hb=r.va+15+u>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var d=t.ed;for(u=0;4>u;++u){var m;if(t.Qa.Cb){var b=t.Qa.Lb[u];t.Qa.Fb||(b+=d.Tb)}else b=d.Tb;for(m=0;1>=m;++m){var y=t.gd[u][m],N=b;if(d.Pc&&(N+=d.vd[0],m&&(N+=d.od[0])),0<(N=0>N?0:63<N?63:N)){var A=N;0<d.wb&&(A=4<d.wb?A>>2:A>>1)>9-d.wb&&(A=9-d.wb),1>A&&(A=1),y.dd=A,y.tc=2*N+A,y.ld=40<=N?2:15<=N?1:0}else y.tc=0;y.La=m}}}u=0}else Fe(t,6,"Frame setup failed"),u=t.a;if(u=u==0){if(u){t.$c=0,0<t.Aa||(t.Ic=uu);t:{u=t.Ic,d=4*(A=t.za);var P=32*A,R=A+1,X=0<t.L?A*(0<t.Aa?2:1):0,K=(t.Aa==2?2:1)*A;if((y=d+832+(m=3*(16*u+Ja[t.L])/2*P)+(b=t.Fa!=null&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=y)u=0;else{if(y>t.Vb){if(t.Vb=0,t.Ec=o(y),t.Fc=0,t.Ec==null){u=Fe(t,1,"no memory during frame initialization.");break t}t.Vb=y}y=t.Ec,N=t.Fc,t.Ac=y,t.Bc=N,N+=d,t.Gd=l(P,ma),t.Hd=0,t.rb=l(R+1,ga),t.sb=1,t.wa=X?l(X,$r):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=A),e(!0),t.oc=y,t.pc=N,N+=832,t.ya=l(K,Bi),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,t.Aa==2&&(t.D.aa+=A),t.R=16*A,t.B=8*A,A=(P=Ja[t.L])*t.R,P=P/2*t.B,t.sa=y,t.ta=N+A,t.qa=t.sa,t.ra=t.ta+16*u*t.R+P,t.Ha=t.qa,t.Ia=t.ra+8*u*t.B+P,t.$c=0,N+=m,t.mb=b?y:null,t.nb=b?N:null,e(N+b<=t.Fc+t.Vb),Mi(t),c(t.Ac,t.Bc,0,d),u=1}}if(u){if(r.ka=0,r.y=t.sa,r.O=t.ta,r.f=t.qa,r.N=t.ra,r.ea=t.Ha,r.Vd=t.Ia,r.fa=t.R,r.Rc=t.B,r.F=null,r.J=0,!qa){for(u=-255;255>=u;++u)Re[255+u]=0>u?-u:u;for(u=-1020;1020>=u;++u)or[1020+u]=-128>u?-128:127<u?127:u;for(u=-112;112>=u;++u)ra[112+u]=-16>u?-16:15<u?15:u;for(u=-255;510>=u;++u)mi[255+u]=0>u?0:255<u?255:u;qa=1}di=Po,ar=So,ta=Na,nn=_o,yn=La,Ce=wa,pi=zi,Ta=Or,ea=Ro,Er=Ui,Tr=Do,gr=ii,qr=Hi,gi=Ia,Dr=Ca,mr=Gn,na=nr,wn=qo,Bn[0]=Wn,Bn[1]=ko,Bn[2]=jo,Bn[3]=Oo,Bn[4]=Sa,Bn[5]=ni,Bn[6]=_a,Bn[7]=qi,Bn[8]=Mo,Bn[9]=Bo,vr[0]=xa,vr[1]=Co,vr[2]=er,vr[3]=ti,vr[4]=Xe,vr[5]=Io,vr[6]=Aa,sr[0]=lr,sr[1]=Fo,sr[2]=Eo,sr[3]=Di,sr[4]=jr,sr[5]=To,sr[6]=Ri,u=1}else u=0}u&&(u=function(W,ht){for(W.M=0;W.M<W.Va;++W.M){var nt,z=W.Jc[W.M&W.Xb],U=W.m,ut=W;for(nt=0;nt<ut.za;++nt){var gt=U,lt=ut,It=lt.Ac,St=lt.Bc+4*nt,Dt=lt.zc,_t=lt.ya[lt.aa+nt];if(lt.Qa.Bb?_t.$b=Q(gt,lt.Pa.jb[0])?2+Q(gt,lt.Pa.jb[2]):Q(gt,lt.Pa.jb[1]):_t.$b=0,lt.kc&&(_t.Ad=Q(gt,lt.Bd)),_t.Za=!Q(gt,145)+0,_t.Za){var se=_t.Ob,le=0;for(lt=0;4>lt;++lt){var ae,ne=Dt[0+lt];for(ae=0;4>ae;++ae){ne=Yc[It[St+ae]][ne];for(var me=Cs[Q(gt,ne[0])];0<me;)me=Cs[2*me+Q(gt,ne[me])];ne=-me,It[St+ae]=ne}a(se,le,It,St,4),le+=4,Dt[0+lt]=ne}}else ne=Q(gt,156)?Q(gt,128)?1:3:Q(gt,163)?2:0,_t.Ob[0]=ne,c(It,St,ne,4),c(Dt,0,ne,4);_t.Dd=Q(gt,142)?Q(gt,114)?Q(gt,183)?1:3:2:0}if(ut.m.Ka)return Fe(W,7,"Premature end-of-partition0 encountered.");for(;W.ja<W.za;++W.ja){if(ut=z,gt=(U=W).rb[U.sb-1],It=U.rb[U.sb+U.ja],nt=U.ya[U.aa+U.ja],St=U.kc?nt.Ad:0)gt.la=It.la=0,nt.Za||(gt.Na=It.Na=0),nt.Hc=0,nt.Gc=0,nt.ia=0;else{var pe,Zt;if(gt=It,It=ut,St=U.Pa.Xc,Dt=U.ya[U.aa+U.ja],_t=U.pb[Dt.$b],lt=Dt.ad,se=0,le=U.rb[U.sb-1],ne=ae=0,c(lt,se,0,384),Dt.Za)var Te=0,un=St[3];else{me=o(16);var qe=gt.Na+le.Na;if(qe=ia(It,St[1],qe,_t.Eb,0,me,0),gt.Na=le.Na=(0<qe)+0,1<qe)di(me,0,lt,se);else{var ln=me[0]+3>>3;for(me=0;256>me;me+=16)lt[se+me]=ln}Te=1,un=St[0]}var he=15&gt.la,gn=15&le.la;for(me=0;4>me;++me){var mn=1&gn;for(ln=Zt=0;4>ln;++ln)he=he>>1|(mn=(qe=ia(It,un,qe=mn+(1&he),_t.Sc,Te,lt,se))>Te)<<7,Zt=Zt<<2|(3<qe?3:1<qe?2:lt[se+0]!=0),se+=16;he>>=4,gn=gn>>1|mn<<7,ae=(ae<<8|Zt)>>>0}for(un=he,Te=gn>>4,pe=0;4>pe;pe+=2){for(Zt=0,he=gt.la>>4+pe,gn=le.la>>4+pe,me=0;2>me;++me){for(mn=1&gn,ln=0;2>ln;++ln)qe=mn+(1&he),he=he>>1|(mn=0<(qe=ia(It,St[2],qe,_t.Qc,0,lt,se)))<<3,Zt=Zt<<2|(3<qe?3:1<qe?2:lt[se+0]!=0),se+=16;he>>=2,gn=gn>>1|mn<<5}ne|=Zt<<4*pe,un|=he<<4<<pe,Te|=(240&gn)<<pe}gt.la=un,le.la=Te,Dt.Hc=ae,Dt.Gc=ne,Dt.ia=43690&ne?0:_t.ia,St=!(ae|ne)}if(0<U.L&&(U.wa[U.Y+U.ja]=U.gd[nt.$b][nt.Za],U.wa[U.Y+U.ja].La|=!St),ut.Ka)return Fe(W,7,"Premature end-of-file encountered.")}if(Mi(W),U=ht,ut=1,nt=(z=W).D,gt=0<z.L&&z.M>=z.zb&&z.M<=z.Va,z.Aa==0)t:{if(nt.M=z.M,nt.uc=gt,Xi(z,nt),ut=1,nt=(Zt=z.D).Nb,gt=(ne=Ja[z.L])*z.R,It=ne/2*z.B,me=16*nt*z.R,ln=8*nt*z.B,St=z.sa,Dt=z.ta-gt+me,_t=z.qa,lt=z.ra-It+ln,se=z.Ha,le=z.Ia-It+ln,gn=(he=Zt.M)==0,ae=he>=z.Va-1,z.Aa==2&&Xi(z,Zt),Zt.uc)for(mn=(qe=z).D.M,e(qe.D.uc),Zt=qe.yb;Zt<qe.Hb;++Zt){Te=Zt,un=mn;var Ue=(Je=(Pe=qe).D).Nb;pe=Pe.R;var Je=Je.wa[Je.Y+Te],rn=Pe.sa,An=Pe.ta+16*Ue*pe+16*Te,Yn=Je.dd,He=Je.tc;if(He!=0)if(e(3<=He),Pe.L==1)0<Te&&mr(rn,An,pe,He+4),Je.La&&wn(rn,An,pe,He),0<un&&Dr(rn,An,pe,He+4),Je.La&&na(rn,An,pe,He);else{var cr=Pe.B,Rr=Pe.qa,vi=Pe.ra+8*Ue*cr+8*Te,yr=Pe.Ha,Pe=Pe.Ia+8*Ue*cr+8*Te;Ue=Je.ld,0<Te&&(Ta(rn,An,pe,He+4,Yn,Ue),Er(Rr,vi,yr,Pe,cr,He+4,Yn,Ue)),Je.La&&(gr(rn,An,pe,He,Yn,Ue),gi(Rr,vi,yr,Pe,cr,He,Yn,Ue)),0<un&&(pi(rn,An,pe,He+4,Yn,Ue),ea(Rr,vi,yr,Pe,cr,He+4,Yn,Ue)),Je.La&&(Tr(rn,An,pe,He,Yn,Ue),qr(Rr,vi,yr,Pe,cr,He,Yn,Ue))}}if(z.ia&&alert("todo:DitherRow"),U.put!=null){if(Zt=16*he,he=16*(he+1),gn?(U.y=z.sa,U.O=z.ta+me,U.f=z.qa,U.N=z.ra+ln,U.ea=z.Ha,U.W=z.Ia+ln):(Zt-=ne,U.y=St,U.O=Dt,U.f=_t,U.N=lt,U.ea=se,U.W=le),ae||(he-=ne),he>U.o&&(he=U.o),U.F=null,U.J=null,z.Fa!=null&&0<z.Fa.length&&Zt<he&&(U.J=Ji(z,U,Zt,he-Zt),U.F=z.mb,U.F==null&&U.F.length==0)){ut=Fe(z,3,"Could not decode alpha data.");break t}Zt<U.j&&(ne=U.j-Zt,Zt=U.j,e(!(1&ne)),U.O+=z.R*ne,U.N+=z.B*(ne>>1),U.W+=z.B*(ne>>1),U.F!=null&&(U.J+=U.width*ne)),Zt<he&&(U.O+=U.v,U.N+=U.v>>1,U.W+=U.v>>1,U.F!=null&&(U.J+=U.v),U.ka=Zt-U.j,U.U=U.va-U.v,U.T=he-Zt,ut=U.put(U))}nt+1!=z.Ic||ae||(a(z.sa,z.ta-gt,St,Dt+16*z.R,gt),a(z.qa,z.ra-It,_t,lt+8*z.B,It),a(z.Ha,z.Ia-It,se,le+8*z.B,It))}if(!ut)return Fe(W,6,"Output aborted.")}return 1}(t,r)),r.bc!=null&&r.bc(r),u&=1}return u?(t.cb=0,u):0}function In(t,r,u,d,m){m=t[r+u+32*d]+(m>>3),t[r+u+32*d]=-256&m?0>m?0:255:m}function Qr(t,r,u,d,m,b){In(t,r,0,u,d+m),In(t,r,1,u,d+b),In(t,r,2,u,d-b),In(t,r,3,u,d-m)}function on(t){return(20091*t>>16)+t}function Ei(t,r,u,d){var m,b=0,y=o(16);for(m=0;4>m;++m){var N=t[r+0]+t[r+8],A=t[r+0]-t[r+8],P=(35468*t[r+4]>>16)-on(t[r+12]),R=on(t[r+4])+(35468*t[r+12]>>16);y[b+0]=N+R,y[b+1]=A+P,y[b+2]=A-P,y[b+3]=N-R,b+=4,r++}for(m=b=0;4>m;++m)N=(t=y[b+0]+4)+y[b+8],A=t-y[b+8],P=(35468*y[b+4]>>16)-on(y[b+12]),In(u,d,0,0,N+(R=on(y[b+4])+(35468*y[b+12]>>16))),In(u,d,1,0,A+P),In(u,d,2,0,A-P),In(u,d,3,0,N-R),b++,d+=32}function wa(t,r,u,d){var m=t[r+0]+4,b=35468*t[r+4]>>16,y=on(t[r+4]),N=35468*t[r+1]>>16;Qr(u,d,0,m+y,t=on(t[r+1]),N),Qr(u,d,1,m+b,t,N),Qr(u,d,2,m-b,t,N),Qr(u,d,3,m-y,t,N)}function So(t,r,u,d,m){Ei(t,r,u,d),m&&Ei(t,r+16,u,d+4)}function Na(t,r,u,d){ar(t,r+0,u,d,1),ar(t,r+32,u,d+128,1)}function _o(t,r,u,d){var m;for(t=t[r+0]+4,m=0;4>m;++m)for(r=0;4>r;++r)In(u,d,r,m,t)}function La(t,r,u,d){t[r+0]&&nn(t,r+0,u,d),t[r+16]&&nn(t,r+16,u,d+4),t[r+32]&&nn(t,r+32,u,d+128),t[r+48]&&nn(t,r+48,u,d+128+4)}function Po(t,r,u,d){var m,b=o(16);for(m=0;4>m;++m){var y=t[r+0+m]+t[r+12+m],N=t[r+4+m]+t[r+8+m],A=t[r+4+m]-t[r+8+m],P=t[r+0+m]-t[r+12+m];b[0+m]=y+N,b[8+m]=y-N,b[4+m]=P+A,b[12+m]=P-A}for(m=0;4>m;++m)y=(t=b[0+4*m]+3)+b[3+4*m],N=b[1+4*m]+b[2+4*m],A=b[1+4*m]-b[2+4*m],P=t-b[3+4*m],u[d+0]=y+N>>3,u[d+16]=P+A>>3,u[d+32]=y-N>>3,u[d+48]=P-A>>3,d+=64}function Ti(t,r,u){var d,m=r-32,b=pn,y=255-t[m-1];for(d=0;d<u;++d){var N,A=b,P=y+t[r-1];for(N=0;N<u;++N)t[r+N]=A[P+t[m+N]];r+=32}}function ko(t,r){Ti(t,r,4)}function Fo(t,r){Ti(t,r,8)}function Co(t,r){Ti(t,r,16)}function er(t,r){var u;for(u=0;16>u;++u)a(t,r+32*u,t,r-32,16)}function ti(t,r){var u;for(u=16;0<u;--u)c(t,r,t[r-1],16),r+=32}function ei(t,r,u){var d;for(d=0;16>d;++d)c(r,u+32*d,t,16)}function xa(t,r){var u,d=16;for(u=0;16>u;++u)d+=t[r-1+32*u]+t[r+u-32];ei(d>>5,t,r)}function Xe(t,r){var u,d=8;for(u=0;16>u;++u)d+=t[r-1+32*u];ei(d>>4,t,r)}function Io(t,r){var u,d=8;for(u=0;16>u;++u)d+=t[r+u-32];ei(d>>4,t,r)}function Aa(t,r){ei(128,t,r)}function Wt(t,r,u){return t+2*r+u+2>>2}function jo(t,r){var u,d=r-32;for(d=new Uint8Array([Wt(t[d-1],t[d+0],t[d+1]),Wt(t[d+0],t[d+1],t[d+2]),Wt(t[d+1],t[d+2],t[d+3]),Wt(t[d+2],t[d+3],t[d+4])]),u=0;4>u;++u)a(t,r+32*u,d,0,d.length)}function Oo(t,r){var u=t[r-1],d=t[r-1+32],m=t[r-1+64],b=t[r-1+96];xt(t,r+0,16843009*Wt(t[r-1-32],u,d)),xt(t,r+32,16843009*Wt(u,d,m)),xt(t,r+64,16843009*Wt(d,m,b)),xt(t,r+96,16843009*Wt(m,b,b))}function Wn(t,r){var u,d=4;for(u=0;4>u;++u)d+=t[r+u-32]+t[r-1+32*u];for(d>>=3,u=0;4>u;++u)c(t,r+32*u,d,4)}function Sa(t,r){var u=t[r-1+0],d=t[r-1+32],m=t[r-1+64],b=t[r-1-32],y=t[r+0-32],N=t[r+1-32],A=t[r+2-32],P=t[r+3-32];t[r+0+96]=Wt(d,m,t[r-1+96]),t[r+1+96]=t[r+0+64]=Wt(u,d,m),t[r+2+96]=t[r+1+64]=t[r+0+32]=Wt(b,u,d),t[r+3+96]=t[r+2+64]=t[r+1+32]=t[r+0+0]=Wt(y,b,u),t[r+3+64]=t[r+2+32]=t[r+1+0]=Wt(N,y,b),t[r+3+32]=t[r+2+0]=Wt(A,N,y),t[r+3+0]=Wt(P,A,N)}function _a(t,r){var u=t[r+1-32],d=t[r+2-32],m=t[r+3-32],b=t[r+4-32],y=t[r+5-32],N=t[r+6-32],A=t[r+7-32];t[r+0+0]=Wt(t[r+0-32],u,d),t[r+1+0]=t[r+0+32]=Wt(u,d,m),t[r+2+0]=t[r+1+32]=t[r+0+64]=Wt(d,m,b),t[r+3+0]=t[r+2+32]=t[r+1+64]=t[r+0+96]=Wt(m,b,y),t[r+3+32]=t[r+2+64]=t[r+1+96]=Wt(b,y,N),t[r+3+64]=t[r+2+96]=Wt(y,N,A),t[r+3+96]=Wt(N,A,A)}function ni(t,r){var u=t[r-1+0],d=t[r-1+32],m=t[r-1+64],b=t[r-1-32],y=t[r+0-32],N=t[r+1-32],A=t[r+2-32],P=t[r+3-32];t[r+0+0]=t[r+1+64]=b+y+1>>1,t[r+1+0]=t[r+2+64]=y+N+1>>1,t[r+2+0]=t[r+3+64]=N+A+1>>1,t[r+3+0]=A+P+1>>1,t[r+0+96]=Wt(m,d,u),t[r+0+64]=Wt(d,u,b),t[r+0+32]=t[r+1+96]=Wt(u,b,y),t[r+1+32]=t[r+2+96]=Wt(b,y,N),t[r+2+32]=t[r+3+96]=Wt(y,N,A),t[r+3+32]=Wt(N,A,P)}function qi(t,r){var u=t[r+0-32],d=t[r+1-32],m=t[r+2-32],b=t[r+3-32],y=t[r+4-32],N=t[r+5-32],A=t[r+6-32],P=t[r+7-32];t[r+0+0]=u+d+1>>1,t[r+1+0]=t[r+0+64]=d+m+1>>1,t[r+2+0]=t[r+1+64]=m+b+1>>1,t[r+3+0]=t[r+2+64]=b+y+1>>1,t[r+0+32]=Wt(u,d,m),t[r+1+32]=t[r+0+96]=Wt(d,m,b),t[r+2+32]=t[r+1+96]=Wt(m,b,y),t[r+3+32]=t[r+2+96]=Wt(b,y,N),t[r+3+64]=Wt(y,N,A),t[r+3+96]=Wt(N,A,P)}function Bo(t,r){var u=t[r-1+0],d=t[r-1+32],m=t[r-1+64],b=t[r-1+96];t[r+0+0]=u+d+1>>1,t[r+2+0]=t[r+0+32]=d+m+1>>1,t[r+2+32]=t[r+0+64]=m+b+1>>1,t[r+1+0]=Wt(u,d,m),t[r+3+0]=t[r+1+32]=Wt(d,m,b),t[r+3+32]=t[r+1+64]=Wt(m,b,b),t[r+3+64]=t[r+2+64]=t[r+0+96]=t[r+1+96]=t[r+2+96]=t[r+3+96]=b}function Mo(t,r){var u=t[r-1+0],d=t[r-1+32],m=t[r-1+64],b=t[r-1+96],y=t[r-1-32],N=t[r+0-32],A=t[r+1-32],P=t[r+2-32];t[r+0+0]=t[r+2+32]=u+y+1>>1,t[r+0+32]=t[r+2+64]=d+u+1>>1,t[r+0+64]=t[r+2+96]=m+d+1>>1,t[r+0+96]=b+m+1>>1,t[r+3+0]=Wt(N,A,P),t[r+2+0]=Wt(y,N,A),t[r+1+0]=t[r+3+32]=Wt(u,y,N),t[r+1+32]=t[r+3+64]=Wt(d,u,y),t[r+1+64]=t[r+3+96]=Wt(m,d,u),t[r+1+96]=Wt(b,m,d)}function Eo(t,r){var u;for(u=0;8>u;++u)a(t,r+32*u,t,r-32,8)}function Di(t,r){var u;for(u=0;8>u;++u)c(t,r,t[r-1],8),r+=32}function Ir(t,r,u){var d;for(d=0;8>d;++d)c(r,u+32*d,t,8)}function lr(t,r){var u,d=8;for(u=0;8>u;++u)d+=t[r+u-32]+t[r-1+32*u];Ir(d>>4,t,r)}function To(t,r){var u,d=4;for(u=0;8>u;++u)d+=t[r+u-32];Ir(d>>3,t,r)}function jr(t,r){var u,d=4;for(u=0;8>u;++u)d+=t[r-1+32*u];Ir(d>>3,t,r)}function Ri(t,r){Ir(128,t,r)}function ri(t,r,u){var d=t[r-u],m=t[r+0],b=3*(m-d)+Ho[1020+t[r-2*u]-t[r+u]],y=Da[112+(b+4>>3)];t[r-u]=pn[255+d+Da[112+(b+3>>3)]],t[r+0]=pn[255+m-y]}function Pa(t,r,u,d){var m=t[r+0],b=t[r+u];return Ln[255+t[r-2*u]-t[r-u]]>d||Ln[255+b-m]>d}function ka(t,r,u,d){return 4*Ln[255+t[r-u]-t[r+0]]+Ln[255+t[r-2*u]-t[r+u]]<=d}function Fa(t,r,u,d,m){var b=t[r-3*u],y=t[r-2*u],N=t[r-u],A=t[r+0],P=t[r+u],R=t[r+2*u],X=t[r+3*u];return 4*Ln[255+N-A]+Ln[255+y-P]>d?0:Ln[255+t[r-4*u]-b]<=m&&Ln[255+b-y]<=m&&Ln[255+y-N]<=m&&Ln[255+X-R]<=m&&Ln[255+R-P]<=m&&Ln[255+P-A]<=m}function Ca(t,r,u,d){var m=2*d+1;for(d=0;16>d;++d)ka(t,r+d,u,m)&&ri(t,r+d,u)}function Gn(t,r,u,d){var m=2*d+1;for(d=0;16>d;++d)ka(t,r+d*u,1,m)&&ri(t,r+d*u,1)}function nr(t,r,u,d){var m;for(m=3;0<m;--m)Ca(t,r+=4*u,u,d)}function qo(t,r,u,d){var m;for(m=3;0<m;--m)Gn(t,r+=4,u,d)}function hr(t,r,u,d,m,b,y,N){for(b=2*b+1;0<m--;){if(Fa(t,r,u,b,y))if(Pa(t,r,u,N))ri(t,r,u);else{var A=t,P=r,R=u,X=A[P-2*R],K=A[P-R],W=A[P+0],ht=A[P+R],nt=A[P+2*R],z=27*(ut=Ho[1020+3*(W-K)+Ho[1020+X-ht]])+63>>7,U=18*ut+63>>7,ut=9*ut+63>>7;A[P-3*R]=pn[255+A[P-3*R]+ut],A[P-2*R]=pn[255+X+U],A[P-R]=pn[255+K+z],A[P+0]=pn[255+W-z],A[P+R]=pn[255+ht-U],A[P+2*R]=pn[255+nt-ut]}r+=d}}function jn(t,r,u,d,m,b,y,N){for(b=2*b+1;0<m--;){if(Fa(t,r,u,b,y))if(Pa(t,r,u,N))ri(t,r,u);else{var A=t,P=r,R=u,X=A[P-R],K=A[P+0],W=A[P+R],ht=Da[112+((nt=3*(K-X))+4>>3)],nt=Da[112+(nt+3>>3)],z=ht+1>>1;A[P-2*R]=pn[255+A[P-2*R]+z],A[P-R]=pn[255+X+nt],A[P+0]=pn[255+K-ht],A[P+R]=pn[255+W-z]}r+=d}}function zi(t,r,u,d,m,b){hr(t,r,u,1,16,d,m,b)}function Or(t,r,u,d,m,b){hr(t,r,1,u,16,d,m,b)}function Do(t,r,u,d,m,b){var y;for(y=3;0<y;--y)jn(t,r+=4*u,u,1,16,d,m,b)}function ii(t,r,u,d,m,b){var y;for(y=3;0<y;--y)jn(t,r+=4,1,u,16,d,m,b)}function Ro(t,r,u,d,m,b,y,N){hr(t,r,m,1,8,b,y,N),hr(u,d,m,1,8,b,y,N)}function Ui(t,r,u,d,m,b,y,N){hr(t,r,1,m,8,b,y,N),hr(u,d,1,m,8,b,y,N)}function Hi(t,r,u,d,m,b,y,N){jn(t,r+4*m,m,1,8,b,y,N),jn(u,d+4*m,m,1,8,b,y,N)}function Ia(t,r,u,d,m,b,y,N){jn(t,r+4,1,m,8,b,y,N),jn(u,d+4,1,m,8,b,y,N)}function ai(){this.ba=new _n,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new Se,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Vi(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Wi(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function ja(){this.ua=0,this.Wa=new B,this.vb=new B,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new D,this.yc=new F}function zo(){this.xb=this.a=0,this.l=new Zr,this.ca=new _n,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new w,this.Pb=0,this.wd=new w,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new ja,this.ab=0,this.gc=l(4,Wi),this.Oc=0}function oi(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new Zr,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function Br(t,r,u,d,m,b,y){for(t=t==null?0:t[r+0],r=0;r<y;++r)m[b+r]=t+u[d+r]&255,t=m[b+r]}function Gi(t,r,u,d,m,b,y){var N;if(t==null)Br(null,null,u,d,m,b,y);else for(N=0;N<y;++N)m[b+N]=t[r+N]+u[d+N]&255}function fr(t,r,u,d,m,b,y){if(t==null)Br(null,null,u,d,m,b,y);else{var N,A=t[r+0],P=A,R=A;for(N=0;N<y;++N)P=R+(A=t[r+N])-P,R=u[d+N]+(-256&P?0>P?0:255:P)&255,P=A,m[b+N]=R}}function Ji(t,r,u,d){var m=r.width,b=r.o;if(e(t!=null&&r!=null),0>u||0>=d||u+d>b)return null;if(!t.Cc){if(t.ga==null){var y;if(t.ga=new oi,(y=t.ga==null)||(y=r.width*r.o,e(t.Gb.length==0),t.Gb=o(y),t.Uc=0,t.Gb==null?y=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,y=1),y=!y),!y){y=t.ga;var N=t.Fa,A=t.P,P=t.qc,R=t.mb,X=t.nb,K=A+1,W=P-1,ht=y.l;if(e(N!=null&&R!=null&&r!=null),br[0]=null,br[1]=Br,br[2]=Gi,br[3]=fr,y.ca=R,y.tb=X,y.c=r.width,y.i=r.height,e(0<y.c&&0<y.i),1>=P)r=0;else if(y.$a=N[A+0]>>0&3,y.Z=N[A+0]>>2&3,y.Lc=N[A+0]>>4&3,A=N[A+0]>>6&3,0>y.$a||1<y.$a||4<=y.Z||1<y.Lc||A)r=0;else if(ht.put=Rn,ht.ac=We,ht.bc=zn,ht.ma=y,ht.width=r.width,ht.height=r.height,ht.Da=r.Da,ht.v=r.v,ht.va=r.va,ht.j=r.j,ht.o=r.o,y.$a)t:{e(y.$a==1),r=vn();e:for(;;){if(r==null){r=0;break t}if(e(y!=null),y.mc=r,r.c=y.c,r.i=y.i,r.l=y.l,r.l.ma=y,r.l.width=y.c,r.l.height=y.i,r.a=0,tt(r.m,N,K,W),!Vn(y.c,y.i,1,r,null)||(r.ab==1&&r.gc[0].hc==3&&tr(r.s)?(y.ic=1,N=r.c*r.i,r.Ta=null,r.Ua=0,r.V=o(N),r.Ba=0,r.V==null?(r.a=1,r=0):r=1):(y.ic=0,r=Cr(r,y.c)),!r))break e;r=1;break t}y.mc=null,r=0}else r=W>=y.c*y.i;y=!r}if(y)return null;t.ga.Lc!=1?t.Ga=0:d=b-u}e(t.ga!=null),e(u+d<=b);t:{if(r=(N=t.ga).c,b=N.l.o,N.$a==0){if(K=t.rc,W=t.Vc,ht=t.Fa,A=t.P+1+u*r,P=t.mb,R=t.nb+u*r,e(A<=t.P+t.qc),N.Z!=0)for(e(br[N.Z]!=null),y=0;y<d;++y)br[N.Z](K,W,ht,A,P,R,r),K=P,W=R,R+=r,A+=r;else for(y=0;y<d;++y)a(P,R,ht,A,r),K=P,W=R,R+=r,A+=r;t.rc=K,t.Vc=W}else{if(e(N.mc!=null),r=u+d,e((y=N.mc)!=null),e(r<=y.i),y.C>=r)r=1;else if(N.ic||Y(),N.ic){N=y.V,K=y.Ba,W=y.c;var nt=y.i,z=(ht=1,A=y.$/W,P=y.$%W,R=y.m,X=y.s,y.$),U=W*nt,ut=W*r,gt=X.wc,lt=z<ut?Me(X,P,A):null;e(z<=U),e(r<=nt),e(tr(X));e:for(;;){for(;!R.h&&z<ut;){if(P&gt||(lt=Me(X,P,A)),e(lt!=null),J(R),256>(nt=an(lt.G[0],lt.H[0],R)))N[K+z]=nt,++z,++P>=W&&(P=0,++A<=r&&!(A%16)&&Fn(y,A));else{if(!(280>nt)){ht=0;break e}nt=Pn(nt-256,R);var It,St=an(lt.G[4],lt.H[4],R);if(J(R),!(z>=(St=Un(W,St=Pn(St,R)))&&U-z>=nt)){ht=0;break e}for(It=0;It<nt;++It)N[K+z+It]=N[K+z+It-St];for(z+=nt,P+=nt;P>=W;)P-=W,++A<=r&&!(A%16)&&Fn(y,A);z<ut&&P&gt&&(lt=Me(X,P,A))}e(R.h==E(R))}Fn(y,A>r?r:A);break e}!ht||R.h&&z<U?(ht=0,y.a=R.h?5:3):y.$=z,r=ht}else r=Cn(y,y.V,y.Ba,y.c,y.i,r,Jr);if(!r){d=0;break t}}u+d>=b&&(t.Cc=1),d=1}if(!d)return null;if(t.Cc&&((d=t.ga)!=null&&(d.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+u*m}function s(t,r,u,d,m,b){for(;0<m--;){var y,N=t,A=r+(u?1:0),P=t,R=r+(u?0:3);for(y=0;y<d;++y){var X=P[R+4*y];X!=255&&(X*=32897,N[A+4*y+0]=N[A+4*y+0]*X>>23,N[A+4*y+1]=N[A+4*y+1]*X>>23,N[A+4*y+2]=N[A+4*y+2]*X>>23)}r+=b}}function v(t,r,u,d,m){for(;0<d--;){var b;for(b=0;b<u;++b){var y=t[r+2*b+0],N=15&(P=t[r+2*b+1]),A=4369*N,P=(240&P|P>>4)*A>>16;t[r+2*b+0]=(240&y|y>>4)*A>>16&240|(15&y|y<<4)*A>>16>>4&15,t[r+2*b+1]=240&P|N}r+=m}}function I(t,r,u,d,m,b,y,N){var A,P,R=255;for(P=0;P<m;++P){for(A=0;A<d;++A){var X=t[r+A];b[y+4*A]=X,R&=X}r+=u,y+=N}return R!=255}function q(t,r,u,d,m){var b;for(b=0;b<m;++b)u[d+b]=t[r+b]>>8}function Y(){Nn=s,we=v,Ne=I,Ie=q}function it(t,r,u){G[t]=function(d,m,b,y,N,A,P,R,X,K,W,ht,nt,z,U,ut,gt){var lt,It=gt-1>>1,St=N[A+0]|P[R+0]<<16,Dt=X[K+0]|W[ht+0]<<16;e(d!=null);var _t=3*St+Dt+131074>>2;for(r(d[m+0],255&_t,_t>>16,nt,z),b!=null&&(_t=3*Dt+St+131074>>2,r(b[y+0],255&_t,_t>>16,U,ut)),lt=1;lt<=It;++lt){var se=N[A+lt]|P[R+lt]<<16,le=X[K+lt]|W[ht+lt]<<16,ae=St+se+Dt+le+524296,ne=ae+2*(se+Dt)>>3;_t=ne+St>>1,St=(ae=ae+2*(St+le)>>3)+se>>1,r(d[m+2*lt-1],255&_t,_t>>16,nt,z+(2*lt-1)*u),r(d[m+2*lt-0],255&St,St>>16,nt,z+(2*lt-0)*u),b!=null&&(_t=ae+Dt>>1,St=ne+le>>1,r(b[y+2*lt-1],255&_t,_t>>16,U,ut+(2*lt-1)*u),r(b[y+2*lt+0],255&St,St>>16,U,ut+(2*lt+0)*u)),St=se,Dt=le}1&gt||(_t=3*St+Dt+131074>>2,r(d[m+gt-1],255&_t,_t>>16,nt,z+(gt-1)*u),b!=null&&(_t=3*Dt+St+131074>>2,r(b[y+gt-1],255&_t,_t>>16,U,ut+(gt-1)*u)))}}function yt(){xn[Ra]=Zc,xn[za]=js,xn[Ps]=Qc,xn[Ua]=Os,xn[Ha]=Bs,xn[Vo]=Ms,xn[ks]=tu,xn[Wo]=js,xn[Go]=Os,xn[Va]=Bs,xn[Jo]=Ms}function Ct(t){return t&~eu?0>t?0:255:t>>Es}function qt(t,r){return Ct((19077*t>>8)+(26149*r>>8)-14234)}function $t(t,r,u){return Ct((19077*t>>8)-(6419*r>>8)-(13320*u>>8)+8708)}function Yt(t,r){return Ct((19077*t>>8)+(33050*r>>8)-17685)}function re(t,r,u,d,m){d[m+0]=qt(t,u),d[m+1]=$t(t,r,u),d[m+2]=Yt(t,r)}function xe(t,r,u,d,m){d[m+0]=Yt(t,r),d[m+1]=$t(t,r,u),d[m+2]=qt(t,u)}function _e(t,r,u,d,m){var b=$t(t,r,u);r=b<<3&224|Yt(t,r)>>3,d[m+0]=248&qt(t,u)|b>>5,d[m+1]=r}function Ee(t,r,u,d,m){var b=240&Yt(t,r)|15;d[m+0]=240&qt(t,u)|$t(t,r,u)>>4,d[m+1]=b}function Ke(t,r,u,d,m){d[m+0]=255,re(t,r,u,d,m+1)}function ze(t,r,u,d,m){xe(t,r,u,d,m),d[m+3]=255}function On(t,r,u,d,m){re(t,r,u,d,m),d[m+3]=255}function dn(t,r){return 0>t?0:t>r?r:t}function Jn(t,r,u){G[t]=function(d,m,b,y,N,A,P,R,X){for(var K=R+(-2&X)*u;R!=K;)r(d[m+0],b[y+0],N[A+0],P,R),r(d[m+1],b[y+0],N[A+0],P,R+u),m+=2,++y,++A,R+=2*u;1&X&&r(d[m+0],b[y+0],N[A+0],P,R)}}function Oa(t,r,u){return u==0?t==0?r==0?6:5:r==0?4:0:u}function Yi(t,r,u,d,m){switch(t>>>30){case 3:ar(r,u,d,m,0);break;case 2:Ce(r,u,d,m);break;case 1:nn(r,u,d,m)}}function Xi(t,r){var u,d,m=r.M,b=r.Nb,y=t.oc,N=t.pc+40,A=t.oc,P=t.pc+584,R=t.oc,X=t.pc+600;for(u=0;16>u;++u)y[N+32*u-1]=129;for(u=0;8>u;++u)A[P+32*u-1]=129,R[X+32*u-1]=129;for(0<m?y[N-1-32]=A[P-1-32]=R[X-1-32]=129:(c(y,N-32-1,127,21),c(A,P-32-1,127,9),c(R,X-32-1,127,9)),d=0;d<t.za;++d){var K=r.ya[r.aa+d];if(0<d){for(u=-1;16>u;++u)a(y,N+32*u-4,y,N+32*u+12,4);for(u=-1;8>u;++u)a(A,P+32*u-4,A,P+32*u+4,4),a(R,X+32*u-4,R,X+32*u+4,4)}var W=t.Gd,ht=t.Hd+d,nt=K.ad,z=K.Hc;if(0<m&&(a(y,N-32,W[ht].y,0,16),a(A,P-32,W[ht].f,0,8),a(R,X-32,W[ht].ea,0,8)),K.Za){var U=y,ut=N-32+16;for(0<m&&(d>=t.za-1?c(U,ut,W[ht].y[15],4):a(U,ut,W[ht+1].y,0,4)),u=0;4>u;u++)U[ut+128+u]=U[ut+256+u]=U[ut+384+u]=U[ut+0+u];for(u=0;16>u;++u,z<<=2)U=y,ut=N+qs[u],Bn[K.Ob[u]](U,ut),Yi(z,nt,16*+u,U,ut)}else if(U=Oa(d,m,K.Ob[0]),vr[U](y,N),z!=0)for(u=0;16>u;++u,z<<=2)Yi(z,nt,16*+u,y,N+qs[u]);for(u=K.Gc,U=Oa(d,m,K.Dd),sr[U](A,P),sr[U](R,X),z=nt,U=A,ut=P,255&(K=u>>0)&&(170&K?ta(z,256,U,ut):yn(z,256,U,ut)),K=R,z=X,255&(u>>=8)&&(170&u?ta(nt,320,K,z):yn(nt,320,K,z)),m<t.Ub-1&&(a(W[ht].y,0,y,N+480,16),a(W[ht].f,0,A,P+224,8),a(W[ht].ea,0,R,X+224,8)),u=8*b*t.B,W=t.sa,ht=t.ta+16*d+16*b*t.R,nt=t.qa,K=t.ra+8*d+u,z=t.Ha,U=t.Ia+8*d+u,u=0;16>u;++u)a(W,ht+u*t.R,y,N+32*u,16);for(u=0;8>u;++u)a(nt,K+u*t.B,A,P+32*u,8),a(z,U+u*t.B,R,X+32*u,8)}}function si(t,r,u,d,m,b,y,N,A){var P=[0],R=[0],X=0,K=A!=null?A.kd:0,W=A??new Vi;if(t==null||12>u)return 7;W.data=t,W.w=r,W.ha=u,r=[r],u=[u],W.gb=[W.gb];t:{var ht=r,nt=u,z=W.gb;if(e(t!=null),e(nt!=null),e(z!=null),z[0]=0,12<=nt[0]&&!n(t,ht[0],"RIFF")){if(n(t,ht[0]+8,"WEBP")){z=3;break t}var U=jt(t,ht[0]+4);if(12>U||4294967286<U){z=3;break t}if(K&&U>nt[0]-8){z=7;break t}z[0]=U,ht[0]+=12,nt[0]-=12}z=0}if(z!=0)return z;for(U=0<W.gb[0],u=u[0];;){t:{var ut=t;nt=r,z=u;var gt=P,lt=R,It=ht=[0];if((_t=X=[X])[0]=0,8>z[0])z=7;else{if(!n(ut,nt[0],"VP8X")){if(jt(ut,nt[0]+4)!=10){z=3;break t}if(18>z[0]){z=7;break t}var St=jt(ut,nt[0]+8),Dt=1+Ot(ut,nt[0]+12);if(2147483648<=Dt*(ut=1+Ot(ut,nt[0]+15))){z=3;break t}It!=null&&(It[0]=St),gt!=null&&(gt[0]=Dt),lt!=null&&(lt[0]=ut),nt[0]+=18,z[0]-=18,_t[0]=1}z=0}}if(X=X[0],ht=ht[0],z!=0)return z;if(nt=!!(2&ht),!U&&X)return 3;if(b!=null&&(b[0]=!!(16&ht)),y!=null&&(y[0]=nt),N!=null&&(N[0]=0),y=P[0],ht=R[0],X&&nt&&A==null){z=0;break}if(4>u){z=7;break}if(U&&X||!U&&!X&&!n(t,r[0],"ALPH")){u=[u],W.na=[W.na],W.P=[W.P],W.Sa=[W.Sa];t:{St=t,z=r,U=u;var _t=W.gb;gt=W.na,lt=W.P,It=W.Sa,Dt=22,e(St!=null),e(U!=null),ut=z[0];var se=U[0];for(e(gt!=null),e(It!=null),gt[0]=null,lt[0]=null,It[0]=0;;){if(z[0]=ut,U[0]=se,8>se){z=7;break t}var le=jt(St,ut+4);if(4294967286<le){z=3;break t}var ae=8+le+1&-2;if(Dt+=ae,0<_t&&Dt>_t){z=3;break t}if(!n(St,ut,"VP8 ")||!n(St,ut,"VP8L")){z=0;break t}if(se[0]<ae){z=7;break t}n(St,ut,"ALPH")||(gt[0]=St,lt[0]=ut+8,It[0]=le),ut+=ae,se-=ae}}if(u=u[0],W.na=W.na[0],W.P=W.P[0],W.Sa=W.Sa[0],z!=0)break}u=[u],W.Ja=[W.Ja],W.xa=[W.xa];t:if(_t=t,z=r,U=u,gt=W.gb[0],lt=W.Ja,It=W.xa,St=z[0],ut=!n(_t,St,"VP8 "),Dt=!n(_t,St,"VP8L"),e(_t!=null),e(U!=null),e(lt!=null),e(It!=null),8>U[0])z=7;else{if(ut||Dt){if(_t=jt(_t,St+4),12<=gt&&_t>gt-12){z=3;break t}if(K&&_t>U[0]-8){z=7;break t}lt[0]=_t,z[0]+=8,U[0]-=8,It[0]=Dt}else It[0]=5<=U[0]&&_t[St+0]==47&&!(_t[St+4]>>5),lt[0]=U[0];z=0}if(u=u[0],W.Ja=W.Ja[0],W.xa=W.xa[0],r=r[0],z!=0)break;if(4294967286<W.Ja)return 3;if(N==null||nt||(N[0]=W.xa?2:1),y=[y],ht=[ht],W.xa){if(5>u){z=7;break}N=y,K=ht,nt=b,t==null||5>u?t=0:5<=u&&t[r+0]==47&&!(t[r+4]>>5)?(U=[0],_t=[0],gt=[0],tt(lt=new w,t,r,u),Rt(lt,U,_t,gt)?(N!=null&&(N[0]=U[0]),K!=null&&(K[0]=_t[0]),nt!=null&&(nt[0]=gt[0]),t=1):t=0):t=0}else{if(10>u){z=7;break}N=ht,t==null||10>u||!va(t,r+3,u-3)?t=0:(K=t[r+0]|t[r+1]<<8|t[r+2]<<16,nt=16383&(t[r+7]<<8|t[r+6]),t=16383&(t[r+9]<<8|t[r+8]),1&K||3<(K>>1&7)||!(K>>4&1)||K>>5>=W.Ja||!nt||!t?t=0:(y&&(y[0]=nt),N&&(N[0]=t),t=1))}if(!t||(y=y[0],ht=ht[0],X&&(P[0]!=y||R[0]!=ht)))return 3;A!=null&&(A[0]=W,A.offset=r-A.w,e(4294967286>r-A.w),e(A.offset==A.ha-u));break}return z==0||z==7&&X&&A==null?(b!=null&&(b[0]|=W.na!=null&&0<W.na.length),d!=null&&(d[0]=y),m!=null&&(m[0]=ht),0):z}function Ki(t,r,u){var d=r.width,m=r.height,b=0,y=0,N=d,A=m;if(r.Da=t!=null&&0<t.Da,r.Da&&(N=t.cd,A=t.bd,b=t.v,y=t.j,11>u||(b&=-2,y&=-2),0>b||0>y||0>=N||0>=A||b+N>d||y+A>m))return 0;if(r.v=b,r.j=y,r.va=b+N,r.o=y+A,r.U=N,r.T=A,r.da=t!=null&&0<t.da,r.da){if(!Kt(N,A,u=[t.ib],b=[t.hb]))return 0;r.ib=u[0],r.hb=b[0]}return r.ob=t!=null&&t.ob,r.Kb=t==null||!t.Sd,r.da&&(r.ob=r.ib<3*d/4&&r.hb<3*m/4,r.Kb=0),1}function $i(t){if(t==null)return 2;if(11>t.S){var r=t.f.RGBA;r.fb+=(t.height-1)*r.A,r.A=-r.A}else r=t.f.kb,t=t.height,r.O+=(t-1)*r.fa,r.fa=-r.fa,r.N+=(t-1>>1)*r.Ab,r.Ab=-r.Ab,r.W+=(t-1>>1)*r.Db,r.Db=-r.Db,r.F!=null&&(r.J+=(t-1)*r.lb,r.lb=-r.lb);return 0}function ci(t,r,u,d){if(d==null||0>=t||0>=r)return 2;if(u!=null){if(u.Da){var m=u.cd,b=u.bd,y=-2&u.v,N=-2&u.j;if(0>y||0>N||0>=m||0>=b||y+m>t||N+b>r)return 2;t=m,r=b}if(u.da){if(!Kt(t,r,m=[u.ib],b=[u.hb]))return 2;t=m[0],r=b[0]}}d.width=t,d.height=r;t:{var A=d.width,P=d.height;if(t=d.S,0>=A||0>=P||!(t>=Ra&&13>t))t=2;else{if(0>=d.Rd&&d.sd==null){y=b=m=r=0;var R=(N=A*Ds[t])*P;if(11>t||(b=(P+1)/2*(r=(A+1)/2),t==12&&(y=(m=A)*P)),(P=o(R+2*b+y))==null){t=1;break t}d.sd=P,11>t?((A=d.f.RGBA).eb=P,A.fb=0,A.A=N,A.size=R):((A=d.f.kb).y=P,A.O=0,A.fa=N,A.Fd=R,A.f=P,A.N=0+R,A.Ab=r,A.Cd=b,A.ea=P,A.W=0+R+b,A.Db=r,A.Ed=b,t==12&&(A.F=P,A.J=0+R+2*b),A.Tc=y,A.lb=m)}if(r=1,m=d.S,b=d.width,y=d.height,m>=Ra&&13>m)if(11>m)t=d.f.RGBA,r&=(N=Math.abs(t.A))*(y-1)+b<=t.size,r&=N>=b*Ds[m],r&=t.eb!=null;else{t=d.f.kb,N=(b+1)/2,R=(y+1)/2,A=Math.abs(t.fa),P=Math.abs(t.Ab);var X=Math.abs(t.Db),K=Math.abs(t.lb),W=K*(y-1)+b;r&=A*(y-1)+b<=t.Fd,r&=P*(R-1)+N<=t.Cd,r=(r&=X*(R-1)+N<=t.Ed)&A>=b&P>=N&X>=N,r&=t.y!=null,r&=t.f!=null,r&=t.ea!=null,m==12&&(r&=K>=b,r&=W<=t.Tc,r&=t.F!=null)}else r=0;t=r?0:2}}return t!=0||u!=null&&u.fd&&(t=$i(d)),t}var Ge=64,ui=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],li=24,hi=32,Zi=8,sn=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];At("Predictor0","PredictorAdd0"),G.Predictor0=function(){return **********},G.Predictor1=function(t){return t},G.Predictor2=function(t,r,u){return r[u+0]},G.Predictor3=function(t,r,u){return r[u+1]},G.Predictor4=function(t,r,u){return r[u-1]},G.Predictor5=function(t,r,u){return Pt(Pt(t,r[u+1]),r[u+0])},G.Predictor6=function(t,r,u){return Pt(t,r[u-1])},G.Predictor7=function(t,r,u){return Pt(t,r[u+0])},G.Predictor8=function(t,r,u){return Pt(r[u-1],r[u+0])},G.Predictor9=function(t,r,u){return Pt(r[u+0],r[u+1])},G.Predictor10=function(t,r,u){return Pt(Pt(t,r[u-1]),Pt(r[u+0],r[u+1]))},G.Predictor11=function(t,r,u){var d=r[u+0];return 0>=Qt(d>>24&255,t>>24&255,(r=r[u-1])>>24&255)+Qt(d>>16&255,t>>16&255,r>>16&255)+Qt(d>>8&255,t>>8&255,r>>8&255)+Qt(255&d,255&t,255&r)?d:t},G.Predictor12=function(t,r,u){var d=r[u+0];return(Tt((t>>24&255)+(d>>24&255)-((r=r[u-1])>>24&255))<<24|Tt((t>>16&255)+(d>>16&255)-(r>>16&255))<<16|Tt((t>>8&255)+(d>>8&255)-(r>>8&255))<<8|Tt((255&t)+(255&d)-(255&r)))>>>0},G.Predictor13=function(t,r,u){var d=r[u-1];return(Gt((t=Pt(t,r[u+0]))>>24&255,d>>24&255)<<24|Gt(t>>16&255,d>>16&255)<<16|Gt(t>>8&255,d>>8&255)<<8|Gt(t>>0&255,d>>0&255))>>>0};var Uo=G.PredictorAdd0;G.PredictorAdd1=te,At("Predictor2","PredictorAdd2"),At("Predictor3","PredictorAdd3"),At("Predictor4","PredictorAdd4"),At("Predictor5","PredictorAdd5"),At("Predictor6","PredictorAdd6"),At("Predictor7","PredictorAdd7"),At("Predictor8","PredictorAdd8"),At("Predictor9","PredictorAdd9"),At("Predictor10","PredictorAdd10"),At("Predictor11","PredictorAdd11"),At("Predictor12","PredictorAdd12"),At("Predictor13","PredictorAdd13");var Qi=G.PredictorAdd2;ee("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),ee("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var Ba,bn=G.ColorIndexInverseTransform,fi=G.MapARGB,Ma=G.VP8LColorIndexInverseTransformAlpha,Ea=G.MapAlpha,dr=G.VP8LPredictorsAdd=[];dr.length=16,(G.VP8LPredictors=[]).length=16,(G.VP8LPredictorsAdd_C=[]).length=16,(G.VP8LPredictors_C=[]).length=16;var Mr,cn,en,pr,rr,ir,di,ar,Ce,ta,nn,yn,pi,Ta,ea,Er,Tr,gr,qr,gi,Dr,mr,na,wn,Nn,we,Ne,Ie,Re=o(511),or=o(2041),ra=o(225),mi=o(767),qa=0,Ho=or,Da=ra,pn=mi,Ln=Re,Ra=0,za=1,Ps=2,Ua=3,Ha=4,Vo=5,ks=6,Wo=7,Go=8,Va=9,Jo=10,qc=[2,3,7],Dc=[3,3,11],Fs=[280,256,256,256,40],Rc=[0,1,1,1,0],zc=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],Uc=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Hc=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],Vc=8,Yo=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],Xo=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],ia=null,Wc=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],Gc=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],Cs=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],Jc=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],Yc=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],Xc=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],Kc=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],vr=[],Bn=[],sr=[],$c=1,Is=2,br=[],xn=[];it("UpsampleRgbLinePair",re,3),it("UpsampleBgrLinePair",xe,3),it("UpsampleRgbaLinePair",On,4),it("UpsampleBgraLinePair",ze,4),it("UpsampleArgbLinePair",Ke,4),it("UpsampleRgba4444LinePair",Ee,2),it("UpsampleRgb565LinePair",_e,2);var Zc=G.UpsampleRgbLinePair,Qc=G.UpsampleBgrLinePair,js=G.UpsampleRgbaLinePair,Os=G.UpsampleBgraLinePair,Bs=G.UpsampleArgbLinePair,Ms=G.UpsampleRgba4444LinePair,tu=G.UpsampleRgb565LinePair,Wa=16,Ga=1<<Wa-1,aa=-227,Ko=482,Es=6,eu=(256<<Es)-1,Ts=0,nu=o(256),ru=o(256),iu=o(256),au=o(256),ou=o(Ko-aa),su=o(Ko-aa);Jn("YuvToRgbRow",re,3),Jn("YuvToBgrRow",xe,3),Jn("YuvToRgbaRow",On,4),Jn("YuvToBgraRow",ze,4),Jn("YuvToArgbRow",Ke,4),Jn("YuvToRgba4444Row",Ee,2),Jn("YuvToRgb565Row",_e,2);var qs=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],Ja=[0,2,8],cu=[8,7,6,4,4,2,2,2,1,1,1,1],uu=1;this.WebPDecodeRGBA=function(t,r,u,d,m){var b=za,y=new ai,N=new _n;y.ba=N,N.S=b,N.width=[N.width],N.height=[N.height];var A=N.width,P=N.height,R=new Qn;if(R==null||t==null)var X=2;else e(R!=null),X=si(t,r,u,R.width,R.height,R.Pd,R.Qd,R.format,null);if(X!=0?A=0:(A!=null&&(A[0]=R.width[0]),P!=null&&(P[0]=R.height[0]),A=1),A){N.width=N.width[0],N.height=N.height[0],d!=null&&(d[0]=N.width),m!=null&&(m[0]=N.height);t:{if(d=new Zr,(m=new Vi).data=t,m.w=r,m.ha=u,m.kd=1,r=[0],e(m!=null),((t=si(m.data,m.w,m.ha,null,null,null,r,null,m))==0||t==7)&&r[0]&&(t=4),(r=t)==0){if(e(y!=null),d.data=m.data,d.w=m.w+m.offset,d.ha=m.ha-m.offset,d.put=Rn,d.ac=We,d.bc=zn,d.ma=y,m.xa){if((t=vn())==null){y=1;break t}if(function(K,W){var ht=[0],nt=[0],z=[0];e:for(;;){if(K==null)return 0;if(W==null)return K.a=2,0;if(K.l=W,K.a=0,tt(K.m,W.data,W.w,W.ha),!Rt(K.m,ht,nt,z)){K.a=3;break e}if(K.xb=Is,W.width=ht[0],W.height=nt[0],!Vn(ht[0],nt[0],1,K,null))break e;return 1}return e(K.a!=0),0}(t,d)){if(d=(r=ci(d.width,d.height,y.Oa,y.ba))==0){e:{d=t;n:for(;;){if(d==null){d=0;break e}if(e(d.s.yc!=null),e(d.s.Ya!=null),e(0<d.s.Wb),e((u=d.l)!=null),e((m=u.ma)!=null),d.xb!=0){if(d.ca=m.ba,d.tb=m.tb,e(d.ca!=null),!Ki(m.Oa,u,Ua)){d.a=2;break n}if(!Cr(d,u.width)||u.da)break n;if((u.da||ce(d.ca.S))&&Y(),11>d.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),d.ca.f.kb.F!=null&&Y()),d.Pb&&0<d.s.ua&&d.s.vb.X==null&&!Vt(d.s.vb,d.s.Wa.Xa)){d.a=1;break n}d.xb=0}if(!Cn(d,d.V,d.Ba,d.c,d.i,u.o,Gr))break n;m.Dc=d.Ma,d=1;break e}e(d.a!=0),d=0}d=!d}d&&(r=t.a)}else r=t.a}else{if((t=new xo)==null){y=1;break t}if(t.Fa=m.na,t.P=m.P,t.qc=m.Sa,ba(t,d)){if((r=ci(d.width,d.height,y.Oa,y.ba))==0){if(t.Aa=0,u=y.Oa,e((m=t)!=null),u!=null){if(0<(A=0>(A=u.Md)?0:100<A?255:255*A/100)){for(P=R=0;4>P;++P)12>(X=m.pb[P]).lc&&(X.ia=A*cu[0>X.lc?0:X.lc]>>3),R|=X.ia;R&&(alert("todo:VP8InitRandom"),m.ia=1)}m.Ga=u.Id,100<m.Ga?m.Ga=100:0>m.Ga&&(m.Ga=0)}Ao(t,d)||(r=t.a)}}else r=t.a}r==0&&y.Oa!=null&&y.Oa.fd&&(r=$i(y.ba))}y=r}b=y!=0?null:11>b?N.f.RGBA.eb:N.f.kb.y}else b=null;return b};var Ds=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function g(G,vt){for(var bt="",k=0;k<4;k++)bt+=String.fromCharCode(G[vt++]);return bt}function x(G,vt){return(G[vt+0]<<0|G[vt+1]<<8|G[vt+2]<<16)>>>0}function L(G,vt){return(G[vt+0]<<0|G[vt+1]<<8|G[vt+2]<<16|G[vt+3]<<24)>>>0}new f;var S=[0],p=[0],O=[],C=new f,T=i,_=function(G,vt){var bt={},k=0,F=!1,H=0,D=0;if(bt.frames=[],!function(j,E,V,J){for(var Z=0;Z<J;Z++)if(j[E+Z]!=V.charCodeAt(Z))return!0;return!1}(G,vt,"RIFF",4)){var ct,ot;for(L(G,vt+=4),vt+=8;vt<G.length;){var mt=g(G,vt),tt=L(G,vt+=4);vt+=4;var pt=tt+(1&tt);switch(mt){case"VP8 ":case"VP8L":bt.frames[k]===void 0&&(bt.frames[k]={}),(w=bt.frames[k]).src_off=F?D:vt-8,w.src_size=H+tt+8,k++,F&&(F=!1,H=0,D=0);break;case"VP8X":(w=bt.header={}).feature_flags=G[vt];var ft=vt+4;w.canvas_width=1+x(G,ft),ft+=3,w.canvas_height=1+x(G,ft),ft+=3;break;case"ALPH":F=!0,H=pt+8,D=vt-8;break;case"ANIM":(w=bt.header).bgcolor=L(G,vt),ft=vt+4,w.loop_count=(ct=G)[(ot=ft)+0]<<0|ct[ot+1]<<8,ft+=2;break;case"ANMF":var Et,w;(w=bt.frames[k]={}).offset_x=2*x(G,vt),vt+=3,w.offset_y=2*x(G,vt),vt+=3,w.width=1+x(G,vt),vt+=3,w.height=1+x(G,vt),vt+=3,w.duration=x(G,vt),vt+=3,Et=G[vt++],w.dispose=1&Et,w.blend=Et>>1&1}mt!="ANMF"&&(vt+=pt)}return bt}}(T,0);_.response=T,_.rgbaoutput=!0,_.dataurl=!1;var M=_.header?_.header:null,$=_.frames?_.frames:null;if(M){M.loop_counter=M.loop_count,S=[M.canvas_height],p=[M.canvas_width];for(var st=0;st<$.length&&$[st].blend!=0;st++);}var dt=$[0],Lt=C.WebPDecodeRGBA(T,dt.src_off,dt.src_size,p,S);dt.rgba=Lt,dt.imgwidth=p[0],dt.imgheight=S[0];for(var rt=0;rt<p[0]*S[0]*4;rt++)O[rt]=Lt[rt];return this.width=p,this.height=S,this.data=O,this}(function(i){var e=function(){return typeof ms=="function"},n=function(S,p,O,C){var T=4,_=l;switch(C){case i.image_compression.FAST:T=1,_=o;break;case i.image_compression.MEDIUM:T=6,_=h;break;case i.image_compression.SLOW:T=9,_=f}S=a(S,p,O,_);var M=ms(S,{level:T});return i.__addimage__.arrayBufferToBinaryString(M)},a=function(S,p,O,C){for(var T,_,M,$=S.length/p,st=new Uint8Array(S.length+$),dt=x(),Lt=0;Lt<$;Lt+=1){if(M=Lt*p,T=S.subarray(M,M+p),C)st.set(C(T,O,_),M+Lt);else{for(var rt,G=dt.length,vt=[];rt<G;rt+=1)vt[rt]=dt[rt](T,O,_);var bt=L(vt.concat());st.set(vt[bt],M+Lt)}_=T}return st},c=function(S){var p=Array.apply([],S);return p.unshift(0),p},o=function(S,p){var O,C=[],T=S.length;C[0]=1;for(var _=0;_<T;_+=1)O=S[_-p]||0,C[_+1]=S[_]-O+256&255;return C},l=function(S,p,O){var C,T=[],_=S.length;T[0]=2;for(var M=0;M<_;M+=1)C=O&&O[M]||0,T[M+1]=S[M]-C+256&255;return T},h=function(S,p,O){var C,T,_=[],M=S.length;_[0]=3;for(var $=0;$<M;$+=1)C=S[$-p]||0,T=O&&O[$]||0,_[$+1]=S[$]+256-(C+T>>>1)&255;return _},f=function(S,p,O){var C,T,_,M,$=[],st=S.length;$[0]=4;for(var dt=0;dt<st;dt+=1)C=S[dt-p]||0,T=O&&O[dt]||0,_=O&&O[dt-p]||0,M=g(C,T,_),$[dt+1]=S[dt]-M+256&255;return $},g=function(S,p,O){if(S===p&&p===O)return S;var C=Math.abs(p-O),T=Math.abs(S-O),_=Math.abs(S+p-O-O);return C<=T&&C<=_?S:T<=_?p:O},x=function(){return[c,o,l,h,f]},L=function(S){var p=S.map(function(O){return O.reduce(function(C,T){return C+Math.abs(T)},0)});return p.indexOf(Math.min.apply(null,p))};i.processPNG=function(S,p,O,C){var T,_,M,$,st,dt,Lt,rt,G,vt,bt,k,F,H,D,ct=this.decode.FLATE_DECODE,ot="";if(this.__addimage__.isArrayBuffer(S)&&(S=new Uint8Array(S)),this.__addimage__.isArrayBufferView(S)){if(S=(M=new Ll(S)).imgData,_=M.bits,T=M.colorSpace,st=M.colors,[4,6].indexOf(M.colorType)!==-1){if(M.bits===8){G=(rt=M.pixelBitlength==32?new Uint32Array(M.decodePixels().buffer):M.pixelBitlength==16?new Uint16Array(M.decodePixels().buffer):new Uint8Array(M.decodePixels().buffer)).length,bt=new Uint8Array(G*M.colors),vt=new Uint8Array(G);var mt,tt=M.pixelBitlength-M.bits;for(H=0,D=0;H<G;H++){for(F=rt[H],mt=0;mt<tt;)bt[D++]=F>>>mt&255,mt+=M.bits;vt[H]=F>>>mt&255}}if(M.bits===16){G=(rt=new Uint32Array(M.decodePixels().buffer)).length,bt=new Uint8Array(G*(32/M.pixelBitlength)*M.colors),vt=new Uint8Array(G*(32/M.pixelBitlength)),k=M.colors>1,H=0,D=0;for(var pt=0;H<G;)F=rt[H++],bt[D++]=F>>>0&255,k&&(bt[D++]=F>>>16&255,F=rt[H++],bt[D++]=F>>>0&255),vt[pt++]=F>>>16&255;_=8}C!==i.image_compression.NONE&&e()?(S=n(bt,M.width*M.colors,M.colors,C),Lt=n(vt,M.width,1,C)):(S=bt,Lt=vt,ct=void 0)}if(M.colorType===3&&(T=this.color_spaces.INDEXED,dt=M.palette,M.transparency.indexed)){var ft=M.transparency.indexed,Et=0;for(H=0,G=ft.length;H<G;++H)Et+=ft[H];if((Et/=255)===G-1&&ft.indexOf(0)!==-1)$=[ft.indexOf(0)];else if(Et!==G){for(rt=M.decodePixels(),vt=new Uint8Array(rt.length),H=0,G=rt.length;H<G;H++)vt[H]=ft[rt[H]];Lt=n(vt,M.width,1)}}var w=function(j){var E;switch(j){case i.image_compression.FAST:E=11;break;case i.image_compression.MEDIUM:E=13;break;case i.image_compression.SLOW:E=14;break;default:E=12}return E}(C);return ct===this.decode.FLATE_DECODE&&(ot="/Predictor "+w+" "),ot+="/Colors "+st+" /BitsPerComponent "+_+" /Columns "+M.width,(this.__addimage__.isArrayBuffer(S)||this.__addimage__.isArrayBufferView(S))&&(S=this.__addimage__.arrayBufferToBinaryString(S)),(Lt&&this.__addimage__.isArrayBuffer(Lt)||this.__addimage__.isArrayBufferView(Lt))&&(Lt=this.__addimage__.arrayBufferToBinaryString(Lt)),{alias:O,data:S,index:p,filter:ct,decodeParameters:ot,transparency:$,palette:dt,sMask:Lt,predictor:w,width:M.width,height:M.height,bitsPerComponent:_,colorSpace:T}}}})(Ut.API),function(i){i.processGIF89A=function(e,n,a,c){var o=new xl(e),l=o.width,h=o.height,f=[];o.decodeAndBlitFrameRGBA(0,f);var g={data:f,width:l,height:h},x=new ps(100).encode(g,100);return i.processJPEG.call(this,x,n,a,c)},i.processGIF87A=i.processGIF89A}(Ut.API),En.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var i=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(i);for(var e=0;e<i;e++){var n=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:c,green:a,blue:n,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},En.prototype.parseBGR=function(){this.pos=this.offset;try{var i="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[i]()}catch(n){ve.log("bit decode error:"+n)}},En.prototype.bit1=function(){var i,e=Math.ceil(this.width/8),n=e%4;for(i=this.height-1;i>=0;i--){for(var a=this.bottom_up?i:this.height-1-i,c=0;c<e;c++)for(var o=this.datav.getUint8(this.pos++,!0),l=a*this.width*4+8*c*4,h=0;h<8&&8*c+h<this.width;h++){var f=this.palette[o>>7-h&1];this.data[l+4*h]=f.blue,this.data[l+4*h+1]=f.green,this.data[l+4*h+2]=f.red,this.data[l+4*h+3]=255}n!==0&&(this.pos+=4-n)}},En.prototype.bit4=function(){for(var i=Math.ceil(this.width/2),e=i%4,n=this.height-1;n>=0;n--){for(var a=this.bottom_up?n:this.height-1-n,c=0;c<i;c++){var o=this.datav.getUint8(this.pos++,!0),l=a*this.width*4+2*c*4,h=o>>4,f=15&o,g=this.palette[h];if(this.data[l]=g.blue,this.data[l+1]=g.green,this.data[l+2]=g.red,this.data[l+3]=255,2*c+1>=this.width)break;g=this.palette[f],this.data[l+4]=g.blue,this.data[l+4+1]=g.green,this.data[l+4+2]=g.red,this.data[l+4+3]=255}e!==0&&(this.pos+=4-e)}},En.prototype.bit8=function(){for(var i=this.width%4,e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,a=0;a<this.width;a++){var c=this.datav.getUint8(this.pos++,!0),o=n*this.width*4+4*a;if(c<this.palette.length){var l=this.palette[c];this.data[o]=l.red,this.data[o+1]=l.green,this.data[o+2]=l.blue,this.data[o+3]=255}else this.data[o]=255,this.data[o+1]=255,this.data[o+2]=255,this.data[o+3]=255}i!==0&&(this.pos+=4-i)}},En.prototype.bit15=function(){for(var i=this.width%3,e=parseInt("11111",2),n=this.height-1;n>=0;n--){for(var a=this.bottom_up?n:this.height-1-n,c=0;c<this.width;c++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var l=(o&e)/e*255|0,h=(o>>5&e)/e*255|0,f=(o>>10&e)/e*255|0,g=o>>15?255:0,x=a*this.width*4+4*c;this.data[x]=f,this.data[x+1]=h,this.data[x+2]=l,this.data[x+3]=g}this.pos+=i}},En.prototype.bit16=function(){for(var i=this.width%3,e=parseInt("11111",2),n=parseInt("111111",2),a=this.height-1;a>=0;a--){for(var c=this.bottom_up?a:this.height-1-a,o=0;o<this.width;o++){var l=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(l&e)/e*255|0,f=(l>>5&n)/n*255|0,g=(l>>11)/e*255|0,x=c*this.width*4+4*o;this.data[x]=g,this.data[x+1]=f,this.data[x+2]=h,this.data[x+3]=255}this.pos+=i}},En.prototype.bit24=function(){for(var i=this.height-1;i>=0;i--){for(var e=this.bottom_up?i:this.height-1-i,n=0;n<this.width;n++){var a=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),l=e*this.width*4+4*n;this.data[l]=o,this.data[l+1]=c,this.data[l+2]=a,this.data[l+3]=255}this.pos+=this.width%4}},En.prototype.bit32=function(){for(var i=this.height-1;i>=0;i--)for(var e=this.bottom_up?i:this.height-1-i,n=0;n<this.width;n++){var a=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),l=this.datav.getUint8(this.pos++,!0),h=e*this.width*4+4*n;this.data[h]=o,this.data[h+1]=c,this.data[h+2]=a,this.data[h+3]=l}},En.prototype.getData=function(){return this.data},function(i){i.processBMP=function(e,n,a,c){var o=new En(e,!1),l=o.width,h=o.height,f={data:o.getData(),width:l,height:h},g=new ps(100).encode(f,100);return i.processJPEG.call(this,g,n,a,c)}}(Ut.API),Lc.prototype.getData=function(){return this.data},function(i){i.processWEBP=function(e,n,a,c){var o=new Lc(e),l=o.width,h=o.height,f={data:o.getData(),width:l,height:h},g=new ps(100).encode(f,100);return i.processJPEG.call(this,g,n,a,c)}}(Ut.API),Ut.API.processRGBA=function(i,e,n){for(var a=i.data,c=a.length,o=new Uint8Array(c/4*3),l=new Uint8Array(c/4),h=0,f=0,g=0;g<c;g+=4){var x=a[g],L=a[g+1],S=a[g+2],p=a[g+3];o[h++]=x,o[h++]=L,o[h++]=S,l[f++]=p}var O=this.__addimage__.arrayBufferToBinaryString(o);return{alpha:this.__addimage__.arrayBufferToBinaryString(l),data:O,index:e,alias:n,colorSpace:"DeviceRGB",bitsPerComponent:8,width:i.width,height:i.height}},Ut.API.setLanguage=function(i){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[i]!==void 0&&(this.internal.languageSettings.languageCode=i,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},Li=Ut.API,so=Li.getCharWidthsArray=function(i,e){var n,a,c=(e=e||{}).font||this.internal.getFont(),o=e.fontSize||this.internal.getFontSize(),l=e.charSpace||this.internal.getCharSpace(),h=e.widths?e.widths:c.metadata.Unicode.widths,f=h.fof?h.fof:1,g=e.kerning?e.kerning:c.metadata.Unicode.kerning,x=g.fof?g.fof:1,L=e.doKerning!==!1,S=0,p=i.length,O=0,C=h[0]||f,T=[];for(n=0;n<p;n++)a=i.charCodeAt(n),typeof c.metadata.widthOfString=="function"?T.push((c.metadata.widthOfGlyph(c.metadata.characterToGlyph(a))+l*(1e3/o)||0)/1e3):(S=L&&fe(g[a])==="object"&&!isNaN(parseInt(g[a][O],10))?g[a][O]/x:0,T.push((h[a]||C)/f+S)),O=a;return T},bc=Li.getStringUnitWidth=function(i,e){var n=(e=e||{}).fontSize||this.internal.getFontSize(),a=e.font||this.internal.getFont(),c=e.charSpace||this.internal.getCharSpace();return Li.processArabic&&(i=Li.processArabic(i)),typeof a.metadata.widthOfString=="function"?a.metadata.widthOfString(i,n,c)/n:so.apply(this,arguments).reduce(function(o,l){return o+l},0)},yc=function(i,e,n,a){for(var c=[],o=0,l=i.length,h=0;o!==l&&h+e[o]<n;)h+=e[o],o++;c.push(i.slice(0,o));var f=o;for(h=0;o!==l;)h+e[o]>a&&(c.push(i.slice(f,o)),h=0,f=o),h+=e[o],o++;return f!==o&&c.push(i.slice(f,o)),c},wc=function(i,e,n){n||(n={});var a,c,o,l,h,f,g,x=[],L=[x],S=n.textIndent||0,p=0,O=0,C=i.split(" "),T=so.apply(this,[" ",n])[0];if(f=n.lineIndent===-1?C[0].length+2:n.lineIndent||0){var _=Array(f).join(" "),M=[];C.map(function(st){(st=st.split(/\s*\n/)).length>1?M=M.concat(st.map(function(dt,Lt){return(Lt&&dt.length?`
`:"")+dt})):M.push(st[0])}),C=M,f=bc.apply(this,[_,n])}for(o=0,l=C.length;o<l;o++){var $=0;if(a=C[o],f&&a[0]==`
`&&(a=a.substr(1),$=1),S+p+(O=(c=so.apply(this,[a,n])).reduce(function(st,dt){return st+dt},0))>e||$){if(O>e){for(h=yc.apply(this,[a,c,e-(S+p),e]),x.push(h.shift()),x=[h.pop()];h.length;)L.push([h.shift()]);O=c.slice(a.length-(x[0]?x[0].length:0)).reduce(function(st,dt){return st+dt},0)}else x=[a];L.push(x),S=O+f,p=T}else x.push(a),S+=p+O,p=T}return g=f?function(st,dt){return(dt?_:"")+st.join(" ")}:function(st){return st.join(" ")},L.map(g)},Li.splitTextToSize=function(i,e,n){var a,c=(n=n||{}).fontSize||this.internal.getFontSize(),o=(function(x){if(x.widths&&x.kerning)return{widths:x.widths,kerning:x.kerning};var L=this.internal.getFont(x.fontName,x.fontStyle);return L.metadata.Unicode?{widths:L.metadata.Unicode.widths||{0:1},kerning:L.metadata.Unicode.kerning||{}}:{font:L.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,n);a=Array.isArray(i)?i:String(i).split(/\r?\n/);var l=1*this.internal.scaleFactor*e/c;o.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/c:0,o.lineIndent=n.lineIndent;var h,f,g=[];for(h=0,f=a.length;h<f;h++)g=g.concat(wc.apply(this,[a[h],l,o]));return g},function(i){i.__fontmetrics__=i.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",n={},a={},c=0;c<e.length;c++)n[e[c]]="0123456789abcdef"[c],a["0123456789abcdef"[c]]=e[c];var o=function(L){return"0x"+parseInt(L,10).toString(16)},l=i.__fontmetrics__.compress=function(L){var S,p,O,C,T=["{"];for(var _ in L){if(S=L[_],isNaN(parseInt(_,10))?p="'"+_+"'":(_=parseInt(_,10),p=(p=o(_).slice(2)).slice(0,-1)+a[p.slice(-1)]),typeof S=="number")S<0?(O=o(S).slice(3),C="-"):(O=o(S).slice(2),C=""),O=C+O.slice(0,-1)+a[O.slice(-1)];else{if(fe(S)!=="object")throw new Error("Don't know what to do with value type "+fe(S)+".");O=l(S)}T.push(p+O)}return T.push("}"),T.join("")},h=i.__fontmetrics__.uncompress=function(L){if(typeof L!="string")throw new Error("Invalid argument passed to uncompress.");for(var S,p,O,C,T={},_=1,M=T,$=[],st="",dt="",Lt=L.length-1,rt=1;rt<Lt;rt+=1)(C=L[rt])=="'"?S?(O=S.join(""),S=void 0):S=[]:S?S.push(C):C=="{"?($.push([M,O]),M={},O=void 0):C=="}"?((p=$.pop())[0][p[1]]=M,O=void 0,M=p[0]):C=="-"?_=-1:O===void 0?n.hasOwnProperty(C)?(st+=n[C],O=parseInt(st,16)*_,_=1,st=""):st+=C:n.hasOwnProperty(C)?(dt+=n[C],M[O]=parseInt(dt,16)*_,_=1,O=void 0,dt=""):dt+=C;return T},f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},g={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},x={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};i.events.push(["addFont",function(L){var S=L.font,p=x.Unicode[S.postScriptName];p&&(S.metadata.Unicode={},S.metadata.Unicode.widths=p.widths,S.metadata.Unicode.kerning=p.kerning);var O=g.Unicode[S.postScriptName];O&&(S.metadata.Unicode.encoding=O,S.encoding=O.codePages[0])}])}(Ut.API),function(i){var e=function(n){for(var a=n.length,c=new Uint8Array(a),o=0;o<a;o++)c[o]=n.charCodeAt(o);return c};i.API.events.push(["addFont",function(n){var a=void 0,c=n.font,o=n.instance;if(!c.isStandardFont){if(o===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+c.postScriptName+"').");if(typeof(a=o.existsFileInVFS(c.postScriptName)===!1?o.loadFile(c.postScriptName):o.getFileFromVFS(c.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+c.postScriptName+"').");(function(l,h){h=/^\x00\x01\x00\x00/.test(h)?e(h):e(da(h)),l.metadata=i.API.TTFFont.open(h),l.metadata.Unicode=l.metadata.Unicode||{encoding:{},kerning:{},widths:[]},l.metadata.glyIdsUsed=[0]})(c,a)}}])}(Ut),function(i){function e(){return(zt.canvg?Promise.resolve(zt.canvg):gs(()=>import("./index.es-247b7e49.js"),["assets/index.es-247b7e49.js","assets/index-9381ab2b.js","assets/index-58f11664.css","assets/index-c19c3f80.js","assets/isUndefined-aa0326a0.js","assets/index-094198d8.js","assets/index-34c19038.js","assets/index-1dbceb27.js","assets/strings-820bea19.js","assets/isEqual-a619023a.js","assets/index-a4ffe93f.js","assets/index-951011fc.js","assets/castArray-25c7c99e.js","assets/_plugin-vue_export-helper-c27b6911.js","assets/browser-6cfa1fde.js"])).catch(function(n){return Promise.reject(new Error("Could not load canvg: "+n))}).then(function(n){return n.default?n.default:n})}Ut.API.addSvgAsImage=function(n,a,c,o,l,h,f,g){if(isNaN(a)||isNaN(c))throw ve.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(o)||isNaN(l))throw ve.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var x=document.createElement("canvas");x.width=o,x.height=l;var L=x.getContext("2d");L.fillStyle="#fff",L.fillRect(0,0,x.width,x.height);var S={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},p=this;return e().then(function(O){return O.fromString(L,n,S)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(O){return O.render(S)}).then(function(){p.addImage(x.toDataURL("image/jpeg",1),a,c,o,l,f,g)})}}(),Ut.API.putTotalPages=function(i){var e,n=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(i,"g"),n=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(i,this.internal.getFont()),"g"),n=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var a=1;a<=this.internal.getNumberOfPages();a++)for(var c=0;c<this.internal.pages[a].length;c++)this.internal.pages[a][c]=this.internal.pages[a][c].replace(e,n);return this},Ut.API.viewerPreferences=function(i,e){var n;i=i||{},e=e||!1;var a,c,o,l={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},h=Object.keys(l),f=[],g=0,x=0,L=0;function S(O,C){var T,_=!1;for(T=0;T<O.length;T+=1)O[T]===C&&(_=!0);return _}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(l)),this.internal.viewerpreferences.isSubscribed=!1),n=this.internal.viewerpreferences.configuration,i==="reset"||e===!0){var p=h.length;for(L=0;L<p;L+=1)n[h[L]].value=n[h[L]].defaultValue,n[h[L]].explicitSet=!1}if(fe(i)==="object"){for(c in i)if(o=i[c],S(h,c)&&o!==void 0){if(n[c].type==="boolean"&&typeof o=="boolean")n[c].value=o;else if(n[c].type==="name"&&S(n[c].valueSet,o))n[c].value=o;else if(n[c].type==="integer"&&Number.isInteger(o))n[c].value=o;else if(n[c].type==="array"){for(g=0;g<o.length;g+=1)if(a=!0,o[g].length===1&&typeof o[g][0]=="number")f.push(String(o[g]-1));else if(o[g].length>1){for(x=0;x<o[g].length;x+=1)typeof o[g][x]!="number"&&(a=!1);a===!0&&f.push([o[g][0]-1,o[g][1]-1].join(" "))}n[c].value="["+f.join(" ")+"]"}else n[c].value=n[c].defaultValue;n[c].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var O,C=[];for(O in n)n[O].explicitSet===!0&&(n[O].type==="name"?C.push("/"+O+" /"+n[O].value):C.push("/"+O+" "+n[O].value));C.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+C.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=n,this},function(i){var e=function(){var a='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',c=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),o=unescape(encodeURIComponent(a)),l=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),h=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),f=unescape(encodeURIComponent("</x:xmpmeta>")),g=o.length+l.length+h.length+c.length+f.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+g+" >>"),this.internal.write("stream"),this.internal.write(c+o+l+h+f),this.internal.write("endstream"),this.internal.write("endobj")},n=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};i.addMetadata=function(a,c){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:a,namespaceuri:c||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",n),this.internal.events.subscribe("postPutResources",e)),this}}(Ut.API),function(i){var e=i.API,n=e.pdfEscape16=function(o,l){for(var h,f=l.metadata.Unicode.widths,g=["","0","00","000","0000"],x=[""],L=0,S=o.length;L<S;++L){if(h=l.metadata.characterToGlyph(o.charCodeAt(L)),l.metadata.glyIdsUsed.push(h),l.metadata.toUnicode[h]=o.charCodeAt(L),f.indexOf(h)==-1&&(f.push(h),f.push([parseInt(l.metadata.widthOfGlyph(h),10)])),h=="0")return x.join("");h=h.toString(16),x.push(g[4-h.length],h)}return x.join("")},a=function(o){var l,h,f,g,x,L,S;for(x=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,f=[],L=0,S=(h=Object.keys(o).sort(function(p,O){return p-O})).length;L<S;L++)l=h[L],f.length>=100&&(x+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar`,f=[]),o[l]!==void 0&&o[l]!==null&&typeof o[l].toString=="function"&&(g=("0000"+o[l].toString(16)).slice(-4),l=("0000"+(+l).toString(16)).slice(-4),f.push("<"+l+"><"+g+">"));return f.length&&(x+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar
`),x+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};e.events.push(["putFont",function(o){(function(l){var h=l.font,f=l.out,g=l.newObject,x=l.putStream;if(h.metadata instanceof i.API.TTFFont&&h.encoding==="Identity-H"){for(var L=h.metadata.Unicode.widths,S=h.metadata.subset.encode(h.metadata.glyIdsUsed,1),p="",O=0;O<S.length;O++)p+=String.fromCharCode(S[O]);var C=g();x({data:p,addLength1:!0,objectId:C}),f("endobj");var T=g();x({data:a(h.metadata.toUnicode),addLength1:!0,objectId:T}),f("endobj");var _=g();f("<<"),f("/Type /FontDescriptor"),f("/FontName /"+Ai(h.fontName)),f("/FontFile2 "+C+" 0 R"),f("/FontBBox "+i.API.PDFObject.convert(h.metadata.bbox)),f("/Flags "+h.metadata.flags),f("/StemV "+h.metadata.stemV),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f(">>"),f("endobj");var M=g();f("<<"),f("/Type /Font"),f("/BaseFont /"+Ai(h.fontName)),f("/FontDescriptor "+_+" 0 R"),f("/W "+i.API.PDFObject.convert(L)),f("/CIDToGIDMap /Identity"),f("/DW 1000"),f("/Subtype /CIDFontType2"),f("/CIDSystemInfo"),f("<<"),f("/Supplement 0"),f("/Registry (Adobe)"),f("/Ordering ("+h.encoding+")"),f(">>"),f(">>"),f("endobj"),h.objectNumber=g(),f("<<"),f("/Type /Font"),f("/Subtype /Type0"),f("/ToUnicode "+T+" 0 R"),f("/BaseFont /"+Ai(h.fontName)),f("/Encoding /"+h.encoding),f("/DescendantFonts ["+M+" 0 R]"),f(">>"),f("endobj"),h.isAlreadyPutted=!0}})(o)}]),e.events.push(["putFont",function(o){(function(l){var h=l.font,f=l.out,g=l.newObject,x=l.putStream;if(h.metadata instanceof i.API.TTFFont&&h.encoding==="WinAnsiEncoding"){for(var L=h.metadata.rawData,S="",p=0;p<L.length;p++)S+=String.fromCharCode(L[p]);var O=g();x({data:S,addLength1:!0,objectId:O}),f("endobj");var C=g();x({data:a(h.metadata.toUnicode),addLength1:!0,objectId:C}),f("endobj");var T=g();f("<<"),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f("/StemV "+h.metadata.stemV),f("/Type /FontDescriptor"),f("/FontFile2 "+O+" 0 R"),f("/Flags 96"),f("/FontBBox "+i.API.PDFObject.convert(h.metadata.bbox)),f("/FontName /"+Ai(h.fontName)),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f(">>"),f("endobj"),h.objectNumber=g();for(var _=0;_<h.metadata.hmtx.widths.length;_++)h.metadata.hmtx.widths[_]=parseInt(h.metadata.hmtx.widths[_]*(1e3/h.metadata.head.unitsPerEm));f("<</Subtype/TrueType/Type/Font/ToUnicode "+C+" 0 R/BaseFont/"+Ai(h.fontName)+"/FontDescriptor "+T+" 0 R/Encoding/"+h.encoding+" /FirstChar 29 /LastChar 255 /Widths "+i.API.PDFObject.convert(h.metadata.hmtx.widths)+">>"),f("endobj"),h.isAlreadyPutted=!0}})(o)}]);var c=function(o){var l,h=o.text||"",f=o.x,g=o.y,x=o.options||{},L=o.mutex||{},S=L.pdfEscape,p=L.activeFontKey,O=L.fonts,C=p,T="",_=0,M="",$=O[C].encoding;if(O[C].encoding!=="Identity-H")return{text:h,x:f,y:g,options:x,mutex:L};for(M=h,C=p,Array.isArray(h)&&(M=h[0]),_=0;_<M.length;_+=1)O[C].metadata.hasOwnProperty("cmap")&&(l=O[C].metadata.cmap.unicode.codeMap[M[_].charCodeAt(0)]),l||M[_].charCodeAt(0)<256&&O[C].metadata.hasOwnProperty("Unicode")?T+=M[_]:T+="";var st="";return parseInt(C.slice(1))<14||$==="WinAnsiEncoding"?st=S(T,C).split("").map(function(dt){return dt.charCodeAt(0).toString(16)}).join(""):$==="Identity-H"&&(st=n(T,O[C])),L.isHex=!0,{text:st,x:f,y:g,options:x,mutex:L}};e.events.push(["postProcessText",function(o){var l=o.text||"",h=[],f={text:l,x:o.x,y:o.y,options:o.options,mutex:o.mutex};if(Array.isArray(l)){var g=0;for(g=0;g<l.length;g+=1)Array.isArray(l[g])&&l[g].length===3?h.push([c(Object.assign({},f,{text:l[g][0]})).text,l[g][1],l[g][2]]):h.push(c(Object.assign({},f,{text:l[g]})).text);o.text=h}else o.text=c(Object.assign({},f,{text:l})).text}])}(Ut),function(i){var e=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};i.existsFileInVFS=function(n){return e.call(this),this.internal.vFS[n]!==void 0},i.addFileToVFS=function(n,a){return e.call(this),this.internal.vFS[n]=a,this},i.getFileFromVFS=function(n){return e.call(this),this.internal.vFS[n]!==void 0?this.internal.vFS[n]:null}}(Ut.API),function(i){i.__bidiEngine__=i.prototype.__bidiEngine__=function(a){var c,o,l,h,f,g,x,L=e,S=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],p=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],O={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},C={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},T=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],_=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),M=!1,$=0;this.__bidiEngine__={};var st=function(k){var F=k.charCodeAt(),H=F>>8,D=C[H];return D!==void 0?L[256*D+(255&F)]:H===252||H===253?"AL":_.test(H)?"L":H===8?"R":"N"},dt=function(k){for(var F,H=0;H<k.length;H++){if((F=st(k.charAt(H)))==="L")return!1;if(F==="R")return!0}return!1},Lt=function(k,F,H,D){var ct,ot,mt,tt,pt=F[D];switch(pt){case"L":case"R":M=!1;break;case"N":case"AN":break;case"EN":M&&(pt="AN");break;case"AL":M=!0,pt="R";break;case"WS":pt="N";break;case"CS":D<1||D+1>=F.length||(ct=H[D-1])!=="EN"&&ct!=="AN"||(ot=F[D+1])!=="EN"&&ot!=="AN"?pt="N":M&&(ot="AN"),pt=ot===ct?ot:"N";break;case"ES":pt=(ct=D>0?H[D-1]:"B")==="EN"&&D+1<F.length&&F[D+1]==="EN"?"EN":"N";break;case"ET":if(D>0&&H[D-1]==="EN"){pt="EN";break}if(M){pt="N";break}for(mt=D+1,tt=F.length;mt<tt&&F[mt]==="ET";)mt++;pt=mt<tt&&F[mt]==="EN"?"EN":"N";break;case"NSM":if(l&&!h){for(tt=F.length,mt=D+1;mt<tt&&F[mt]==="NSM";)mt++;if(mt<tt){var ft=k[D],Et=ft>=1425&&ft<=2303||ft===64286;if(ct=F[mt],Et&&(ct==="R"||ct==="AL")){pt="R";break}}}pt=D<1||(ct=F[D-1])==="B"?"N":H[D-1];break;case"B":M=!1,c=!0,pt=$;break;case"S":o=!0,pt="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":M=!1;break;case"BN":pt="N"}return pt},rt=function(k,F,H){var D=k.split("");return H&&G(D,H,{hiLevel:$}),D.reverse(),F&&F.reverse(),D.join("")},G=function(k,F,H){var D,ct,ot,mt,tt,pt=-1,ft=k.length,Et=0,w=[],j=$?p:S,E=[];for(M=!1,c=!1,o=!1,ct=0;ct<ft;ct++)E[ct]=st(k[ct]);for(ot=0;ot<ft;ot++){if(tt=Et,w[ot]=Lt(k,E,w,ot),D=240&(Et=j[tt][O[w[ot]]]),Et&=15,F[ot]=mt=j[Et][5],D>0)if(D===16){for(ct=pt;ct<ot;ct++)F[ct]=1;pt=-1}else pt=-1;if(j[Et][6])pt===-1&&(pt=ot);else if(pt>-1){for(ct=pt;ct<ot;ct++)F[ct]=mt;pt=-1}E[ot]==="B"&&(F[ot]=0),H.hiLevel|=mt}o&&function(V,J,Z){for(var et=0;et<Z;et++)if(V[et]==="S"){J[et]=$;for(var Q=et-1;Q>=0&&V[Q]==="WS";Q--)J[Q]=$}}(E,F,ft)},vt=function(k,F,H,D,ct){if(!(ct.hiLevel<k)){if(k===1&&$===1&&!c)return F.reverse(),void(H&&H.reverse());for(var ot,mt,tt,pt,ft=F.length,Et=0;Et<ft;){if(D[Et]>=k){for(tt=Et+1;tt<ft&&D[tt]>=k;)tt++;for(pt=Et,mt=tt-1;pt<mt;pt++,mt--)ot=F[pt],F[pt]=F[mt],F[mt]=ot,H&&(ot=H[pt],H[pt]=H[mt],H[mt]=ot);Et=tt}Et++}}},bt=function(k,F,H){var D=k.split(""),ct={hiLevel:$};return H||(H=[]),G(D,H,ct),function(ot,mt,tt){if(tt.hiLevel!==0&&x)for(var pt,ft=0;ft<ot.length;ft++)mt[ft]===1&&(pt=T.indexOf(ot[ft]))>=0&&(ot[ft]=T[pt+1])}(D,H,ct),vt(2,D,F,H,ct),vt(1,D,F,H,ct),D.join("")};return this.__bidiEngine__.doBidiReorder=function(k,F,H){if(function(ct,ot){if(ot)for(var mt=0;mt<ct.length;mt++)ot[mt]=mt;h===void 0&&(h=dt(ct)),g===void 0&&(g=dt(ct))}(k,F),l||!f||g)if(l&&f&&h^g)$=h?1:0,k=rt(k,F,H);else if(!l&&f&&g)$=h?1:0,k=bt(k,F,H),k=rt(k,F);else if(!l||h||f||g){if(l&&!f&&h^g)k=rt(k,F),h?($=0,k=bt(k,F,H)):($=1,k=bt(k,F,H),k=rt(k,F));else if(l&&h&&!f&&g)$=1,k=bt(k,F,H),k=rt(k,F);else if(!l&&!f&&h^g){var D=x;h?($=1,k=bt(k,F,H),$=0,x=!1,k=bt(k,F,H),x=D):($=0,k=bt(k,F,H),k=rt(k,F),$=1,x=!1,k=bt(k,F,H),x=D,k=rt(k,F))}}else $=0,k=bt(k,F,H);else $=h?1:0,k=bt(k,F,H);return k},this.__bidiEngine__.setOptions=function(k){k&&(l=k.isInputVisual,f=k.isOutputVisual,h=k.isInputRtl,g=k.isOutputRtl,x=k.isSymmetricSwapping)},this.__bidiEngine__.setOptions(a),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],n=new i.__bidiEngine__({isInputVisual:!0});i.API.events.push(["postProcessText",function(a){var c=a.text,o=(a.x,a.y,a.options||{}),l=(a.mutex,o.lang,[]);if(o.isInputVisual=typeof o.isInputVisual!="boolean"||o.isInputVisual,n.setOptions(o),Object.prototype.toString.call(c)==="[object Array]"){var h=0;for(l=[],h=0;h<c.length;h+=1)Object.prototype.toString.call(c[h])==="[object Array]"?l.push([n.doBidiReorder(c[h][0]),c[h][1],c[h][2]]):l.push([n.doBidiReorder(c[h])]);a.text=l}else a.text=n.doBidiReorder(c);n.setOptions({isInputVisual:!0})}])}(Ut),Ut.API.TTFFont=function(){function i(e){var n;if(this.rawData=e,n=this.contents=new Pr(e),this.contents.pos=4,n.readString(4)==="ttcf")throw new Error("TTCF not supported.");n.pos=0,this.parse(),this.subset=new ql(this),this.registerTTF()}return i.open=function(e){return new i(e)},i.prototype.parse=function(){return this.directory=new Al(this.contents),this.head=new _l(this),this.name=new Il(this),this.cmap=new Ec(this),this.toUnicode={},this.hhea=new Pl(this),this.maxp=new jl(this),this.hmtx=new Ol(this),this.post=new Fl(this),this.os2=new kl(this),this.loca=new Tl(this),this.glyf=new Bl(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},i.prototype.registerTTF=function(){var e,n,a,c,o;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var l,h,f,g;for(g=[],l=0,h=(f=this.bbox).length;l<h;l++)e=f[l],g.push(Math.round(e*this.scaleFactor));return g}).call(this),this.stemV=0,this.post.exists?(a=255&(c=this.post.italic_angle),32768&(n=c>>16)&&(n=-(1+(65535^n))),this.italicAngle=+(n+"."+a)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(o=this.familyClass)===1||o===2||o===3||o===4||o===5||o===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},i.prototype.characterToGlyph=function(e){var n;return((n=this.cmap.unicode)!=null?n.codeMap[e]:void 0)||0},i.prototype.widthOfGlyph=function(e){var n;return n=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(e).advance*n},i.prototype.widthOfString=function(e,n,a){var c,o,l,h;for(l=0,o=0,h=(e=""+e).length;0<=h?o<h:o>h;o=0<=h?++o:--o)c=e.charCodeAt(o),l+=this.widthOfGlyph(this.characterToGlyph(c))+a*(1e3/n)||0;return l*(n/1e3)},i.prototype.lineHeight=function(e,n){var a;return n==null&&(n=!1),a=n?this.lineGap:0,(this.ascender+a-this.decender)/1e3*e},i}();var qn,Pr=function(){function i(e){this.data=e??[],this.pos=0,this.length=this.data.length}return i.prototype.readByte=function(){return this.data[this.pos++]},i.prototype.writeByte=function(e){return this.data[this.pos++]=e},i.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},i.prototype.writeUInt32=function(e){return this.writeByte(e>>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e)},i.prototype.readInt32=function(){var e;return(e=this.readUInt32())>=2147483648?e-4294967296:e},i.prototype.writeInt32=function(e){return e<0&&(e+=4294967296),this.writeUInt32(e)},i.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},i.prototype.writeUInt16=function(e){return this.writeByte(e>>8&255),this.writeByte(255&e)},i.prototype.readInt16=function(){var e;return(e=this.readUInt16())>=32768?e-65536:e},i.prototype.writeInt16=function(e){return e<0&&(e+=65536),this.writeUInt16(e)},i.prototype.readString=function(e){var n,a;for(a=[],n=0;0<=e?n<e:n>e;n=0<=e?++n:--n)a[n]=String.fromCharCode(this.readByte());return a.join("")},i.prototype.writeString=function(e){var n,a,c;for(c=[],n=0,a=e.length;0<=a?n<a:n>a;n=0<=a?++n:--n)c.push(this.writeByte(e.charCodeAt(n)));return c},i.prototype.readShort=function(){return this.readInt16()},i.prototype.writeShort=function(e){return this.writeInt16(e)},i.prototype.readLongLong=function(){var e,n,a,c,o,l,h,f;return e=this.readByte(),n=this.readByte(),a=this.readByte(),c=this.readByte(),o=this.readByte(),l=this.readByte(),h=this.readByte(),f=this.readByte(),128&e?-1*(72057594037927940*(255^e)+281474976710656*(255^n)+1099511627776*(255^a)+4294967296*(255^c)+16777216*(255^o)+65536*(255^l)+256*(255^h)+(255^f)+1):72057594037927940*e+281474976710656*n+1099511627776*a+4294967296*c+16777216*o+65536*l+256*h+f},i.prototype.writeLongLong=function(e){var n,a;return n=Math.floor(e/4294967296),a=**********&e,this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n),this.writeByte(a>>24&255),this.writeByte(a>>16&255),this.writeByte(a>>8&255),this.writeByte(255&a)},i.prototype.readInt=function(){return this.readInt32()},i.prototype.writeInt=function(e){return this.writeInt32(e)},i.prototype.read=function(e){var n,a;for(n=[],a=0;0<=e?a<e:a>e;a=0<=e?++a:--a)n.push(this.readByte());return n},i.prototype.write=function(e){var n,a,c,o;for(o=[],a=0,c=e.length;a<c;a++)n=e[a],o.push(this.writeByte(n));return o},i}(),Al=function(){var i;function e(n){var a,c,o;for(this.scalarType=n.readInt(),this.tableCount=n.readShort(),this.searchRange=n.readShort(),this.entrySelector=n.readShort(),this.rangeShift=n.readShort(),this.tables={},c=0,o=this.tableCount;0<=o?c<o:c>o;c=0<=o?++c:--c)a={tag:n.readString(4),checksum:n.readInt(),offset:n.readInt(),length:n.readInt()},this.tables[a.tag]=a}return e.prototype.encode=function(n){var a,c,o,l,h,f,g,x,L,S,p,O,C;for(C in p=Object.keys(n).length,f=Math.log(2),L=16*Math.floor(Math.log(p)/f),l=Math.floor(L/f),x=16*p-L,(c=new Pr).writeInt(this.scalarType),c.writeShort(p),c.writeShort(L),c.writeShort(l),c.writeShort(x),o=16*p,g=c.pos+o,h=null,O=[],n)for(S=n[C],c.writeString(C),c.writeInt(i(S)),c.writeInt(g),c.writeInt(S.length),O=O.concat(S),C==="head"&&(h=g),g+=S.length;g%4;)O.push(0),g++;return c.write(O),a=2981146554-i(c.data),c.pos=h+8,c.writeUInt32(a),c.data},i=function(n){var a,c,o,l;for(n=Tc.call(n);n.length%4;)n.push(0);for(o=new Pr(n),c=0,a=0,l=n.length;a<l;a=a+=4)c+=o.readUInt32();return **********&c},e}(),Sl={}.hasOwnProperty,Zn=function(i,e){for(var n in e)Sl.call(e,n)&&(i[n]=e[n]);function a(){this.constructor=i}return a.prototype=e.prototype,i.prototype=new a,i.__super__=e.prototype,i};qn=function(){function i(e){var n;this.file=e,n=this.file.directory.tables[this.tag],this.exists=!!n,n&&(this.offset=n.offset,this.length=n.length,this.parse(this.file.contents))}return i.prototype.parse=function(){},i.prototype.encode=function(){},i.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},i}();var _l=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="head",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.revision=n.readInt(),this.checkSumAdjustment=n.readInt(),this.magicNumber=n.readInt(),this.flags=n.readShort(),this.unitsPerEm=n.readShort(),this.created=n.readLongLong(),this.modified=n.readLongLong(),this.xMin=n.readShort(),this.yMin=n.readShort(),this.xMax=n.readShort(),this.yMax=n.readShort(),this.macStyle=n.readShort(),this.lowestRecPPEM=n.readShort(),this.fontDirectionHint=n.readShort(),this.indexToLocFormat=n.readShort(),this.glyphDataFormat=n.readShort()},e.prototype.encode=function(n){var a;return(a=new Pr).writeInt(this.version),a.writeInt(this.revision),a.writeInt(this.checkSumAdjustment),a.writeInt(this.magicNumber),a.writeShort(this.flags),a.writeShort(this.unitsPerEm),a.writeLongLong(this.created),a.writeLongLong(this.modified),a.writeShort(this.xMin),a.writeShort(this.yMin),a.writeShort(this.xMax),a.writeShort(this.yMax),a.writeShort(this.macStyle),a.writeShort(this.lowestRecPPEM),a.writeShort(this.fontDirectionHint),a.writeShort(n),a.writeShort(this.glyphDataFormat),a.data},e}(),xc=function(){function i(e,n){var a,c,o,l,h,f,g,x,L,S,p,O,C,T,_,M,$;switch(this.platformID=e.readUInt16(),this.encodingID=e.readShort(),this.offset=n+e.readInt(),L=e.pos,e.pos=this.offset,this.format=e.readUInt16(),this.length=e.readUInt16(),this.language=e.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(f=0;f<256;++f)this.codeMap[f]=e.readByte();break;case 4:for(p=e.readUInt16(),S=p/2,e.pos+=6,o=function(){var st,dt;for(dt=[],f=st=0;0<=S?st<S:st>S;f=0<=S?++st:--st)dt.push(e.readUInt16());return dt}(),e.pos+=2,C=function(){var st,dt;for(dt=[],f=st=0;0<=S?st<S:st>S;f=0<=S?++st:--st)dt.push(e.readUInt16());return dt}(),g=function(){var st,dt;for(dt=[],f=st=0;0<=S?st<S:st>S;f=0<=S?++st:--st)dt.push(e.readUInt16());return dt}(),x=function(){var st,dt;for(dt=[],f=st=0;0<=S?st<S:st>S;f=0<=S?++st:--st)dt.push(e.readUInt16());return dt}(),c=(this.length-e.pos+this.offset)/2,h=function(){var st,dt;for(dt=[],f=st=0;0<=c?st<c:st>c;f=0<=c?++st:--st)dt.push(e.readUInt16());return dt}(),f=_=0,$=o.length;_<$;f=++_)for(T=o[f],a=M=O=C[f];O<=T?M<=T:M>=T;a=O<=T?++M:--M)x[f]===0?l=a+g[f]:(l=h[x[f]/2+(a-O)-(S-f)]||0)!==0&&(l+=g[f]),this.codeMap[a]=65535&l}e.pos=L}return i.encode=function(e,n){var a,c,o,l,h,f,g,x,L,S,p,O,C,T,_,M,$,st,dt,Lt,rt,G,vt,bt,k,F,H,D,ct,ot,mt,tt,pt,ft,Et,w,j,E,V,J,Z,et,Q,xt,Nt,Ot;switch(D=new Pr,l=Object.keys(e).sort(function(jt,Vt){return jt-Vt}),n){case"macroman":for(C=0,T=function(){var jt=[];for(O=0;O<256;++O)jt.push(0);return jt}(),M={0:0},o={},ct=0,pt=l.length;ct<pt;ct++)M[Q=e[c=l[ct]]]==null&&(M[Q]=++C),o[c]={old:e[c],new:M[e[c]]},T[c]=M[e[c]];return D.writeUInt16(1),D.writeUInt16(0),D.writeUInt32(12),D.writeUInt16(0),D.writeUInt16(262),D.writeUInt16(0),D.write(T),{charMap:o,subtable:D.data,maxGlyphID:C+1};case"unicode":for(F=[],L=[],$=0,M={},a={},_=g=null,ot=0,ft=l.length;ot<ft;ot++)M[dt=e[c=l[ot]]]==null&&(M[dt]=++$),a[c]={old:dt,new:M[dt]},h=M[dt]-c,_!=null&&h===g||(_&&L.push(_),F.push(c),g=h),_=c;for(_&&L.push(_),L.push(65535),F.push(65535),bt=2*(vt=F.length),G=2*Math.pow(Math.log(vt)/Math.LN2,2),S=Math.log(G/2)/Math.LN2,rt=2*vt-G,f=[],Lt=[],p=[],O=mt=0,Et=F.length;mt<Et;O=++mt){if(k=F[O],x=L[O],k===65535){f.push(0),Lt.push(0);break}if(k-(H=a[k].new)>=32768)for(f.push(0),Lt.push(2*(p.length+vt-O)),c=tt=k;k<=x?tt<=x:tt>=x;c=k<=x?++tt:--tt)p.push(a[c].new);else f.push(H-k),Lt.push(0)}for(D.writeUInt16(3),D.writeUInt16(1),D.writeUInt32(12),D.writeUInt16(4),D.writeUInt16(16+8*vt+2*p.length),D.writeUInt16(0),D.writeUInt16(bt),D.writeUInt16(G),D.writeUInt16(S),D.writeUInt16(rt),Z=0,w=L.length;Z<w;Z++)c=L[Z],D.writeUInt16(c);for(D.writeUInt16(0),et=0,j=F.length;et<j;et++)c=F[et],D.writeUInt16(c);for(xt=0,E=f.length;xt<E;xt++)h=f[xt],D.writeUInt16(h);for(Nt=0,V=Lt.length;Nt<V;Nt++)st=Lt[Nt],D.writeUInt16(st);for(Ot=0,J=p.length;Ot<J;Ot++)C=p[Ot],D.writeUInt16(C);return{charMap:a,subtable:D.data,maxGlyphID:$+1}}},i}(),Ec=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="cmap",e.prototype.parse=function(n){var a,c,o;for(n.pos=this.offset,this.version=n.readUInt16(),o=n.readUInt16(),this.tables=[],this.unicode=null,c=0;0<=o?c<o:c>o;c=0<=o?++c:--c)a=new xc(n,this.offset),this.tables.push(a),a.isUnicode&&this.unicode==null&&(this.unicode=a);return!0},e.encode=function(n,a){var c,o;return a==null&&(a="macroman"),c=xc.encode(n,a),(o=new Pr).writeUInt16(0),o.writeUInt16(1),c.table=o.data.concat(c.subtable),c},e}(),Pl=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="hhea",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.ascender=n.readShort(),this.decender=n.readShort(),this.lineGap=n.readShort(),this.advanceWidthMax=n.readShort(),this.minLeftSideBearing=n.readShort(),this.minRightSideBearing=n.readShort(),this.xMaxExtent=n.readShort(),this.caretSlopeRise=n.readShort(),this.caretSlopeRun=n.readShort(),this.caretOffset=n.readShort(),n.pos+=8,this.metricDataFormat=n.readShort(),this.numberOfMetrics=n.readUInt16()},e}(),kl=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="OS/2",e.prototype.parse=function(n){if(n.pos=this.offset,this.version=n.readUInt16(),this.averageCharWidth=n.readShort(),this.weightClass=n.readUInt16(),this.widthClass=n.readUInt16(),this.type=n.readShort(),this.ySubscriptXSize=n.readShort(),this.ySubscriptYSize=n.readShort(),this.ySubscriptXOffset=n.readShort(),this.ySubscriptYOffset=n.readShort(),this.ySuperscriptXSize=n.readShort(),this.ySuperscriptYSize=n.readShort(),this.ySuperscriptXOffset=n.readShort(),this.ySuperscriptYOffset=n.readShort(),this.yStrikeoutSize=n.readShort(),this.yStrikeoutPosition=n.readShort(),this.familyClass=n.readShort(),this.panose=function(){var a,c;for(c=[],a=0;a<10;++a)c.push(n.readByte());return c}(),this.charRange=function(){var a,c;for(c=[],a=0;a<4;++a)c.push(n.readInt());return c}(),this.vendorID=n.readString(4),this.selection=n.readShort(),this.firstCharIndex=n.readShort(),this.lastCharIndex=n.readShort(),this.version>0&&(this.ascent=n.readShort(),this.descent=n.readShort(),this.lineGap=n.readShort(),this.winAscent=n.readShort(),this.winDescent=n.readShort(),this.codePageRange=function(){var a,c;for(c=[],a=0;a<2;a=++a)c.push(n.readInt());return c}(),this.version>1))return this.xHeight=n.readShort(),this.capHeight=n.readShort(),this.defaultChar=n.readShort(),this.breakChar=n.readShort(),this.maxContext=n.readShort()},e}(),Fl=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="post",e.prototype.parse=function(n){var a,c,o;switch(n.pos=this.offset,this.format=n.readInt(),this.italicAngle=n.readInt(),this.underlinePosition=n.readShort(),this.underlineThickness=n.readShort(),this.isFixedPitch=n.readInt(),this.minMemType42=n.readInt(),this.maxMemType42=n.readInt(),this.minMemType1=n.readInt(),this.maxMemType1=n.readInt(),this.format){case 65536:break;case 131072:var l;for(c=n.readUInt16(),this.glyphNameIndex=[],l=0;0<=c?l<c:l>c;l=0<=c?++l:--l)this.glyphNameIndex.push(n.readUInt16());for(this.names=[],o=[];n.pos<this.offset+this.length;)a=n.readByte(),o.push(this.names.push(n.readString(a)));return o;case 151552:return c=n.readUInt16(),this.offsets=n.read(c);case 196608:break;case 262144:return this.map=(function(){var h,f,g;for(g=[],l=h=0,f=this.file.maxp.numGlyphs;0<=f?h<f:h>f;l=0<=f?++h:--h)g.push(n.readUInt32());return g}).call(this)}},e}(),Cl=function(i,e){this.raw=i,this.length=i.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},Il=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="name",e.prototype.parse=function(n){var a,c,o,l,h,f,g,x,L,S,p;for(n.pos=this.offset,n.readShort(),a=n.readShort(),f=n.readShort(),c=[],l=0;0<=a?l<a:l>a;l=0<=a?++l:--l)c.push({platformID:n.readShort(),encodingID:n.readShort(),languageID:n.readShort(),nameID:n.readShort(),length:n.readShort(),offset:this.offset+f+n.readShort()});for(g={},l=L=0,S=c.length;L<S;l=++L)o=c[l],n.pos=o.offset,x=n.readString(o.length),h=new Cl(x,o),g[p=o.nameID]==null&&(g[p]=[]),g[o.nameID].push(h);this.strings=g,this.copyright=g[0],this.fontFamily=g[1],this.fontSubfamily=g[2],this.uniqueSubfamily=g[3],this.fontName=g[4],this.version=g[5];try{this.postscriptName=g[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=g[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=g[7],this.manufacturer=g[8],this.designer=g[9],this.description=g[10],this.vendorUrl=g[11],this.designerUrl=g[12],this.license=g[13],this.licenseUrl=g[14],this.preferredFamily=g[15],this.preferredSubfamily=g[17],this.compatibleFull=g[18],this.sampleText=g[19]},e}(),jl=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="maxp",e.prototype.parse=function(n){return n.pos=this.offset,this.version=n.readInt(),this.numGlyphs=n.readUInt16(),this.maxPoints=n.readUInt16(),this.maxContours=n.readUInt16(),this.maxCompositePoints=n.readUInt16(),this.maxComponentContours=n.readUInt16(),this.maxZones=n.readUInt16(),this.maxTwilightPoints=n.readUInt16(),this.maxStorage=n.readUInt16(),this.maxFunctionDefs=n.readUInt16(),this.maxInstructionDefs=n.readUInt16(),this.maxStackElements=n.readUInt16(),this.maxSizeOfInstructions=n.readUInt16(),this.maxComponentElements=n.readUInt16(),this.maxComponentDepth=n.readUInt16()},e}(),Ol=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="hmtx",e.prototype.parse=function(n){var a,c,o,l,h,f,g;for(n.pos=this.offset,this.metrics=[],a=0,f=this.file.hhea.numberOfMetrics;0<=f?a<f:a>f;a=0<=f?++a:--a)this.metrics.push({advance:n.readUInt16(),lsb:n.readInt16()});for(o=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var x,L;for(L=[],a=x=0;0<=o?x<o:x>o;a=0<=o?++x:--x)L.push(n.readInt16());return L}(),this.widths=(function(){var x,L,S,p;for(p=[],x=0,L=(S=this.metrics).length;x<L;x++)l=S[x],p.push(l.advance);return p}).call(this),c=this.widths[this.widths.length-1],g=[],a=h=0;0<=o?h<o:h>o;a=0<=o?++h:--h)g.push(this.widths.push(c));return g},e.prototype.forGlyph=function(n){return n in this.metrics?this.metrics[n]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[n-this.metrics.length]}},e}(),Tc=[].slice,Bl=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(n){var a,c,o,l,h,f,g,x,L,S;return n in this.cache?this.cache[n]:(l=this.file.loca,a=this.file.contents,c=l.indexOf(n),(o=l.lengthOf(n))===0?this.cache[n]=null:(a.pos=this.offset+c,h=(f=new Pr(a.read(o))).readShort(),x=f.readShort(),S=f.readShort(),g=f.readShort(),L=f.readShort(),this.cache[n]=h===-1?new El(f,x,S,g,L):new Ml(f,h,x,S,g,L),this.cache[n]))},e.prototype.encode=function(n,a,c){var o,l,h,f,g;for(h=[],l=[],f=0,g=a.length;f<g;f++)o=n[a[f]],l.push(h.length),o&&(h=h.concat(o.encode(c)));return l.push(h.length),{table:h,offsets:l}},e}(),Ml=function(){function i(e,n,a,c,o,l){this.raw=e,this.numberOfContours=n,this.xMin=a,this.yMin=c,this.xMax=o,this.yMax=l,this.compound=!1}return i.prototype.encode=function(){return this.raw.data},i}(),El=function(){function i(e,n,a,c,o){var l,h;for(this.raw=e,this.xMin=n,this.yMin=a,this.xMax=c,this.yMax=o,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],l=this.raw;h=l.readShort(),this.glyphOffsets.push(l.pos),this.glyphIDs.push(l.readUInt16()),32&h;)l.pos+=1&h?4:2,128&h?l.pos+=8:64&h?l.pos+=4:8&h&&(l.pos+=2)}return i.prototype.encode=function(){var e,n,a;for(n=new Pr(Tc.call(this.raw.data)),e=0,a=this.glyphIDs.length;e<a;++e)n.pos=this.glyphOffsets[e];return n.data},i}(),Tl=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Zn(e,qn),e.prototype.tag="loca",e.prototype.parse=function(n){var a,c;return n.pos=this.offset,a=this.file.head.indexToLocFormat,this.offsets=a===0?(function(){var o,l;for(l=[],c=0,o=this.length;c<o;c+=2)l.push(2*n.readUInt16());return l}).call(this):(function(){var o,l;for(l=[],c=0,o=this.length;c<o;c+=4)l.push(n.readUInt32());return l}).call(this)},e.prototype.indexOf=function(n){return this.offsets[n]},e.prototype.lengthOf=function(n){return this.offsets[n+1]-this.offsets[n]},e.prototype.encode=function(n,a){for(var c=new Uint32Array(this.offsets.length),o=0,l=0,h=0;h<c.length;++h)if(c[h]=o,l<a.length&&a[l]==h){++l,c[h]=o;var f=this.offsets[h],g=this.offsets[h+1]-f;g>0&&(o+=g)}for(var x=new Array(4*c.length),L=0;L<c.length;++L)x[4*L+3]=255&c[L],x[4*L+2]=(65280&c[L])>>8,x[4*L+1]=(16711680&c[L])>>16,x[4*L]=(**********&c[L])>>24;return x},e}(),ql=function(){function i(e){this.font=e,this.subset={},this.unicodes={},this.next=33}return i.prototype.generateCmap=function(){var e,n,a,c,o;for(n in c=this.font.cmap.tables[0].codeMap,e={},o=this.subset)a=o[n],e[n]=c[a];return e},i.prototype.glyphsFor=function(e){var n,a,c,o,l,h,f;for(c={},l=0,h=e.length;l<h;l++)c[o=e[l]]=this.font.glyf.glyphFor(o);for(o in n=[],c)(a=c[o])!=null&&a.compound&&n.push.apply(n,a.glyphIDs);if(n.length>0)for(o in f=this.glyphsFor(n))a=f[o],c[o]=a;return c},i.prototype.encode=function(e,n){var a,c,o,l,h,f,g,x,L,S,p,O,C,T,_;for(c in a=Ec.encode(this.generateCmap(),"unicode"),l=this.glyphsFor(e),p={0:0},_=a.charMap)p[(f=_[c]).old]=f.new;for(O in S=a.maxGlyphID,l)O in p||(p[O]=S++);return x=function(M){var $,st;for($ in st={},M)st[M[$]]=$;return st}(p),L=Object.keys(x).sort(function(M,$){return M-$}),C=function(){var M,$,st;for(st=[],M=0,$=L.length;M<$;M++)h=L[M],st.push(x[h]);return st}(),o=this.font.glyf.encode(l,C,p),g=this.font.loca.encode(o.offsets,C),T={cmap:this.font.cmap.raw(),glyf:o.table,loca:g,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(n)},this.font.os2.exists&&(T["OS/2"]=this.font.os2.raw()),this.font.directory.encode(T)},i}();Ut.API.PDFObject=function(){var i;function e(){}return i=function(n,a){return(Array(a+1).join("0")+n).slice(-a)},e.convert=function(n){var a,c,o,l;if(Array.isArray(n))return"["+function(){var h,f,g;for(g=[],h=0,f=n.length;h<f;h++)a=n[h],g.push(e.convert(a));return g}().join(" ")+"]";if(typeof n=="string")return"/"+n;if(n!=null&&n.isString)return"("+n+")";if(n instanceof Date)return"(D:"+i(n.getUTCFullYear(),4)+i(n.getUTCMonth(),2)+i(n.getUTCDate(),2)+i(n.getUTCHours(),2)+i(n.getUTCMinutes(),2)+i(n.getUTCSeconds(),2)+"Z)";if({}.toString.call(n)==="[object Object]"){for(c in o=["<<"],n)l=n[c],o.push("/"+c+" "+e.convert(l));return o.push(">>"),o.join(`
`)}return""+n},e}();export{Jl as A,Gl as C,Wl as E,Xl as P,Yl as S,fe as _,Ut as a,Zl as m,Kl as p,$l as r};
