var E=Uint8Array,P=Uint16Array,Cr=Int32Array,or=new E([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),ur=new E([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),gr=new E([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Er=function(r,a){for(var n=new P(31),e=0;e<31;++e)n[e]=a+=1<<r[e-1];for(var v=new Cr(n[30]),e=1;e<30;++e)for(var f=n[e];f<n[e+1];++f)v[f]=f-n[e]<<5|e;return{b:n,r:v}},Ur=Er(or,2),sr=Ur.b,yr=Ur.r;sr[28]=258,yr[258]=28;var mr=Er(ur,0),Dr=mr.b,kr=mr.r,Mr=new P(32768);for(var g=0;g<32768;++g){var j=(g&43690)>>1|(g&21845)<<1;j=(j&52428)>>2|(j&13107)<<2,j=(j&61680)>>4|(j&3855)<<4,Mr[g]=((j&65280)>>8|(j&255)<<8)>>1}var X=function(r,a,n){for(var e=r.length,v=0,f=new P(a);v<e;++v)r[v]&&++f[r[v]-1];var o=new P(a);for(v=1;v<a;++v)o[v]=o[v-1]+f[v-1]<<1;var t;if(n){t=new P(1<<a);var w=15-a;for(v=0;v<e;++v)if(r[v])for(var C=v<<4|r[v],i=a-r[v],l=o[r[v]-1]++<<i,h=l|(1<<i)-1;l<=h;++l)t[Mr[l]>>w]=C}else for(t=new P(e),v=0;v<e;++v)r[v]&&(t[v]=Mr[o[r[v]-1]++]>>15-r[v]);return t},L=new E(288);for(var g=0;g<144;++g)L[g]=8;for(var g=144;g<256;++g)L[g]=9;for(var g=256;g<280;++g)L[g]=7;for(var g=280;g<288;++g)L[g]=8;var fr=new E(32);for(var g=0;g<32;++g)fr[g]=5;var Gr=X(L,9,0),Hr=X(L,9,1),Jr=X(fr,5,0),Kr=X(fr,5,1),cr=function(r){for(var a=r[0],n=1;n<r.length;++n)r[n]>a&&(a=r[n]);return a},V=function(r,a,n){var e=a/8|0;return(r[e]|r[e+1]<<8)>>(a&7)&n},tr=function(r,a){var n=a/8|0;return(r[n]|r[n+1]<<8|r[n+2]<<16)>>(a&7)},Sr=function(r){return(r+7)/8|0},Fr=function(r,a,n){return(a==null||a<0)&&(a=0),(n==null||n>r.length)&&(n=r.length),new E(r.subarray(a,n))},Nr=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],G=function(r,a,n){var e=new Error(a||Nr[r]);if(e.code=r,Error.captureStackTrace&&Error.captureStackTrace(e,G),!n)throw e;return e},br=function(r,a,n,e){var v=r.length,f=e?e.length:0;if(!v||a.f&&!a.l)return n||new E(0);var o=!n,t=o||a.i!=2,w=a.i;o&&(n=new E(v*3));var C=function(ar){var nr=n.length;if(ar>nr){var d=new E(Math.max(nr*2,ar));d.set(n),n=d}},i=a.f||0,l=a.p||0,h=a.b||0,y=a.l,U=a.d,M=a.m,I=a.n,R=v*8;do{if(!y){i=V(r,l,1);var H=V(r,l+1,3);if(l+=3,H)if(H==1)y=Hr,U=Kr,M=9,I=5;else if(H==2){var B=V(r,l,31)+257,b=V(r,l+10,15)+4,c=B+V(r,l+5,31)+1;l+=14;for(var u=new E(c),k=new E(19),S=0;S<b;++S)k[gr[S]]=V(r,l+S*3,7);l+=b*3;for(var m=cr(k),$=(1<<m)-1,J=X(k,m,1),S=0;S<c;){var O=J[V(r,l,$)];l+=O&15;var F=O>>4;if(F<16)u[S++]=F;else{var A=0,x=0;for(F==16?(x=3+V(r,l,3),l+=2,A=u[S-1]):F==17?(x=3+V(r,l,7),l+=3):F==18&&(x=11+V(r,l,127),l+=7);x--;)u[S++]=A}}var D=u.subarray(0,B),T=u.subarray(B);M=cr(D),I=cr(T),y=X(D,M,1),U=X(T,I,1)}else G(1);else{var F=Sr(l)+4,q=r[F-4]|r[F-3]<<8,s=F+q;if(s>v){w&&G(0);break}t&&C(h+q),n.set(r.subarray(F,s),h),a.b=h+=q,a.p=l=s*8,a.f=i;continue}if(l>R){w&&G(0);break}}t&&C(h+131072);for(var rr=(1<<M)-1,Q=(1<<I)-1,Y=l;;Y=l){var A=y[tr(r,l)&rr],K=A>>4;if(l+=A&15,l>R){w&&G(0);break}if(A||G(2),K<256)n[h++]=K;else if(K==256){Y=l,y=null;break}else{var N=K-254;if(K>264){var S=K-257,z=or[S];N=V(r,l,(1<<z)-1)+sr[S],l+=z}var W=U[tr(r,l)&Q],_=W>>4;W||G(3),l+=W&15;var T=Dr[_];if(_>3){var z=ur[_];T+=tr(r,l)&(1<<z)-1,l+=z}if(l>R){w&&G(0);break}t&&C(h+131072);var p=h+N;if(h<T){var ir=f-T,lr=Math.min(T,p);for(ir+h<0&&G(3);h<lr;++h)n[h]=e[ir+h]}for(;h<p;++h)n[h]=n[h-T]}}a.l=y,a.p=Y,a.b=h,a.f=i,y&&(i=1,a.m=M,a.d=U,a.n=I)}while(!i);return h!=n.length&&o?Fr(n,0,h):n.subarray(0,h)},Z=function(r,a,n){n<<=a&7;var e=a/8|0;r[e]|=n,r[e+1]|=n>>8},er=function(r,a,n){n<<=a&7;var e=a/8|0;r[e]|=n,r[e+1]|=n>>8,r[e+2]|=n>>16},wr=function(r,a){for(var n=[],e=0;e<r.length;++e)r[e]&&n.push({s:e,f:r[e]});var v=n.length,f=n.slice();if(!v)return{t:Br,l:0};if(v==1){var o=new E(n[0].s+1);return o[n[0].s]=1,{t:o,l:1}}n.sort(function(s,B){return s.f-B.f}),n.push({s:-1,f:25001});var t=n[0],w=n[1],C=0,i=1,l=2;for(n[0]={s:-1,f:t.f+w.f,l:t,r:w};i!=v-1;)t=n[n[C].f<n[l].f?C++:l++],w=n[C!=i&&n[C].f<n[l].f?C++:l++],n[i++]={s:-1,f:t.f+w.f,l:t,r:w};for(var h=f[0].s,e=1;e<v;++e)f[e].s>h&&(h=f[e].s);var y=new P(h+1),U=xr(n[i-1],y,0);if(U>a){var e=0,M=0,I=U-a,R=1<<I;for(f.sort(function(B,b){return y[b.s]-y[B.s]||B.f-b.f});e<v;++e){var H=f[e].s;if(y[H]>a)M+=R-(1<<U-y[H]),y[H]=a;else break}for(M>>=I;M>0;){var F=f[e].s;y[F]<a?M-=1<<a-y[F]++-1:++e}for(;e>=0&&M;--e){var q=f[e].s;y[q]==a&&(--y[q],++M)}U=a}return{t:new E(y),l:U}},xr=function(r,a,n){return r.s==-1?Math.max(xr(r.l,a,n+1),xr(r.r,a,n+1)):a[r.s]=n},Ar=function(r){for(var a=r.length;a&&!r[--a];);for(var n=new P(++a),e=0,v=r[0],f=1,o=function(w){n[e++]=w},t=1;t<=a;++t)if(r[t]==v&&t!=a)++f;else{if(!v&&f>2){for(;f>138;f-=138)o(32754);f>2&&(o(f>10?f-11<<5|28690:f-3<<5|12305),f=0)}else if(f>3){for(o(v),--f;f>6;f-=6)o(8304);f>2&&(o(f-3<<5|8208),f=0)}for(;f--;)o(v);f=1,v=r[t]}return{c:n.subarray(0,e),n:a}},vr=function(r,a){for(var n=0,e=0;e<a.length;++e)n+=r[e]*a[e];return n},qr=function(r,a,n){var e=n.length,v=Sr(a+2);r[v]=e&255,r[v+1]=e>>8,r[v+2]=r[v]^255,r[v+3]=r[v+1]^255;for(var f=0;f<e;++f)r[v+f+4]=n[f];return(v+4+e)*8},Tr=function(r,a,n,e,v,f,o,t,w,C,i){Z(a,i++,n),++v[256];for(var l=wr(v,15),h=l.t,y=l.l,U=wr(f,15),M=U.t,I=U.l,R=Ar(h),H=R.c,F=R.n,q=Ar(M),s=q.c,B=q.n,b=new P(19),c=0;c<H.length;++c)++b[H[c]&31];for(var c=0;c<s.length;++c)++b[s[c]&31];for(var u=wr(b,7),k=u.t,S=u.l,m=19;m>4&&!k[gr[m-1]];--m);var $=C+5<<3,J=vr(v,L)+vr(f,fr)+o,O=vr(v,h)+vr(f,M)+o+14+3*m+vr(b,k)+2*b[16]+3*b[17]+7*b[18];if(w>=0&&$<=J&&$<=O)return qr(a,i,r.subarray(w,w+C));var A,x,D,T;if(Z(a,i,1+(O<J)),i+=2,O<J){A=X(h,y,0),x=h,D=X(M,I,0),T=M;var rr=X(k,S,0);Z(a,i,F-257),Z(a,i+5,B-1),Z(a,i+10,m-4),i+=14;for(var c=0;c<m;++c)Z(a,i+3*c,k[gr[c]]);i+=3*m;for(var Q=[H,s],Y=0;Y<2;++Y)for(var K=Q[Y],c=0;c<K.length;++c){var N=K[c]&31;Z(a,i,rr[N]),i+=k[N],N>15&&(Z(a,i,K[c]>>5&127),i+=K[c]>>12)}}else A=Gr,x=L,D=Jr,T=fr;for(var c=0;c<t;++c){var z=e[c];if(z>255){var N=z>>18&31;er(a,i,A[N+257]),i+=x[N+257],N>7&&(Z(a,i,z>>23&31),i+=or[N]);var W=z&31;er(a,i,D[W]),i+=T[W],W>3&&(er(a,i,z>>5&8191),i+=ur[W])}else er(a,i,A[z]),i+=x[z]}return er(a,i,A[256]),i+x[256]},Pr=new Cr([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Br=new E(0),Qr=function(r,a,n,e,v,f){var o=f.z||r.length,t=new E(e+o+5*(1+Math.ceil(o/7e3))+v),w=t.subarray(e,t.length-v),C=f.l,i=(f.r||0)&7;if(a){i&&(w[0]=f.r>>3);for(var l=Pr[a-1],h=l>>13,y=l&8191,U=(1<<n)-1,M=f.p||new P(32768),I=f.h||new P(U+1),R=Math.ceil(n/3),H=2*R,F=function(hr){return(r[hr]^r[hr+1]<<R^r[hr+2]<<H)&U},q=new Cr(25e3),s=new P(288),B=new P(32),b=0,c=0,u=f.i||0,k=0,S=f.w||0,m=0;u+2<o;++u){var $=F(u),J=u&32767,O=I[$];if(M[J]=O,I[$]=J,S<=u){var A=o-u;if((b>7e3||k>24576)&&(A>423||!C)){i=Tr(r,w,0,q,s,B,c,k,m,u-m,i),k=b=c=0,m=u;for(var x=0;x<286;++x)s[x]=0;for(var x=0;x<30;++x)B[x]=0}var D=2,T=0,rr=y,Q=J-O&32767;if(A>2&&$==F(u-Q))for(var Y=Math.min(h,A)-1,K=Math.min(32767,u),N=Math.min(258,A);Q<=K&&--rr&&J!=O;){if(r[u+D]==r[u+D-Q]){for(var z=0;z<N&&r[u+z]==r[u+z-Q];++z);if(z>D){if(D=z,T=Q,z>Y)break;for(var W=Math.min(Q,z-2),_=0,x=0;x<W;++x){var p=u-Q+x&32767,ir=M[p],lr=p-ir&32767;lr>_&&(_=lr,O=p)}}}J=O,O=M[J],Q+=J-O&32767}if(T){q[k++]=268435456|yr[D]<<18|kr[T];var ar=yr[D]&31,nr=kr[T]&31;c+=or[ar]+ur[nr],++s[257+ar],++B[nr],S=u+D,++b}else q[k++]=r[u],++s[r[u]]}}for(u=Math.max(u,S);u<o;++u)q[k++]=r[u],++s[r[u]];i=Tr(r,w,C,q,s,B,c,k,m,u-m,i),C||(f.r=i&7|w[i/8|0]<<3,i-=7,f.h=I,f.p=M,f.i=u,f.w=S)}else{for(var u=f.w||0;u<o+C;u+=65535){var d=u+65535;d>=o&&(w[i/8|0]=C,d=o),i=qr(w,i+1,r.subarray(u,d))}f.i=o}return Fr(t,0,e+Sr(i)+v)},Ir=function(){var r=1,a=0;return{p:function(n){for(var e=r,v=a,f=n.length|0,o=0;o!=f;){for(var t=Math.min(o+2655,f);o<t;++o)v+=e+=n[o];e=(e&65535)+15*(e>>16),v=(v&65535)+15*(v>>16)}r=e,a=v},d:function(){return r%=65521,a%=65521,(r&255)<<24|(r&65280)<<8|(a&255)<<8|a>>8}}},Rr=function(r,a,n,e,v){if(!v&&(v={l:1},a.dictionary)){var f=a.dictionary.subarray(-32768),o=new E(f.length+r.length);o.set(f),o.set(r,f.length),r=o,v.w=f.length}return Qr(r,a.level==null?6:a.level,a.mem==null?Math.ceil(Math.max(8,Math.min(13,Math.log(r.length)))*1.5):12+a.mem,n,e,v)},Or=function(r,a,n){for(;n;++a)r[a]=n,n>>>=8},Vr=function(r){(r[0]!=31||r[1]!=139||r[2]!=8)&&G(6,"invalid gzip data");var a=r[3],n=10;a&4&&(n+=(r[10]|r[11]<<8)+2);for(var e=(a>>3&1)+(a>>4&1);e>0;e-=!r[n++]);return n+(a&2)},Wr=function(r){var a=r.length;return(r[a-4]|r[a-3]<<8|r[a-2]<<16|r[a-1]<<24)>>>0},Xr=function(r,a){var n=a.level,e=n==0?0:n<6?1:n==9?3:2;if(r[0]=120,r[1]=e<<6|(a.dictionary&&32),r[1]|=31-(r[0]<<8|r[1])%31,a.dictionary){var v=Ir();v.p(a.dictionary),Or(r,2,v.d())}},Yr=function(r,a){return((r[0]&15)!=8||r[0]>>4>7||(r[0]<<8|r[1])%31)&&G(6,"invalid zlib data"),(r[1]>>5&1)==+!a&&G(6,"invalid zlib data: "+(r[1]&32?"need":"unexpected")+" dictionary"),(r[1]>>3&4)+2};function Zr(r,a){return br(r,{i:2},a&&a.out,a&&a.dictionary)}function $r(r,a){var n=Vr(r);return n+8>r.length&&G(6,"invalid gzip data"),br(r.subarray(n,-8),{i:2},a&&a.out||new E(Wr(r)),a&&a.dictionary)}function pr(r,a){a||(a={});var n=Ir();n.p(r);var e=Rr(r,a,a.dictionary?6:2,4);return Xr(e,a),Or(e,e.length-4,n.d()),e}function jr(r,a){return br(r.subarray(Yr(r,a&&a.dictionary),-4),{i:2},a&&a.out,a&&a.dictionary)}function dr(r,a){return r[0]==31&&r[1]==139&&r[2]==8?$r(r,a):(r[0]&15)!=8||r[0]>>4>7||(r[0]<<8|r[1])%31?Zr(r,a):jr(r,a)}var zr=typeof TextDecoder<"u"&&new TextDecoder,Lr=0;try{zr.decode(Br,{stream:!0}),Lr=1}catch{}var _r=function(r){for(var a="",n=0;;){var e=r[n++],v=(e>127)+(e>223)+(e>239);if(n+v>r.length)return{s:a,r:Fr(r,n-1)};v?v==3?(e=((e&15)<<18|(r[n++]&63)<<12|(r[n++]&63)<<6|r[n++]&63)-65536,a+=String.fromCharCode(55296|e>>10,56320|e&1023)):v&1?a+=String.fromCharCode((e&31)<<6|r[n++]&63):a+=String.fromCharCode((e&15)<<12|(r[n++]&63)<<6|r[n++]&63):a+=String.fromCharCode(e)}};function ra(r,a){if(a){for(var n="",e=0;e<r.length;e+=16384)n+=String.fromCharCode.apply(null,r.subarray(e,e+16384));return n}else{if(zr)return zr.decode(r);var v=_r(r),f=v.s,n=v.r;return n.length&&G(8),f}}export{dr as d,ra as s,jr as u,pr as z};
