import{L as $,ir as x,dR as k,M as ee,ep as A,hF as I,dP as V,ea as E,eq as D,eD as z,q as L,dM as ae,ei as te,em as ie,v as oe,hY as ne,eG as le,w as u,r as T,a3 as se,ac as B,en as ce,o as re,x as o,B as v,P as N,A as a,D as r,b1 as ue,y as c,z as p,b9 as y,H as h,J as d,Q as P,G as de,dL as ve,C as O,E as fe,_ as pe,a2 as he,aG as me,iW as G,m as ye,K as be}from"./index-9381ab2b.js";const ge=$({modelValue:{type:[Boolean,String,Number],default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},size:{type:String,validator:x},width:{type:[String,Number],default:""},inlinePrompt:{type:Boolean,default:!1},inactiveActionIcon:{type:k},activeActionIcon:{type:k},activeIcon:{type:k},inactiveIcon:{type:k},activeText:{type:String,default:""},inactiveText:{type:String,default:""},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},activeColor:{type:String,default:""},inactiveColor:{type:String,default:""},borderColor:{type:String,default:""},name:{type:String,default:""},validateEvent:{type:Boolean,default:!0},beforeChange:{type:ee(Function)},id:String,tabindex:{type:[String,Number]},value:{type:[Boolean,String,Number],default:!1},label:{type:String,default:void 0}}),Ce={[A]:s=>I(s)||V(s)||E(s),[D]:s=>I(s)||V(s)||E(s),[z]:s=>I(s)||V(s)||E(s)},Se=["onClick"],ke=["id","aria-checked","aria-disabled","aria-label","name","true-value","false-value","disabled","tabindex","onKeydown"],Ie=["aria-hidden"],we=["aria-hidden"],Ve=["aria-hidden"],K="ElSwitch",Ee=L({name:K}),Te=L({...Ee,props:ge,emits:Ce,setup(s,{expose:U,emit:f}){const t=s,q=ae(),{formItem:b}=te(),H=ie(),i=oe("switch");(e=>{e.forEach(l=>{he({from:l[0],replacement:l[1],scope:K,version:"2.3.0",ref:"https://element-plus.org/en-US/component/switch.html#attributes",type:"Attribute"},u(()=>{var S;return!!((S=q.vnode.props)!=null&&S[l[2]])}))})})([['"value"','"model-value" or "v-model"',"value"],['"active-color"',"CSS var `--el-switch-on-color`","activeColor"],['"inactive-color"',"CSS var `--el-switch-off-color`","inactiveColor"],['"border-color"',"CSS var `--el-switch-border-color`","borderColor"]]);const{inputId:R}=ne(t,{formItemContext:b}),g=le(u(()=>t.loading)),w=T(t.modelValue!==!1),m=T(),W=T(),J=u(()=>[i.b(),i.m(H.value),i.is("disabled",g.value),i.is("checked",n.value)]),Q=u(()=>[i.e("label"),i.em("label","left"),i.is("active",!n.value)]),Y=u(()=>[i.e("label"),i.em("label","right"),i.is("active",n.value)]),j=u(()=>({width:se(t.width)}));B(()=>t.modelValue,()=>{w.value=!0}),B(()=>t.value,()=>{w.value=!1});const M=u(()=>w.value?t.modelValue:t.value),n=u(()=>M.value===t.activeValue);[t.activeValue,t.inactiveValue].includes(M.value)||(f(A,t.inactiveValue),f(D,t.inactiveValue),f(z,t.inactiveValue)),B(n,e=>{var l;m.value.checked=e,t.validateEvent&&((l=b==null?void 0:b.validate)==null||l.call(b,"change").catch(S=>ce()))});const C=()=>{const e=n.value?t.inactiveValue:t.activeValue;f(A,e),f(D,e),f(z,e),me(()=>{m.value.checked=n.value})},F=()=>{if(g.value)return;const{beforeChange:e}=t;if(!e){C();return}const l=e();[G(l),I(l)].includes(!0)||ye(K,"beforeChange must return type `Promise<boolean>` or `boolean`"),G(l)?l.then(_=>{_&&C()}).catch(_=>{}):l&&C()},X=u(()=>i.cssVarBlock({...t.activeColor?{"on-color":t.activeColor}:null,...t.inactiveColor?{"off-color":t.inactiveColor}:null,...t.borderColor?{"border-color":t.borderColor}:null})),Z=()=>{var e,l;(l=(e=m.value)==null?void 0:e.focus)==null||l.call(e)};return re(()=>{m.value.checked=n.value}),U({focus:Z,checked:n}),(e,l)=>(o(),v("div",{class:r(a(J)),style:O(a(X)),onClick:fe(F,["prevent"])},[N("input",{id:a(R),ref_key:"input",ref:m,class:r(a(i).e("input")),type:"checkbox",role:"switch","aria-checked":a(n),"aria-disabled":a(g),"aria-label":e.label,name:e.name,"true-value":e.activeValue,"false-value":e.inactiveValue,disabled:a(g),tabindex:e.tabindex,onChange:C,onKeydown:ue(F,["enter"])},null,42,ke),!e.inlinePrompt&&(e.inactiveIcon||e.inactiveText)?(o(),v("span",{key:0,class:r(a(Q))},[e.inactiveIcon?(o(),c(a(h),{key:0},{default:p(()=>[(o(),c(y(e.inactiveIcon)))]),_:1})):d("v-if",!0),!e.inactiveIcon&&e.inactiveText?(o(),v("span",{key:1,"aria-hidden":a(n)},P(e.inactiveText),9,Ie)):d("v-if",!0)],2)):d("v-if",!0),N("span",{ref_key:"core",ref:W,class:r(a(i).e("core")),style:O(a(j))},[e.inlinePrompt?(o(),v("div",{key:0,class:r(a(i).e("inner"))},[e.activeIcon||e.inactiveIcon?(o(),c(a(h),{key:0,class:r(a(i).is("icon"))},{default:p(()=>[(o(),c(y(a(n)?e.activeIcon:e.inactiveIcon)))]),_:1},8,["class"])):e.activeText||e.inactiveText?(o(),v("span",{key:1,class:r(a(i).is("text")),"aria-hidden":!a(n)},P(a(n)?e.activeText:e.inactiveText),11,we)):d("v-if",!0)],2)):d("v-if",!0),N("div",{class:r(a(i).e("action"))},[e.loading?(o(),c(a(h),{key:0,class:r(a(i).is("loading"))},{default:p(()=>[de(a(ve))]),_:1},8,["class"])):e.activeActionIcon&&a(n)?(o(),c(a(h),{key:1},{default:p(()=>[(o(),c(y(e.activeActionIcon)))]),_:1})):e.inactiveActionIcon&&!a(n)?(o(),c(a(h),{key:2},{default:p(()=>[(o(),c(y(e.inactiveActionIcon)))]),_:1})):d("v-if",!0)],2)],6),!e.inlinePrompt&&(e.activeIcon||e.activeText)?(o(),v("span",{key:1,class:r(a(Y))},[e.activeIcon?(o(),c(a(h),{key:0},{default:p(()=>[(o(),c(y(e.activeIcon)))]),_:1})):d("v-if",!0),!e.activeIcon&&e.activeText?(o(),v("span",{key:1,"aria-hidden":!a(n)},P(e.activeText),9,Ve)):d("v-if",!0)],2)):d("v-if",!0)],14,Se))}});var Be=pe(Te,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/switch/src/switch.vue"]]);const Ae=be(Be);export{Ae as E};
