#使用文件创建镜像
FROM harbor.kaiya.com:30443/public/kylin_jdk:1.8

#作者信息
MAINTAINER "winiger <EMAIL>"

ENV MNJX_PATH=/usr/local/mnjx_eterm
ENV PATH=$PATH:$MNJX_PATH
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

#设定工作目录
WORKDIR $MNJX_PATH

#拷贝工程
COPY target/mnjx_eterm.jar ./
COPY target/lib ./lib/

#拷贝执行脚本
#COPY entrypoint.sh ./

#打印的日志
VOLUME $MNJX_PATH/logs/

#容器暴露端口（可以暴露的，容器启动时确定具体暴露的端口）
EXPOSE 351 352

#执行的命令
CMD ["java","-jar","mnjx_eterm.jar"]
#ENTRYPOINT ["/bin/bash","-c","$MNJX_PATH/entrypoint.sh"]
