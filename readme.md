## 本地访问地址
### web接口地址
    <a>http://127.0.0.1:8351/mnjx/swagger-ui/index.html</a>
### web前端地址
    <a>http://localhost/sts</a>
### 定时任务地址
    <a>http://127.0.0.1:8380/mnjx_job</a>
    
    用户名：admin
    密码：MNJX&JOB1.1


### 描述 

#### mnjx_core
    单表操作的所有核心

#### mnjx_eterm
    指令模块

#### mnjx_printer
    打印机模块

#### mnjx_web
    web接口模块


查看系统发的tcp连接数

    netstat -an |grep 'TIME_WAIT' |grep 'tcp'

查询监听的端口

    netstat -an |grep 'tcp' | grep '370'

查询监听的端口（使用或者关系）

    -a或--all 显示所有连线中的Socket。
    -A<网络类型>或--<网络类型> 列出该网络类型连线中的相关地址。
    -c或--continuous 持续列出网络状态。
    -C或--cache 显示路由器配置的快取信息。
    -e或--extend 显示网络其他相关信息。
    -F或--fib 显示FIB。
    -g或--groups 显示多重广播功能群组组员名单。
    -h或--help 在线帮助。
    -i或--interfaces 显示网络界面信息表单。
    -l或--listening 显示监控中的服务器的Socket。
    -M或--masquerade 显示伪装的网络连线。
    -n或--numeric 直接使用IP地址，而不通过域名服务器。
    -N或--netlink或--symbolic 显示网络硬件外围设备的符号连接名称。
    -o或--timers 显示计时器。
    -p或--programs 显示正在使用Socket的程序识别码和程序名称。
    -r或--route 显示Routing Table。
    -s或--statistics 显示网络工作信息统计表。
    -t或--tcp 显示TCP传输协议的连线状况。
    -u或--udp 显示UDP传输协议的连线状况。
    -v或--verbose 显示指令执行过程。
    -V或--version 显示版本信息。
    -w或--raw 显示RAW传输协议的连线状况。ss
    -x或--unix 此参数的效果和指定"-A unix"参数相同。
    --ip或--inet 此参数的效果和指定"-A inet"参数相同。
    
    netstat -an | grep -E '350|37001'  
    
    netstat -anpto | grep -E '350|370|360|380' 

查看系统开启端口

    netstat -tnlp    



启动jar包

    前台启动 java -Dfile.encoding=utf-8 -jar mnjx_eterm-1.0-SNAPSHOT-shaded.jar
    后台启动 nohup java -Dfile.encoding=utf-8 -jar mnjx_eterm-1.0-SNAPSHOT-shaded.jar 2>&1 &

更新分支元数据

    参考：https://www.cnblogs.com/bigtreei/p/********.html
    git remote update origin --prune


#协议
>DA
[1, 0, 0, 23, 0, 0, 0, 1, 57, 81, 112, 2, 27, 11, 53, 32, 0, 15, 30, 100, 97, 32, 3]
>SI 2001 8888F 82
[1, 0, 0, 37, 0, 0, 0, 1, 57, 81, 112, 2, 27, 11, 39, 32, 0, 15, 30, 83, 73, 32, 50, 48, 48, 49, 32, 56, 56, 56, 56, 70, 32, 56, 50, 32, 3]
